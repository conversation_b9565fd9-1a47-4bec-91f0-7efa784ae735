@startuml execute-export时序图
!theme plain
title Summer - execute-export接口时序图

skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor "用户" as User
participant "ExecuteController" as Controller
participant "ExecuteService" as Service
participant "WebSQLContextInfo" as Context
participant "SummerThreadScheduler" as Scheduler
participant "ExportThread" as Thread
participant "WebDataTransfer" as Transfer
participant "DataTransferProcessor" as Processor
participant "ResultService" as ResultSvc
participant "MessageService" as MsgSvc
participant "FileUtil" as FileUtil
database "Redis缓存" as Redis
database "数据库" as Database
participant "外部存储" as Storage

== 1. 接收导出请求 ==
User -> Controller: POST /execute/execute-export
note right: SqlExportMessage包含:\n- 导出格式(XLSX/CSV/TXT等)\n- 文件编码、分隔符\n- 水印、加密参数\n- 脱敏配置等

Controller -> Controller: 验证请求参数
note right: @Valid注解验证\nSqlExportMessage参数

Controller -> Service: asyncSqlExport(message)
note right: 调用异步导出服务

== 2. 创建异步任务 ==
Service -> Context: getAndSetContext(token, tokenConfig)
note right: 获取或创建SQL执行上下文

Context -> Context: 验证会话状态
alt 会话不存在或已过期
    Context -> Database: 建立数据库连接
    Database --> Context: 连接成功
end

Service -> Context: createAsyncTask("Sql Export", taskFunction)
note right: 创建异步任务\n生成唯一taskId

Context -> Context: 生成WebAsyncTaskInfo
note right: 任务ID: 自增序列\n任务名称: "Sql Export"\n状态: 初始化

Service --> Controller: 返回WebAsyncTaskInfo
note right: 包含taskId和任务状态

Controller -> Scheduler: exec(EXECUTE_EXPORT, webAsyncTaskInfo)
note right: 提交任务到导出线程池

Controller --> User: Result<WebAsyncTaskInfo>
note right: 立即返回任务信息\n用户可通过taskId查询进度

== 3. 异步任务调度 ==
Scheduler -> Scheduler: 获取ExportThread线程池
note right: 根据ExecuteType.EXECUTE_EXPORT\n路由到专用导出线程

Scheduler -> Thread: execute(taskRunnable)
note right: 在导出线程池中执行任务

Thread -> Thread: 设置线程上下文
note right: HandlerCounter设置:\n- operator, userId\n- token, connectionPattern

== 4. 执行导出任务 ==
Thread -> Service: 执行taskFunction
note right: 异步任务的具体执行逻辑

Service -> Service: 获取DataTransferProcessor
note right: 根据exportType获取处理器\n如: stream.xlsx, stream.csv等

Service -> Service: getAsyncWebSQLQueryResultSets()
note right: 获取SQL结果集数据

loop 遍历SqlExportModel列表
    Service -> ResultSvc: getStringValue(taskResultMessage)
    note right: 从Redis获取SQL执行结果
    
    alt 结果集不存在
        Service -> Context: asyncTaskStatus(taskId)
        Context -> Context: 重新执行SQL查询
        Context --> Service: 返回执行结果
    end
    
    ResultSvc -> Redis: 获取缓存的结果集
    Redis --> ResultSvc: 返回WebSQLExecuteInfo
    ResultSvc --> Service: 返回结果集数据
end

== 5. 数据导出处理 ==
Service -> Transfer: exportDataByContext(参数列表)
note right: 核心导出方法\n包含所有导出配置

Transfer -> Transfer: 初始化导出环境
note right: 设置文件路径、命名规则\n配置压缩和上传参数

loop 处理每个结果集
    Transfer -> Transfer: 获取导出配置
    note right: 解析SqlExportMessage:\n- 文件编码、分隔符\n- 水印、加密设置
    
    alt 需要数据脱敏
        Transfer -> Transfer: 创建脱敏处理器
        note right: DBDDataDesensitizeProcessor\n根据规则脱敏敏感数据
    end
    
    Transfer -> Processor: 创建数据导出器实例
    note right: 根据格式创建:\n- DataExporterXLSX\n- DataExporterCSV等
    
    Transfer -> Transfer: 配置导出参数
    note right: StreamConsumerSettings:\n- 输出文件夹和文件名\n- 文件拆分大小限制\n- 编码格式等
    
    == 6. 文件生成过程 ==
    Transfer -> Processor: 初始化导出器
    Processor -> Processor: 创建输出文件
    note right: 在临时目录创建文件\n应用文件命名规则
    
    Transfer -> Processor: exportHeader()
    note right: 写入文件头部信息\n如CSV列名、Excel表头等
    
    loop 逐行导出数据
        Transfer -> Processor: exportRow(session, resultSet, row)
        note right: 处理每一行数据:\n- 数据类型转换\n- 脱敏处理\n- 格式化输出
        
        alt 文件大小超限
            Processor -> Processor: 创建新的分割文件
            note right: 根据splitFileSize参数\n自动拆分大文件
        end
        
        alt 任务被取消
            Transfer -> Transfer: 检查monitor.isCanceled()
            Transfer -> Transfer: 抛出中断异常
        end
    end
    
    Transfer -> Processor: exportFooter()
    note right: 写入文件尾部信息\n关闭文件流
end

== 7. 文件后处理 ==
alt 需要添加水印
    Transfer -> Transfer: 添加水印处理
    note right: 根据watermarkContent\n和watermarkAngle参数
end

alt 多个文件或需要加密
    Transfer -> Transfer: 创建ZIP压缩包
    note right: ZipUtils.createZip()\n可选密码保护
end

== 8. 文件上传存储 ==
Transfer -> FileUtil: fileUpload(文件路径, 文件, 用户ID)
note right: 上传到文件存储系统

FileUtil -> Storage: 上传文件
Storage --> FileUtil: 返回文件访问路径

FileUtil --> Transfer: 返回uploadPath
Transfer -> Transfer: 设置transferResult.path

== 9. 结果保存和通知 ==
Transfer --> Service: 返回WebSQLTransferResult
note right: 包含导出结果:\n- 成功/失败状态\n- 文件路径\n- 错误信息等

Service -> Service: 创建WebSQLExecuteInfo
note right: 封装导出结果信息

Service -> ResultSvc: saveStringValue(taskId, executeInfo)
note right: 保存任务结果到Redis\n设置过期时间

ResultSvc -> Redis: 缓存任务结果
Redis --> ResultSvc: 保存成功

alt 需要安全告警
    Service -> MsgSvc: sendAlertMessage(message, executeInfo)
    note right: 发送安全协作告警\n调用PHP接口
    
    MsgSvc -> MsgSvc: 发送邮件/消息通知
end

== 10. 任务完成 ==
Service -> Context: 更新任务状态
Context -> Context: setStatus(FINISHED)
note right: 更新WebAsyncTaskInfo状态

Thread -> Thread: 清理线程上下文
note right: HandlerCounter.release()

== 11. 用户查询结果 ==
User -> Controller: POST /execute/task-result
note right: TaskResultMessage\n包含taskId查询结果

Controller -> ResultSvc: asyncSqlExecuteResults(taskMessage)
ResultSvc -> Redis: 获取任务结果
Redis --> ResultSvc: 返回WebSQLExecuteInfo
ResultSvc --> Controller: 返回导出结果
Controller --> User: Result<WebSQLExecuteInfo>
note right: 包含文件下载路径\n和导出统计信息

@enduml
