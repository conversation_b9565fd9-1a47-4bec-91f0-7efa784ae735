package com.dc.summer.exec.monitor.impl;

import com.dc.summer.exec.handler.DataSourceConnectionHandler;
import com.dc.summer.exec.handler.RecordHandler;
import com.dc.summer.exec.model.ConnectionManager;
import com.dc.summer.exec.model.type.RecordType;
import com.dc.summer.exec.monitor.AbstractCacheStatMonitor;
import com.dc.summer.exec.monitor.data.SessionStatResult;
import com.dc.summer.exec.monitor.data.StatParam;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.utils.ArithmeticUtils;
import com.dc.utils.bean.CloneUtils;
import com.dc.utils.net.IPUtils;

import java.util.*;
import java.util.stream.Collectors;

public class SessionMonitor extends AbstractCacheStatMonitor<SessionStatResult> {

    @Override
    protected void execute() {

        final Map<String, ConnectionManager> connectionManagerMap = CloneUtils.transListToMap(
                new ArrayList<>(DataSourceConnectionHandler.getFake().getAllConnectionManagers().stream().filter(Objects::nonNull).collect(Collectors.toList())),
                DBPDataSourceContainer::getId,
                connectionManager -> connectionManager);

        Map<String, SessionStatResult> resultMap = CloneUtils.transListToMap(super.read(),
                cord -> ((SessionStatResult) cord.getRow()).getSessionId(), cord -> (SessionStatResult) cord.getRow());

        RecordHandler handler = RecordHandler.handle(RecordType.SESSION);
        List<RecordHandler.Record> list = handler.extractRow(StatParam.class);
        super.cachesToList(list);
        list.stream()
                .sorted(Comparator.comparing(RecordHandler.Record::getTime))
                .filter(cord -> cord.getRow() != null && ((StatParam) cord.getRow()).getContainerId() != null)
                .collect(Collectors.groupingBy(cord -> ((StatParam) cord.getRow()).getToken()))
                .values()
                .forEach(records -> {

                    RecordHandler.Record start = null;
                    RecordHandler.Record read = null;
                    RecordHandler.Record renew = null;

                    for (RecordHandler.Record cord : records) {
                        StatParam param = (StatParam) cord.getRow();
                        String sessionId = param.getToken();
                        if (sessionId == null) {
                            list.remove(cord);
                            continue;
                        }

                        Long row = param.getRow();
                        resultMap.putIfAbsent(sessionId, new SessionStatResult());
                        SessionStatResult result = resultMap.get(sessionId);
                        Date date = new Date();
                        switch (cord.getSign()) {
                            case CONTEXT_ADD:
                                String containerId = param.getContainerId();
                                ConnectionManager connectionManager = connectionManagerMap.get(containerId);
                                if (connectionManager == null) {
                                    resultMap.remove(sessionId);
                                } else {
                                    DataSourceConnectionHandler container = (DataSourceConnectionHandler) connectionManager;
                                    result.setContainerId(containerId);
                                    result.setConnectionId(param.getConnectionId());
                                    result.setContainerName(container.getName());
                                    result.setConnectionDesc(container.getConnectionDesc());
                                    result.setInstanceName(container.getInstanceName());
                                    result.setEnvironment(container.getEnvironment());
                                    result.setDbType(container.getDatabaseType().getValue());
                                    result.setNode(IPUtils.getSpringBootNode());
                                    result.setSessionId(sessionId);
                                    result.setCreateDate(date);
                                    result.setLastDate(date);
                                    result.setRequestCount(ArithmeticUtils.plus(result.getRequestCount()));
                                    result.setOperator(param.getOperator());
                                    result.setUserId(param.getUserId());
                                    result.setConnectionPattern(param.getConnectionPattern());
                                }
                                list.remove(cord);
                                break;
                            case CONTEXT_GET:
                                result.setLastDate(date);
                                result.setRequestCount(ArithmeticUtils.plus(result.getRequestCount()));
                                result.setOperator(param.getOperator());
                                result.setUserId(param.getUserId());
                                result.setConnectionPattern(param.getConnectionPattern());
                                list.remove(cord);
                                break;
                            case CONTEXT_CLOSE:
                                resultMap.remove(sessionId);
                                list.remove(cord);
                                break;
                            case EXECUTE_START:
                                start = cord;
                                result.setOngoing(true);
                                result.setCurrSql(param.getSql());
                                break;
                            case READ_START:
                                read = cord;
                                break;
                            case RENEW_START:
                                renew = cord;
                                break;
                            case EXECUTE_OVER:
                            case EXECUTE_ERROR:
                                if (start != null) {
                                    long time = cord.getTime() - start.getTime();
                                    result.setJdbcExecuteCount(ArithmeticUtils.plus(result.getJdbcExecuteCount()));
                                    result.setJdbcExecuteTime(ArithmeticUtils.plus(result.getJdbcExecuteTime(), time));
                                    result.setOngoing(false);
                                    result.setRequestTime(ArithmeticUtils.plus(result.getRequestTime(), time));
                                    result.setCurrSql(null);
                                    list.remove(start);
                                    list.remove(cord);
                                }
                                break;
                            case READ_OVER:
                                if (read != null) {
                                    long time = cord.getTime() - read.getTime();
                                    result.setReadRowCount(ArithmeticUtils.plus(row, result.getReadRowCount()));
                                    result.setRequestTime(ArithmeticUtils.plus(result.getRequestTime(), time));
                                    list.remove(read);
                                    list.remove(cord);
                                }
                                break;
                            case RENEW_OVER:
                                if (renew != null) {
                                    long time = cord.getTime() - renew.getTime();
                                    result.setRenewRowCount(ArithmeticUtils.plus(row, result.getRenewRowCount()));
                                    result.setRequestTime(ArithmeticUtils.plus(result.getRequestTime(), time));
                                    list.remove(renew);
                                    list.remove(cord);
                                }
                                break;
                            case WAIT_COMMIT:
                                result.setHasTransaction(true);
                                list.remove(cord);
                                break;
                            case NONE_COMMIT:
                                result.setHasTransaction(false);
                                list.remove(cord);
                                break;
                            case EXECUTE_COMMIT:
                                result.setTransactionCommit(ArithmeticUtils.plus(result.getTransactionCommit()));
                                list.remove(cord);
                                break;
                            case EXECUTE_ROLLBACK:
                                result.setTransactionRollback(ArithmeticUtils.plus(result.getTransactionRollback()));
                                list.remove(cord);
                                break;
                            default:
                        }
                    }
                });
        super.listToCaches(list);
        super.write(resultMap.values());
    }

    @Override
    protected RecordType getRecordType() {
        return RecordType.STAT_SESSION;
    }

    @Override
    public String getName() {
        return "stat.session";
    }

}
