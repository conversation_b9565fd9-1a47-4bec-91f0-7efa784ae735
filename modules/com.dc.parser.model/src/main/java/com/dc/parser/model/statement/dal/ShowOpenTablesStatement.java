package com.dc.parser.model.statement.dal;

import com.dc.parser.model.segment.dal.FromDatabaseSegment;
import com.dc.parser.model.segment.dal.ShowFilterSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Setter;

import java.util.Optional;

/**
 * Show open tables statement.
 */
@Setter
public abstract class ShowOpenTablesStatement extends AbstractSQLStatement implements DALStatement {

    private FromDatabaseSegment fromDatabase;

    private ShowFilterSegment filter;

    /**
     * Get from database.
     *
     * @return from database
     */
    public Optional<FromDatabaseSegment> getFromDatabase() {
        return Optional.ofNullable(fromDatabase);
    }

    /**
     * Get filter segment.
     *
     * @return filter segment
     */
    public Optional<ShowFilterSegment> getFilter() {
        return Optional.ofNullable(filter);
    }
}
