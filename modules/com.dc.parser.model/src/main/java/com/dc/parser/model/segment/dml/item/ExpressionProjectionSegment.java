
package com.dc.parser.model.segment.dml.item;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.complex.ComplexExpressionSegment;
import com.dc.parser.model.segment.generic.AliasAvailable;
import com.dc.parser.model.segment.generic.AliasSegment;
import com.dc.parser.model.util.SQLUtils;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

/**
 * Expression projection segment.
 */
@Getter
public final class ExpressionProjectionSegment implements ProjectionSegment, ComplexExpressionSegment, AliasAvailable {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    private final String text;
    
    private final ExpressionSegment expr;
    
    @Setter
    private AliasSegment alias;
    
    public ExpressionProjectionSegment(final int startIndex, final int stopIndex, final String text) {
        this.startIndex = startIndex;
        this.stopIndex = stopIndex;
        this.text = SQLUtils.getExpressionWithoutOutsideParentheses(text);
        this.expr = null;
    }
    
    public ExpressionProjectionSegment(final int startIndex, final int stopIndex, final String text, final ExpressionSegment expr) {
        this.startIndex = startIndex;
        this.stopIndex = stopIndex;
        this.text = SQLUtils.getExpressionWithoutOutsideParentheses(text);
        this.expr = expr;
    }
    
    @Override
    public String getColumnLabel() {
        return getAliasName().orElse(text);
    }
    
    @Override
    public Optional<String> getAliasName() {
        return null == alias ? Optional.empty() : Optional.ofNullable(alias.getIdentifier().getValue());
    }
    
    @Override
    public Optional<IdentifierValue> getAlias() {
        return Optional.ofNullable(alias).map(AliasSegment::getIdentifier);
    }
    
    /**
     * Get alias segment.
     *
     * @return alias segment
     */
    public AliasSegment getAliasSegment() {
        return alias;
    }
    
    @Override
    public int getStartIndex() {
        return null != alias && alias.getStartIndex() < startIndex ? alias.getStartIndex() : startIndex;
    }
    
    @Override
    public int getStopIndex() {
        return null != alias && alias.getStopIndex() > stopIndex ? alias.getStopIndex() : stopIndex;
    }
}
