
package com.dc.parser.model.segment.dml.expr.subquery;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import com.dc.parser.model.enums.SubqueryType;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.statement.dml.MergeStatement;
import com.dc.parser.model.statement.dml.SelectStatement;

/**
 * Subquery segment.
 */
@RequiredArgsConstructor
@Getter
public final class SubquerySegment implements ExpressionSegment {
    
    private final int startIndex;
    
    private final int stopIndex;
    
    @Setter
    private SelectStatement select;
    
    @Setter
    private MergeStatement merge;
    
    private final String text;
    
    @Setter
    private SubqueryType subqueryType;
    
    public SubquerySegment(final int startIndex, final int stopIndex, final SelectStatement select, final String text) {
        this.startIndex = startIndex;
        this.stopIndex = stopIndex;
        this.select = select;
        this.text = text;
    }
}
