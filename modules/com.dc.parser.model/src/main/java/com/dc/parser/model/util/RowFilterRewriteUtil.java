package com.dc.parser.model.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.dc.parser.model.enums.CombineType;
import com.dc.parser.model.segment.dml.assignment.ColumnAssignmentSegment;
import com.dc.infra.database.enums.QuoteCharacter;
import com.dc.parser.model.segment.dml.assignment.SetAssignmentSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.BetweenExpression;
import com.dc.parser.model.segment.dml.expr.CollateExpression;
import com.dc.parser.model.segment.dml.expr.ExistsSubqueryExpression;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.dml.expr.InExpression;
import com.dc.parser.model.segment.dml.expr.ListExpression;
import com.dc.parser.model.segment.dml.expr.NotExpression;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubquerySegment;
import com.dc.parser.model.segment.dml.expr.BinaryOperationExpression;
import com.dc.parser.model.segment.dml.item.*;
import com.dc.parser.model.segment.dml.order.GroupBySegment;
import com.dc.parser.model.segment.dml.order.OrderBySegment;
import com.dc.parser.model.segment.dml.order.item.ExpressionOrderByItemSegment;
import com.dc.parser.model.segment.dml.order.item.OrderByItemSegment;
import com.dc.parser.model.segment.dml.predicate.WhereSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.table.*;
import com.dc.parser.model.util.token.StringToken;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import org.apache.commons.lang3.StringUtils;

public class RowFilterRewriteUtil {
    public static final String ROW_FILTER_TABLE = "%RowFilterTable";
    public static final String CAST = "cast(";
    public static final String ROW_FILTER_CAST_TABLE_FORMAT = "cast(%RowFilterTable";

    public RowFilterRewriteUtil() {
    }

    public static String rewrite(SelectStatement selectStatement, Map<TableNameSegment, String> tableRowFilter, String sql) {
        List<SelectStatement> list = extractAllSelectFromSelect(selectStatement);
        List<StringToken> tokens = getRowFilterStringTokensFromSelects(list, sql, tableRowFilter);
        return rewriteSQL(tokens, sql);
    }

    public static String rewriteDeleteToSelectCount(DeleteStatement deleteStatement, String originSQL, String ownerName) {
        TableSegment tableSegment = deleteStatement.getTable();
        if (!(tableSegment instanceof SimpleTableSegment)) {
            return null;
        } else {
            String tableName = ((SimpleTableSegment) tableSegment).getTableName().getIdentifier().getValueWithQuoteCharacters();
            String whereString;
            if (((SimpleTableSegment) tableSegment).getOwner().isPresent()) {
                String owner = (((SimpleTableSegment) tableSegment).getOwner().get()).getIdentifier().getValueWithQuoteCharacters();
                tableName = owner + "." + tableName;
            } else if (!StringUtils.isEmpty(ownerName)) {
                QuoteCharacter quoteCharacter = ((SimpleTableSegment) tableSegment).getTableName().getIdentifier().getQuoteCharacter();
                whereString = quoteCharacter.wrap(ownerName);
                tableName = whereString + "." + tableName;
            }

            if (StringUtils.isEmpty(tableName)) {
                return null;
            } else if (deleteStatement.getWhere().isEmpty()) {
                return "SELECT COUNT(*) as \"COUNT\" FROM " + tableName;
            } else {
                WhereSegment whereSegment = deleteStatement.getWhere().get();
                whereString = originSQL.substring(whereSegment.getStartIndex(), whereSegment.getStopIndex() + 1);
                return whereString.trim().toUpperCase().startsWith("WHERE")
                        ? "SELECT COUNT(*) as \"COUNT\" FROM " + tableName + " " + whereString
                        : "SELECT COUNT(*) as \"COUNT\" FROM " + tableName + " WHERE " + whereString;
            }
        }
    }

    public static String rewriteUpdateToSelectCount(UpdateStatement updateStatement, String originSQL, String ownerName) {
        TableSegment tableSegment = updateStatement.getTable();
        if (!(tableSegment instanceof SimpleTableSegment)) {
            return null;
        } else {
            String tableName = ((SimpleTableSegment) tableSegment).getTableName().getIdentifier().getValueWithQuoteCharacters();
            if (StringUtils.isEmpty(tableName)) {
                return null;
            } else if (updateStatement.getWhere().isEmpty()) {
                return "SELECT COUNT(*) as \"COUNT\" FROM " + tableName;
            } else {
                WhereSegment whereSegment = updateStatement.getWhere().get();
                String whereString = originSQL.substring(whereSegment.getStartIndex(), whereSegment.getStopIndex() + 1);
                return whereString.trim().toUpperCase().startsWith("WHERE")
                        ? "SELECT COUNT(*) as \"COUNT\" FROM " + tableName + " " + whereString
                        : "SELECT COUNT(*) as \"COUNT\" FROM " + tableName + " WHERE " + whereString;
            }
        }
    }

    public static String rewriteSQL(List<StringToken> tokens, String sql) {
        if (tokens != null && tokens.size() != 0) {
            StringBuilder result = new StringBuilder(sql.substring(0, (tokens.get(0)).getStartIndex()));

            for (StringToken token : tokens) {
                result.append(token.getText());
                int nowIndex = tokens.indexOf(token);
                int nextIndex = nowIndex + 1;
                if (nextIndex >= tokens.size()) {
                    result.append(sql.substring(token.getStopIndex()));
                } else {
                    StringToken nextToken = tokens.get(nextIndex);
                    result.append(sql, token.getStopIndex(), nextToken.getStartIndex());
                }
            }

            return result.toString();
        } else {
            return sql;
        }
    }

    public static List<SimpleTableSegment> extractTableNameFromSelect(SelectStatement selectStatement) {
        List<SelectStatement> list = extractAllSelectFromSelect(selectStatement);
        List<SimpleTableSegment> tableSegments = new ArrayList<>();
        list.forEach((e) -> {
            tableSegments.addAll(extractTableNameFromSelectFromSegment(e.getFrom().isPresent() ? e.getFrom().get() : null));
        });
        return tableSegments;
    }

    public static List<FunctionSegment> extractFunctionSegmentSelect(SelectStatement select) {
        if (select == null) {
            return new ArrayList<>();
        } else {
            List<SelectStatement> list = extractAllSelectFromSelect(select);
            List<FunctionSegment> result = new ArrayList<>();

            for (SelectStatement selectStatement : list) {
                if (selectStatement.getProjections() != null) {
                    result.addAll(extractFunctionSegmentFromProjection(selectStatement.getProjections()));
                }

                if (selectStatement.getWhere().isPresent()) {
                    result.addAll(extractFunctionSegmentFromWhereSegment(selectStatement.getWhere().get()));
                }

                if (selectStatement.getGroupBy().isPresent()) {
                    result.addAll(extractFunctionSegmentFromGroupBySegment(selectStatement.getGroupBy().get()));
                }

                if (selectStatement.getOrderBy().isPresent()) {
                    result.addAll(extractFunctionSegmentFromOrderBySegment(selectStatement.getOrderBy().get()));
                }

                if (selectStatement.getHaving().isPresent()) {
                    result.addAll(extractFunctionSegmentFromExpression((selectStatement.getHaving().get()).getExpr()));
                }
            }

            // todo
            /*List<FunctionSegment> newList = (List)result.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing((f) -> {
                    String comKey = f.getFunctionName();
                    comKey = comKey + (String)Optional.ofNullable(f.getOwner()).map(OwnerSegment::getIdentifier).map(IdentifierValue::getValue).orElse((Object)null);
                    comKey = comKey + (String)Optional.ofNullable(f.getSchema()).map(OwnerSegment::getIdentifier).map(IdentifierValue::getValue).orElse((Object)null);
                    return comKey;
                }));
            }), ArrayList::new));*/

            return result;
        }
    }

    private static List<FunctionSegment> extractFunctionSegmentFromOrderBySegment(OrderBySegment orderBySegment) {
        List<FunctionSegment> result = new ArrayList<>();
        if (orderBySegment == null) {
            return result;
        } else if (orderBySegment.getOrderByItems() != null && orderBySegment.getOrderByItems().size() != 0) {
            for (OrderByItemSegment item : orderBySegment.getOrderByItems()) {
                if (item instanceof ExpressionOrderByItemSegment) {
                    result.addAll(extractFunctionSegmentFromExpression(((ExpressionOrderByItemSegment) item).getExpr()));
                }
            }

            return result;
        } else {
            return result;
        }
    }

    private static List<FunctionSegment> extractFunctionSegmentFromGroupBySegment(GroupBySegment groupBySegment) {
        List<FunctionSegment> result = new ArrayList<>();
        if (groupBySegment == null) {
            return result;
        } else if (groupBySegment.getGroupByItems() != null && groupBySegment.getGroupByItems().size() != 0) {
            for (OrderByItemSegment item : groupBySegment.getGroupByItems()) {
                if (item instanceof ExpressionOrderByItemSegment) {
                    result.addAll(extractFunctionSegmentFromExpression(((ExpressionOrderByItemSegment) item).getExpr()));
                }
            }

            return result;
        } else {
            return result;
        }
    }

    private static List<FunctionSegment> extractFunctionSegmentFromWhereSegment(WhereSegment where) {
        List<FunctionSegment> result = new ArrayList<>();
        return where == null ? result : extractFunctionSegmentFromExpression(where.getExpr());
    }

    private static List<FunctionSegment> extractFunctionSegmentFromProjection(ProjectionsSegment projections) {
        List<FunctionSegment> result = new ArrayList<>();
        if (projections == null) {
            return new ArrayList<>();
        } else if (projections.getProjections() != null && projections.getProjections().size() != 0) {
            for (ProjectionSegment projection : projections.getProjections()) {
                if (projection instanceof AggregationProjectionSegment) {
                    FunctionSegment functionSegment = new FunctionSegment(projection.getStartIndex(), projection.getStopIndex(), ((AggregationProjectionSegment) projection).getType().name(), ((AggregationProjectionSegment) projection).getExpression());
                    result.add(functionSegment);
                } else if (projection instanceof ExpressionProjectionSegment) {
                    ExpressionProjectionSegment expressionProjectionSegment = (ExpressionProjectionSegment) projection;
                    if (expressionProjectionSegment.getExpr() != null) {
                        ExpressionSegment expr = expressionProjectionSegment.getExpr();
                        result.addAll(extractFunctionSegmentFromExpression(expr));
                    }
                }
            }
            return result;
        } else {
            return new ArrayList<>();
        }
    }

    private static List<FunctionSegment> extractFunctionSegmentFromExpression(ExpressionSegment expr) {
        List<FunctionSegment> result = new ArrayList<>();
        if (expr == null) {
            return result;
        } else {
            if (expr instanceof AggregationProjectionSegment) {
                FunctionSegment functionSegment = new FunctionSegment(expr.getStartIndex(), expr.getStopIndex(), ((AggregationProjectionSegment) expr).getType().name(), ((AggregationProjectionSegment) expr).getExpression());
                result.add(functionSegment);
                for (ExpressionSegment expressionSegment : ((AggregationProjectionSegment) expr).getParameters()) {
                    result.addAll(extractFunctionSegmentFromExpression(expressionSegment));
                }
                return result;
            } else if (expr instanceof BinaryOperationExpression) {
                result.addAll(extractFunctionSegmentFromExpression(((BinaryOperationExpression) expr).getLeft()));
                result.addAll(extractFunctionSegmentFromExpression(((BinaryOperationExpression) expr).getRight()));
                return result;
            } else {
                if (expr instanceof FunctionSegment) {
                    result.add((FunctionSegment) expr);
                    for (ExpressionSegment expressionSegment : ((FunctionSegment) expr).getParameters()) {
                        result.addAll(extractFunctionSegmentFromExpression(expressionSegment));
                    }
                    return result;
                }
                // todo
                /*else if (expr instanceof FunctionExpression) {
                    functionSegment = new FunctionSegment(expr.getStartIndex(), expr.getStopIndex(), ((FunctionExpression)expr).getFunctionName().getValue(), ((FunctionExpression)expr).getFunctionName().getValue());
                    functionSegment.setOwner((OwnerSegment)((FunctionExpression)expr).getOwner().orElse((Object)null));
                    functionSegment.setSchema(((FunctionExpression)expr).getSchema());
                    result.add(functionSegment);
                    var6 = ((FunctionExpression)expr).getFunctionParameters().iterator();

                    while(var6.hasNext()) {
                        FunctionParameterSegment param = (FunctionParameterSegment)var6.next();
                        result.addAll(extractFunctionSegmentFromExpression(param.getFunctionParameterExpress()));
                    }

                    return result;
                } else if (expr instanceof ExpressionsSegment) {
                    var2 = ((ExpressionsSegment)expr).getExpressionSegments().iterator();

                    while(var2.hasNext()) {
                        ExpressionSegment expression = (ExpressionSegment)var2.next();
                        result.addAll(extractFunctionSegmentFromExpression(expression));
                    }

                    return result;
                } */
                else if (expr instanceof InExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((InExpression) expr).getLeft()));
                    result.addAll(extractFunctionSegmentFromExpression(((InExpression) expr).getRight()));
                    return result;
                }
                // todo
                /*else if (expr instanceof IsNotExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((IsNotExpression)expr).getExpression()));
                    return result;
                } else if (expr instanceof UnaryExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((UnaryExpression)expr).getExpression()));
                    return result;
                } else if (expr instanceof SingleKeyWordUnaryExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((SingleKeyWordUnaryExpression)expr).getExpression()));
                    return result;
                }*/
                else if (expr instanceof NotExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((NotExpression) expr).getExpression()));
                    return result;
                } else if (expr instanceof BetweenExpression) {
                    result.addAll(extractFunctionSegmentFromExpression(((BetweenExpression) expr).getLeft()));
                    result.addAll(extractFunctionSegmentFromExpression(((BetweenExpression) expr).getAndExpr()));
                    result.addAll(extractFunctionSegmentFromExpression(((BetweenExpression) expr).getBetweenExpr()));
                    return result;
                } else if (!(expr instanceof ListExpression)) {
                    return result;
                } else {
                    for (ExpressionSegment expressionSegment : ((ListExpression) expr).getItems()) {
                        result.addAll(extractFunctionSegmentFromExpression(expressionSegment));
                    }
                    return result;
                }
            }
        }
    }

    public static List<StringToken> getRowFilterStringTokens(List<SelectStatement> list, String originSQL, Map<TableNameSegment, String> tableRowFilter) {
        return getRowFilterStringTokensFromSelects(list, originSQL, tableRowFilter);
    }

    public static List<StringToken> getColumnDesensStringTokens(List<SelectStatement> list, String originSQL, Map<ColumnSegment, String> columnDesens, String currentSchema) {
        return null;
    }

    private static List<StringToken> getRowFilterStringTokensFromSelects(List<SelectStatement> list, String originSQL, Map<TableNameSegment, String> tableRowFilter) {
        List<StringToken> stringTokens = new ArrayList<>();
        for (SelectStatement select : list) {
            stringTokens.addAll(getStringTokensFromSelect(select, originSQL, tableRowFilter));
        }
        return stringTokens.stream().sorted().collect(Collectors.toList());
    }

    private static List<StringToken> getColumnDesensStringTokensFromSelects(List<SelectStatement> list, String originSQL, Map<ColumnSegment, String> columnDesens, String currentSchema) {
        return null;
    }

    private static List<SimpleTableSegment> extractTableNameFromSelectFromSegment(TableSegment tableSegment) {
        List<SimpleTableSegment> tables = new ArrayList<>();
        if (tableSegment == null) {
            return tables;
        } else if (tableSegment instanceof SubqueryTableSegment) {
            return tables;
        } else {
            if (tableSegment instanceof SimpleTableSegment) {
                tables.add((SimpleTableSegment) tableSegment);
            }

            if (tableSegment instanceof JoinTableSegment) {
                tables.addAll(extractTableNameFromSelectFromSegment(((JoinTableSegment) tableSegment).getLeft()));
                tables.addAll(extractTableNameFromSelectFromSegment(((JoinTableSegment) tableSegment).getRight()));
            }

            return tables;
        }
    }

    private static String generateWhereConditionText(List<SimpleTableSegment> tables, Map<TableNameSegment, String> tableRowFilter) {
        StringBuilder result = new StringBuilder();
        if (tables != null && !tables.isEmpty()) {
            boolean needAnd = false;
            for (SimpleTableSegment simpleTableSegment : tables) {
                String tableName = simpleTableSegment.getTableName().getIdentifier().getValue();
                int i = 0;
                for (Map.Entry<TableNameSegment, String> tableNameSegmentStringEntry : tableRowFilter.entrySet()) {
                    String filterTableName = tableNameSegmentStringEntry.getKey().getIdentifier().getValue();
                    if (Objects.equals(tableName, filterTableName)) {
                        String entryValue = tableNameSegmentStringEntry.getValue();
                        if (i == 0) {
                            if (needAnd) {
                                result.append(" ");
                                result.append("and");
                            }

                            result.append(" ");
                            if (entryValue.startsWith("cast(%RowFilterTable")) {
                                result.append(rowFilterTableFormat(entryValue, getFullyQualifiedNameFromSimpleTable(simpleTableSegment)));
                            } else {
                                result.append(getFullyQualifiedNameFromSimpleTable(simpleTableSegment));
                                result.append(entryValue);
                            }

                            needAnd = true;
                        } else {
                            result.append(" ");
                            result.append("and");
                            result.append(" ");
                            if (entryValue.startsWith("cast(%RowFilterTable")) {
                                result.append(rowFilterTableFormat(entryValue, getFullyQualifiedNameFromSimpleTable(simpleTableSegment)));
                            } else {
                                result.append(getFullyQualifiedNameFromSimpleTable(simpleTableSegment));
                                result.append(entryValue);
                            }
                        }

                        ++i;
                    }
                }
            }

            return result.toString();
        } else {
            return null;
        }
    }

    private static String rowFilterTableFormat(String format, String table) {
        int indexOf = format.indexOf("%RowFilterTable");
        if (indexOf != -1) {
            String start = format.substring(0, indexOf);
            String end = format.substring(indexOf + "%RowFilterTable".length());
            return start + table + end;
        } else {
            return format;
        }
    }

    private static String getFullyQualifiedNameFromSimpleTable(SimpleTableSegment simpleTableSegment) {
        String result = "";
        if (simpleTableSegment.getAlias().isPresent()) {
            result = result + simpleTableSegment.getAlias().get();
            return result + ".";
        } else {
            result = simpleTableSegment.getTableName().getIdentifier().getValueWithQuoteCharacters() + "." + result;
            if (simpleTableSegment.getOwner().isPresent()) {
                result = simpleTableSegment.getOwner().map(OwnerSegment::getIdentifier).map(IdentifierValue::getValueWithQuoteCharacters).map((r) -> {
                    return r + ".";
                }).orElse("") + result;
            }

            if (simpleTableSegment.getOwner().isPresent() && (simpleTableSegment.getOwner().get()).getOwner().isPresent()) {
                result = (simpleTableSegment.getOwner().get()).getOwner().map(OwnerSegment::getIdentifier).map(IdentifierValue::getValueWithQuoteCharacters).map((r) -> {
                    return r + ".";
                }).orElse("") + result;
            }

            return result;
        }
    }

    private static List<StringToken> getStringTokensFromSelect(SelectStatement select, String originSQL, Map<TableNameSegment, String> tableRowFilter) {
        List<StringToken> stringTokens = new ArrayList<>();
        List<SimpleTableSegment> tables = extractTableNameFromSelectFromSegment(select.getFrom().isEmpty() ? null : select.getFrom().get());
        String whereText = generateWhereConditionText(tables, tableRowFilter);
        if (whereText != null && !"".equalsIgnoreCase(whereText)) {
            int whereStartIndex;
            if (select.getWhere().isEmpty()) {
                if (select.getFrom().isEmpty()) {
                    return stringTokens;
                }

                TableSegment from = select.getFrom().get();
                whereStartIndex = from.getStopIndex() + 1;
                StringToken whereToken = new StringToken(whereStartIndex);
                whereToken.setText(" where " + whereText);
                stringTokens.add(whereToken);
            } else {
                WhereSegment where = select.getWhere().get();
                whereStartIndex = where.getStartIndex();
                int leftPStartIndex = where.getStartIndex() - 1;
                if (whereStartIndex + 5 <= originSQL.length()) {
                    String head5 = originSQL.substring(whereStartIndex, whereStartIndex + 5);
                    if ("where".equalsIgnoreCase(head5)) {
                        leftPStartIndex = whereStartIndex + 5;
                    }
                }

                StringToken leftPToken = new StringToken(leftPStartIndex);
                leftPToken.setText(" (");
                int rightPStartIndex = where.getStopIndex() + 1;
                StringToken rightPToken = new StringToken(rightPStartIndex);
                rightPToken.setText(") and " + whereText);
                stringTokens.add(leftPToken);
                stringTokens.add(rightPToken);
            }

            return stringTokens;
        } else {
            return stringTokens;
        }
    }

    public static List<SelectStatement> extractAllSelectFromSelect(SelectStatement selectStatement) {
        if (Objects.isNull(selectStatement)) {
            return new ArrayList<>();
        } else {
            List<SelectStatement> list = new ArrayList<>();
            list.add(selectStatement);
            list.addAll(extractAllSelectFromProjectionsSegment(selectStatement.getProjections()));
            list.addAll(extractAllSelectFromFromTableSegment(selectStatement.getFrom().isEmpty() ? null : selectStatement.getFrom().get()));
            list.addAll(extractAllSelectFromWhereSegment(selectStatement.getWhere().orElse(null)));
            // todo
            /*list.addAll(extractAllSelectFromUnionSegment(selectStatement.getUnionSegments()));*/
            return list.stream().distinct().collect(Collectors.toList());
        }
    }

    public static List<SelectStatement> extractAllSelectFromSelectWithoutSelf(SelectStatement selectStatement) {
        if (Objects.isNull(selectStatement)) {
            return new ArrayList<>();
        } else {
            List<SelectStatement> list = new ArrayList<>();
            list.addAll(extractAllSelectFromProjectionsSegment(selectStatement.getProjections()));
            list.addAll(extractAllSelectFromFromTableSegment(selectStatement.getFrom().orElse(null)));
            list.addAll(extractAllSelectFromWhereSegment(selectStatement.getWhere().orElse(null)));
            // todo
            /*list.addAll(extractAllSelectFromUnionSegment(selectStatement.getUnionSegments()));*/
            return list.stream().distinct().collect(Collectors.toList());
        }
    }

    // todo
    /*private static List<SelectStatement> extractAllSelectFromUnionSegment(Collection<UnionSegment> unionSegments) {
        List<SelectStatement> list = new ArrayList<>();
        if (unionSegments == null) {
            return list;
        } else if (unionSegments.size() == 0) {
            return list;
        } else {
            for (UnionSegment unionSegment : unionSegments) {
                if (unionSegment.getSelectStatement() != null) {
                    list.addAll(extractAllSelectFromSelect(unionSegment.getSelectStatement()));
                }
            }

            return list;
        }
    }*/

    public static List<SelectStatement> extractAllSelectFromWhereSegment(WhereSegment where) {
        List<SelectStatement> list = new ArrayList<>();
        if (where == null) {
            return list;
        } else {
            list.addAll(extractAllSelectFromExpr(where.getExpr()));
            return list;
        }
    }

    private static List<SelectStatement> extractAllSelectFromExpr(ExpressionSegment expr) {
        List<SelectStatement> list = new ArrayList<>();
        if (expr == null) {
            return list;
        } else {
            if (expr instanceof SubquerySegment) {
                list.addAll(extractAllSelectFromSelect(((SubquerySegment) expr).getSelect()));
            }

            if (expr instanceof ExistsSubqueryExpression) {
                list.addAll(extractAllSelectFromSelect(((ExistsSubqueryExpression) expr).getSubquery().getSelect()));
            }

            if (expr instanceof ListExpression) {
                list.addAll(extractAllSelectFromListExpression((ListExpression) expr));
            }

            if (expr instanceof SubqueryExpressionSegment) {
                list.addAll(extractAllSelectFromSelect(((SubqueryExpressionSegment) expr).getSubquery().getSelect()));
            }

            if (expr instanceof BetweenExpression) {
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getBetweenExpr()));
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getAndExpr()));
            }

            if (expr instanceof ExpressionProjectionSegment) {
                list.addAll(extractAllSelectFromExpr(((ExpressionProjectionSegment) expr).getExpr()));
            }

            if (expr instanceof NotExpression) {
                list.addAll(extractAllSelectFromExpr(((NotExpression) expr).getExpression()));
            }

            // todo
            /*if (expr instanceof UnaryExpression) {
                list.addAll(extractAllSelectFromExpr(((UnaryExpression)expr).getExpression()));
            }*/

            if (expr instanceof BinaryOperationExpression) {
                list.addAll(extractAllSelectFromExpr(((BinaryOperationExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((BinaryOperationExpression) expr).getRight()));
            }

            if (expr instanceof InExpression) {
                list.addAll(extractAllSelectFromExpr(((InExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((InExpression) expr).getRight()));
            }

            // todo
            /*if (expr instanceof CommonTableExpressionSegment) {
                WithSubquerySegment withSubquerySegment = ((CommonTableExpressionSegment)expr).getSubquery();
                if (withSubquerySegment.getSqlStatement() instanceof SelectStatement && !((SelectStatement)withSubquerySegment.getSqlStatement()).isTrinoValues()) {
                    list.add((SelectStatement)withSubquerySegment.getSqlStatement());
                }
            }*/

            if (expr instanceof ListExpression) {
                list.addAll(extractAllSelectFromListExpression((ListExpression) expr));
            }

            return list;
        }
    }

    private static List<SelectStatement> extractAllSelectFromFromTableSegment(TableSegment tableSegment) {
        List<SelectStatement> list = new ArrayList<>();
        if (tableSegment == null) {
            return list;
        } else {
            if (tableSegment instanceof SubqueryTableSegment) {
                list.addAll(extractAllSelectFromSelect(((SubqueryTableSegment) tableSegment).getSubquery().getSelect()));
            }

            if (tableSegment instanceof JoinTableSegment) {
                list.addAll(extractAllSelectFromFromTableSegment(((JoinTableSegment) tableSegment).getLeft()));
                list.addAll(extractAllSelectFromFromTableSegment(((JoinTableSegment) tableSegment).getRight()));
            }

            return list;
        }
    }

    private static List<SelectStatement> extractAllSelectFromProjectionsSegment(ProjectionsSegment projectionsSegment) {
        List<SelectStatement> list = new ArrayList<>();
        if (projectionsSegment == null) {
            return list;
        } else if (projectionsSegment.getProjections() == null) {
            return list;
        } else if (projectionsSegment.getProjections().isEmpty()) {
            return list;
        } else {
            for (ProjectionSegment projection : projectionsSegment.getProjections()) {
                if (projection instanceof SubqueryProjectionSegment) {
                    list.addAll(extractAllSelectFromSelect(((SubqueryProjectionSegment) projection).getSubquery().getSelect()));
                }

                if (projection instanceof ExpressionProjectionSegment) {
                    list.addAll(extractAllSelectFromExpr(((ExpressionProjectionSegment) projection).getExpr()));
                }
            }

            return list;
        }
    }

    private static List<SelectStatement> extractAllSelectFromListExpression(ListExpression listExpression) {
        List<SelectStatement> list = new ArrayList<>();
        if (listExpression == null) {
            return list;
        } else if (listExpression.getItems() != null && listExpression.getItems().size() != 0) {
            for (ExpressionSegment expr : listExpression.getItems()) {
                list.addAll(extractAllSelectFromExpr(expr));
            }

            return list;
        } else {
            return list;
        }
    }

    public static Set<TableSegment> extractAllTablesFromDelete(DeleteStatement deleteStatement) {
        Set<TableSegment> tableSegments = new LinkedHashSet<>();
        List<SelectStatement> selectStatements = extractAllSelectFromWhereSegment(deleteStatement.getWhere().orElse(null));
        for (SelectStatement selectStatement : selectStatements) {
            tableSegments.addAll(extractTableNameAbstractFromSelect(selectStatement));
        }
        return tableSegments;
    }

    public static Set<TableSegment> extractAllTablesFromUpdate(UpdateStatement updateStatement) {
        Set<TableSegment> tableSegments = new LinkedHashSet<>();
        List<SelectStatement> selectStatements = extractAllSelectFromWhereSegment(updateStatement.getWhere().orElse(null));

        for (SelectStatement selectStatement : selectStatements) {
            tableSegments.addAll(extractTableNameAbstractFromSelect(selectStatement));
        }

        List<SelectStatement> selectStatementsFromSet = extractAllSelectFromSetSegment(updateStatement.getSetAssignment());

        for (SelectStatement selectStatement : selectStatementsFromSet) {
            tableSegments.addAll(extractTableNameAbstractFromSelect(selectStatement));
        }

        return tableSegments;
    }

    public static List<SelectStatement> extractAllSelectFromSetSegment(SetAssignmentSegment setAssignmentSegment) {
        List<SelectStatement> list = new ArrayList<>();
        if (setAssignmentSegment == null) {
            return list;
        } else {
            for (ColumnAssignmentSegment columnAssignmentSegment : setAssignmentSegment.getAssignments()) {
                list.addAll(extractAllSelectFromUpdateSet(columnAssignmentSegment));
            }
            return list;
        }
    }

    private static List<SelectStatement> extractAllSelectFromUpdateSet(ColumnAssignmentSegment segment) {
        List<SelectStatement> list = new ArrayList<>();
        Object expr = segment.getValue();
        if (!Objects.isNull(expr)) {
            if (expr instanceof SubquerySegment) {
                list.addAll(extractAllSelectFromSelect(((SubquerySegment) expr).getSelect()));
            }

            if (expr instanceof ExistsSubqueryExpression) {
                list.addAll(extractAllSelectFromSelect(((ExistsSubqueryExpression) expr).getSubquery().getSelect()));
            }

            if (expr instanceof ListExpression) {
                list.addAll(extractAllSelectFromListExpression((ListExpression) expr));
            }

            if (expr instanceof SubqueryExpressionSegment) {
                list.addAll(extractAllSelectFromSelect(((SubqueryExpressionSegment) expr).getSubquery().getSelect()));
            }

            if (expr instanceof BetweenExpression) {
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getBetweenExpr()));
                list.addAll(extractAllSelectFromExpr(((BetweenExpression) expr).getAndExpr()));
            }

            if (expr instanceof ExpressionProjectionSegment) {
                list.addAll(extractAllSelectFromExpr(((ExpressionProjectionSegment) expr).getExpr()));
            }

            if (expr instanceof NotExpression) {
                list.addAll(extractAllSelectFromExpr(((NotExpression) expr).getExpression()));
            }

            // todo
            /*if (expr instanceof UnaryExpression) {
                list.addAll(extractAllSelectFromExpr(((UnaryExpression) expr).getExpression()));
            }*/

            if (expr instanceof BinaryOperationExpression) {
                list.addAll(extractAllSelectFromExpr(((BinaryOperationExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((BinaryOperationExpression) expr).getRight()));
            }

            if (expr instanceof InExpression) {
                list.addAll(extractAllSelectFromExpr(((InExpression) expr).getLeft()));
                list.addAll(extractAllSelectFromExpr(((InExpression) expr).getRight()));
            }

            // todo
            /*if (expr instanceof CommonTableExpressionSegment) {
                WithSubquerySegment withSubquerySegment = ((CommonTableExpressionSegment) expr).getSubquery();
                if (withSubquerySegment.getSqlStatement() instanceof SelectStatement && !((SelectStatement) withSubquerySegment.getSqlStatement()).isTrinoValues()) {
                    list.add((SelectStatement) withSubquerySegment.getSqlStatement());
                }
            }*/

            if (expr instanceof ListExpression) {
                list.addAll(extractAllSelectFromListExpression((ListExpression) expr));
            }

            return list;
        } else {
            return list;
        }
    }

    public static List<TableSegment> extractTableNameAbstractFromSelect(SelectStatement selectStatement) {
        List<SelectStatement> list = extractAllSelectFromSelect(selectStatement);
        List<TableSegment> tableSegments = new ArrayList<>();
        list.forEach((e) -> {
            tableSegments.addAll(extractTableNameAbstractFromSelectFromSegment(e.getFrom().orElse(null)));
        });
        return tableSegments;
    }

    private static List<TableSegment> extractTableNameAbstractFromSelectFromSegment(TableSegment tableSegment) {
        List<TableSegment> tables = new ArrayList<>();
        if (tableSegment == null) {
            return tables;
        } else {
            if (tableSegment instanceof FunctionTableSegment) {
                tables.add(tableSegment);
            }

            if (tableSegment instanceof SubqueryTableSegment) {
                return tables;
            } else {
                if (tableSegment instanceof SimpleTableSegment) {
                    tables.add(tableSegment);
                }

                if (tableSegment instanceof JoinTableSegment) {
                    tables.addAll(extractTableNameFromSelectFromSegment(((JoinTableSegment) tableSegment).getLeft()));
                    tables.addAll(extractTableNameFromSelectFromSegment(((JoinTableSegment) tableSegment).getRight()));
                }

                return tables;
            }
        }
    }

    public static List<ColumnSegment> extractColumnSegmentFromSelectProjections(SelectStatement selectStatement) {
        List<ColumnSegment> result = new ArrayList<>();
        Collection<ProjectionSegment> projections = selectStatement.getProjections().getProjections();

        for (ProjectionSegment projection : projections) {
            if (projection instanceof ColumnProjectionSegment) {
                result.add(((ColumnProjectionSegment) projection).getColumn());
            }
        }

        return result;
    }

    public static List<ColumnProjectionSegment> extractColumnProjectionFromSelectProjections(SelectStatement selectStatement) {
        List<ColumnProjectionSegment> result = new ArrayList<>();
        Collection<ProjectionSegment> projections = selectStatement.getProjections().getProjections();

        for (ProjectionSegment projection : projections) {
            if (projection instanceof ColumnProjectionSegment) {
                result.add((ColumnProjectionSegment) projection);
            }
        }

        return result;
    }

    public static void traverseAllExpressionSegment(ExpressionSegment expressionSegment, List<ExpressionSegment> list) {
        if (expressionSegment != null) {
            list.add(expressionSegment);
            if (expressionSegment instanceof BetweenExpression) {
                BetweenExpression betweenExpression = (BetweenExpression) expressionSegment;
                traverseAllExpressionSegment(betweenExpression.getLeft(), list);
                traverseAllExpressionSegment(betweenExpression.getBetweenExpr(), list);
                traverseAllExpressionSegment(betweenExpression.getAndExpr(), list);
            } else if (expressionSegment instanceof BinaryOperationExpression) {
                BinaryOperationExpression binaryOperationExpression = (BinaryOperationExpression) expressionSegment;
                traverseAllExpressionSegment(binaryOperationExpression.getLeft(), list);
                traverseAllExpressionSegment(binaryOperationExpression.getRight(), list);
            } else if (expressionSegment instanceof CollateExpression) {
                traverseAllExpressionSegment(((CollateExpression) expressionSegment).getCollateName(), list);
            }
            // todo
            /*else if (expressionSegment instanceof CursorSubqueryExpressionSegment) {
                traverseAllExpressionSegment(((CursorSubqueryExpressionSegment) expressionSegment).getSubquery(), list);
            }*/
            else if (expressionSegment instanceof ExistsSubqueryExpression) {
                traverseAllExpressionSegment(((ExistsSubqueryExpression) expressionSegment).getSubquery(), list);
            } else if (expressionSegment instanceof SubqueryExpressionSegment) {
                traverseAllExpressionSegment(((SubqueryExpressionSegment) expressionSegment).getSubquery(), list);
            } else if (expressionSegment instanceof ExpressionProjectionSegment) {
                traverseAllExpressionSegment(((ExpressionProjectionSegment) expressionSegment).getExpr(), list);
            }
            // todo
            /*else if (expressionSegment instanceof ExpressionsSegment) {
                ((ExpressionsSegment) expressionSegment).getExpressionSegments().forEach((e) -> {
                    traverseAllExpressionSegment(e, list);
                });
            } else if (expressionSegment instanceof FunctionExpression) {
                traverseAllExpressionSegment(((FunctionExpression) expressionSegment).toFunctionSegment(), list);
            }*/
            else if (expressionSegment instanceof FunctionSegment) {
                ((FunctionSegment) expressionSegment).getParameters().forEach((e) -> {
                    traverseAllExpressionSegment(e, list);
                });
            } else if (expressionSegment instanceof InExpression) {
                InExpression inExpression = (InExpression) expressionSegment;
                traverseAllExpressionSegment(inExpression.getLeft(), list);
                traverseAllExpressionSegment(inExpression.getRight(), list);
            }
            // todo
            /*else if (expressionSegment instanceof IsNotExpression) {
                traverseAllExpressionSegment(((IsNotExpression) expressionSegment).getExpression(), list);
            }*/
            else if (expressionSegment instanceof ListExpression) {
                ((ListExpression) expressionSegment).getItems().forEach((l) -> {
                    traverseAllExpressionSegment(l, list);
                });
            } else if (expressionSegment instanceof NotExpression) {
                traverseAllExpressionSegment(((NotExpression) expressionSegment).getExpression(), list);
            }
            // todo
            /*else if (expressionSegment instanceof SingleKeyWordUnaryExpression) {
                traverseAllExpressionSegment(((SingleKeyWordUnaryExpression) expressionSegment).getExpression(), list);
            } */
            else if (expressionSegment instanceof SubquerySegment) {
                getAllExpressionSegmentFromSelectWhere(((SubquerySegment) expressionSegment).getSelect(), list);
            }
            // todo
            /*else if (expressionSegment instanceof UnaryExpression) {
                traverseAllExpressionSegment(((UnaryExpression) expressionSegment).getExpression(), list);
            }*/
//            else if (expressionSegment instanceof CommonTableExpressionSegment) {
//                CommonTableExpressionSegment commonTableExpressionSegment = (CommonTableExpressionSegment) expressionSegment;
//                traverseAllExpressionSegment(commonTableExpressionSegment.getSubquery(), list);
//            }
            // todo
            /*else if (expressionSegment instanceof WithSubquerySegment) {
                SQLStatement sqlStatement = ((WithSubquerySegment) expressionSegment).getSqlStatement();
                if (sqlStatement instanceof SelectStatement) {
                    getAllExpressionSegmentFromSelectWhere((SelectStatement) sqlStatement, list);
                }
            }*/

        }
    }

    public static void getAllExpressionSegmentFromSelectWhere(SelectStatement selectStatement, List<ExpressionSegment> list) {
        List<SelectStatement> selectList = extractAllSelectFromSelect(selectStatement);
        for (SelectStatement statement : selectList) {
            if (statement.getWhere().isPresent()) {
                traverseAllExpressionSegment((statement.getWhere().get()).getExpr(), list);
            }
        }
    }

    public static List<LiteralExpressionSegment> traverseAllLiteralExpressionSegment(ExpressionSegment expressionSegment) {
        List<LiteralExpressionSegment> result = new ArrayList<>();
        List<ExpressionSegment> segments = new ArrayList<>();
        traverseAllExpressionSegment(expressionSegment, segments);

        for (ExpressionSegment expr : segments) {
            if (expr instanceof LiteralExpressionSegment) {
                result.add((LiteralExpressionSegment) expr);
            }
        }

        return result;
    }

    public static List<LiteralExpressionSegment> traverseAllLiteralExpressionSegment(List<ExpressionSegment> list) {
        List<LiteralExpressionSegment> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof LiteralExpressionSegment) {
                result.add((LiteralExpressionSegment) expr);
            }
        }

        return result;
    }

    public static List<InExpression> traverseAllInExpr(List<ExpressionSegment> list) {
        List<InExpression> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof InExpression) {
                result.add((InExpression) expr);
            }
        }

        return result;
    }

    public static List<ExistsSubqueryExpression> traverseExistsSubqueryExpr(List<ExpressionSegment> list) {
        List<ExistsSubqueryExpression> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof ExistsSubqueryExpression) {
                result.add((ExistsSubqueryExpression) expr);
            }
        }

        return result;
    }

    public static List<NotExpression> traverseAllNotExpr(List<ExpressionSegment> list) {
        List<NotExpression> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof NotExpression) {
                result.add((NotExpression) expr);
            }
        }

        return result;
    }

    public static List<SubquerySegment> traverseAllSubqueryExpr(List<ExpressionSegment> list) {
        List<SubquerySegment> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof SubquerySegment) {
                result.add((SubquerySegment) expr);
            }

            if (expr instanceof ExistsSubqueryExpression) {
                result.add(((ExistsSubqueryExpression) expr).getSubquery());
            }

            if (expr instanceof SubqueryExpressionSegment) {
                result.add(((SubqueryExpressionSegment) expr).getSubquery());
            }
        }

        return result.stream().distinct().collect(Collectors.toList());
    }

    public static List<BinaryOperationExpression> traverseAllBinaryExpr(List<ExpressionSegment> list) {
        List<BinaryOperationExpression> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof BinaryOperationExpression) {
                result.add((BinaryOperationExpression) expr);
            }
        }

        return result;
    }

    public static List<FunctionSegment> traverseAllFunctionExpr(List<ExpressionSegment> list) {
        List<FunctionSegment> result = new ArrayList<>();

        for (ExpressionSegment expr : list) {
            if (expr instanceof FunctionSegment) {
                result.add((FunctionSegment) expr);
            }
        }

        return result;
    }

    public static int getSelectNestLoop(SelectStatement selectStatement, int i) {
        if (Objects.isNull(selectStatement)) {
            return i;
        } else {
            int i1 = getSelectNestLoopFromTableSegment(selectStatement.getFrom().orElse(null), i);
            int i2 = getSelectNestLoopFromWhereSegment(selectStatement.getWhere().orElse(null), i);
            return Math.max(i1, i2);
        }
    }

    public static int getSelectNestLoopFromTableSegment(TableSegment tableSegment, int i) {
        if (Objects.isNull(tableSegment)) {
            return i;
        } else if (tableSegment instanceof JoinTableSegment) {
            JoinTableSegment joinTableSegment = (JoinTableSegment) tableSegment;
            if (joinTableSegment.getLeft() instanceof SubqueryTableSegment) {
                return getSelectNestLoop(((SubqueryTableSegment) joinTableSegment.getLeft()).getSubquery().getSelect(), i + 1);
            }
            if (joinTableSegment.getRight() instanceof SubqueryTableSegment) {
                return getSelectNestLoop(((SubqueryTableSegment) joinTableSegment.getRight()).getSubquery().getSelect(), i + 1);
            }
            return i;
        } else {
            return tableSegment instanceof SubqueryTableSegment ? getSelectNestLoop(((SubqueryTableSegment) tableSegment).getSubquery().getSelect(), i + 1) : i;
        }
    }

    public static int getSelectNestLoopFromExpr(ExpressionSegment expr, int i) {
        if (expr == null) {
            return i;
        } else {
            int i1;
            if (expr instanceof SubquerySegment) {
                i1 = getSelectNestLoop(((SubquerySegment) expr).getSelect(), i + 1);
                return i1;
            } else if (expr instanceof ExistsSubqueryExpression) {
                i1 = getSelectNestLoop(((ExistsSubqueryExpression) expr).getSubquery().getSelect(), i + 1);
                return i1;
            } else if (expr instanceof SubqueryExpressionSegment) {
                i1 = getSelectNestLoop(((SubqueryExpressionSegment) expr).getSubquery().getSelect(), i + 1);
                return i1;
            } else {
                int i2;
                if (expr instanceof BetweenExpression) {
                    i1 = getSelectNestLoopFromExpr(((BetweenExpression) expr).getLeft(), i);
                    i2 = getSelectNestLoopFromExpr(((BetweenExpression) expr).getBetweenExpr(), i);
                    int i3 = getSelectNestLoopFromExpr(((BetweenExpression) expr).getAndExpr(), i);
                    int i4 = Math.max(i1, i2);
                    return Math.max(i3, i4);
                } else if (expr instanceof NotExpression) {
                    return getSelectNestLoopFromExpr(((NotExpression) expr).getExpression(), i);
                }
                // todo
                /*else if (expr instanceof UnaryExpression) {
                    return getSelectNestLoopFromExpr(((UnaryExpression) expr).getExpression(), i);
                } else if (expr instanceof SingleKeyWordUnaryExpression) {
                    return getSelectNestLoopFromExpr(((SingleKeyWordUnaryExpression) expr).getExpression(), i);
                }*/
                else if (expr instanceof BinaryOperationExpression) {
                    i1 = getSelectNestLoopFromExpr(((BinaryOperationExpression) expr).getLeft(), i);
                    i2 = getSelectNestLoopFromExpr(((BinaryOperationExpression) expr).getRight(), i);
                    return Math.max(i1, i2);
                } else if (expr instanceof InExpression) {
                    i1 = getSelectNestLoopFromExpr(((InExpression) expr).getLeft(), i);
                    i2 = getSelectNestLoopFromExpr(((InExpression) expr).getRight(), i);
                    return Math.max(i1, i2);
                } else {
                    return i;
                }
            }
        }
    }

    public static int getSelectNestLoopFromWhereSegment(WhereSegment where, int i) {
        if (Objects.isNull(where)) {
            return i;
        } else {
            ExpressionSegment expr = where.getExpr();
            return getSelectNestLoopFromExpr(expr, i);
        }
    }

    public static List<SelectStatement> extractAllSelectFromSelectUnion(SelectStatement selectStatement, int level) {
        if (Objects.isNull(selectStatement)) {
            return new ArrayList<>();
        } else {
            List<SelectStatement> list = new ArrayList<>();
            if (selectStatement.getCombine().isPresent() && selectStatement.getCombine().get().getLeft() != null) {
                list.addAll(extractAllSelectFromSelectUnion(selectStatement.getCombine().get().getLeft().getSelect(), level + 1));
            }
            if (selectStatement.getCombine().isPresent() && selectStatement.getCombine().get().getRight() != null) {
                list.addAll(extractAllSelectFromSelectUnion(selectStatement.getCombine().get().getRight().getSelect(), level + 1));
            }
            if (selectStatement.getCombine().isEmpty()) {
                list.add(selectStatement);
            }
            return list;
        }
    }

    public static List<SelectStatement> extractAllSelectFromSelectJoin(SelectStatement selectStatement, int level) {
        if (Objects.isNull(selectStatement)) {
            return new ArrayList<>();
        } else {
            List<SelectStatement> list = new ArrayList<>();
            if (selectStatement.getCombine().isPresent() && selectStatement.getCombine().get().getLeft() != null) {
                if (!selectStatement.getCombine().get().getCombineType().equals(CombineType.UNION_ALL) && !selectStatement.getCombine().get().getCombineType().equals(CombineType.UNION)) {
                    list.addAll(extractAllSelectFromSelectJoin(selectStatement.getCombine().get().getLeft().getSelect(), level + 1));
                }
            }
            if (selectStatement.getCombine().isPresent() && !selectStatement.getCombine().get().getCombineType().equals(CombineType.UNION_ALL) && selectStatement.getCombine().get().getRight() != null) {
                if (!selectStatement.getCombine().get().getCombineType().equals(CombineType.UNION_ALL) && !selectStatement.getCombine().get().getCombineType().equals(CombineType.UNION)) {
                    list.addAll(extractAllSelectFromSelectJoin(selectStatement.getCombine().get().getRight().getSelect(), level + 1));
                }
            }
            if (selectStatement.getCombine().isEmpty()) {
                list.add(selectStatement);
            }
            return list;
        }
    }

    public static List<SelectStatement> getSelectStatementList(SelectStatement selectStatement) {
        List<SelectStatement> listAll = new ArrayList<>();
        if (selectStatement != null) {
            if (selectStatement.getCombine().isPresent()) {
                List<SelectStatement> selectStatements = RowFilterRewriteUtil.extractAllSelectFromSelectUnion(selectStatement, 0);
                for (SelectStatement subSelect : selectStatements) {
                    List<SelectStatement> selectList = RowFilterRewriteUtil.extractAllSelectFromSelect(subSelect);
                    listAll.addAll(selectList);
                }
            } else {
                List<SelectStatement> selectList = RowFilterRewriteUtil.extractAllSelectFromSelect(selectStatement);
                for (SelectStatement subSelect : selectList) {
                    if (subSelect.getCombine().isPresent()) {
                        List<SelectStatement> selectStatements = RowFilterRewriteUtil.extractAllSelectFromSelectUnion(subSelect, 0);
                        for (SelectStatement subSelectInner : selectStatements) {
                            listAll.addAll(RowFilterRewriteUtil.extractAllSelectFromSelect(subSelectInner));
                        }
                    } else {
                        listAll.add(subSelect);
                    }
                }
            }
        }
        return listAll;
    }
}

