package com.dc.parser.model.enums;

import lombok.Getter;

@Getter
public enum CheckRuleUniqueKey {

    DDL_CHECK_PK_NOT_EXIST("ddl_check_pk_not_exist", "表建议使用主键"),
    DDL_CHECK_FK_INDEX("ddl_check_fk_index", "外键必须有索引"),
    DDL_DISABLE_FK("ddl_disable_fk", "表不建议使用外键"),
    DDL_CHECK_TABLE_NAME_LENGTH("ddl_check_table_name_length", "表名长度不能大于指定字节"),
    DDL_CHECK_COLUMN_NAME_LENGTH("ddl_check_column_name_length", "列名长度不能大于指定字节"),
    DDL_CHECK_TABLE_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED("ddl_check_table_name_is_upper_and_lower_letter_mixed", "限制表名大小写"),
    DDL_CHECK_COLUMN_NAME_IS_UPPER_AND_LOWER_LETTER_MIXED("ddl_check_column_name_is_upper_and_lower_letter_mixed", "限制列名大小写"),
    DDL_CHECK_TABLE_NAME_USING_KEYWORD("ddl_check_table_name_using_keyword", "表名不能是关键字"),
    DDL_CHECK_COLUMN_NAME_USING_KEYWORD("ddl_check_column_name_using_keyword", "列名不能是关键字"),
    DDL_CHECK_PARTITION_COLUMN_NOT_NULL("ddl_check_partition_column_not_null", "分区列不允许为空"),
    DDL_CHECK_SEQUENCE_CACHE_NOT_ZERO("ddl_check_sequence_cache_not_zero", "序列建议设置cache值"),
    DDL_CHECK_SEQUENCE_CACHE_VALUE_SIZE("ddl_check_sequence_cache_value_size", "序列cache值设置不合理"),
    DDL_CHECK_COLUMN_SPECIFIED_TYPE_NOTICE("ddl_check_column_specified_type_notice", "表不建议使用指定的字段类型"),
    DDL_CHECK_COLUMN_BLOB_NOTICE("ddl_check_column_blob_notice", "不建议使用 BLOB 类型"),
    DDL_CHECK_INDEX_INITIALIZE_TRANSACTION_SLOT("ddl_check_index_initialize_transaction_slot", "创建索引必须初始化事务槽"),
    DDL_CHECK_UNIQUE_INDEX_PREFIX("ddl_check_unique_index_prefix", "unique索引必须使用固定前缀"),
    DDL_CHECK_INDEX_PREFIX("ddl_check_index_prefix", "普通索引必须使用固定前缀"),
    DDL_CHECK_INDEX_MUST_TABLESPACE("ddl_check_index_must_tablespace", "建表索引时未指定索引表空间"),
    DDL_CHECK_COMPOSITE_INDEX_MAX("ddl_check_composite_index_max", "复合索引的列数量不建议超过阈值"),
    DDL_CHECK_INDEX_COUNT("ddl_check_index_count", "索引个数建议不超过阈值"),
    DDL_CHECK_CREATE_VIEW("ddl_check_create_view", "限制创建视图"),
    DDL_CHECK_CREATE_TRIGGER("ddl_check_create_trigger", "限制创建触发器"),
    DDL_CHECK_CREATE_PROCEDURE("ddl_check_create_procedure", "限制创建存储过程"),
    DDL_CHECK_CHAR_LENGTH("ddl_check_char_length", "char长度大于20时，必须使用varchar2类型"),
    DDL_HINT_DROP_COLUMN("ddl_hint_drop_column", "禁止删除列"),
    DDL_HINT_MODIFY_COLUMN_DATA_TYPE("ddl_hint_modify_column_data_type", "禁止修改列的数据类型"),
    DML_CHECK_INSERT_COLUMNS_EXIST("dml_check_insert_columns_exist", "INSET语句未指定字段"),
    DML_CHECK_BATCH_INSERT_LISTS_MAX("dml_check_batch_insert_lists_max", "单条INSERT语句，建议批量插入不超过阈值"),
    DML_CHECK_WITH_ORDER_BY("dml_check_with_order_by", "DML语句中使用了order by"),
    DML_CHECK_SELECT_HAS_WHERE("dml_check_select_has_where", "select语句建议指定where条件"),
    DML_CHECK_UPDATE_OR_DELETE_HAS_WHERE("dml_check_update_or_delete_has_where", "update/delete语句建议指定where条件"),
    DML_CHECK_AFFECTED_ROWS("dml_check_affected_rows", "UPDATE和DELETE语句评估影响行数过大"),
    ALL_CHECK_OPERATION_HIGH_RISK("all_check_operation_high_risk", "自定义规则"),
    ALL_CHECK_WHERE_IS_INVALID("all_check_where_is_invalid", "禁止where条件中出现1=1"),
    DML_CHECK_SELECT_HINT("dml_check_select_hint", "不建议SQL中包含hint指令"),
    DML_CHECK_NUMBER_OF_JOIN_TABLES("dml_check_number_of_join_tables", "参与连接操作的表数量太多"),
    DML_NOT_RECOMMEND_HAVING("dml_not_recommend_having", "避免使用 having 子句"),
    DML_CHECK_ALIAS("dml_check_alias", "别名不要与表或列的名字相同"),
    DML_CHECK_FUZZY_SEARCH("dml_check_fuzzy_search", "使用了全模糊查询或左模糊查询"),
    DML_CHECK_WHERE_EXIST_NOT("dml_check_where_exist_not", "对条件字段使用负向查询"),
    DML_CHECK_SELECT_FOR_UPDATE("dml_check_select_for_update", "建议避免使用select for update"),
    DDL_CHECK_NUMBER_OF_COLUMNS("ddl_check_number_of_columns", "表字段过多"),
    DML_NOT_RECOMMEND_SUBQUERY("dml_not_recommend_subquery", "不推荐使用子查询"),
    DML_CHECK_IS_AFTER_UNION_DISTINCT("dml_check_is_after_union_distinct", "建议使用UNION ALL替代UNION"),
    DML_CHECK_IN_QUERY_LIMIT("dml_check_in_query_limit", "where条件内in语句中的参数个数不能超过阈值"),
    DML_NOT_RECOMMEND_EXPRESSION_IN_WHERE("dml_not_recommend_expression_in_where", "查询语句的条件中使用表达式"),
    DDL_CHECK_CREATE_TIME_COLUMN("ddl_check_create_time_column", "建表DDL建议包含创建时间字段"),
    DDL_CHECK_UPDATE_TIME_COLUMN("ddl_check_update_time_column", "建表DDL建议包含更新时间字段"),
    DDL_CHECK_TABLE_MUST_TABLESPACE("ddl_check_table_must_tablespace", "创建表时未指定表空间"),
    DDL_CHECK_FORBIDDEN_TABLESPACE("ddl_check_forbidden_tablespace", "CREATE TABLE/INDEX使用禁止的表空间"),
    DDL_DISABLE_TYPE_TIMESTAMP("ddl_disable_type_timestamp", "建议使用DATE替代TIMESTAMP类型"),
    DML_CHECK_SQL_LENGTH("dml_check_sql_length", "单条SQL不建议过长"),
    DML_CHECK_SUB_QUERY_DEPTH("dml_check_sub_query_depth", "表关联嵌套循环层次过多"),
    DML_CHECK_WHERE_EXIST_SCALAR_SUB_QUERIES("dml_check_where_exist_scalar_sub_queries", "不建议使用标量子查询"),
    ALL_CHECK_SYNTAX_ERROR("all_check_syntax_error", "语法错误或者解析器不支持，请人工确认SQL正确性"),
    DML_DISABLE_SELECT_ALL_COLUMN("dml_disable_select_all_column", "禁止使用 \"SELECT *\""),

    DDL_CHECK_DATABASE_ENGINE("ddl_check_database_engine", "建议使用指定数据库引擎"),
    DDL_CHECK_PARTITION_TABLE("ddl_check_partition_table", "不建议使用分区表相关功能"),
    DDL_CHECK_COLUMN_COMMENT("ddl_check_column_comment", "列建议添加注释"),
    DDL_CHECK_TABLE_COMMENT("ddl_check_table_comment", "表建议添加注释"),
    DDL_CHECK_CHARSET("ddl_check_charset", "建议使用指定数据库字符集"),
    DDL_CHECK_TABLE_CHARSET("ddl_check_table_charset", "不建议修改表的默认字符集"),
    DDL_CHECK_COLUMN_CHARSET("ddl_check_column_charset", "不能设置列的字符集"),
    DDL_CHECK_DATABASE_ORDER_RULE("ddl_check_database_order_rule", "建议使用规定的数据库排序规则"),
    DDL_CHECK_TABLE_UPPERCASE("ddl_check_table_uppercase", "强制表名大小写"),
    DDL_CHECK_COLUMN_UPPERCASE("ddl_check_column_uppercase", "强制列名大小写"),
    DML_CHECK_SELECT_WITH_ORDER_BY("dml_check_select_with_order_by", "SELECT 语句不能有ORDER BY"),
    DDL_CHECK_CREATE_UDF("ddl_check_create_udf", "禁止创建自定义函数"),
    DDL_CHECK_DEL_FK("ddl_check_del_fk", "禁止进行删除外键的操作"),
    DDL_CHECK_DEL_PK("ddl_check_del_pk", "禁止进行删除主键的操作"),
    DDL_CHECK_PK_AUTO_INCREMENT("ddl_check_pk_auto_increment", "主键建议使用自增"),
    DDL_CHECK_COLUMN_INITIAL_VALUE("ddl_check_column_initial_value", "限制自增列初始值"),
    DDL_CHECK_COLUMN_DEFAULT_VALUE("ddl_check_column_default_value", "每个列都要有默认值"),
    DDL_CHECK_COLUMN_CHAR_LENGTH("ddl_check_column_char_length", "限制 \"CHAR\" 类型长度"),
    DDL_CHECK_COLUMN_VARCHAR_LENGTH("ddl_check_column_varchar_length", "限制 \"VARCHAR\" 类型长度"),
    DDL_CHECK_PK_COLUMN_TYPE("ddl_check_pk_column_type", "建议主键列类型"),
    DDL_CHECK_COLUMN_TYPE("ddl_check_column_type", "不建议使用 BLOB 或 TEXT 类型"),
    DDL_CHECK_BLOB_TEXT_DEFAULT_NULL("ddl_check_blob_text_default_null", "BLOB 和 TEXT 类型的字段默认值只能为NULL"),
    DDL_CHECK_COLUMN_ENUM("ddl_check_column_enum", "不建议使用 ENUM 类型"),
    DDL_CHECK_COLUMN_NUM("ddl_check_column_num", "主键包含的列数不建议超过阈值"),
    DDL_CHECK_SET("ddl_check_set", "不建议使用 SET 类型"),
    DDL_CHECK_TIMESTAMP_DEFAULT_VALUE("ddl_check_timestamp_default_value", "TIMESTAMP 类型的列必须添加默认值"),
    DDL_CHECK_COLUMN_NOT_NULL_ADD_DEFAULT_VALUE("ddl_check_column_not_null_add_default_value", "建议字段约束为NOT NULL时带默认值"),
    DDL_CHECK_CREATE_EVENT("ddl_check_create_event", "禁止创建事件"),
    DDL_DISABLE_COLUMN_TYPE("ddl_disable_column_type", "禁止使用的字段类型"),
    DDL_CHECK_PK_BIGINT_COLUMN_TYPE("ddl_check_pk_bigint_column_type", "主键建议使用 BIGINT 无符号类型，即 BIGINT UNSIGNED"),
    DML_CHECK_WHERE_NULL_COMPARE("dml_check_where_null_compare", "WHERE子句中禁止将NULL值与其他字段或值进行比较运算"),
    DDL_CHECK_BLOB_INDEX("ddl_check_blob_index", "禁止将BLOB类型的列加入索引"),
    DDL_CHECK_INDEX_TYPE_NOT_NULL("ddl_check_index_type_not_null", "索引字段需要有非空约束"),
    DML_CHECK_IN("dml_check_in", "不建议使用IN"),
    DML_CHECK_LIKE_USE_PATTERN_MATCH("dml_check_like_use_pattern_match", "不建议使用没有通配符的 LIKE 查询"),
    DML_CHECK_COUNT_COL("dml_check_count_col", "避免使用 COUNT(COL)"),
    DML_CHECK_STANDARD_OPERATOR("dml_check_standard_operator", "建议使用'<>'代替'!='"),
    DML_CHECK_GROUP_BY_CONSTANT("dml_check_group_by_constant", "不建议对常量进行 GROUP BY"),
    DML_CHECK_TABLE_EXISTS("dml_check_table_exists", "表是否存在"),
    DDL_CHECK_BIG_SIZE_TABLE_DDL("ddl_check_big_size_table_ddl", "不建议对数据量过大的表执行DDL操作"),
    DML_CHECK_WHERE_USE_FUNCTION_OPERATORS("dml_check_where_use_function_operators", "应避免在 WHERE 条件中使用函数或其他运算符"),
    DDL_CHECK_FUNCTION_SYSDATE("ddl_check_function_sysdate", "不建议使用 SYSDATE() 函数"),
    DML_CHECK_ORDER_RULE_DIFF("dml_check_order_rule_diff", "不建议在 ORDER BY 语句中对多个不同条件使用不同方向的排序"),
    DML_CHECK_ORDER_RAND("dml_check_order_rand", "不建议使用 ORDER BY RAND()"),
    DML_CHECK_ORDER_EXPRESSION("dml_check_order_expression", "不建议ORDER BY 的条件为表达式"),
    ;

    private final String value;

    private final String name;

    CheckRuleUniqueKey(String value, String name) {
        this.value = value;
        this.name = name;
    }

}
