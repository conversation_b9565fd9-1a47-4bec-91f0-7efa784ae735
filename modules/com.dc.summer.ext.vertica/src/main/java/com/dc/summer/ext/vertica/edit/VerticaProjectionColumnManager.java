

package com.dc.summer.ext.vertica.edit;

import com.dc.summer.model.impl.sql.edit.struct.SQLTableColumnManager;
import com.dc.summer.DBException;
import com.dc.summer.ext.vertica.model.VerticaProjection;
import com.dc.summer.ext.vertica.model.VerticaProjectionColumn;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEPersistAction;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.impl.edit.SQLDatabasePersistAction;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.sql.SQLUtils;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.utils.CommonUtils;

import java.util.List;
import java.util.Map;

/**
 * Vertica table column manager
 */
public class VerticaProjectionColumnManager extends SQLTableColumnManager<VerticaProjectionColumn, VerticaProjection> {
    @Override
    public boolean canCreateObject(Object container) {
        return false;
    }

    @Override
    public boolean canDeleteObject(VerticaProjectionColumn object) {
        return false;
    }

    @Override
    protected ColumnModifier[] getSupportedModifiers(VerticaProjectionColumn column, Map<String, Object> options) {
        // According to SQL92 DEFAULT comes before constraints
        return new ColumnModifier[] {};
    }

    @Override
    protected VerticaProjectionColumn createDatabaseObject(DBRProgressMonitor monitor, DBECommandContext context, Object container, Object copyFrom, Map<String, Object> options) throws DBException {
        return null;
    }

    /**
     * Copy-pasted from PostgreSQL implementation.
     * TODO: Vertica is originally based on PG. Maybe we should refactor this stuff somehow.
     */
    @Override
    protected void addObjectModifyActions(DBRProgressMonitor monitor, DBCExecutionContext executionContext, List<DBEPersistAction> actionList, ObjectChangeCommand command, Map<String, Object> options)
    {
        final VerticaProjectionColumn column = command.getObject();

        String prefix = "ALTER TABLE " + DBUtils.getObjectFullName(column.getTable(), DBPEvaluationContext.DDL) + " ALTER COLUMN " + DBUtils.getQuotedIdentifier(column) + " ";
        String typeClause = column.getFullTypeName();
        if (command.getProperty(DBConstants.PROP_ID_TYPE_NAME) != null || command.getProperty("maxLength") != null || command.getProperty("precision") != null || command.getProperty("scale") != null) {
            actionList.add(new SQLDatabasePersistAction("Set column type", prefix + "SET DATA TYPE " + typeClause));
        }
        if (command.getProperty(DBConstants.PROP_ID_REQUIRED) != null) {
            actionList.add(new SQLDatabasePersistAction("Set column nullability", prefix + (column.isRequired() ? "SET" : "DROP") + " NOT NULL"));
        }
        if (command.getProperty(DBConstants.PROP_ID_DEFAULT_VALUE) != null) {
            if (CommonUtils.isEmpty(column.getDefaultValue())) {
                actionList.add(new SQLDatabasePersistAction("Drop column default", prefix + "DROP DEFAULT"));
            } else {
                actionList.add(new SQLDatabasePersistAction("Set column default", prefix + "SET DEFAULT " + column.getDefaultValue()));
            }
        }
        if (command.getProperty(DBConstants.PROP_ID_DESCRIPTION) != null) {
            actionList.add(new SQLDatabasePersistAction("Set column comment", "COMMENT ON COLUMN " +
                DBUtils.getObjectFullName(column.getTable(), DBPEvaluationContext.DDL) + "." + DBUtils.getQuotedIdentifier(column) +
                " IS " + SQLUtils.quoteString(column, CommonUtils.notEmpty(column.getDescription()))));
        }
    }

    @Override
    public DBSObjectCache<VerticaProjection, VerticaProjectionColumn> getObjectsCache(VerticaProjectionColumn object) {
        return null;//object.getParentObject().getParentObject().getProjectionCache();
    }
}
