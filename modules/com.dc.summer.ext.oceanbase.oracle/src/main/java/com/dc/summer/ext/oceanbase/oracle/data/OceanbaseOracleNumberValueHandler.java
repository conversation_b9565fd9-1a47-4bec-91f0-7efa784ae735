package com.dc.summer.ext.oceanbase.oracle.data;

import com.dc.summer.ext.oceanbase.oracle.model.OceanbaseOracleConstants;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCNumberValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;

public class OceanbaseOracleNumberValueHandler extends JDBCNumberValueHandler {

    public OceanbaseOracleNumberValueHandler(DBSTypedObject type, DBDFormatSettings formatSettings) {
        super(type, formatSettings);
    }

    @Override
    protected Object fetchColumnValue(DBCSession session, JDBCResultSet resultSet, DBSTypedObject type, int index) throws DBCException, SQLException {
        if (OceanbaseOracleConstants.TYPE_BINARY_FLOAT.equalsIgnoreCase(type.getTypeName())
                || OceanbaseOracleConstants.TYPE_BINARY_DOUBLE.equalsIgnoreCase(type.getTypeName())
                || OceanbaseOracleConstants.TYPE_CURSOR.equalsIgnoreCase(type.getTypeName())) {
            try {
                return resultSet.getString(index);
            } catch (Exception e) {
                try {
                    return new String(resultSet.getBytes(index));
                } catch (Exception e1) {
                    return resultSet.getObject(index);
                }

            }

        }

        return super.fetchColumnValue(session, resultSet, type, index);
    }
}
