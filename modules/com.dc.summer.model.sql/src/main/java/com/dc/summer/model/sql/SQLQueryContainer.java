

package com.dc.summer.model.sql;

import com.dc.summer.model.DBPContextProvider;
import com.dc.summer.model.IDataSourceContainerProvider;

import java.util.Map;

/**
 * SQLQuery container.
 */
public interface SQLQueryContainer extends IDataSourceContainerProvider, DBPContextProvider {

    SQLScriptContext getScriptContext();

    SQLScriptElement getQuery();

    Map<String, Object> getQueryParameters();

    String getTableName();

}
