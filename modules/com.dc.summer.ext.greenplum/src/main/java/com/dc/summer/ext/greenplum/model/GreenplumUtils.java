
package com.dc.summer.ext.greenplum.model;

import com.dc.annotation.SQL;
import com.dc.summer.model.struct.DBSEntityConstraintType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.PostgreUtils;
import com.dc.summer.ext.postgresql.model.PostgreTableColumn;
import com.dc.summer.ext.postgresql.model.PostgreTableConstraint;
import com.dc.summer.ext.postgresql.model.PostgreTableReal;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.exec.jdbc.JDBCStatement;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.utils.CommonUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public class GreenplumUtils {

    @Nullable
    static int[] readDistributedColumns(@NotNull DBRProgressMonitor monitor, @NotNull PostgreTableReal table) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, table, "Read Greenplum table distributed columns")) {
            try (JDBCStatement dbStat = session.createStatement()) {
                if (((GreenplumDataSource) table.getDataSource()).isGreenplumVersionAtLeast(session.getProgressMonitor(), 6, 0)) {
                    @SQL String sql = "SELECT distkey FROM @_catalog.gp_distribution_policy WHERE localoid=" + table.getObjectId();
                    try (JDBCResultSet dbResult = dbStat.executeQuery(sql.replace("@", table.getDataSource().getInstancePrefix()))) {
                        if (dbResult.next()) {
                            return PostgreUtils.getIntVector(JDBCUtils.safeGetObject(dbResult, 1));
                        } else {
                            return null;
                        }
                    }
                } else {
                    @SQL String sql = "SELECT attrnums FROM @_catalog.gp_distribution_policy WHERE localoid=" + table.getObjectId();
                    try (JDBCResultSet dbResult = dbStat.executeQuery(sql.replace("@", table.getDataSource().getInstancePrefix()))) {
                        if (dbResult.next()) {
                            return PostgreUtils.getIntVector(JDBCUtils.safeGetObject(dbResult, 1));
                        } else {
                            return null;
                        }
                    }
                }
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
    }

    static List<PostgreTableColumn> getDistributionTableColumns(@NotNull DBRProgressMonitor monitor, List<PostgreTableColumn> distributionColumns, @NotNull PostgreTableReal table) throws DBException {
        // Get primary key
        PostgreTableConstraint pk = null;
        Collection<PostgreTableConstraint> tableConstraints = CommonUtils.safeCollection(table.getConstraints(monitor));
        // First - search PK in table
        Optional<PostgreTableConstraint> constraint = tableConstraints.stream().filter(key -> key.getConstraintType() == DBSEntityConstraintType.PRIMARY_KEY).findFirst();
        if (constraint.isPresent()) {
            pk = constraint.get();
        } else {
            // If no PK in the table - then search first UK for distribution
            Optional<PostgreTableConstraint> tableConstraint = tableConstraints.stream().filter(key -> key.getConstraintType() == DBSEntityConstraintType.UNIQUE_KEY).findFirst();
            if (tableConstraint.isPresent()) {
                pk = tableConstraint.get();
            }
        }
        if (pk != null) {
            List<DBSEntityAttribute> pkAttrs = DBUtils.getEntityAttributes(monitor, pk);
            if (!CommonUtils.isEmpty(pkAttrs)) {
                distributionColumns = new ArrayList<>(pkAttrs.size());
                for (DBSEntityAttribute attr : pkAttrs) {
                    distributionColumns.add((PostgreTableColumn) attr);
                }
            }
        }
        return distributionColumns;
    }

    private static boolean isDistributedByReplicated(DBRProgressMonitor monitor, @NotNull PostgreTableReal table) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, table, "Read Greenplum table distributed columns")) {
            try (JDBCStatement dbStat = session.createStatement()) {
                @SQL String sql = "SELECT policytype FROM @_catalog.gp_distribution_policy WHERE localoid=" + table.getObjectId();
                try (JDBCResultSet dbResult = dbStat.executeQuery(sql.replace("@", table.getDataSource().getInstancePrefix()))) {
                    if (dbResult.next()) {
                        return CommonUtils.equalObjects(JDBCUtils.safeGetString(dbResult, 1), "r");
                    } else {
                        return false;
                    }
                }
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
    }

    private static String getPartitionData(@NotNull DBRProgressMonitor monitor, @NotNull PostgreTableReal table) throws DBCException {
        try (JDBCSession session = DBUtils.openMetaSession(monitor, table, "Read Greenplum table partition data")) {
            try (JDBCStatement dbStat = session.createStatement()) {
                @SQL String sql = "SELECT @_get_partition_def('" + table.getSchema().getName() + "." + table.getName() + "'::regclass, true, false);";
                try (JDBCResultSet dbResult = dbStat.executeQuery(sql.replace("@", table.getDataSource().getInstancePrefix()))) {
                    if (dbResult.next()) {
                        String result = dbResult.getString(1);
                        if (result != null && result.startsWith("PARTITION ")) {
                            return result;
                        }
                        return null;
                    } else {
                        return null;
                    }
                }
            } catch (SQLException e) {
                throw new DBCException(e, session.getExecutionContext());
            }
        }
    }

    static void addObjectModifiersToDDL(@NotNull DBRProgressMonitor monitor, @NotNull StringBuilder ddl, @NotNull PostgreTableReal table, List<PostgreTableColumn> distributionColumns, boolean supportsReplicatedDistribution) throws DBCException {
        ddl.append("\nDISTRIBUTED ");
        if (supportsReplicatedDistribution && table.isPersisted() && GreenplumUtils.isDistributedByReplicated(monitor, table)) {
            ddl.append("REPLICATED");
        } else if (!CommonUtils.isEmpty(distributionColumns)) {
            ddl.append("BY (");
            for (int i = 0; i < distributionColumns.size(); i++) {
                if (i > 0) ddl.append(", ");
                ddl.append(DBUtils.getQuotedIdentifier(distributionColumns.get(i)));
            }
            ddl.append(")");
        } else {
            ddl.append("RANDOMLY");
        }

        String partitionData = table.isPersisted() ? GreenplumUtils.getPartitionData(monitor, table) : null;
        if (partitionData != null) {
            ddl.append("\n");
            ddl.append(partitionData);
        }
    }
}
