package com.dc.summer.ext.hetu;

import com.dc.summer.ext.generic.GenericDataSourceProvider;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.utils.http.UrlUtils;
import org.apache.commons.lang3.StringUtils;


public class HetuDataSourceProvider extends GenericDataSourceProvider {

    @Override
    public String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {

        UrlUtils builder;

        String ssl = connectionInfo.getAuthProperty(DBConstants.AUTH_PROP_SSL);
        String principal = connectionInfo.getAuthProperty(DBConstants.AUTH_PROP_PRINCIPAL);
        String keytab = connectionInfo.getAuthProperty(DBConstants.AUTH_PROP_KEYTAB);
        String krb5Conf = connectionInfo.getAuthProperty(DBConstants.AUTH_PROP_KRB5_CONF);

        String url = connectionInfo.getUrl();
        if (StringUtils.isNotBlank(url)) {
            builder = UrlUtils.builder(url);
        } else {
            builder = UrlUtils.builder(super.getConnectionURL(driver, connectionInfo));
            builder.query("serviceDiscoveryMode", "hsbroker");
            builder.query("deploymentMode", "on_yarn");
        }

        if (StringUtils.isNotBlank(ssl)) {
            builder.query("SSL", "true");
            builder.query("SSLTrustStorePath", ssl);
        }

        if (StringUtils.isNotBlank(principal)) {
            builder.query("KerberosConfigPath", krb5Conf);
            builder.query("KerberosPrincipal", principal);
            builder.query("KerberosKeytabPath", keytab);
            builder.query("KerberosRemoteServiceName", "HTTP");
            builder.query("KerberosServicePrincipalPattern", "%24%7BSERVICE%7D%40%24%7BHOST%7D");
        }

        return builder.build();
    }

}
