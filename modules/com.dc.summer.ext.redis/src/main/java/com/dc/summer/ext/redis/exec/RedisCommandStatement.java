package com.dc.summer.ext.redis.exec;

import com.dc.summer.ext.redis.RedisUtils;
import com.dc.summer.ext.redis.data.RedisNull;
import com.dc.summer.ext.redis.model.RedisDatabase;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.utils.GeneralUtils;
import com.dc.utils.BeanUtils;
import com.dc.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.UnifiedJedis;
import redis.clients.jedis.commands.ProtocolCommand;
import redis.clients.jedis.util.SafeEncoder;

@Slf4j
public class RedisCommandStatement extends RedisBaseStatement implements DBCStatement {
   protected Object result;
   private final String[] command;

   public RedisCommandStatement(RedisSession session, String[] command) {
      super(session, String.join(" ", command), 0L, 0L);
      this.command = command;
   }

   public boolean executeStatement() throws DBCException {
      if (this.session.isLoggingEnabled()) {
         QMUtils.getDefaultHandler().handleStatementExecuteBegin(this);
      }

      boolean var7;
      try {
         RedisUtils.selectCurDatabase(this.session);
         String commandName = this.command[0];
         String[] commandArguments = new String[this.command.length - 1];
         System.arraycopy(this.command, 1, commandArguments, 0, this.command.length - 1);
         Object jedisClient = this.session.getExecutionContext().getClient();
         this.result = executeCommand(jedisClient, commandName, this.session, commandArguments);
         if ("select".equalsIgnoreCase(commandName) && commandArguments.length == 1) {
            int defDatabase = CommonUtils.toInt(commandArguments[0]);
            RedisDatabase database = this.getSession().getDataSource().getDatabase(defDatabase);
            if (database == null) {
               database = this.getSession().getDataSource().addDatabase(defDatabase);
            }

            if (database != null) {
               this.getSession().getExecutionContext().setDefaultCatalog(this.session.getProgressMonitor(), database, null, false);
            }
         }
         if (this.result == null) {
            this.result = new RedisNull();
         }

         var7 = true;
      } catch (Throwable var10) {
         throw this.handleExecuteError(var10);
      } finally {
         if (this.session.isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleStatementExecuteEnd(this, -1L, this.executeError);
         }
      }

      return var7;
   }

   public static Object executeCommand(Object jedisClient, String commandName, RedisSession session, String... commandArguments) throws DBCException {
      ProtocolCommand pc = () -> SafeEncoder.encode(commandName);
      if (!(jedisClient instanceof UnifiedJedis)) {
         throw new DBCException("Unsupported Redis client: " + jedisClient);
      } else {
         for (int i = 0; i < commandArguments.length; ++i) {
            String script = commandArguments[i];
            if (script.length() >= 2 && (script.charAt(0) == '"' || script.charAt(0) == '\'') && script.charAt(0) == script.charAt(script.length() - 1)) {
               script = script.substring(1, script.length() - 1);
               commandArguments[i] = script;
            }
         }
         return ((UnifiedJedis) jedisClient).sendCommand(pc, commandArguments);
      }
   }

   private Object evaluateCommand(String[] command, Object jedis) throws DBCException {
      String commandName = command[0].toLowerCase(Locale.ENGLISH);
      Method[] methods = this.getSession().getDataSource().getCommandMethods(commandName);
      if (methods == null) {
         throw new DBCException("Unrecognized command [" + commandName + "]");
      } else {
         Method[] var8 = methods;
         int var7 = methods.length;

         for (int var6 = 0; var6 < var7; ++var6) {
            Method method = var8[var6];
            Class[] paramTypes = method.getParameterTypes();
            if (paramTypes.length == command.length - 1) {
               Object[] params = new Object[paramTypes.length];
               boolean hasBadParams = false;

               try {
                  for (int i = 0; i < paramTypes.length; ++i) {
                     Class<?> paramType = paramTypes[i];
                     if (!paramType.isPrimitive() && !BeanUtils.isNumericType(paramType) && !BeanUtils.isBooleanType(paramType) && paramType != String.class) {
                        if (Map.class.isAssignableFrom(paramType)) {
                           hasBadParams = true;
                           break;
                        }

                        if (paramType.isArray() && paramType.getComponentType() == String.class) {
                           params[i] = new String[]{command[1 + i]};
                        }
                     } else {
                        params[i] = GeneralUtils.convertString(command[1 + i], paramType);
                     }
                  }

                  if (!hasBadParams) {
                     return method.invoke(jedis, params);
                  }
               } catch (IllegalAccessException var14) {
                  throw new DBCException(var14, this.session.getExecutionContext());
               } catch (InvocationTargetException var15) {
                  throw new DBCException(var15.getTargetException(), this.session.getExecutionContext());
               }
            }
         }

         throw new DBCException("Can't find appropriate method for command '" + commandName + "' (" + (command.length - 1) + ")");
      }
   }

   public RedisBaseResultSet openResultSet() throws DBCException {
      return this.result == null ? null : new RedisSimpleResultSet(this, this.result, (long) ((int) this.offset));
   }

}
