
package com.dc.summer.ext.datavirtuality.model;

import com.dc.summer.Log;
import com.dc.summer.ext.generic.model.GenericTableBase;
import com.dc.summer.DBException;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.generic.model.GenericProcedure;
import com.dc.summer.ext.generic.model.GenericView;
import com.dc.summer.ext.generic.model.meta.GenericMetaModel;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.sql.SQLException;
import java.util.Map;

/**
 * DataVirtualityMetaModel
 */
public class DataVirtualityMetaModel extends GenericMetaModel
{
    private static final Log log = Log.getLog(DataVirtualityMetaModel.class);

    public DataVirtualityMetaModel() {
        super();
    }

    @Override
    public GenericDataSource createDataSourceImpl(DBRProgressMonitor monitor, DBPDataSourceContainer container) throws DBException {
        return new DataVirtualityDataSource(monitor, container, this);
    }

    @Override
    public boolean supportsTableDDLSplit(GenericTableBase sourceObject) {
        return false;
    }

    @Override
    public String getViewDDL(DBRProgressMonitor monitor, GenericView sourceObject, Map<String, Object> options) throws DBException {
        GenericDataSource dataSource = sourceObject.getDataSource();

        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Read DataVirtuality object DDL")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                    "SELECT definition FROM SYSADMIN.ViewDefinitions WHERE name ='" + sourceObject.getFullyQualifiedName(DBPEvaluationContext.DDL) + "'"))
            {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    StringBuilder sql = new StringBuilder();
                    while (dbResult.nextRow()) {
                        sql.append(dbResult.getString(1));
                    }
                    String result = sql.toString().trim();
                    while (result.endsWith(";")) {
                        result = result.substring(0, result.length() - 1);
                    }
                    return result;
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, dataSource);
        }
    }

    @Override
    public String getProcedureDDL(DBRProgressMonitor monitor, GenericProcedure sourceObject) throws DBException {
        GenericDataSource dataSource = sourceObject.getDataSource();

        try (JDBCSession session = DBUtils.openMetaSession(monitor, sourceObject, "Read DataVirtuality object DDL")) {
            try (JDBCPreparedStatement dbStat = session.prepareStatement(
                "SELECT definition FROM SYSADMIN.ProcDefinitions WHERE name ='" + sourceObject.getFullyQualifiedName(DBPEvaluationContext.DDL) + "'"))
            {
                try (JDBCResultSet dbResult = dbStat.executeQuery()) {
                    StringBuilder sql = new StringBuilder();
                    while (dbResult.nextRow()) {
                        sql.append(dbResult.getString(1));
                    }
                    return sql.toString();
                }
            }
        } catch (SQLException e) {
            throw new DBException(e, dataSource);
        }
    }

    @Override
    public boolean isTableCommentEditable() {
        return false;
    }

    @Override
    public boolean isTableColumnCommentEditable() {
        return false;
    }
}
