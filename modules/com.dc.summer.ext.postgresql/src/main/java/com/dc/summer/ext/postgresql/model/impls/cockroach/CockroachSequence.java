
package com.dc.summer.ext.postgresql.model.impls.cockroach;

import com.dc.summer.Log;
import com.dc.summer.model.DBPEvaluationContext;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.Nullable;
import com.dc.summer.ext.postgresql.model.PostgreSchema;
import com.dc.summer.ext.postgresql.model.PostgreSequence;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.exec.jdbc.JDBCResultSet;
import com.dc.summer.model.exec.jdbc.JDBCSession;

public class CockroachSequence extends PostgreSequence {

    private static final Log log = Log.getLog(CockroachSequence.class);

    public CockroachSequence(PostgreSchema schema, JDBCResultSet dbResult) {
        super(schema, dbResult);
    }

    public CockroachSequence(PostgreSchema catalog) {
        super(catalog);
    }

    @Nullable
    @Override
    public String getDescription() {
        // Not supported yet (Cockroach 22.1.2)
        return super.getDescription();
    }

    @Override
    public void loadAdditionalInfo(DBRProgressMonitor monitor) {
        // Cache and cycle options not supported correctly yet (Cockroach 22.1.2)
        AdditionalInfo additionalInfo = getAdditionalInfo();
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load sequence additional info")) {
            try (JDBCPreparedStatement dbSeqStat = session.prepareStatement(
                "SELECT * from information_schema.\"sequences\" WHERE sequence_schema=? AND sequence_name=?")) {
                dbSeqStat.setString(1, getSchema().getName());
                dbSeqStat.setString(2, getName());
                try (JDBCResultSet seqResults = dbSeqStat.executeQuery()) {
                    if (seqResults.next()) {
                        additionalInfo.setStartValue(JDBCUtils.safeGetLong(seqResults, "start_value"));
                        additionalInfo.setMinValue(JDBCUtils.safeGetLong(seqResults, "minimum_value"));
                        additionalInfo.setMaxValue(JDBCUtils.safeGetLong(seqResults, "maximum_value"));
                        additionalInfo.setIncrementBy(JDBCUtils.safeGetLong(seqResults, "increment"));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error reading sequence values", e);
        }
        try (JDBCSession session = DBUtils.openMetaSession(monitor, this, "Load sequence last value")) {
            try (JDBCPreparedStatement dbSeqStat = session.prepareStatement(
                "SELECT * from " + getFullyQualifiedName(DBPEvaluationContext.DML))) {
                try (JDBCResultSet seqResults = dbSeqStat.executeQuery()) {
                    if (seqResults.next()) {
                        additionalInfo.setLastValue(JDBCUtils.safeGetLong(seqResults, "last_value"));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error reading sequence las value", e);
        }
        additionalInfo.setLoaded(true);
    }

    @Override
    public boolean supportsCacheAndCycle() {
        return false;
    }
}
