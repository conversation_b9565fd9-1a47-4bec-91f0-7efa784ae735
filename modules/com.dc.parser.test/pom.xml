<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dc</groupId>
        <artifactId>summer.modules</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>com.dc.parser.test</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <commons-csv.version>1.9.0</commons-csv.version>
    </properties>

    <dependencies>
        <!-- parser 解析模块 -->
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.oracle</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.mysql</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.postgresql</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.gaussdb</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.mssql</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.hive</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.ext.hetu</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.dc</groupId>
            <artifactId>com.dc.parser.test-util</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>${commons-csv.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
        </dependency>
    </dependencies>
</project>