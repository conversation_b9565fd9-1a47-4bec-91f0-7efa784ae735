package com.dc.parser.test.mysql.external;

import com.dc.parser.test.it.external.ExternalSQLParserIT;
import com.dc.parser.test.it.loader.ExternalCaseSettings;
import com.parser.test.loader.MySQLExternalTestParameterLoadTemplate;

@ExternalCaseSettings(value = "MySQL", caseURL = ExternalMySQLParserIT.CASE_URL, resultURL = ExternalMySQLParserIT.RESULT_URL, template = MySQLExternalTestParameterLoadTemplate.class)
class ExternalMySQLParserIT extends ExternalSQLParserIT {

    static final String CASE_URL = "https://github.com/mysql/mysql-server/tree/8.0/mysql-test/t";

    static final String RESULT_URL = "https://github.com/mysql/mysql-server/tree/8.0/mysql-test/r";
}
