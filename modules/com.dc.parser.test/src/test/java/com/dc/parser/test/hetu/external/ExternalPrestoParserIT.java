package com.dc.parser.test.hetu.external;

import com.dc.parser.test.it.external.ExternalSQLParserIT;
import com.dc.parser.test.it.external.loader.PrestoExternalTestParameterLoadTemplate;
import com.dc.parser.test.it.loader.ExternalCaseSettings;

@ExternalCaseSettings(value = "Hetu", caseURL = ExternalPrestoParserIT.CASE_URL, resultURL = "", template = PrestoExternalTestParameterLoadTemplate.class)
class ExternalPrestoParserIT extends ExternalSQLParserIT {

    static final String CASE_URL = "https://github.com/prestodb/presto/tree/c63a7c7773e62fc8d96e9a9ec00ffb5edef37def/presto-product-tests/src/main/resources/sql-tests/testcases";
}
