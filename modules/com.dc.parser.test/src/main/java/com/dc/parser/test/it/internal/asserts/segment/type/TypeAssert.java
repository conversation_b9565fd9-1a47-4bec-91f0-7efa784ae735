package com.dc.parser.test.it.internal.asserts.segment.type;

import com.dc.parser.model.segment.ddl.type.TypeSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.SQLSegmentAssert;
import com.dc.parser.test.it.internal.asserts.segment.identifier.IdentifierValueAssert;
import com.dc.parser.test.it.internal.asserts.segment.owner.OwnerAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.type.ExpectedType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Type assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TypeAssert {

    /**
     * Assert actual type segment is correct with expected type.
     *
     * @param assertContext assert context
     * @param actual        actual type segment
     * @param expected      expected type
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final TypeSegment actual, final ExpectedType expected) {
        assertNotNull(expected, assertContext.getText("Type should exist."));
        IdentifierValueAssert.assertIs(assertContext, actual.getIdentifier(), expected, "Type");
        if (null == expected.getOwner()) {
            assertFalse(actual.getOwner().isPresent(), assertContext.getText("Actual owner should not exist."));
        } else {
            assertTrue(actual.getOwner().isPresent(), assertContext.getText("Actual owner should exist."));
            OwnerAssert.assertIs(assertContext, actual.getOwner().get(), expected.getOwner());
        }
        SQLSegmentAssert.assertIs(assertContext, actual, expected);
    }
}
