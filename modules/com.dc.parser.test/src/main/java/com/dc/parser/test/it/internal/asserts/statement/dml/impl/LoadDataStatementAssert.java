package com.dc.parser.test.it.internal.asserts.statement.dml.impl;

import com.dc.parser.model.statement.dml.LoadDataStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.LoadDataStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Load data statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class LoadDataStatementAssert {

    /**
     * Assert load data statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual load data statement
     * @param expected      expected load data statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final LoadDataStatement actual, final LoadDataStatementTestCase expected) {
        assertTable(assertContext, actual, expected);
    }

    private static void assertTable(final SQLCaseAssertContext assertContext, final LoadDataStatement actual, final LoadDataStatementTestCase expected) {
        if (null == expected.getTable()) {
            assertNull(actual.getTableSegment(), assertContext.getText("Actual table should not exist."));
        } else {
            TableAssert.assertIs(assertContext, actual.getTableSegment(), expected.getTable());
        }
    }
}
