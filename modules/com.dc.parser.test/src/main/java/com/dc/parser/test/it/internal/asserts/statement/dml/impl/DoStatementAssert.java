package com.dc.parser.test.it.internal.asserts.statement.dml.impl;

import com.dc.parser.ext.mysql.statement.dml.MySQLDoStatement;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.dc.parser.model.statement.dml.DoStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.expression.ExpressionAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.dostatement.ExpectedDoParameter;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dml.DoStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Do statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DoStatementAssert {

    /**
     * Assert do statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual do statement
     * @param expected      expected do statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final DoStatement actual, final DoStatementTestCase expected) {
        if (actual instanceof MySQLDoStatement) {
            MySQLDoStatement actualStatement = (MySQLDoStatement) actual;
            if (null != actualStatement.getParameters() && !expected.getDoParameters().isEmpty()) {
                assertThat(assertContext.getText("Do parameters assertion error: "), actualStatement.getParameters().size(), is(expected.getDoParameters().size()));
                int count = 0;
                for (ExpressionSegment each : actualStatement.getParameters()) {
                    assertParameter(assertContext, each, expected.getDoParameters().get(count));
                    count++;
                }
            }
        }
    }

    private static void assertParameter(final SQLCaseAssertContext assertContext, final ExpressionSegment actual, final ExpectedDoParameter expected) {
        if (actual instanceof ParameterMarkerExpressionSegment) {
            ExpressionAssert.assertParameterMarkerExpression(assertContext, (ParameterMarkerExpressionSegment) actual, expected.getParameterMarkerExpression());
        } else if (actual instanceof LiteralExpressionSegment) {
            ExpressionAssert.assertLiteralExpression(assertContext, (LiteralExpressionSegment) actual, expected.getLiteralExpression());
        } else if (actual instanceof CommonExpressionSegment) {
            ExpressionAssert.assertCommonExpression(assertContext, (CommonExpressionSegment) actual, expected.getCommonExpression());
        } else if (actual instanceof FunctionSegment) {
            ExpressionAssert.assertFunction(assertContext, (FunctionSegment) actual, expected.getFunction());
        }
    }
}
