package com.dc.parser.test.it.internal.asserts.statement.dcl.impl.sqlserver;

import com.dc.parser.ext.mssql.statement.dcl.SQLServerRevokeStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.column.ColumnAssert;
import com.dc.parser.test.it.internal.asserts.segment.table.TableAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.dcl.RevokeStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * SQLServer revoke statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SQLServerRevokeStatementAssert {

    /**
     * Assert SQLServer revoke statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual SQLServer revoke statement
     * @param expected      expected revoke statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final SQLServerRevokeStatement actual, final RevokeStatementTestCase expected) {
        if (!expected.getTables().isEmpty()) {
            TableAssert.assertIs(assertContext, actual.getTables(), expected.getTables());
        }
        if (!expected.getColumns().isEmpty()) {
            ColumnAssert.assertIs(assertContext, actual.getColumns(), expected.getColumns());
        }
    }
}
