package com.dc.parser.test.it.internal.asserts.segment.assignment;

import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.BinaryOperationExpression;
import com.dc.parser.model.segment.dml.expr.CaseWhenExpression;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.dml.expr.complex.CommonExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.LiteralExpressionSegment;
import com.dc.parser.model.segment.dml.expr.simple.ParameterMarkerExpressionSegment;
import com.dc.parser.model.segment.dml.expr.subquery.SubqueryExpressionSegment;
import com.dc.parser.model.segment.dml.item.ExpressionProjectionSegment;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.asserts.segment.column.ColumnAssert;
import com.dc.parser.test.it.internal.asserts.segment.expression.ExpressionAssert;
import com.dc.parser.test.it.internal.cases.parser.jaxb.segment.impl.assignment.ExpectedAssignmentValue;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Assignment value assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AssignmentValueAssert {

    /**
     * Assert actual expression segment is correct with expected assignment value.
     *
     * @param assertContext assert context
     * @param actual        actual expression segment
     * @param expected      expected assignment value
     * @throws UnsupportedOperationException unsupported assertion segment exception
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final ExpressionSegment actual, final ExpectedAssignmentValue expected) {
        if (actual instanceof ParameterMarkerExpressionSegment) {
            ExpressionAssert.assertParameterMarkerExpression(assertContext, (ParameterMarkerExpressionSegment) actual, expected.getParameterMarkerExpression());
            return;
        }
        if (actual instanceof LiteralExpressionSegment) {
            ExpressionAssert.assertLiteralExpression(assertContext, (LiteralExpressionSegment) actual, expected.getLiteralExpression());
            // FIXME should be CommonExpressionProjection, not ExpressionProjectionSegment
            return;
        }
        if (actual instanceof ExpressionProjectionSegment) {
            ExpressionAssert.assertCommonExpression(assertContext, (ExpressionProjectionSegment) actual, expected.getCommonExpression());
            return;
        }
        if (actual instanceof ColumnSegment) {
            ColumnAssert.assertIs(assertContext, (ColumnSegment) actual, expected.getColumn());
            return;
        }
        if (actual instanceof SubqueryExpressionSegment) {
            ExpressionAssert.assertSubqueryExpression(assertContext, (SubqueryExpressionSegment) actual, expected.getSubquery());
            return;
        }
        if (actual instanceof FunctionSegment) {
            ExpressionAssert.assertFunction(assertContext, (FunctionSegment) actual, expected.getFunction());
            return;
        }
        if (actual instanceof CommonExpressionSegment) {
            ExpressionAssert.assertCommonExpression(assertContext, (CommonExpressionSegment) actual, expected.getCommonExpression());
            return;
        }
        if (actual instanceof CaseWhenExpression) {
            ExpressionAssert.assertCaseWhenExpression(assertContext, (CaseWhenExpression) actual, expected.getCaseWhenExpression());
            return;
        }
        if (actual instanceof BinaryOperationExpression) {
            ExpressionAssert.assertBinaryOperationExpression(assertContext, (BinaryOperationExpression) actual, expected.getBinaryOperationExpression());
        }
    }
}
