package com.dc.parser.test.it.internal.asserts.statement.tcl.impl;

import com.dc.parser.model.statement.tcl.BeginTransactionStatement;
import com.dc.parser.test.it.internal.asserts.SQLCaseAssertContext;
import com.dc.parser.test.it.internal.cases.parser.jaxb.statement.tcl.BeginTransactionStatementTestCase;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Begin transaction statement assert.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BeginTransactionStatementAssert {

    /**
     * Assert begin transaction statement is correct with expected parser result.
     *
     * @param assertContext assert context
     * @param actual        actual begin transaction statement
     * @param expected      expected begin transaction statement test case
     */
    public static void assertIs(final SQLCaseAssertContext assertContext, final BeginTransactionStatement actual, final BeginTransactionStatementTestCase expected) {
    }
}
