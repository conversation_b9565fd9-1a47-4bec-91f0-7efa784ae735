<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-view sql-case-id="create_view_with_udf" view-definition="SELECT DB29023216.TESTFUNC()">
        <view name="V_T1_C" start-index="12" stop-index="17"/>
        <select>
            <projections start-index="29" stop-index="49">
                <expression-projection text="DB29023216.TESTFUNC()" start-index="29" stop-index="49">
                    <expr>
                        <function function-name="DB29023216.TESTFUNC()" text="DB29023216.TESTFUNC()" start-index="29"
                                  stop-index="49"/>
                    </expr>
                </expression-projection>
            </projections>
        </select>
    </create-view>
    <create-view sql-case-id="create_view_with_udf_nested_fun" view-definition="SELECT DB29023216.TESTFUNC(NOW())">
        <view name="V_T1_C" start-index="12" stop-index="17"/>
        <select>
            <projections start-index="29" stop-index="54">
                <expression-projection text="DB29023216.TESTFUNC(NOW())" start-index="29" stop-index="54">
                    <expr>
                        <function function-name="DB29023216.TESTFUNC(NOW())" text="DB29023216.TESTFUNC(NOW())"
                                  start-index="29" stop-index="54">
                            <parameter>
                                <function function-name="NOW" text="NOW()" start-index="49" stop-index="53"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
        </select>
    </create-view>
    <create-view sql-case-id="create_view_with_udf_arg" view-definition="SELECT DB29023216.TESTFUNC(1,2,3,4)">
        <view name="V_T1_C" start-index="12" stop-index="17"/>
        <select>
            <projections start-index="29" stop-index="56">
                <expression-projection text="DB29023216.TESTFUNC(1,2,3,4)" start-index="29" stop-index="56">
                    <expr>
                        <function function-name="DB29023216.TESTFUNC(1,2,3,4)" text="DB29023216.TESTFUNC(1,2,3,4)"
                                  start-index="29" stop-index="56">
                            <parameter>
                                <literal-expression start-index="49" stop-index="49" value="1"/>
                            </parameter>
                            <parameter>
                                <literal-expression start-index="51" stop-index="51" value="2"/>
                            </parameter>
                            <parameter>
                                <literal-expression start-index="53" stop-index="53" value="3"/>
                            </parameter>
                            <parameter>
                                <literal-expression start-index="55" stop-index="55" value="4"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
        </select>
    </create-view>
    <create-view sql-case-id="create_view" view-definition="SELECT * FROM films WHERE kind = 'Comedy'">
        <view name="comedies" start-index="12" stop-index="19"/>
        <select>
            <projections start-index="31" stop-index="31">
                <shorthand-projection start-index="31" stop-index="31"/>
            </projections>
            <from>
                <simple-table name="films" start-index="38" stop-index="42"/>
            </from>
            <where start-index="44" stop-index="64">
                <expr>
                    <binary-operation-expression start-index="50" stop-index="64">
                        <left>
                            <column name="kind" start-index="50" stop-index="53"/>
                        </left>
                        <right>
                            <literal-expression value="Comedy" start-index="57" stop-index="64"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_check_option"
                 view-definition="SELECT * FROM comedies WHERE classification = 'U'">
        <view name="universal_comedies" start-index="12" stop-index="29"/>
        <select>
            <projections start-index="41" stop-index="41">
                <shorthand-projection start-index="41" stop-index="41"/>
            </projections>
            <from>
                <simple-table name="comedies" start-index="48" stop-index="55"/>
            </from>
            <where start-index="57" stop-index="82">
                <expr>
                    <binary-operation-expression start-index="63" stop-index="82">
                        <left>
                            <column name="classification" start-index="63" stop-index="76"/>
                        </left>
                        <right>
                            <literal-expression value="U" start-index="80" stop-index="82"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_recursive"
                 view-definition="VALUES (1) UNION ALL SELECT n+1 FROM nums_1_100 WHERE n = 100">
        <view name="nums_1_100" start-index="22" stop-index="38">
            <owner name="public" start-index="22" stop-index="27"/>
        </view>
        <select>
            <!-- FIXME support VALUES (1) projection parse -->
            <projections start-index="-1" stop-index="-1"/>
            <combine combine-type="UNION_ALL" start-index="58" stop-index="107">
                <left>
                    <projections start-index="-1" stop-index="-1"/>
                </left>
                <right>
                    <projections start-index="75" stop-index="77">
                        <expression-projection text="n+1" start-index="75" stop-index="77"/>
                    </projections>
                    <from>
                        <simple-table name="nums_1_100" start-index="84" stop-index="93"/>
                    </from>
                    <where start-index="95" stop-index="107">
                        <expr>
                            <binary-operation-expression start-index="101" stop-index="107">
                                <left>
                                    <column name="n" start-index="101" stop-index="101"/>
                                </left>
                                <right>
                                    <literal-expression value="100" start-index="105" stop-index="107"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </expr>
                    </where>
                </right>
            </combine>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_option" view-definition="SELECT * FROM t_order">
        <view name="order_view" start-index="28" stop-index="37"/>
        <select>
            <projections start-index="97" stop-index="97">
                <shorthand-projection start-index="97" stop-index="97"/>
            </projections>
            <from>
                <simple-table name="t_order" start-index="104" stop-index="110"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_or_replace_view_with_select"
                 view-definition="select p.name planet, p.class, m.name moon, m.radius from planets p, moons m where p.name = m.planet_name(+) and m.name(+) not like 'S/%'">
        <view name="named_moons" start-index="23" stop-index="33"/>
        <select>
            <projections start-index="45" stop-index="89">
                <column-projection name="name" alias="planet" start-index="45" stop-index="57">
                    <owner name="p" start-index="45" stop-index="45"/>
                </column-projection>
                <column-projection name="class" start-index="60" stop-index="66">
                    <owner name="p" start-index="60" stop-index="60"/>
                </column-projection>
                <column-projection name="name" alias="moon" start-index="69" stop-index="79">
                    <owner name="m" start-index="69" stop-index="69"/>
                </column-projection>
                <column-projection name="radius" start-index="82" stop-index="89">
                    <owner name="m" start-index="82" stop-index="82"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="planets" alias="p" start-index="96" stop-index="104"/>
                    </left>
                    <right>
                        <simple-table name="moons" alias="m" start-index="107" stop-index="113"/>
                    </right>
                </join-table>
            </from>
            <where start-index="115" stop-index="174">
                <expr>
                    <binary-operation-expression start-index="121" stop-index="174">
                        <left>
                            <binary-operation-expression start-index="121" stop-index="145">
                                <left>
                                    <column name="name" start-index="121" stop-index="126">
                                        <owner name="p" start-index="121" stop-index="121"/>
                                    </column>
                                </left>
                                <right>
                                    <outer-join-expression start-index="130" stop-index="145" text="m.planet_name(+)">
                                        <column name="planet_name" start-index="130" stop-index="142">
                                            <owner name="m" start-index="130" stop-index="130"/>
                                        </column>
                                        <join-operator>(+)</join-operator>
                                    </outer-join-expression>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="151" stop-index="174">
                                <left>
                                    <outer-join-expression start-index="151" stop-index="159" text="m.name(+)">
                                        <column name="name" start-index="151" stop-index="156">
                                            <owner name="m" start-index="151" stop-index="151"/>
                                        </column>
                                        <join-operator>(+)</join-operator>
                                    </outer-join-expression>
                                </left>
                                <right>
                                    <list-expression start-index="170" stop-index="174">
                                        <items>
                                            <literal-expression value="S/%" start-index="170" stop-index="174"/>
                                        </items>
                                    </list-expression>
                                </right>
                                <operator>NOT LIKE</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>and</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_simple_select1" view-definition="SELECT * FROM laurel.emp">
        <view name="employee" start-index="12" stop-index="26">
            <owner name="laurel" start-index="12" stop-index="17"/>
        </view>
        <select>
            <projections start-index="38" stop-index="38">
                <shorthand-projection start-index="38" stop-index="38"/>
            </projections>
            <from>
                <simple-table name="emp" start-index="45" stop-index="54">
                    <owner name="laurel" start-index="45" stop-index="50"/>
                </simple-table>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_simple_select2" view-definition="SELECT * FROM scott.dept">
        <view name="dept" start-index="12" stop-index="15"/>
        <select>
            <projections start-index="27" stop-index="27">
                <shorthand-projection start-index="27" stop-index="27"/>
            </projections>
            <from>
                <simple-table name="dept" start-index="34" stop-index="43">
                    <owner name="scott" start-index="34" stop-index="38"/>
                </simple-table>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_simple_select3"
                 view-definition="SELECT empno, ename, mgr, deptno FROM company.emp">
        <view name="emp" start-index="12" stop-index="14"/>
        <select>
            <projections start-index="26" stop-index="50">
                <column-projection name="empno" start-index="26" stop-index="30"/>
                <column-projection name="ename" start-index="33" stop-index="37"/>
                <column-projection name="mgr" start-index="40" stop-index="42"/>
                <column-projection name="deptno" start-index="45" stop-index="50"/>
            </projections>
            <from>
                <simple-table name="emp" start-index="57" stop-index="67">
                    <owner name="company" start-index="57" stop-index="63"/>
                </simple-table>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_simple_select4" view-definition="SELECT empno, ename, deptno FROM emp">
        <view name="emp_v" start-index="12" stop-index="16"/>
        <select>
            <projections start-index="28" stop-index="47">
                <column-projection name="empno" start-index="28" stop-index="32"/>
                <column-projection name="ename" start-index="35" stop-index="39"/>
                <column-projection name="deptno" start-index="42" stop-index="47"/>
            </projections>
            <from>
                <simple-table name="emp" start-index="54" stop-index="56"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_simple_select5"
                 view-definition="SELECT EMPLOYEE_ID, FIRST_NAME, LAST_NAME, EMAIL, MANAGER_ID FROM EMPLOYEES">
        <view name="hr_verify" start-index="12" stop-index="20"/>
        <select>
            <projections start-index="32" stop-index="84">
                <column-projection name="EMPLOYEE_ID" start-index="32" stop-index="42"/>
                <column-projection name="FIRST_NAME" start-index="45" stop-index="54"/>
                <column-projection name="LAST_NAME" start-index="57" stop-index="65"/>
                <column-projection name="EMAIL" start-index="68" stop-index="72"/>
                <column-projection name="MANAGER_ID" start-index="75" stop-index="84"/>
            </projections>
            <from>
                <simple-table name="EMPLOYEES" start-index="91" stop-index="99"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition1"
                 view-definition="SELECT e1.ename, e2.empno, e2.deptno FROM emp e1, emp e2 WHERE e1.empno = e2.empno">
        <view name="emp_emp" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="30" stop-index="58">
                <column-projection name="ename" start-index="30" stop-index="37">
                    <owner name="e1" start-index="30" stop-index="31"/>
                </column-projection>
                <column-projection name="empno" start-index="40" stop-index="47">
                    <owner name="e2" start-index="40" stop-index="41"/>
                </column-projection>
                <column-projection name="deptno" start-index="50" stop-index="58">
                    <owner name="e2" start-index="50" stop-index="51"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" alias="e1" start-index="65" stop-index="70"/>
                    </left>
                    <right>
                        <simple-table name="emp" alias="e2" start-index="73" stop-index="78"/>
                    </right>
                </join-table>
            </from>
            <where start-index="80" stop-index="104">
                <expr>
                    <binary-operation-expression start-index="86" stop-index="104">
                        <left>
                            <column name="empno" start-index="86" stop-index="93">
                                <owner name="e1" start-index="86" stop-index="87"/>
                            </column>
                        </left>
                        <right>
                            <column name="empno" start-index="97" stop-index="104">
                                <owner name="e2" start-index="97" stop-index="98"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition2"
                 view-definition="SELECT * FROM employees  WHERE salary &lt; 10000">
        <view name="lowsal" start-index="12" stop-index="17"/>
        <select>
            <projections start-index="29" stop-index="29">
                <shorthand-projection start-index="29" stop-index="29"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="36" stop-index="44"/>
            </from>
            <where start-index="47" stop-index="66">
                <expr>
                    <binary-operation-expression start-index="53" stop-index="66">
                        <left>
                            <column name="salary" start-index="53" stop-index="58"/>
                        </left>
                        <right>
                            <literal-expression value="10000" start-index="62" stop-index="66"/>
                        </right>
                        <operator>&lt;</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition3"
                 view-definition="SELECT employee_id, last_name, department_id FROM employees, departments WHERE employees.department_id = departments.department_id">
        <view name="employees_departments" start-index="12" stop-index="32"/>
        <select>
            <projections start-index="44" stop-index="80">
                <column-projection name="employee_id" start-index="44" stop-index="54"/>
                <column-projection name="last_name" start-index="57" stop-index="65"/>
                <column-projection name="department_id" start-index="68" stop-index="80"/>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="employees" start-index="87" stop-index="95"/>
                    </left>
                    <right>
                        <simple-table name="departments" start-index="98" stop-index="108"/>
                    </right>
                </join-table>
            </from>
            <where start-index="110" stop-index="166">
                <expr>
                    <binary-operation-expression start-index="116" stop-index="166">
                        <left>
                            <column name="department_id" start-index="116" stop-index="138">
                                <owner name="employees" start-index="116" stop-index="124"/>
                            </column>
                        </left>
                        <right>
                            <column name="department_id" start-index="142" stop-index="166">
                                <owner name="departments" start-index="142" stop-index="152"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition4"
                 view-definition="SELECT employee_id, last_name, department_id, job_id FROM employees WHERE job_id = 'PU_CLERK' or job_id = 'SH_CLERK' or job_id = 'ST_CLERK'">
        <view name="clerk" start-index="12" stop-index="16"/>
        <select>
            <projections start-index="28" stop-index="72">
                <column-projection name="employee_id" start-index="28" stop-index="38"/>
                <column-projection name="last_name" start-index="41" stop-index="49"/>
                <column-projection name="department_id" start-index="52" stop-index="64"/>
                <column-projection name="job_id" start-index="67" stop-index="72"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="79" stop-index="87"/>
            </from>
            <where start-index="89" stop-index="159">
                <expr>
                    <binary-operation-expression start-index="95" stop-index="159">
                        <left>
                            <binary-operation-expression start-index="95" stop-index="136">
                                <left>
                                    <binary-operation-expression start-index="95" stop-index="113">
                                        <left>
                                            <column name="job_id" start-index="95" stop-index="100"/>
                                        </left>
                                        <right>
                                            <literal-expression value="PU_CLERK" start-index="104" stop-index="113"/>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="118" stop-index="136">
                                        <left>
                                            <column name="job_id" start-index="118" stop-index="123"/>
                                        </left>
                                        <right>
                                            <literal-expression value="SH_CLERK" start-index="127" stop-index="136"/>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>or</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="141" stop-index="159">
                                <left>
                                    <column name="job_id" start-index="141" stop-index="146"/>
                                </left>
                                <right>
                                    <literal-expression value="ST_CLERK" start-index="150" stop-index="159"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>or</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition5"
                 view-definition="SELECT last_name, salary*12 annual_salary FROM employees WHERE department_id = 20">
        <view name="emp_view" start-index="12" stop-index="19"/>
        <select>
            <projections start-index="31" stop-index="64">
                <column-projection name="last_name" start-index="31" stop-index="39"/>
                <expression-projection alias="annual_salary" text="salary*12" start-index="42" stop-index="64">
                    <expr>
                        <binary-operation-expression start-index="42" stop-index="50">
                            <left>
                                <column name="salary" start-index="42" stop-index="47"/>
                            </left>
                            <right>
                                <literal-expression value="12" start-index="49" stop-index="50"/>
                            </right>
                            <operator>*</operator>
                        </binary-operation-expression>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="employees" start-index="71" stop-index="79"/>
            </from>
            <where start-index="81" stop-index="104">
                <expr>
                    <binary-operation-expression start-index="87" stop-index="104">
                        <left>
                            <column name="department_id" start-index="87" stop-index="99"/>
                        </left>
                        <right>
                            <literal-expression value="20" start-index="103" stop-index="104"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition6"
                 view-definition="SELECT empno, ename, deptno FROM emp WHERE sal &gt; 1000">
        <view name="emp_v_2" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="30" stop-index="49">
                <column-projection name="empno" start-index="30" stop-index="34"/>
                <column-projection name="ename" start-index="37" stop-index="41"/>
                <column-projection name="deptno" start-index="44" stop-index="49"/>
            </projections>
            <from>
                <simple-table name="emp" start-index="56" stop-index="58"/>
            </from>
            <where start-index="60" stop-index="75">
                <expr>
                    <binary-operation-expression start-index="66" stop-index="75">
                        <left>
                            <column name="sal" start-index="66" stop-index="68"/>
                        </left>
                        <right>
                            <literal-expression value="1000" start-index="72" stop-index="75"/>
                        </right>
                        <operator>&gt;</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition7"
                 view-definition="SELECT e.*, Loc, d.dname FROM emp_v e, dept d WHERE e.deptno = d.deptno (+)">
        <view name="emp_dept_oj1" start-index="12" stop-index="23"/>
        <select>
            <projections start-index="35" stop-index="51">
                <shorthand-projection start-index="35" stop-index="37">
                    <owner name="e" start-index="35" stop-index="35"/>
                </shorthand-projection>
                <column-projection name="Loc" start-index="40" stop-index="42"/>
                <column-projection name="dname" start-index="45" stop-index="51">
                    <owner name="d" start-index="45" stop-index="45"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp_v" alias="e" start-index="58" stop-index="64"/>
                    </left>
                    <right>
                        <simple-table name="dept" alias="d" start-index="67" stop-index="72"/>
                    </right>
                </join-table>
            </from>
            <where start-index="74" stop-index="102">
                <expr>
                    <binary-operation-expression start-index="80" stop-index="102">
                        <left>
                            <column name="deptno" start-index="80" stop-index="87">
                                <owner name="e" start-index="80" stop-index="80"/>
                            </column>
                        </left>
                        <right>
                            <outer-join-expression start-index="91" stop-index="102" text="d.deptno (+)">
                                <column name="deptno" start-index="91" stop-index="98">
                                    <owner name="d" start-index="91" stop-index="91"/>
                                </column>
                                <join-operator>(+)</join-operator>
                            </outer-join-expression>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition8"
                 view-definition="SELECT employee_id, last_name, job_id, salary, commission_pct, department_id FROM employees WHERE department_id = 50">
        <view name="employees_50_vw" start-index="12" stop-index="26"/>
        <select>
            <projections start-index="38" stop-index="106">
                <column-projection name="employee_id" start-index="38" stop-index="48"/>
                <column-projection name="last_name" start-index="51" stop-index="59"/>
                <column-projection name="job_id" start-index="62" stop-index="67"/>
                <column-projection name="salary" start-index="70" stop-index="75"/>
                <column-projection name="commission_pct" start-index="78" stop-index="91"/>
                <column-projection name="department_id" start-index="94" stop-index="106"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="113" stop-index="121"/>
            </from>
            <where start-index="123" stop-index="146">
                <expr>
                    <binary-operation-expression start-index="129" stop-index="146">
                        <left>
                            <column name="department_id" start-index="129" stop-index="141"/>
                        </left>
                        <right>
                            <literal-expression value="50" start-index="145" stop-index="146"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition9"
                 view-definition="SELECT d.department_id, d.department_name, l.location_id, l.city FROM departments d, locations l WHERE d.location_id = l.location_id">
        <view name="locations_view" start-index="12" stop-index="25"/>
        <select>
            <projections start-index="37" stop-index="93">
                <column-projection name="department_id" start-index="37" stop-index="51">
                    <owner name="d" start-index="37" stop-index="37"/>
                </column-projection>
                <column-projection name="department_name" start-index="54" stop-index="70">
                    <owner name="d" start-index="54" stop-index="54"/>
                </column-projection>
                <column-projection name="location_id" start-index="73" stop-index="85">
                    <owner name="l" start-index="73" stop-index="73"/>
                </column-projection>
                <column-projection name="city" start-index="88" stop-index="93">
                    <owner name="l" start-index="88" stop-index="88"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="departments" alias="d" start-index="100" stop-index="112"/>
                    </left>
                    <right>
                        <simple-table name="locations" alias="l" start-index="115" stop-index="125"/>
                    </right>
                </join-table>
            </from>
            <where start-index="127" stop-index="161">
                <expr>
                    <binary-operation-expression start-index="133" stop-index="161">
                        <left>
                            <column name="location_id" start-index="133" stop-index="145">
                                <owner name="d" start-index="133" stop-index="133"/>
                            </column>
                        </left>
                        <right>
                            <column name="location_id" start-index="149" stop-index="161">
                                <owner name="l" start-index="149" stop-index="149"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition10"
                 view-definition="SELECT e.empno, e.ename, e.deptno, d.dname, d.loc FROM emp e, dept d WHERE e.deptno (+) = d.deptno">
        <view name="emp_dept_oj2" start-index="12" stop-index="23"/>
        <select>
            <projections start-index="35" stop-index="76">
                <column-projection name="empno" start-index="35" stop-index="41">
                    <owner name="e" start-index="35" stop-index="35"/>
                </column-projection>
                <column-projection name="ename" start-index="44" stop-index="50">
                    <owner name="e" start-index="44" stop-index="44"/>
                </column-projection>
                <column-projection name="deptno" start-index="53" stop-index="60">
                    <owner name="e" start-index="53" stop-index="53"/>
                </column-projection>
                <column-projection name="dname" start-index="63" stop-index="69">
                    <owner name="d" start-index="63" stop-index="63"/>
                </column-projection>
                <column-projection name="loc" start-index="72" stop-index="76">
                    <owner name="d" start-index="72" stop-index="72"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" alias="e" start-index="83" stop-index="87"/>
                    </left>
                    <right>
                        <simple-table name="dept" alias="d" start-index="90" stop-index="95"/>
                    </right>
                </join-table>
            </from>
            <where start-index="97" stop-index="125">
                <expr>
                    <binary-operation-expression start-index="103" stop-index="125">
                        <left>
                            <outer-join-expression start-index="103" stop-index="114" text="e.deptno (+)">
                                <column name="deptno" start-index="103" stop-index="110">
                                    <owner name="e" start-index="103" stop-index="103"/>
                                </column>
                                <join-operator>(+)</join-operator>
                            </outer-join-expression>
                        </left>
                        <right>
                            <column name="deptno" start-index="118" stop-index="125">
                                <owner name="d" start-index="118" stop-index="118"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_select_with_condition11"
                 view-definition="SELECT last_name, salary FROM employees WHERE last_name = USER">
        <view name="own_salary" start-index="12" stop-index="21"/>
        <select>
            <projections start-index="33" stop-index="49">
                <column-projection name="last_name" start-index="33" stop-index="41"/>
                <column-projection name="salary" start-index="44" stop-index="49"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="56" stop-index="64"/>
            </from>
            <where start-index="66" stop-index="87">
                <expr>
                    <binary-operation-expression start-index="72" stop-index="87">
                        <left>
                            <column name="last_name" start-index="72" stop-index="80"/>
                        </left>
                        <right>
                            <column name="USER" start-index="84" stop-index="87"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_as_where_where"
                 view-definition="SELECT employee_id, last_name, department_id FROM employees, departments WHERE employees.department_id = departments.department_id">
        <view name="employees_departments" start-index="12" stop-index="32"/>
        <select>
            <projections start-index="44" stop-index="80">
                <column-projection name="employee_id" start-index="44" stop-index="54"/>
                <column-projection name="last_name" start-index="57" stop-index="65"/>
                <column-projection name="department_id" start-index="68" stop-index="80"/>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="employees" start-index="87" stop-index="95"/>
                    </left>
                    <right>
                        <simple-table name="departments" start-index="98" stop-index="108"/>
                    </right>
                </join-table>
            </from>
            <where start-index="110" stop-index="166">
                <expr>
                    <binary-operation-expression start-index="116" stop-index="166">
                        <left>
                            <column name="department_id" start-index="116" stop-index="138">
                                <owner name="employees" start-index="116" stop-index="124"/>
                            </column>
                        </left>
                        <right>
                            <column name="department_id" start-index="142" stop-index="166">
                                <owner name="departments" start-index="142" stop-index="152"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_partition"
                 view-definition="SELECT * FROM sales PARTITION (SALES_Q1_2000)">
        <view name="Q1_2000_sales" start-index="12" stop-index="24"/>
        <select>
            <projections start-index="36" stop-index="36">
                <shorthand-projection start-index="36" stop-index="36"/>
            </projections>
            <from>
                <simple-table name="sales" start-index="43" stop-index="47"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_check_option1"
                 view-definition="SELECT employee_id, last_name, department_id, job_id FROM employees WHERE job_id = 'PU_CLERK' or job_id = 'SH_CLERK' or job_id = 'ST_CLERK'">
        <view name="clerk" start-index="12" stop-index="16"/>
        <select>
            <projections start-index="28" stop-index="72">
                <column-projection name="employee_id" start-index="28" stop-index="38"/>
                <column-projection name="last_name" start-index="41" stop-index="49"/>
                <column-projection name="department_id" start-index="52" stop-index="64"/>
                <column-projection name="job_id" start-index="67" stop-index="72"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="79" stop-index="87"/>
            </from>
            <where start-index="89" stop-index="159">
                <expr>
                    <binary-operation-expression start-index="95" stop-index="159">
                        <left>
                            <binary-operation-expression start-index="95" stop-index="136">
                                <left>
                                    <binary-operation-expression start-index="95" stop-index="113">
                                        <left>
                                            <column name="job_id" start-index="95" stop-index="100"/>
                                        </left>
                                        <right>
                                            <literal-expression value="PU_CLERK" start-index="104" stop-index="113"/>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="118" stop-index="136">
                                        <left>
                                            <column name="job_id" start-index="118" stop-index="123"/>
                                        </left>
                                        <right>
                                            <literal-expression value="SH_CLERK" start-index="127" stop-index="136"/>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>or</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="141" stop-index="159">
                                <left>
                                    <column name="job_id" start-index="141" stop-index="146"/>
                                </left>
                                <right>
                                    <literal-expression value="ST_CLERK" start-index="150" stop-index="159"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>or</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_check_option2"
                 view-definition="SELECT e1.ename, e2.ename mname FROM emp e1, emp e2 WHERE e1.mgr = e2.empno">
        <view name="emp_mgr" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="30" stop-index="53">
                <column-projection name="ename" start-index="30" stop-index="37">
                    <owner name="e1" start-index="30" stop-index="31"/>
                </column-projection>
                <column-projection name="ename" alias="mname" start-index="40" stop-index="53">
                    <owner name="e2" start-index="40" stop-index="41"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" alias="e1" start-index="60" stop-index="65"/>
                    </left>
                    <right>
                        <simple-table name="emp" alias="e2" start-index="68" stop-index="73"/>
                    </right>
                </join-table>
            </from>
            <where start-index="75" stop-index="97">
                <expr>
                    <binary-operation-expression start-index="81" stop-index="97">
                        <left>
                            <column name="mgr" start-index="81" stop-index="86">
                                <owner start-index="81" stop-index="82" name="e1"/>
                            </column>
                        </left>
                        <right>
                            <column name="empno" start-index="90" stop-index="97">
                                <owner start-index="90" stop-index="91" name="e2"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_union"
                 view-definition="(SELECT employee_id, last_name, job_id, commission_pct, department_id FROM employees) UNION (SELECT employee_id, last_name, job_id, commission_pct, department_id FROM contract_workers)">
        <view name="all_employees_vw" start-index="12" stop-index="27"/>
        <select>
            <projections start-index="40" stop-index="100">
                <column-projection name="employee_id" start-index="40" stop-index="50"/>
                <column-projection name="last_name" start-index="53" stop-index="61"/>
                <column-projection name="job_id" start-index="64" stop-index="69"/>
                <column-projection name="commission_pct" start-index="72" stop-index="85"/>
                <column-projection name="department_id" start-index="88" stop-index="100"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="107" stop-index="115"/>
            </from>
            <combine combine-type="UNION" start-index="32" stop-index="215">
                <left>
                    <projections start-index="40" stop-index="100">
                        <column-projection name="employee_id" start-index="40" stop-index="50"/>
                        <column-projection name="last_name" start-index="53" stop-index="61"/>
                        <column-projection name="job_id" start-index="64" stop-index="69"/>
                        <column-projection name="commission_pct" start-index="72" stop-index="85"/>
                        <column-projection name="department_id" start-index="88" stop-index="100"/>
                    </projections>
                    <from>
                        <simple-table name="employees" start-index="107" stop-index="115"/>
                    </from>
                </left>
                <right>
                    <projections start-index="132" stop-index="192">
                        <column-projection name="employee_id" start-index="132" stop-index="142"/>
                        <column-projection name="last_name" start-index="145" stop-index="153"/>
                        <column-projection name="job_id" start-index="156" stop-index="161"/>
                        <column-projection name="commission_pct" start-index="164" stop-index="177"/>
                        <column-projection name="department_id" start-index="180" stop-index="192"/>
                    </projections>
                    <from>
                        <simple-table name="contract_workers" start-index="199" stop-index="214"/>
                    </from>
                </right>
            </combine>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_object_identifier1"
                 view-definition="SELECT e.empnum, e.ename, e.salary, e.job FROM emp_table e WHERE job = 'Developer'">
        <view name="emp_view1" start-index="12" stop-index="20"/>
        <select>
            <projections start-index="77" stop-index="110">
                <column-projection name="empnum" start-index="77" stop-index="84">
                    <owner name="e" start-index="77" stop-index="77"/>
                </column-projection>
                <column-projection name="ename" start-index="87" stop-index="93">
                    <owner name="e" start-index="87" stop-index="87"/>
                </column-projection>
                <column-projection name="salary" start-index="96" stop-index="103">
                    <owner name="e" start-index="96" stop-index="96"/>
                </column-projection>
                <column-projection name="job" start-index="106" stop-index="110">
                    <owner name="e" start-index="106" stop-index="106"/>
                </column-projection>
            </projections>
            <from>
                <simple-table alias="e" name="emp_table" start-index="117" stop-index="127"/>
            </from>
            <where start-index="129" stop-index="151">
                <expr>
                    <binary-operation-expression start-index="135" stop-index="151">
                        <left>
                            <column name="job" start-index="135" stop-index="137"/>
                        </left>
                        <right>
                            <literal-expression value="Developer" start-index="141" stop-index="151"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_object_identifier2"
                 view-definition="SELECT d.deptno, d.deptname, address_t(d.deptstreet,d.deptcity,d.deptstate,d.deptzip) AS deptaddr FROM dept d">
        <view name="dept_view" start-index="12" stop-index="20"/>
        <select>
            <projections start-index="74" stop-index="163">
                <column-projection name="deptno" start-index="74" stop-index="81">
                    <owner name="d" start-index="74" stop-index="74"/>
                </column-projection>
                <column-projection name="deptname" start-index="84" stop-index="93">
                    <owner name="d" start-index="84" stop-index="84"/>
                </column-projection>
                <expression-projection alias="deptaddr" text="address_t(d.deptstreet,d.deptcity,d.deptstate,d.deptzip)"
                                       start-index="96" stop-index="163">
                    <expr>
                        <function function-name="address_t"
                                  text="address_t(d.deptstreet,d.deptcity,d.deptstate,d.deptzip)" start-index="96"
                                  stop-index="151">
                            <parameter>
                                <column name="deptstreet" start-index="106" stop-index="117">
                                    <owner name="d" start-index="106" stop-index="106"/>
                                </column>
                            </parameter>
                            <parameter>
                                <column name="deptcity" start-index="119" stop-index="128">
                                    <owner name="d" start-index="119" stop-index="119"/>
                                </column>
                            </parameter>
                            <parameter>
                                <column name="deptstate" start-index="130" stop-index="140">
                                    <owner name="d" start-index="130" stop-index="130"/>
                                </column>
                            </parameter>
                            <parameter>
                                <column name="deptzip" start-index="142" stop-index="150">
                                    <owner name="d" start-index="142" stop-index="142"/>
                                </column>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table alias="d" name="dept" start-index="170" stop-index="175"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_host_table"
                 view-definition="SELECT a.empno, a.ename, b.dname FROM scott.emp a, <EMAIL> b WHERE a.deptno = b.deptno">
        <view name="company" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="30" stop-index="54">
                <column-projection name="empno" start-index="30" stop-index="36">
                    <owner name="a" start-index="30" stop-index="30"/>
                </column-projection>
                <column-projection name="ename" start-index="39" stop-index="45">
                    <owner name="a" start-index="39" stop-index="39"/>
                </column-projection>
                <column-projection name="dname" start-index="48" stop-index="54">
                    <owner name="b" start-index="48" stop-index="48"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" alias="a" start-index="61" stop-index="71">
                            <owner name="scott" start-index="61" stop-index="65"/>
                        </simple-table>
                    </left>
                    <right>
                        <simple-table name="dept" alias="b" start-index="74" stop-index="100">
                            <owner name="jward" start-index="74" stop-index="78"/>
                        </simple-table>
                    </right>
                </join-table>
            </from>
            <where start-index="102" stop-index="126">
                <expr>
                    <binary-operation-expression start-index="108" stop-index="126">
                        <left>
                            <column name="deptno" start-index="108" stop-index="115">
                                <owner name="a" start-index="108" stop-index="108"/>
                            </column>
                        </left>
                        <right>
                            <column name="deptno" start-index="119" stop-index="126">
                                <owner name="b" start-index="119" stop-index="119"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_function_group_by_order_by"
                 view-definition="SELECT deptno, MIN(sal), AVG(sal), MAX(sal) FROM emp GROUP BY deptno ORDER BY deptno">
        <view name="dept_salaries" start-index="12" stop-index="24"/>
        <select>
            <projections start-index="36" stop-index="71">
                <column-projection name="deptno" start-index="36" stop-index="41"/>
                <aggregation-projection type="MIN" expression="MIN(sal)" start-index="44" stop-index="51">
                    <parameter>
                        <column name="sal" start-index="48" stop-index="50"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection type="AVG" expression="AVG(sal)" start-index="54" stop-index="61">
                    <parameter>
                        <column name="sal" start-index="58" stop-index="60"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection type="MAX" expression="MAX(sal)" start-index="64" stop-index="71">
                    <parameter>
                        <column name="sal" start-index="68" stop-index="70"/>
                    </parameter>
                </aggregation-projection>
            </projections>
            <from>
                <simple-table name="emp" start-index="78" stop-index="80"/>
            </from>
            <group-by>
                <column-item name="deptno" start-index="91" stop-index="96"/>
            </group-by>
            <order-by>
                <column-item name="deptno" order-direction="ASC" start-index="107" stop-index="112"/>
            </order-by>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_in1"
                 view-definition="SELECT ename, empno, job, dname FROM emp, dept WHERE emp.deptno IN (10, 30) AND emp.deptno = dept.deptno">
        <view name="division1_staff" start-index="12" stop-index="26"/>
        <select>
            <projections start-index="38" stop-index="61">
                <column-projection name="ename" start-index="38" stop-index="42"/>
                <column-projection name="empno" start-index="45" stop-index="49"/>
                <column-projection name="job" start-index="52" stop-index="54"/>
                <column-projection name="dname" start-index="57" stop-index="61"/>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" start-index="68" stop-index="70"/>
                    </left>
                    <right>
                        <simple-table name="dept" start-index="73" stop-index="76"/>
                    </right>
                </join-table>
            </from>
            <where start-index="78" stop-index="134">
                <expr>
                    <binary-operation-expression start-index="84" stop-index="134">
                        <left>
                            <in-expression start-index="84" stop-index="105">
                                <left>
                                    <column name="deptno" start-index="84" stop-index="93">
                                        <owner name="emp" start-index="84" stop-index="86"/>
                                    </column>
                                </left>
                                <right>
                                    <list-expression start-index="98" stop-index="105">
                                        <items>
                                            <literal-expression start-index="99" stop-index="100" value="10"/>
                                        </items>
                                        <items>
                                            <literal-expression start-index="103" stop-index="104" value="30"/>
                                        </items>
                                    </list-expression>
                                </right>
                            </in-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="111" stop-index="134">
                                <left>
                                    <column name="deptno" start-index="111" stop-index="120">
                                        <owner name="emp" start-index="111" stop-index="113"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="deptno" start-index="124" stop-index="134">
                                        <owner name="dept" start-index="124" stop-index="127"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_in2"
                 view-definition="SELECT emp.empno, emp.ename, emp.deptno, emp.sal, dept.dname, dept.loc FROM emp, dept WHERE emp.deptno = dept.deptno AND dept.loc IN ('DALLAS', 'NEW YORK', 'BOSTON')">
        <view name="emp_dept" start-index="12" stop-index="19"/>
        <select>
            <projections start-index="31" stop-index="93">
                <column-projection name="empno" start-index="31" stop-index="39">
                    <owner name="emp" start-index="31" stop-index="33"/>
                </column-projection>
                <column-projection name="ename" start-index="42" stop-index="50">
                    <owner name="emp" start-index="42" stop-index="44"/>
                </column-projection>
                <column-projection name="deptno" start-index="53" stop-index="62">
                    <owner name="emp" start-index="53" stop-index="55"/>
                </column-projection>
                <column-projection name="sal" start-index="65" stop-index="71">
                    <owner name="emp" start-index="65" stop-index="67"/>
                </column-projection>
                <column-projection name="dname" start-index="74" stop-index="83">
                    <owner name="dept" start-index="74" stop-index="77"/>
                </column-projection>
                <column-projection name="loc" start-index="86" stop-index="93">
                    <owner name="dept" start-index="86" stop-index="89"/>
                </column-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <simple-table name="emp" start-index="100" stop-index="102"/>
                    </left>
                    <right>
                        <simple-table name="dept" start-index="105" stop-index="108"/>
                    </right>
                </join-table>
            </from>
            <where start-index="110" stop-index="188">
                <expr>
                    <binary-operation-expression start-index="116" stop-index="188">
                        <left>
                            <binary-operation-expression start-index="116" stop-index="139">
                                <left>
                                    <column name="deptno" start-index="116" stop-index="125">
                                        <owner name="emp" start-index="116" stop-index="118"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="deptno" start-index="129" stop-index="139">
                                        <owner name="dept" start-index="129" stop-index="132"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <in-expression start-index="145" stop-index="188">
                                <left>
                                    <column name="loc" start-index="145" stop-index="152">
                                        <owner name="dept" start-index="145" stop-index="148"/>
                                    </column>
                                </left>
                                <right>
                                    <list-expression start-index="157" stop-index="188">
                                        <items>
                                            <literal-expression start-index="158" stop-index="165" value="DALLAS"/>
                                        </items>
                                        <items>
                                            <literal-expression start-index="168" stop-index="177" value="NEW YORK"/>
                                        </items>
                                        <items>
                                            <literal-expression start-index="180" stop-index="187" value="BOSTON"/>
                                        </items>
                                    </list-expression>
                                </right>
                            </in-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_read_only"
                 view-definition="SELECT cust_last_name, nls_language, credit_limit FROM customers">
        <view name="customer_ro" start-index="12" stop-index="22"/>
        <select>
            <projections start-index="59" stop-index="100">
                <column-projection name="cust_last_name" start-index="59" stop-index="72"/>
                <column-projection name="nls_language" start-index="75" stop-index="86"/>
                <column-projection name="credit_limit" start-index="89" stop-index="100"/>
            </projections>
            <from>
                <simple-table name="customers" start-index="107" stop-index="115"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_of_timestamp_minute"
                 view-definition="SELECT * FROM employees AS OF TIMESTAMP (SYSTIMESTAMP - INTERVAL '60' MINUTE)">
        <view name="hour_ago" start-index="12" stop-index="19"/>
        <select>
            <projections start-index="31" stop-index="31">
                <shorthand-projection start-index="31" stop-index="31"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="38" stop-index="46"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_sys_context"
                 view-definition="SELECT * FROM employees WHERE department_id = SYS_CONTEXT('hr_context', 'department_id')">
        <view name="hr_org_secure_view" start-index="12" stop-index="29"/>
        <select>
            <projections start-index="41" stop-index="41">
                <shorthand-projection start-index="41" stop-index="41"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="48" stop-index="56"/>
            </from>
            <where start-index="58" stop-index="121">
                <expr>
                    <binary-operation-expression start-index="64" stop-index="121">
                        <left>
                            <column name="department_id" start-index="64" stop-index="76"/>
                        </left>
                        <right>
                            <function function-name="SYS_CONTEXT" text="SYS_CONTEXT('hr_context', 'department_id')"
                                      start-index="80" stop-index="121">
                                <parameter>
                                    <literal-expression value="hr_context" start-index="92" stop-index="103"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="department_id" start-index="106" stop-index="120"/>
                                </parameter>
                            </function>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_of_with_object_identifier"
                 view-definition="SELECT e.employee_id, e.last_name, e.job_id FROM employees e WHERE job_id = 'IT_PROG'">
        <view name="it_prog_view" start-index="12" stop-index="23"/>
        <select>
            <projections start-index="89" stop-index="124">
                <column-projection name="employee_id" start-index="89" stop-index="101">
                    <owner name="e" start-index="89" stop-index="89"/>
                </column-projection>
                <column-projection name="last_name" start-index="104" stop-index="114">
                    <owner name="e" start-index="104" stop-index="104"/>
                </column-projection>
                <column-projection name="job_id" start-index="117" stop-index="124">
                    <owner name="e" start-index="117" stop-index="117"/>
                </column-projection>
            </projections>
            <from>
                <simple-table name="employees" alias="e" start-index="131" stop-index="141"/>
            </from>
            <where start-index="143" stop-index="166">
                <expr>
                    <binary-operation-expression start-index="149" stop-index="166">
                        <left>
                            <column name="job_id" start-index="149" stop-index="154"/>
                        </left>
                        <right>
                            <literal-expression value="IT_PROG" start-index="158" stop-index="166"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_define_column_with_select"
                 view-definition="SELECT employee_id, last_name, email FROM employees">
        <view name="emp_sal" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="151" stop-index="179">
                <column-projection name="employee_id" start-index="151" stop-index="161"/>
                <column-projection name="last_name" start-index="164" stop-index="172"/>
                <column-projection name="email" start-index="175" stop-index="179"/>
            </projections>
            <from>
                <simple-table name="employees" start-index="186" stop-index="194"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_join_using"
                 view-definition="SELECT employee_id, last_name, salary, location_id FROM employees JOIN departments USING (department_id) WHERE department_id = 10">
        <view name="employees_view" start-index="12" stop-index="25"/>
        <select>
            <projections start-index="37" stop-index="79">
                <column-projection name="employee_id" start-index="37" stop-index="47"/>
                <column-projection name="last_name" start-index="50" stop-index="58"/>
                <column-projection name="salary" start-index="61" stop-index="66"/>
                <column-projection name="location_id" start-index="69" stop-index="79"/>
            </projections>
            <from>
                <join-table join-type="INNER">
                    <left>
                        <simple-table name="employees" start-index="86" stop-index="94"/>
                    </left>
                    <right>
                        <simple-table name="departments" start-index="101" stop-index="111"/>
                    </right>
                    <using-columns name="department_id" start-index="120" stop-index="132"/>
                </join-table>
            </from>
            <where start-index="135" stop-index="158">
                <expr>
                    <binary-operation-expression start-index="141" stop-index="158">
                        <left>
                            <column name="department_id" start-index="141" stop-index="153"/>
                        </left>
                        <right>
                            <literal-expression value="10" start-index="157" stop-index="158"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_check_option_constraint"
                 view-definition="SELECT empno, ename, deptno FROM emp WHERE deptno = 10">
        <view name="sales_staff" start-index="12" stop-index="22"/>
        <select>
            <projections start-index="34" stop-index="53">
                <column-projection name="empno" start-index="34" stop-index="38"/>
                <column-projection name="ename" start-index="41" stop-index="45"/>
                <column-projection name="deptno" start-index="48" stop-index="53"/>
            </projections>
            <from>
                <simple-table name="emp" start-index="60" stop-index="62"/>
            </from>
            <where start-index="64" stop-index="80">
                <expr>
                    <binary-operation-expression start-index="70" stop-index="80">
                        <left>
                            <column name="deptno" start-index="70" stop-index="75"/>
                        </left>
                        <right>
                            <literal-expression value="10" start-index="79" stop-index="80"/>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_group_by_function"
                 view-definition="SELECT     prod_name product, country_name country, channel_id channel,     SUBSTR(calendar_quarter_desc, 6,2) quarter,     SUM(amount_sold) amount_sold, SUM(quantity_sold) quantity_sold      FROM sales, times, customers, countries, products     WHERE sales.time_id = times.time_id AND     sales.prod_id = products.prod_id AND     sales.cust_id = customers.cust_id AND     customers.country_id = countries.country_id     GROUP BY prod_name, country_name, channel_id,     SUBSTR(calendar_quarter_desc, 6, 2)">
        <view name="sales_view" start-index="12" stop-index="21"/>
        <select>
            <projections start-index="41" stop-index="215">
                <column-projection name="prod_name" alias="product" start-index="41" stop-index="57"/>
                <column-projection name="country_name" alias="country" start-index="60" stop-index="79"/>
                <column-projection name="channel_id" alias="channel" start-index="82" stop-index="99"/>
                <expression-projection alias="quarter" text="SUBSTR(calendar_quarter_desc, 6,2)" start-index="106"
                                       stop-index="147">
                    <expr>
                        <function function-name="SUBSTR" text="SUBSTR(calendar_quarter_desc, 6,2)" start-index="106"
                                  stop-index="139">
                            <parameter>
                                <column name="calendar_quarter_desc" start-index="113" stop-index="133"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="6" start-index="136" stop-index="136"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="2" start-index="138" stop-index="138"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
                <aggregation-projection alias="amount_sold" type="SUM" expression="SUM(amount_sold)" start-index="154"
                                        stop-index="169">
                    <parameter>
                        <column name="amount_sold" start-index="158" stop-index="169"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection alias="quantity_sold" type="SUM" expression="SUM(quantity_sold)"
                                        start-index="184" stop-index="201">
                    <parameter>
                        <column name="quantity_sold" start-index="188" stop-index="200"/>
                    </parameter>
                </aggregation-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <join-table join-type="COMMA">
                            <left>
                                <join-table join-type="COMMA">
                                    <left>
                                        <join-table join-type="COMMA">
                                            <left>
                                                <simple-table name="sales" start-index="227" stop-index="231"/>
                                            </left>
                                            <right>
                                                <simple-table name="times" start-index="234" stop-index="238"/>
                                            </right>
                                        </join-table>
                                    </left>
                                    <right>
                                        <simple-table name="customers" start-index="241" stop-index="249"/>
                                    </right>
                                </join-table>
                            </left>
                            <right>
                                <simple-table name="countries" start-index="252" stop-index="260"/>
                            </right>
                        </join-table>
                    </left>
                    <right>
                        <simple-table name="products" start-index="263" stop-index="270"/>
                    </right>
                </join-table>
            </from>
            <where start-index="276" stop-index="445">
                <expr>
                    <binary-operation-expression start-index="282" stop-index="445">
                        <left>
                            <binary-operation-expression start-index="282" stop-index="393">
                                <left>
                                    <binary-operation-expression start-index="282" stop-index="351">
                                        <left>
                                            <binary-operation-expression start-index="282" stop-index="310">
                                                <left>
                                                    <column name="time_id" start-index="282" stop-index="294">
                                                        <owner name="sales" start-index="282" stop-index="286"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="time_id" start-index="298" stop-index="310">
                                                        <owner name="times" start-index="298" stop-index="302"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </left>
                                        <right>
                                            <binary-operation-expression start-index="320" stop-index="351">
                                                <left>
                                                    <column name="prod_id" start-index="320" stop-index="332">
                                                        <owner name="sales" start-index="320" stop-index="324"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="prod_id" start-index="336" stop-index="351">
                                                        <owner name="products" start-index="336" stop-index="343"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </right>
                                        <operator>AND</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="361" stop-index="393">
                                        <left>
                                            <column name="cust_id" start-index="361" stop-index="373">
                                                <owner name="sales" start-index="361" stop-index="365"/>
                                            </column>
                                        </left>
                                        <right>
                                            <column name="cust_id" start-index="377" stop-index="393">
                                                <owner name="customers" start-index="377" stop-index="385"/>
                                            </column>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>AND</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="403" stop-index="445">
                                <left>
                                    <column name="country_id" start-index="403" stop-index="422">
                                        <owner name="customers" start-index="403" stop-index="411"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="country_id" start-index="426" stop-index="445">
                                        <owner name="countries" start-index="426" stop-index="434"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
            <group-by>
                <column-item name="prod_name" order-direction="ASC" start-index="460" stop-index="468"/>
                <column-item name="country_name" order-direction="ASC" start-index="471" stop-index="482"/>
                <column-item name="channel_id" order-direction="ASC" start-index="485" stop-index="494"/>
                <expression-item expression="SUBSTR(calendar_quarter_desc, 6, 2)" order-direction="ASC"
                                 start-index="501" stop-index="535">
                    <expr>
                        <function function-name="SUBSTR" text="SUBSTR(calendar_quarter_desc, 6, 2)" start-index="501"
                                  stop-index="535">
                            <parameter>
                                <column name="calendar_quarter_desc" start-index="508" stop-index="528"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="6" start-index="531" stop-index="531"/>
                            </parameter>
                            <parameter>
                                <literal-expression value="2" start-index="534" stop-index="534"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-item>
            </group-by>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_group_by"
                 view-definition="SELECT country_name country, prod_name product, calendar_year year,     calendar_month_name month, SUM(amount_sold) sale, COUNT(amount_sold) cnt     FROM sales, times, customers, countries, products     WHERE sales.time_id = times.time_id AND     sales.prod_id = products.prod_id AND     sales.cust_id = customers.cust_id AND     customers.country_id = countries.country_id     GROUP BY country_name, prod_name, calendar_year, calendar_month_name">
        <view name="sales_view2" start-index="12" stop-index="22"/>
        <select>
            <projections start-index="38" stop-index="174">
                <column-projection name="country_name" alias="country" start-index="38" stop-index="57"/>
                <column-projection name="prod_name" alias="product" start-index="60" stop-index="76"/>
                <column-projection name="calendar_year" alias="year" start-index="79" stop-index="96"/>
                <column-projection name="calendar_month_name" alias="month" start-index="103" stop-index="127"/>
                <aggregation-projection alias="sale" type="SUM" expression="SUM(amount_sold)" start-index="130"
                                        stop-index="145">
                    <parameter>
                        <column name="amount_sold" start-index="134" stop-index="144"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection alias="cnt" type="COUNT" expression="COUNT(amount_sold)" start-index="153"
                                        stop-index="170">
                    <parameter>
                        <column name="amount_sold" start-index="159" stop-index="169"/>
                    </parameter>
                </aggregation-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <join-table join-type="COMMA">
                            <left>
                                <join-table join-type="COMMA">
                                    <left>
                                        <join-table join-type="COMMA">
                                            <left>
                                                <simple-table name="sales" start-index="185" stop-index="189"/>
                                            </left>
                                            <right>
                                                <simple-table name="times" start-index="192" stop-index="196"/>
                                            </right>
                                        </join-table>
                                    </left>
                                    <right>
                                        <simple-table name="customers" start-index="199" stop-index="207"/>
                                    </right>
                                </join-table>
                            </left>
                            <right>
                                <simple-table name="countries" start-index="210" stop-index="218"/>
                            </right>
                        </join-table>
                    </left>
                    <right>
                        <simple-table name="products" start-index="221" stop-index="228"/>
                    </right>
                </join-table>
            </from>
            <where start-index="234" stop-index="403">
                <expr>
                    <binary-operation-expression start-index="240" stop-index="403">
                        <left>
                            <binary-operation-expression start-index="240" stop-index="351">
                                <left>
                                    <binary-operation-expression start-index="240" stop-index="309">
                                        <left>
                                            <binary-operation-expression start-index="240" stop-index="268">
                                                <left>
                                                    <column name="time_id" start-index="240" stop-index="252">
                                                        <owner name="sales" start-index="240" stop-index="244"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="time_id" start-index="256" stop-index="268">
                                                        <owner name="times" start-index="256" stop-index="260"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </left>
                                        <right>
                                            <binary-operation-expression start-index="278" stop-index="309">
                                                <left>
                                                    <column name="prod_id" start-index="278" stop-index="290">
                                                        <owner name="sales" start-index="278" stop-index="282"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="prod_id" start-index="294" stop-index="309">
                                                        <owner name="products" start-index="294" stop-index="301"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </right>
                                        <operator>AND</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="319" stop-index="351">
                                        <left>
                                            <column name="cust_id" start-index="319" stop-index="331">
                                                <owner name="sales" start-index="319" stop-index="323"/>
                                            </column>
                                        </left>
                                        <right>
                                            <column name="cust_id" start-index="335" stop-index="351">
                                                <owner name="customers" start-index="335" stop-index="343"/>
                                            </column>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>AND</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="361" stop-index="403">
                                <left>
                                    <column name="country_id" start-index="361" stop-index="380">
                                        <owner name="customers" start-index="361" stop-index="369"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="country_id" start-index="384" stop-index="403">
                                        <owner name="countries" start-index="384" stop-index="392"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
            <group-by>
                <column-item name="country_name" start-index="418" stop-index="429"/>
                <column-item name="prod_name" start-index="432" stop-index="440"/>
                <column-item name="calendar_year" start-index="443" stop-index="455"/>
                <column-item name="calendar_month_name" start-index="458" stop-index="476"/>
            </group-by>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_partition_by"
                 view-definition="SELECT country_name country, prod_name product, calendar_year year,     SUM(amount_sold) sales, COUNT(amount_sold) cnt,     MAX(calendar_year) KEEP (DENSE_RANK FIRST ORDER BY SUM(amount_sold) DESC)     OVER (PARTITION BY country_name, prod_name) best_year,     MAX(calendar_year) KEEP (DENSE_RANK LAST ORDER BY SUM(amount_sold) DESC)     OVER (PARTITION BY country_name, prod_name) worst_year     FROM sales, times, customers, countries, products     WHERE sales.time_id = times.time_id AND sales.prod_id = products.prod_id AND     sales.cust_id =customers.cust_id AND customers.country_id=countries.country_id     GROUP BY country_name, prod_name, calendar_year">
        <view name="sales_view" start-index="12" stop-index="21"/>
        <select>
            <projections start-index="37" stop-index="421">
                <column-projection name="country_name" alias="country" start-index="37" stop-index="56"/>
                <column-projection name="prod_name" alias="product" start-index="59" stop-index="75"/>
                <column-projection name="calendar_year" alias="year" start-index="78" stop-index="95"/>
                <aggregation-projection type="SUM" alias="sales" expression="SUM(amount_sold)" start-index="102"
                                        stop-index="117">
                    <parameter>
                        <column name="amount_sold" start-index="106" stop-index="116"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection type="COUNT" alias="cnt" expression="COUNT(amount_sold)" start-index="126"
                                        stop-index="143">
                    <parameter>
                        <column name="amount_sold" start-index="132" stop-index="142"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection type="MAX" alias="best_year"
                                        expression="MAX(calendar_year) KEEP (DENSE_RANK FIRST ORDER BY SUM(amount_sold) DESC)     OVER (PARTITION BY country_name, prod_name)"
                                        start-index="154" stop-index="274">
                    <parameter>
                        <column name="calendar_year" start-index="158" stop-index="170"/>
                    </parameter>
                </aggregation-projection>
                <aggregation-projection type="MAX" alias="worst_year"
                                        expression="MAX(calendar_year) KEEP (DENSE_RANK LAST ORDER BY SUM(amount_sold) DESC)     OVER (PARTITION BY country_name, prod_name)"
                                        start-index="291" stop-index="410">
                    <parameter>
                        <column name="calendar_year" start-index="295" stop-index="307"/>
                    </parameter>
                </aggregation-projection>
            </projections>
            <from>
                <join-table join-type="COMMA">
                    <left>
                        <join-table join-type="COMMA">
                            <left>
                                <join-table join-type="COMMA">
                                    <left>
                                        <join-table join-type="COMMA">
                                            <left>
                                                <simple-table name="sales" start-index="432" stop-index="436"/>
                                            </left>
                                            <right>
                                                <simple-table name="times" start-index="439" stop-index="443"/>
                                            </right>
                                        </join-table>
                                    </left>
                                    <right>
                                        <simple-table name="customers" start-index="446" stop-index="454"/>
                                    </right>
                                </join-table>
                            </left>
                            <right>
                                <simple-table name="countries" start-index="457" stop-index="465"/>
                            </right>
                        </join-table>
                    </left>
                    <right>
                        <simple-table name="products" start-index="468" stop-index="475"/>
                    </right>
                </join-table>
            </from>
            <where start-index="481" stop-index="639">
                <expr>
                    <binary-operation-expression start-index="487" stop-index="639">
                        <left>
                            <binary-operation-expression start-index="487" stop-index="593">
                                <left>
                                    <binary-operation-expression start-index="487" stop-index="552">
                                        <left>
                                            <binary-operation-expression start-index="487" stop-index="515">
                                                <left>
                                                    <column name="time_id" start-index="487" stop-index="499">
                                                        <owner name="sales" start-index="487" stop-index="491"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="time_id" start-index="503" stop-index="515">
                                                        <owner name="times" start-index="503" stop-index="507"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </left>
                                        <right>
                                            <binary-operation-expression start-index="521" stop-index="552">
                                                <left>
                                                    <column name="prod_id" start-index="521" stop-index="533">
                                                        <owner name="sales" start-index="521" stop-index="525"/>
                                                    </column>
                                                </left>
                                                <right>
                                                    <column name="prod_id" start-index="537" stop-index="552">
                                                        <owner name="products" start-index="537" stop-index="544"/>
                                                    </column>
                                                </right>
                                                <operator>=</operator>
                                            </binary-operation-expression>
                                        </right>
                                        <operator>AND</operator>
                                    </binary-operation-expression>
                                </left>
                                <right>
                                    <binary-operation-expression start-index="562" stop-index="593">
                                        <left>
                                            <column name="cust_id" start-index="562" stop-index="574">
                                                <owner name="sales" start-index="562" stop-index="566"/>
                                            </column>
                                        </left>
                                        <right>
                                            <column name="cust_id" start-index="577" stop-index="593">
                                                <owner name="customers" start-index="577" stop-index="585"/>
                                            </column>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </right>
                                <operator>AND</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="599" stop-index="639">
                                <left>
                                    <column name="country_id" start-index="599" stop-index="618">
                                        <owner name="customers" start-index="599" stop-index="607"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="country_id" start-index="620" stop-index="639">
                                        <owner name="countries" start-index="620" stop-index="628"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </expr>
            </where>
            <group-by>
                <column-item name="country_name" order-direction="ASC" start-index="654" stop-index="665"/>
                <column-item name="prod_name" order-direction="ASC" start-index="668" stop-index="676"/>
                <column-item name="calendar_year" order-direction="ASC" start-index="679" stop-index="691"/>
            </group-by>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_with_multiset"
                 view-definition="SELECT d.deptno, d.deptname, CAST(MULTISET(SELECT e.ename, e.salary, e.deptname, CAST(MULTISET(SELECT p.projname, p.mgr FROM projects p WHERE p.mgr = e.ename) AS nt_project_t) FROM emps e WHERE e.deptname = d.deptname) AS nt_emp_t) FROM depts d">
        <view name="v_depts" start-index="12" stop-index="18"/>
        <select>
            <projections start-index="77" stop-index="300">
                <column-projection name="deptno" start-index="77" stop-index="84">
                    <owner name="d" start-index="77" stop-index="77"/>
                </column-projection>
                <column-projection name="deptname" start-index="87" stop-index="96">
                    <owner name="d" start-index="87" stop-index="87"/>
                </column-projection>
                <expression-projection
                        text="CAST(MULTISET(SELECT e.ename, e.salary, e.deptname, CAST(MULTISET(SELECT p.projname, p.mgr FROM projects p WHERE p.mgr = e.ename) AS nt_project_t) FROM emps e WHERE e.deptname = d.deptname) AS nt_emp_t)"
                        start-index="99" stop-index="300">
                    <expr>
                        <function function-name="CAST"
                                  text="CAST(MULTISET(SELECT e.ename, e.salary, e.deptname, CAST(MULTISET(SELECT p.projname, p.mgr FROM projects p WHERE p.mgr = e.ename) AS nt_project_t) FROM emps e WHERE e.deptname = d.deptname) AS nt_emp_t)"
                                  start-index="99" stop-index="300">
                            <parameter>
                                <subquery start-index="112" stop-index="287">
                                    <select>
                                        <projections start-index="120" stop-index="244">
                                            <column-projection name="ename" start-index="120" stop-index="126">
                                                <owner name="e" start-index="120" stop-index="120"/>
                                            </column-projection>
                                            <column-projection name="salary" start-index="129" stop-index="136">
                                                <owner name="e" start-index="129" stop-index="129"/>
                                            </column-projection>
                                            <column-projection name="deptname" start-index="139" stop-index="148">
                                                <owner name="e" start-index="139" stop-index="139"/>
                                            </column-projection>
                                            <expression-projection
                                                    text="CAST(MULTISET(SELECT p.projname, p.mgr FROM projects p WHERE p.mgr = e.ename) AS nt_project_t)"
                                                    start-index="151" stop-index="244">
                                                <expr>
                                                    <function function-name="CAST"
                                                              text="CAST(MULTISET(SELECT p.projname, p.mgr FROM projects p WHERE p.mgr = e.ename) AS nt_project_t)"
                                                              start-index="151" stop-index="244">
                                                        <parameter>
                                                            <subquery start-index="164" stop-index="227">
                                                                <select>
                                                                    <projections start-index="172" stop-index="188">
                                                                        <column-projection name="projname"
                                                                                           start-index="172"
                                                                                           stop-index="181">
                                                                            <owner name="p" start-index="172"
                                                                                   stop-index="172"/>
                                                                        </column-projection>
                                                                        <column-projection name="mgr" start-index="184"
                                                                                           stop-index="188">
                                                                            <owner name="p" start-index="184"
                                                                                   stop-index="184"/>
                                                                        </column-projection>
                                                                    </projections>
                                                                    <from>
                                                                        <simple-table name="projects" alias="p"
                                                                                      start-index="195"
                                                                                      stop-index="204"/>
                                                                    </from>
                                                                    <where start-index="206" stop-index="226">
                                                                        <expr>
                                                                            <binary-operation-expression
                                                                                    start-index="212" stop-index="226">
                                                                                <left>
                                                                                    <column name="mgr" start-index="212"
                                                                                            stop-index="216">
                                                                                        <owner name="p"
                                                                                               start-index="212"
                                                                                               stop-index="212"/>
                                                                                    </column>
                                                                                </left>
                                                                                <operator>=</operator>
                                                                                <right>
                                                                                    <column name="ename"
                                                                                            start-index="220"
                                                                                            stop-index="226">
                                                                                        <owner name="e"
                                                                                               start-index="220"
                                                                                               stop-index="220"/>
                                                                                    </column>
                                                                                </right>
                                                                            </binary-operation-expression>
                                                                        </expr>
                                                                    </where>
                                                                </select>
                                                            </subquery>
                                                        </parameter>
                                                        <parameter>
                                                            <data-type value="nt_project_t" start-index="232"
                                                                       stop-index="243"/>
                                                        </parameter>
                                                    </function>
                                                </expr>
                                            </expression-projection>
                                        </projections>
                                        <from>
                                            <simple-table alias="e" name="emps" start-index="251" stop-index="256"/>
                                        </from>
                                        <where start-index="258" stop-index="286">
                                            <expr>
                                                <binary-operation-expression start-index="264" stop-index="286">
                                                    <left>
                                                        <column name="deptname" start-index="264" stop-index="273">
                                                            <owner name="e" start-index="264" stop-index="264"/>
                                                        </column>
                                                    </left>
                                                    <right>
                                                        <column name="deptname" start-index="277" stop-index="286">
                                                            <owner name="d" start-index="277" stop-index="277"/>
                                                        </column>
                                                    </right>
                                                    <operator>=</operator>
                                                </binary-operation-expression>
                                            </expr>
                                        </where>
                                    </select>
                                </subquery>
                            </parameter>
                            <parameter>
                                <data-type value="nt_emp_t" start-index="292" stop-index="299"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="depts" alias="d" start-index="307" stop-index="313"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_with_select_value"
                 view-definition="SELECT VALUE(p) AS warehouse_xml FROM xwarehouses p">
        <view name="warehouse_view" start-index="12" stop-index="25"/>
        <select>
            <projections start-index="37" stop-index="61">
                <expression-projection text="VALUE(p)" alias="warehouse_xml" start-index="37" stop-index="61">
                    <expr>
                        <function function-name="VALUE" text="VALUE(p)" start-index="37" stop-index="44">
                            <parameter>
                                <column name="p" start-index="43" stop-index="43"/>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="xwarehouses" alias="p" start-index="68" stop-index="80"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_to_number_to_char"
                 view-definition="SELECT time_id, TO_NUMBER(TO_CHAR(time_id, 'ddd')) AS day_in_year FROM times">
        <view name="time_view" start-index="12" stop-index="20"/>
        <select>
            <projections start-index="32" stop-index="89">
                <column-projection name="time_id" start-index="32" stop-index="38"/>
                <expression-projection text="TO_NUMBER(TO_CHAR(time_id, 'ddd'))" alias="day_in_year" start-index="41"
                                       stop-index="89">
                    <expr>
                        <function function-name="TO_NUMBER" text="TO_NUMBER(TO_CHAR(time_id, 'ddd'))" start-index="41"
                                  stop-index="74">
                            <parameter>
                                <function function-name="TO_CHAR" text="TO_CHAR(time_id, 'ddd')" start-index="51"
                                          stop-index="73">
                                    <parameter>
                                        <column name="time_id" start-index="59" stop-index="65"/>
                                    </parameter>
                                    <parameter>
                                        <literal-expression value="ddd" start-index="68" stop-index="72"/>
                                    </parameter>
                                </function>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="times" start-index="96" stop-index="100"/>
            </from>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_subquery_with_union_all"
                 view-definition="(SELECT c.rowid crid, c.cust_id, 2 umarker FROM customers c WHERE c.cust_last_name = 'Smith' UNION ALL SELECT c.rowid crid, c.cust_id, 3 umarker FROM customers c WHERE c.cust_last_name = 'Jones')">
        <view name="view_with_unionall" start-index="12" stop-index="29"/>
        <select>
            <projections start-index="42" stop-index="75">
                <column-projection name="rowid" alias="crid" start-index="42" stop-index="53">
                    <owner name="c" start-index="42" stop-index="42"/>
                </column-projection>
                <column-projection name="cust_id" start-index="56" stop-index="64">
                    <owner name="c" start-index="56" stop-index="56"/>
                </column-projection>
                <expression-projection text="2" alias="umarker" start-index="67" stop-index="75">
                    <expr>
                        <literal-expression value="2" start-index="67" stop-index="67"/>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table name="customers" alias="c" start-index="82" stop-index="92"/>
            </from>
            <combine combine-type="UNION_ALL" start-index="35" stop-index="227">
                <left>
                    <projections start-index="42" stop-index="75">
                        <column-projection name="rowid" alias="crid" start-index="42" stop-index="53">
                            <owner name="c" start-index="42" stop-index="42"/>
                        </column-projection>
                        <column-projection name="cust_id" start-index="56" stop-index="64">
                            <owner name="c" start-index="56" stop-index="56"/>
                        </column-projection>
                        <expression-projection text="2" alias="umarker" start-index="67" stop-index="75">
                            <expr>
                                <literal-expression value="2" start-index="67" stop-index="67"/>
                            </expr>
                        </expression-projection>
                    </projections>
                    <from>
                        <simple-table name="customers" alias="c" start-index="82" stop-index="92"/>
                    </from>
                    <where start-index="94" stop-index="125">
                        <expr>
                            <binary-operation-expression start-index="100" stop-index="125">
                                <left>
                                    <column name="cust_last_name" start-index="100" stop-index="115">
                                        <owner name="c" start-index="100" stop-index="100"/>
                                    </column>
                                </left>
                                <right>
                                    <literal-expression value="Smith" start-index="119" stop-index="125"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </expr>
                    </where>
                </left>
                <right>
                    <projections start-index="144" stop-index="177">
                        <column-projection name="rowid" alias="crid" start-index="144" stop-index="155">
                            <owner name="c" start-index="144" stop-index="144"/>
                        </column-projection>
                        <column-projection name="cust_id" start-index="158" stop-index="166">
                            <owner name="c" start-index="158" stop-index="158"/>
                        </column-projection>
                        <expression-projection text="3" alias="umarker" start-index="169" stop-index="177">
                            <expr>
                                <literal-expression value="3" start-index="169" stop-index="169"/>
                            </expr>
                        </expression-projection>
                    </projections>
                    <from>
                        <simple-table name="customers" alias="c" start-index="184" stop-index="194"/>
                    </from>
                    <where start-index="196" stop-index="227">
                        <expr>
                            <binary-operation-expression start-index="202" stop-index="227">
                                <left>
                                    <column name="cust_last_name" start-index="202" stop-index="217">
                                        <owner name="c" start-index="202" stop-index="202"/>
                                    </column>
                                </left>
                                <right>
                                    <literal-expression value="Jones" start-index="221" stop-index="227"/>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </expr>
                    </where>
                </right>
            </combine>
        </select>
    </create-view>

    <create-view sql-case-id="create_view_select_sys_XMLGen"
                 view-definition="SELECT sys_XMLGen(emp_t(e.employee_id, e.first_name, e.last_name, e.hire_date),XMLFormat('EMP')) FROM employees e WHERE salary > 15000">
        <view name="employee_view" start-index="23" stop-index="35"/>
        <select>
            <projections start-index="164" stop-index="252">
                <expression-projection
                        text="sys_XMLGen(emp_t(e.employee_id, e.first_name, e.last_name, e.hire_date),XMLFormat('EMP'))"
                        start-index="164" stop-index="252">
                    <expr>
                        <function function-name="sys_XMLGen"
                                  text="sys_XMLGen(emp_t(e.employee_id, e.first_name, e.last_name, e.hire_date),XMLFormat('EMP'))"
                                  start-index="164" stop-index="252">
                            <parameter>
                                <function function-name="emp_t"
                                          text="emp_t(e.employee_id, e.first_name, e.last_name, e.hire_date)"
                                          start-index="175" stop-index="234">
                                    <parameter>
                                        <column name="employee_id" start-index="181" stop-index="193">
                                            <owner name="e" start-index="181" stop-index="181"/>
                                        </column>
                                    </parameter>
                                    <parameter>
                                        <column name="first_name" start-index="196" stop-index="207">
                                            <owner name="e" start-index="196" stop-index="196"/>
                                        </column>
                                    </parameter>
                                    <parameter>
                                        <column name="last_name" start-index="210" stop-index="220">
                                            <owner name="e" start-index="210" stop-index="210"/>
                                        </column>
                                    </parameter>
                                    <parameter>
                                        <column name="hire_date" start-index="223" stop-index="233">
                                            <owner name="e" start-index="223" stop-index="223"/>
                                        </column>
                                    </parameter>
                                </function>
                            </parameter>
                            <parameter>
                                <function function-name="XMLFormat" text="XMLFormat('EMP')" start-index="236"
                                          stop-index="251">
                                    <parameter>
                                        <literal-expression value="EMP" start-index="246" stop-index="250"/>
                                    </parameter>
                                </function>
                            </parameter>
                        </function>
                    </expr>
                </expression-projection>
            </projections>
            <from>
                <simple-table alias="e" name="employees" start-index="259" stop-index="269"/>
            </from>
            <where start-index="271" stop-index="290">
                <expr>
                    <binary-operation-expression start-index="277" stop-index="290">
                        <left>
                            <column name="salary" start-index="277" stop-index="282"/>
                        </left>
                        <right>
                            <literal-expression value="15000" start-index="286" stop-index="290"/>
                        </right>
                        <operator>></operator>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </create-view>
</sql-parser-test-cases>
