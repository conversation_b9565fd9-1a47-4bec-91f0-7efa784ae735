<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-materialized-view sql-case-id="create_materialized_view_with_if_not_exists"/>
    <create-materialized-view sql-case-id="create_materialized_view"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_using"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_no_data"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_data"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_fast"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_fast_query_rewrite"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_fast_disable_query_rewrite"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_tablespace_parallel_build_immediate"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_pctfree_storage_parallel"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_fast_for_update"/>
    <create-materialized-view sql-case-id="create_materialized_view_for_update"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_force"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_fast_for_update_as_with_recursive"/>
    <create-materialized-view sql-case-id="create_materialized_view_scope_for"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_refresh_complete"/>
    <create-materialized-view sql-case-id="create_materialized_view_with_pctfree_storage_build_deferred"/>
    <create-materialized-view sql-case-id="create_materialized_view_refresh_fast_on_remand_as_select"/>
    <create-materialized-view sql-case-id="create_materialized_view_refresh_with_date"/>
</sql-parser-test-cases>
