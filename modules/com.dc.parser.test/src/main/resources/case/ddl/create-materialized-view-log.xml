<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_including_new"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_tablespace"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_table_schema"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_object_id"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_row_id"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_multi_row_id_"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_row_id_sequence_including_new"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_row_id_multi_sequence"/>
    <create-materialized-view-log sql-case-id="create_materialized_view_log_with_pctfree_storage_purge_repeat"/>
</sql-parser-test-cases>
