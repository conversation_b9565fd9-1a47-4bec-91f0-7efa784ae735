<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <drop-table sql-case-id="drop_table">
        <table name="t_log" start-index="11" stop-index="15"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_if_exists">
        <table name="t_log" start-index="21" stop-index="25"/>
    </drop-table>

    <drop-table sql-case-id="drop_temporary_table_if_exists">
        <table name="t_temp_log" start-index="31" stop-index="40"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_restrict">
        <table name="t_log" start-index="11" stop-index="15"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_cascade">
        <table name="t_log" start-index="11" stop-index="15"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_cascade_constraints_and_purge">
        <table name="t_log" start-index="11" stop-index="15"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_purge_with_schema">
        <table name="t_log" start-index="11" stop-index="27">
            <owner name="sharding_db" start-index="11" stop-index="21"/>
        </table>
    </drop-table>

    <drop-table sql-case-id="drop_table_with_space">
        <table name="t_order" start-index="23" stop-index="29"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_with_back_quota">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="11" stop-index="19"/>
    </drop-table>

    <drop-table sql-case-id="drop_tables">
        <table name="t_order_item" start-index="11" stop-index="22"/>
        <table name="t_order" start-index="25" stop-index="31"/>
    </drop-table>

    <drop-table sql-case-id="drop_temporary_table">
        <table name="t_order" start-index="21" stop-index="27"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_with_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="11" stop-index="19"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_with_double_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="11" stop-index="19"/>
    </drop-table>

    <drop-table sql-case-id="drop_table_with_bracket">
        <table name="t_order" start-delimiter="[" end-delimiter="]" start-index="11" stop-index="19"/>
    </drop-table>

    <drop-table sql-case-id="drop_bit_xor_table">
        <table name="BIT_XOR" start-index="11" stop-index="17"/>
    </drop-table>
</sql-parser-test-cases>
