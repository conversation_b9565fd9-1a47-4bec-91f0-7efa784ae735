<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-diskgroup sql-case-id="alter_diskgroup_add_disk"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_drop_disk"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_undrop_disk"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_resize"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_rebalance"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_verify"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_add_template"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_change_template"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_drop_template"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_create_directory"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_create_alias"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_scrub"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_dismount"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_mount"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_offline_disk_drop_after_time_unit_m"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_attribute_time_h"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_attribute_time_m"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_attribute_1"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_attribute_2"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_attribute_3"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_permission_owner_group_other_for_file"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_set_ownership_owner_group_for_file"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_add_template_attribute_hot_hirrorhot"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_modify_drop_member"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_resize_disks_in_failgroup_size_g"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_add_template_attribute_unprotected_coarse"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_modify_template_attribute_fine"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_modify_file_attribute_hot_mirrorhot"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_modify_usergroup_add_member"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_add_volume_size_g"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_resize_volume_size_g"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_disable_volume"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_enable_volume"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_all_disable_volume_all"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_drop_volume"/>
    <alter-diskgroup sql-case-id="alter_diskgroup_data_add_user"/>
</sql-parser-test-cases>
