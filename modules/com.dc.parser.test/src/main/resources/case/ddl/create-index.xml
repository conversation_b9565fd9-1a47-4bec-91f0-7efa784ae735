<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-index sql-case-id="create_index_with_lock_algorithm">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <index name="idx_name" start-index="13" stop-index="20"/>
        <column start-index="34" stop-index="37" name="name"/>
        <lock-option type="SHARED" start-index="40" stop-index="50"/>
        <algorithm-option type="COPY" start-index="52" stop-index="65"/>
    </create-index>
    <create-index sql-case-id="create_index_with_lock">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <index name="idx_name" start-index="13" stop-index="20"/>
        <column start-index="34" stop-index="37" name="name"/>
        <lock-option type="SHARED" start-index="40" stop-index="50"/>
    </create-index>

    <create-index sql-case-id="create_index_with_algorithm">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <index name="idx_name" start-index="13" stop-index="20"/>
        <column start-index="34" stop-index="37" name="name"/>
        <algorithm-option type="COPY" start-index="40" stop-index="53"/>
    </create-index>

    <create-index sql-case-id="create_index_with_algorithm_lock">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <index name="idx_name" start-index="13" stop-index="20"/>
        <column start-index="34" stop-index="37" name="name"/>
        <algorithm-option type="COPY" start-index="40" stop-index="53"/>
        <lock-option type="SHARED" start-index="60" stop-index="65"/>
    </create-index>

    <create-index sql-case-id="create_index">
        <table>
            <simple-table name="t_log" start-index="28" stop-index="32"/>
        </table>
        <index name="t_log_index" start-index="13" stop-index="23"/>
        <column start-index="35" stop-index="36" name="id"/>
    </create-index>

    <create-index sql-case-id="create_unique_index">
        <table>
            <simple-table name="t_log" start-index="35" stop-index="39"/>
        </table>
        <index name="t_log_index" start-index="20" stop-index="30"/>
        <column start-index="42" stop-index="43" name="id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_space">
        <table>
            <simple-table name="t_order" start-index="52" stop-index="58"/>
        </table>
        <column start-index="61" stop-index="68" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_back_quota">
        <table>
            <simple-table name="t_order" start-delimiter="`" end-delimiter="`" start-index="30" stop-index="38"/>
        </table>
        <column start-index="41" stop-index="50" name="order_id" start-delimiter="`" end-delimiter="`"/>
    </create-index>

    <create-index sql-case-id="create_composite_index">
        <table>
            <simple-table name="t_order" start-index="28" stop-index="34"/>
        </table>
        <index name="order_index" start-index="13" stop-index="23"/>
        <column name="order_id" start-index="37" stop-index="44"/>
        <column name="user_id" start-index="47" stop-index="53"/>
        <column name="status" start-index="56" stop-index="61"/>
    </create-index>

    <create-index sql-case-id="create_btree_index">
        <table>
            <simple-table name="t_order" start-index="28" stop-index="34"/>
        </table>
        <column start-index="49" stop-index="56" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_quota">
        <table>
            <simple-table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="30"
                          stop-index="38"/>
        </table>
        <index name="order_index" start-delimiter="&quot;" end-delimiter="&quot;" start-index="13" stop-index="25"/>
        <column start-index="41" stop-index="50" name="order_id" start-delimiter="&quot;" end-delimiter="&quot;"/>
    </create-index>

    <create-index sql-case-id="create_index_with_function">
        <table>
            <simple-table name="t_order" start-index="33" stop-index="39"/>
        </table>
        <index name="idx_upper_status" start-index="13" stop-index="28"/>
        <column name="status" start-index="48" stop-index="53" literal-start-index="48" literal-stop-index="53"/>
    </create-index>

    <create-index sql-case-id="create_index_with_double_quota">
        <table>
            <simple-table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="30"
                          stop-index="38"/>
        </table>
        <column start-index="41" stop-index="50" name="order_id" start-delimiter="&quot;" end-delimiter="&quot;"/>
    </create-index>

    <create-index sql-case-id="create_index_with_double_quota_and_uescape">
        <!-- FIXME table index error -->
        <table>
            <simple-table name="u&amp;t_order" start-index="58" stop-index="68"/>
        </table>
        <column start-index="71" stop-index="82" name="u&amp;order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_concurrently">
        <table>
            <simple-table name="t_order" start-index="41" stop-index="47"/>
        </table>
        <column start-index="50" stop-index="57" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_if_not_exists">
        <table>
            <simple-table name="t_order" start-index="42" stop-index="48"/>
        </table>
        <column start-index="51" stop-index="58" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_using_btree">
        <table>
            <simple-table name="t_order" start-index="28" stop-index="34"/>
        </table>
        <column start-index="49" stop-index="56" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_unique">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <index name="order_index" start-index="20" stop-index="30"/>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_usable">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <index name="order_index" start-index="20" stop-index="30"/>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_unusable">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <index name="order_index" start-index="20" stop-index="30"/>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_immediate_invalidation">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <index name="order_index" start-index="20" stop-index="30"/>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_cluster_index">
        <index name="order_index" start-index="20" stop-index="30"/>
    </create-index>

    <create-index sql-case-id="create_index_with_bitmap">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <index name="order_index" start-index="20" stop-index="30"/>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_bracket">
        <table>
            <simple-table name="t_order" start-delimiter="[" end-delimiter="]" start-index="30" stop-index="38"/>
        </table>
        <column start-index="41" stop-index="50" name="order_id" start-delimiter="[" end-delimiter="]"/>
    </create-index>

    <create-index sql-case-id="create_index_with_no_default_fill_factor">
        <table>
            <simple-table name="t_order" start-index="35" stop-index="41"/>
        </table>
        <column start-index="44" stop-index="51" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_with_tablespace">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <column start-index="34" stop-index="37" name="code"/>
    </create-index>

    <create-index sql-case-id="create_index_using_gist">
        <table>
            <simple-table name="t_order" start-index="25" stop-index="31"/>
        </table>
        <column name="location" start-index="51" stop-index="58" literal-start-index="51" literal-stop-index="58"/>
        <column name="location" start-index="60" stop-index="67" literal-start-index="60" literal-stop-index="67"/>
    </create-index>

    <create-index sql-case-id="create_index_with_concurrently">
        <table>
            <simple-table name="t_order" start-index="50" stop-index="56"/>
        </table>
        <column start-index="58" stop-index="65" name="quantity"/>
    </create-index>

    <create-index sql-case-id="create_gin_index">
        <table>
            <simple-table name="t_order" start-index="24" stop-index="30"/>
        </table>
        <column start-index="43" stop-index="51" name="locations"/>
    </create-index>

    <create-index sql-case-id="create_index_using_ignore_dup_key">
        <table>
            <simple-table name="t_order" start-index="32" stop-index="38"/>
        </table>
        <column start-index="41" stop-index="48" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_using_drop_existing">
        <table>
            <simple-table name="t_order" start-index="52" stop-index="58"/>
        </table>
        <column start-index="60" stop-index="67" name="order_id"/>
    </create-index>

    <create-index sql-case-id="create_index_on_local_parallel_nologging">
        <index name="from_number_ix" start-index="13" stop-index="26"/>
        <table>
            <simple-table name="call_detail_records" start-index="31" stop-index="49"/>
        </table>
        <column start-index="51" stop-index="61" name="from_number"/>
    </create-index>

    <create-index sql-case-id="create_bitmap_index_on_local_parallel_nologging">
        <index name="is_active_bix" start-index="20" stop-index="32"/>
        <table>
            <simple-table name="credit_card_accounts" start-index="37" stop-index="56"/>
        </table>
        <column start-index="58" stop-index="66" name="is_active"/>
    </create-index>

    <create-index sql-case-id="create_index_with_engine_attribute">
        <index name="i1" start-index="13" stop-index="14"/>
        <table>
            <simple-table name="t1" start-index="19" stop-index="20"/>
        </table>
        <column name="c1" start-index="23" stop-index="24"/>
    </create-index>
</sql-parser-test-cases>
