<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-function sql-case-id="create_function"/>
    <create-function sql-case-id="create_function_with_argname"/>
    <create-function sql-case-id="create_function_with_multiple_output_parameters"/>
    <create-function sql-case-id="create_function_return_table"/>
    <create-function sql-case-id="create_function_with_execute"/>
    <create-function sql-case-id="create_function_inline_table"/>
    <create-function sql-case-id="create_function_declare_without_at"/>
    <create-function sql-case-id="create_function_call_spec_java"/>
    <create-function sql-case-id="create_function_call_spec_c"/>
    <create-function sql-case-id="create_function_with_set_var"/>
    <create-function sql-case-id="create_function_with_create_view"/>
    <create-function sql-case-id="create_function_with_loop"/>
    <create-function sql-case-id="create_function_with_aggregate_using_function"/>
    <create-function sql-case-id="create_function_with_simple_arithmetic"/>
</sql-parser-test-cases>
