<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-trigger sql-case-id="create_trigger"/>
    <create-trigger sql-case-id="create_trigger_with_database_scoped"/>
    <!--    <create-trigger sql-case-id="create_trigger_of_balance" />-->
    <!--    <create-trigger sql-case-id="create_trigger_with_when" />-->
    <!--    <create-trigger sql-case-id="create_trigger_after_update" />-->
    <create-trigger sql-case-id="create_trigger_with_create_view"/>
    <create-trigger sql-case-id="create_trigger_with_dml_event_clause"/>
    <create-trigger sql-case-id="create_trigger_with_body"/>
    <create-trigger sql-case-id="create_trigger_with_execute_immediate_statement"/>
    <create-trigger sql-case-id="create_trigger_with_assignment_statement"/>
    <create-trigger sql-case-id="create_trigger_with_logon"/>
    <create-trigger sql-case-id="create_trigger_with_cascade_1"/>
    <create-trigger sql-case-id="create_trigger_with_cascade_2"/>
    <create-trigger sql-case-id="create_trigger_with_cascade_3"/>
    <create-trigger sql-case-id="create_trigger_with_cascade_4"/>
    <create-trigger sql-case-id="create_trigger_with_dataManipulationLanguage_statement"/>
    <create-trigger sql-case-id="create_trigger_with_exceptionInit_pragma"/>
    <create-trigger sql-case-id="create_trigger_with_dml_event"/>
</sql-parser-test-cases>
