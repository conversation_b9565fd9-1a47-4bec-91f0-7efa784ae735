<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_rename"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_handler"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_multi_handler"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_no_validator"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_add"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_add_drop"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_drop_set_add"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_drop"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_set"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_option"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_option_defined"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_options_option_nonexistent"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_owner"/>
    <alter-foreign-data-wrapper sql-case-id="alter_foreign_data_wrapper_with_validator"/>
</sql-parser-test-cases>
