<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <drop-index sql-case-id="drop_index_with_algorithm">
        <table name="t_order" start-index="23" stop-index="29"/>
        <index name="idx_name" start-index="11" stop-index="18"/>
        <algorithm-option type="INPLACE" start-index="31" stop-index="47"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_lock">
        <table name="t_order" start-index="23" stop-index="29"/>
        <index name="idx_name" start-index="11" stop-index="18"/>
        <lock-option type="EXCLUSIVE" start-index="31" stop-index="44"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_algorithm_lock">
        <table name="t_order" start-index="23" stop-index="29"/>
        <index name="idx_name" start-index="11" stop-index="18"/>
        <algorithm-option type="INPLACE" start-index="31" stop-index="47"/>
        <lock-option type="EXCLUSIVE" start-index="49" stop-index="62"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_lock_algorithm">
        <table name="t_order" start-index="23" stop-index="29"/>
        <index name="idx_name" start-index="11" stop-index="18"/>
        <lock-option type="EXCLUSIVE" start-index="31" stop-index="44"/>
        <algorithm-option type="INPLACE" start-index="46" stop-index="62"/>
    </drop-index>

    <drop-index sql-case-id="drop_index">
        <table name="t_log" start-index="26" stop-index="30"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_without_on">
        <index name="order_index" start-index="11" stop-index="21"/>
    </drop-index>
    <drop-index sql-case-id="drop_index_if_exists"/>

    <drop-index sql-case-id="drop_index_with_space">
        <table name="t_order" start-index="50" stop-index="56"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_only_with_name">
        <index name="order_index" start-index="11" stop-index="21"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_back_quota">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="28" stop-index="36"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_quota">
        <index name="order_index" start-delimiter="&quot;" end-delimiter="&quot;" start-index="11" stop-index="23"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_double_quota"/>
    <drop-index sql-case-id="drop_index_concurrently"/>
    <drop-index sql-case-id="drop_index_with_schema"/>

    <drop-index sql-case-id="drop_index_with_bracket">
        <table name="t_order" start-delimiter="[" end-delimiter="]" start-index="28" stop-index="36"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_if_exists_on_table">
        <table name="t_order" start-index="36" stop-index="42"/>
    </drop-index>

    <drop-index sql-case-id="drop_index_with_online_force_invalidation">
        <index name="order_index" start-index="11" stop-index="21"/>
    </drop-index>

</sql-parser-test-cases>
