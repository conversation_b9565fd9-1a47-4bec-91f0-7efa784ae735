<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <rename-table sql-case-id="rename_single_table">
        <rename start-index="13" stop-index="31">
            <table name="t_order" start-index="13" stop-index="19"/>
            <rename-table name="t_order1" start-index="24" stop-index="31"/>
        </rename>
    </rename-table>
    <rename-table sql-case-id="rename_tables_rule">
        <rename start-index="14" stop-index="32">
            <table name="t_order" start-index="14" stop-index="20"/>
            <rename-table name="t_order1" start-index="25" stop-index="32"/>
        </rename>
    </rename-table>
    <rename-table sql-case-id="rename_multiple_tables">
        <rename start-index="13" stop-index="31">
            <table name="t_order" start-index="13" stop-index="19"/>
            <rename-table name="t_order1" start-index="24" stop-index="31"/>
        </rename>
        <rename start-index="34" stop-index="62">
            <table name="t_order_item" start-index="34" stop-index="45"/>
            <rename-table name="t_order_item1" start-index="50" stop-index="62"/>
        </rename>
    </rename-table>
    <rename-table sql-case-id="rename_by_mysql_source">
        <rename start-index="13" stop-index="56">
            <table name="columns_priv" start-index="13" stop-index="30">
                <owner name="mysql" start-index="13" stop-index="17"/>
            </table>
            <rename-table name="columns_priv_bak" start-index="35" stop-index="56">
                <owner name="mysql" start-index="35" stop-index="39"/>
            </rename-table>
        </rename>
    </rename-table>
</sql-parser-test-cases>
