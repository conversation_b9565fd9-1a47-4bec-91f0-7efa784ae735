<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <associate-statistics sql-case-id="associate_statistics_with_column">
        <table name="employee" start-index="34" stop-index="41"/>
        <column name="age" start-index="43" stop-index="45"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_columns">
        <table name="employee" start-index="34" stop-index="41"/>
        <column name="age" start-index="43" stop-index="45"/>
        <table name="employee" start-index="48" stop-index="55"/>
        <column name="salary" start-index="57" stop-index="62"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_index">
        <index name="salary_index" start-index="34" stop-index="45"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_function">
        <function function-name="myFunction" start-index="36" stop-index="45" text="myFunction"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_package">
        <package name="emp_mgmt" start-index="35" stop-index="42"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_type">
        <type name="Example_typ" start-index="32" stop-index="42"/>
    </associate-statistics>

    <associate-statistics sql-case-id="associate_statistics_with_index_type">
        <index-type name="indtype" start-index="37" stop-index="43"/>
    </associate-statistics>
</sql-parser-test-cases>
