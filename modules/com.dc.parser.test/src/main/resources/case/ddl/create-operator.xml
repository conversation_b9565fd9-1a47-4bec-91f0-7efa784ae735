<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-operator sql-case-id="create_operator"/>
    <create-operator sql-case-id="create_operator_with_procedure"/>
    <create-operator sql-case-id="create_operator_with_function"/>
    <create-operator sql-case-id="create_operator_with_schema_qualified_name"/>
    <create-operator sql-case-id="create_operator_with_binding"/>
    <create-operator sql-case-id="create_operator_with_binding_custom_datatype"/>
    <create-operator sql-case-id="create_operator_with_compute_ancillary_data"/>
</sql-parser-test-cases>
