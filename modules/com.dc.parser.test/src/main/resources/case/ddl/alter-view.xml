<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-view sql-case-id="alter_view">
        <view name="customer_ro" start-index="11" stop-index="21"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_set_default_column">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_drop_default">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_owner">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_rename">
        <view name="order_view" start-index="11" stop-index="20"/>
        <rename-view name="new_order_view" start-index="32" stop-index="45"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_rename_column">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_set_schema">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_set_view_option">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_reset">
        <view name="order_view" start-index="11" stop-index="20"/>
    </alter-view>

    <alter-view sql-case-id="alter_view_definition" view-definition="SELECT * FROM t_order">
        <view name="order_view" start-index="11" stop-index="20"/>
        <select>
            <projections start-index="32" stop-index="32">
                <shorthand-projection start-index="32" stop-index="32"/>
            </projections>
            <from>
                <simple-table name="t_order" start-index="39" stop-index="45"/>
            </from>
        </select>
    </alter-view>

    <alter-view sql-case-id="alter_view_add_constraint_primary_key1">
        <view name="time_view" start-index="11" stop-index="19"/>
        <constraint-definition constraint-name="time_view_pk" start-index="26" stop-index="89">
            <primary-key-column name="time_id" start-index="63" stop-index="69"/>
        </constraint-definition>
    </alter-view>

    <alter-view sql-case-id="alter_view_add_constraint_primary_key2">
        <view name="time_view" start-index="11" stop-index="19"/>
        <constraint-definition constraint-name="time_view_pk" start-index="26" stop-index="95">
            <primary-key-column name="time_id" start-index="63" stop-index="69"/>
            <primary-key-column name="name" start-index="72" stop-index="75"/>
        </constraint-definition>
    </alter-view>

    <alter-view sql-case-id="alter_view_modify_constraint_rely">
        <view name="time_view" start-index="11" stop-index="19"/>
        <constraint-definition constraint-name="time_view_pk" start-index="0" stop-index="55"/>
    </alter-view>
</sql-parser-test-cases>
