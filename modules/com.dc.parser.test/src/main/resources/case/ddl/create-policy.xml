<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-policy sql-case-id="create_policy_using_expr"/>
    <create-policy sql-case-id="create_policy_using_comparison_expr"/>
    <create-policy sql-case-id="create_policy_using_select_expr"/>
    <create-policy sql-case-id="create_policy"/>
    <create-policy sql-case-id="create_policy_for_all_using_expr"/>
    <create-policy sql-case-id="create_policy_for_select_using_expr"/>
    <create-policy sql-case-id="create_policy_as_restrictive_using_expr"/>
    <create-policy sql-case-id="create_policy_for_delete_using_expr"/>
    <create-policy sql-case-id="create_policy_for_insert_with_check_expr"/>
    <create-policy sql-case-id="create_policy_as_permissive_using_expr"/>
</sql-parser-test-cases>
