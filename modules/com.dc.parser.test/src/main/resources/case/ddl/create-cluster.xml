<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-cluster sql-case-id="create_cluster_default"/>
    <create-cluster sql-case-id="create_cluster_schema"/>
    <create-cluster sql-case-id="create_cluster_schema_multi_col"/>
    <create-cluster sql-case-id="create_cluster_schema_multi_col_sort"/>
    <create-cluster sql-case-id="create_cluster_parallel"/>
    <create-cluster sql-case-id="create_cluster_parallel_number"/>
    <create-cluster sql-case-id="create_cluster_noparallel"/>
    <create-cluster sql-case-id="create_cluster_no_row_depend"/>
    <create-cluster sql-case-id="create_cluster_row_depend"/>
    <create-cluster sql-case-id="create_cluster_no_cache"/>
    <create-cluster sql-case-id="create_cluster_cache"/>
    <create-cluster sql-case-id="create_cluster_pctfree"/>
    <create-cluster sql-case-id="create_cluster_pctused"/>
    <create-cluster sql-case-id="create_cluster_initrans"/>
    <create-cluster sql-case-id="create_cluster_set_size"/>
    <create-cluster sql-case-id="create_cluster_storage_init"/>
    <create-cluster sql-case-id="create_cluster_storage_next"/>
    <create-cluster sql-case-id="create_cluster_storage_minextents"/>
    <create-cluster sql-case-id="create_cluster_storage_maxextents"/>
    <create-cluster sql-case-id="create_cluster_storage_pctincrease"/>
    <create-cluster sql-case-id="create_cluster_storage_freelists"/>
    <create-cluster sql-case-id="create_cluster_storage_freelist_groups"/>
    <create-cluster sql-case-id="create_cluster_storage_optimal"/>
    <create-cluster sql-case-id="create_cluster_storage_buffer_pool"/>
    <create-cluster sql-case-id="create_cluster_storage_flash_cache"/>
    <create-cluster sql-case-id="create_cluster_storage_call_flash_cache"/>
    <create-cluster sql-case-id="create_cluster_storage_encrypt"/>
    <create-cluster sql-case-id="create_cluster_tablespace"/>
    <create-cluster sql-case-id="create_cluster_index"/>
    <create-cluster sql-case-id="create_cluster_hashkeys"/>
    <create-cluster sql-case-id="create_cluster_single_table"/>
    <create-cluster sql-case-id="create_cluster_single_table_hash"/>
    <create-cluster sql-case-id="create_cluster_size_initial_next"/>
    <create-cluster sql-case-id="create_cluster_hash_is_mod"/>
    <create-cluster sql-case-id="create_cluster_number_size"/>
    <create-cluster sql-case-id="create_cluster_number_size_hashkeys"/>
</sql-parser-test-cases>
