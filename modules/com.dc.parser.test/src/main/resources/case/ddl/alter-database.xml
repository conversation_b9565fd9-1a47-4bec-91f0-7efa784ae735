<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-database sql-case-id="alter_database_enable_block_change_tracking"/>
    <alter-database sql-case-id="alter_database_add_logffile_group_size_m_blocksize_reuse"/>
    <alter-database sql-case-id="alter_database_add_logfile_group_size_k_reuse"/>
    <alter-database sql-case-id="alter_database_start_logical_standby_apply"/>
    <alter-database sql-case-id="alter_database_start_logical_standby_apply_nodelay"/>
    <alter-database sql-case-id="alter_database_start_logical_standby_apply_immediate_new_primary"/>
    <alter-database sql-case-id="alter_database_set_standby_to_maximize_performance"/>
    <alter-database sql-case-id="alter_database_register_physical_logfile"/>
    <alter-database sql-case-id="alter_database_register_logfile"/>
    <alter-database sql-case-id="alter_database_backup_controlfile_to_reuse"/>
    <alter-database sql-case-id="alter_database_stop_logical_standby_apply"/>
    <alter-database sql-case-id="alter_database_start_logical_standby_apply_immediate"/>
    <alter-database sql-case-id="alter_database_rename_file"/>
    <alter-database sql-case-id="alter_database_recover_to_logical_standby"/>
    <alter-database sql-case-id="alter_database_recover_managed_standby"/>
    <alter-database sql-case-id="alter_database_recover_managed_standby_database_finish"/>
    <alter-database sql-case-id="alter_database_enable_block_change_tracking_using_file_reuse"/>
    <alter-database sql-case-id="alter_database_recover_managed_standby_database_nodelay"/>
    <alter-database sql-case-id="alter_database_recover_managed_standby_database_disconnect"/>
    <alter-database sql-case-id="alter_database_recover_to_logical_standby_keep_identity"/>
    <alter-database sql-case-id="alter_database_backup_controlfile_to_trace"/>
    <alter-database sql-case-id="alter_database_flashback_off"/>
    <alter-database sql-case-id="alter_database_flashback_on"/>
    <alter-database sql-case-id="alter_database_disable_block_change_tracking"/>
    <alter-database sql-case-id="alter_database_recover_managed_standby_database_cancel"/>
    <alter-database sql-case-id="alter_database_prepare_to_switchover_to_primary"/>
    <alter-database
            sql-case-id="alter_database_recover_managed_standby_database_using_current_logfile_disconnect_from_session"/>
    <alter-database sql-case-id="alter_database_prepare_to_switchover_to_logical_standby"/>
    <alter-database sql-case-id="alter_database_prepare_to_switchover_cancel"/>
    <alter-database sql-case-id="alter_database_drop_logfile_group"/>
    <alter-database sql-case-id="alter_database_open"/>
    <alter-database sql-case-id="alter_database_create_standby_controlfile"/>
    <alter-database sql-case-id="alter_database_convert_to_physical_standby"/>
    <alter-database sql-case-id="alter_database_convert_to_snapshot_standby"/>
    <alter-database sql-case-id="alter_database_commit_to_switchover_to_primary"/>
    <alter-database sql-case-id="alter_database_commit_to_switchover_to_logical_standby"/>
    <alter-database sql-case-id="alter_database_commit_to_switchover_to_primary_with_session_shutdown"/>
    <alter-database sql-case-id="alter_database_clear_unarchived_logfile_group_unrecoverable_datafile"/>
    <alter-database sql-case-id="alter_database_archivelog"/>
    <alter-database sql-case-id="alter_database_clear_logfile_group"/>
    <alter-database sql-case-id="alter_database_clear_unarchived_logfile_group"/>
    <alter-database sql-case-id="alter_database_activate_logical_standby_database_finish_apply"/>
    <alter-database sql-case-id="alter_database_add_supplemental_log_data_primary_key_unique_index_columns"/>
    <alter-database sql-case-id="alter_database_clear_logfile"/>
    <alter-database sql-case-id="alter_database_activate_physical_standby_database"/>
    <alter-database sql-case-id="alter_database_add_standby_logfile_thread_size_m"/>
    <alter-database sql-case-id="alter_database_add_standby_logfile_size_m"/>
    <alter-database sql-case-id="alter_database_add_logfile_member_to_rdo_files_2"/>
    <alter-database sql-case-id="alter_database_add_logfile_member_to_group_2"/>
    <alter-database sql-case-id="alter_database_add_logfile_member_to_group_3"/>
    <alter-database sql-case-id="alter_database_add_logfile_group_size_m_block_size_reuse"/>
    <alter-database sql-case-id="alter_database_add_logfile_group_size_m_block_size"/>
    <alter-database sql-case-id="alter_database_add_logfile_thread_group"/>
    <alter-database sql-case-id="alter_database_add_logfile_with_size_m"/>
    <alter-database sql-case-id="alter_database_recover_automatic_until_time"/>
    <alter-database sql-case-id="alter_database_add_logfile_member_reuse_to_group"/>
    <alter-database sql-case-id="alter_database_add_logfile_group"/>
    <alter-database sql-case-id="alter_database_drop_logfile_member"/>
    <alter-database sql-case-id="alter_database_mount"/>
    <alter-database sql-case-id="alter_database_open_readonly"/>
    <alter-database sql-case-id="alter_database_recovery1"/>
    <alter-database sql-case-id="alter_database_recovery2"/>
    <alter-database sql-case-id="alter_database_rename"/>
    <alter-database sql-case-id="alter_database_create1"/>
    <alter-database sql-case-id="alter_database_create2"/>
    <alter-database sql-case-id="alter_database_create3"/>
    <alter-database sql-case-id="alter_database_modify_name"/>
    <alter-database sql-case-id="alter_database_set_options"/>
    <alter-database sql-case-id="alter_database_enable_block_change_tracking_using_file"/>
    <alter-database sql-case-id="alter_database_backup_controlfile_to"/>
</sql-parser-test-cases>
