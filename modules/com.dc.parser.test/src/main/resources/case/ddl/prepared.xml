<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <prepared sql-case-id="prepare_with_drop"/>
    <prepared sql-case-id="prepare_with_statement"/>
    <prepared sql-case-id="prepare_with_var"/>
    <prepared sql-case-id="execute_with_statement"/>
    <prepared sql-case-id="execute_with_statement_and_using"/>
    <prepared sql-case-id="deallocate_statement"/>
    <prepared sql-case-id="drop_statement"/>
    <prepared sql-case-id="execute_stmt_with_using"/>
</sql-parser-test-cases>
