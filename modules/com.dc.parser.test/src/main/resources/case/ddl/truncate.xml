<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <truncate sql-case-id="truncate_table">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>

    <truncate sql-case-id="truncate_table_only">
        <table name="t_log" start-index="20" stop-index="24"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_space">
        <table name="t_order" start-index="27" stop-index="33"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_back_quota">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="15" stop-index="23"/>
    </truncate>

    <truncate sql-case-id="truncate_table_simple">
        <table name="t_order" start-index="9" stop-index="15"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="15" stop-index="23"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_double_quota">
        <table name="t_order" start-delimiter="&quot;" end-delimiter="&quot;" start-index="15" stop-index="23"/>
    </truncate>

    <truncate sql-case-id="truncate_tables">
        <table name="t_order_item" start-index="15" stop-index="26"/>
        <table name="t_order" start-index="29" stop-index="35"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_asterisk">
        <table name="t_order" start-index="15" stop-index="21"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_bracket">
        <table name="t_order" start-delimiter="[" end-delimiter="]" start-index="15" stop-index="23"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_preserve_view_log">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_purge_view_log">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_drop_all_storage">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_reuse_all_storage">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>

    <truncate sql-case-id="truncate_table_with_cascade">
        <table name="t_log" start-index="15" stop-index="19"/>
    </truncate>
</sql-parser-test-cases>
