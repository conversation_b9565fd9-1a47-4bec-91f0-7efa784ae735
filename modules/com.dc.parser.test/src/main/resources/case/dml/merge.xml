<?xml version="1.0" encoding="UTF-8"?>

<sql-parser-test-cases>
    <merge sql-case-id="merge_into_table_using_table">
        <target>
            <simple-table name="people_target" start-index="11" stop-index="23"/>
        </target>
        <source>
            <simple-table name="people_source" start-index="31" stop-index="43"/>
        </source>
        <expr>
            <binary-operation-expression start-index="49" stop-index="97">
                <left>
                    <column name="person_id" start-index="49" stop-index="71">
                        <owner name="people_target" start-index="49" stop-index="61"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="person_id" start-index="75" stop-index="97">
                        <owner name="people_source" start-index="75" stop-index="87"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
    </merge>
    <merge sql-case-id="merge_into_table_using_subquery_alias">
        <target>
            <simple-table name="bonuses" alias="D" start-index="11" stop-index="19"/>
        </target>
        <source>
            <subquery-table alias="S" start-index="27" stop-index="111">
                <subquery>
                    <select>
                        <from>
                            <simple-table name="employees" start-index="75" stop-index="83"/>
                        </from>
                        <projections start-index="35" stop-index="68">
                            <column-projection name="employee_id" start-index="35" stop-index="45"/>
                            <column-projection name="salary" start-index="48" stop-index="53"/>
                            <column-projection name="department_id" start-index="56" stop-index="68"/>
                        </projections>
                        <where start-index="85" stop-index="108">
                            <expr>
                                <binary-operation-expression start-index="91" stop-index="108">
                                    <left>
                                        <column name="department_id" start-index="91" stop-index="103"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="80" start-index="107" stop-index="108"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </source>
        <expr>
            <binary-operation-expression start-index="117" stop-index="145">
                <left>
                    <column name="employee_id" start-index="117" stop-index="129">
                        <owner name="D" start-index="117" stop-index="117"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="employee_id" start-index="133" stop-index="145">
                        <owner name="S" start-index="133" stop-index="133"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
    </merge>
    <merge sql-case-id="merge_update_table">
        <target>
            <simple-table name="people_target" alias="pt" start-index="11" stop-index="26"/>
        </target>
        <source>
            <simple-table name="people_source" alias="ps" start-index="34" stop-index="49"/>
        </source>
        <expr>
            <binary-operation-expression start-index="55" stop-index="81">
                <left>
                    <column name="person_id" start-index="55" stop-index="66">
                        <owner name="pt" start-index="55" stop-index="56"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="person_id" start-index="70" stop-index="81">
                        <owner name="ps" start-index="70" stop-index="71"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
        <update>
            <set start-index="113" stop-index="191">
                <assignment start-index="113" stop-index="141">
                    <column name="first_name" start-index="113" stop-index="125">
                        <owner name="pt" start-index="113" stop-index="114"/>
                    </column>
                    <assignment-value>
                        <column name="first_name" start-index="129" stop-index="141">
                            <owner name="ps" start-index="129" stop-index="130"/>
                        </column>
                    </assignment-value>
                </assignment>
                <assignment start-index="144" stop-index="170">
                    <column name="last_name" start-index="144" stop-index="155">
                        <owner name="pt" start-index="144" stop-index="145"/>
                    </column>
                    <assignment-value>
                        <column name="last_name" start-index="159" stop-index="170">
                            <owner name="ps" start-index="159" stop-index="160"/>
                        </column>
                    </assignment-value>
                </assignment>
                <assignment start-index="173" stop-index="191">
                    <column name="title" start-index="173" stop-index="180">
                        <owner name="pt" start-index="173" stop-index="174"/>
                    </column>
                    <assignment-value>
                        <column name="title" start-index="184" stop-index="191">
                            <owner name="ps" start-index="184" stop-index="185"/>
                        </column>
                    </assignment-value>
                </assignment>
            </set>
        </update>
    </merge>
    <merge sql-case-id="merge_update_table_with_delete">
        <target>
            <simple-table name="bonuses" alias="D" start-index="11" stop-index="19"/>
        </target>
        <source>
            <subquery-table alias="S" start-index="27" stop-index="111">
                <subquery>
                    <select>
                        <from>
                            <simple-table name="employees" start-index="75" stop-index="83"/>
                        </from>
                        <projections start-index="35" stop-index="68">
                            <column-projection name="employee_id" start-index="35" stop-index="45"/>
                            <column-projection name="salary" start-index="48" stop-index="53"/>
                            <column-projection name="department_id" start-index="56" stop-index="68"/>
                        </projections>
                        <where start-index="85" stop-index="108">
                            <expr>
                                <binary-operation-expression start-index="91" stop-index="108">
                                    <left>
                                        <column name="department_id" start-index="91" stop-index="103"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="80" start-index="107" stop-index="108"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </source>
        <expr>
            <binary-operation-expression start-index="117" stop-index="145">
                <left>
                    <column name="employee_id" start-index="117" stop-index="129">
                        <owner name="D" start-index="117" stop-index="117"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="employee_id" start-index="133" stop-index="145">
                        <owner name="S" start-index="133" stop-index="133"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
        <update>
            <set start-index="177" stop-index="208">
                <assignment>
                    <columns name="bonus" start-index="177" stop-index="183" literal-start-index="177"
                             literal-stop-index="183">
                        <owner name="D" start-index="177" stop-index="177" literal-start-index="177"
                               literal-stop-index="177"/>
                    </columns>
                    <assignment-value>
                        <binary-operation-expression start-index="187" stop-index="208" literal-start-index="187"
                                                     literal-stop-index="208">
                            <left>
                                <binary-operation-expression start-index="187" stop-index="204"
                                                             literal-start-index="187" literal-stop-index="204">
                                    <left>
                                        <column name="bonus" start-index="187" stop-index="193"
                                                literal-start-index="187" literal-stop-index="193">
                                            <owner name="D" start-index="187" stop-index="187" literal-start-index="187"
                                                   literal-stop-index="187"/>
                                        </column>
                                    </left>
                                    <operator>+</operator>
                                    <right>
                                        <column name="salary" start-index="197" stop-index="204"
                                                literal-start-index="197" literal-stop-index="204">
                                            <owner name="S" start-index="197" stop-index="197" literal-start-index="197"
                                                   literal-stop-index="197"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>*</operator>
                            <right>
                                <literal-expression value="0.01" start-index="206" stop-index="208"
                                                    literal-start-index="206" literal-stop-index="208"/>
                            </right>
                        </binary-operation-expression>
                    </assignment-value>
                </assignment>
            </set>
        </update>
        <delete>
            <where start-index="210" stop-index="239">
                <expr>
                    <binary-operation-expression start-index="224" stop-index="238">
                        <left>
                            <column name="salary" start-index="224" stop-index="231">
                                <owner name="S" start-index="224" stop-index="224"/>
                            </column>
                        </left>
                        <operator>&gt;</operator>
                        <right>
                            <literal-expression value="8000" start-index="235" stop-index="238"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </delete>
    </merge>
    <merge sql-case-id="merge_update_and_insert_table">
        <target>
            <simple-table alias="D" name="bonuses" start-index="11" stop-index="19" literal-start-index="11"
                          literal-stop-index="19"/>
        </target>
        <source>
            <subquery-table alias="S" start-index="30" stop-index="120">
                <subquery>
                    <select>
                        <projections start-index="38" stop-index="71" literal-start-index="38" literal-stop-index="71">
                            <column-projection name="employee_id" start-index="38" stop-index="48"
                                               literal-start-index="38" literal-stop-index="48"/>
                            <column-projection name="salary" start-index="51" stop-index="56" literal-start-index="51"
                                               literal-stop-index="56"/>
                            <column-projection name="department_id" start-index="59" stop-index="71"
                                               literal-start-index="59" literal-stop-index="71"/>
                        </projections>
                        <from>
                            <simple-table name="employees" start-index="78" stop-index="89" literal-start-index="78"
                                          literal-stop-index="89">
                                <owner name="hr" start-index="78" stop-index="79" literal-start-index="78"
                                       literal-stop-index="79"/>
                            </simple-table>
                        </from>
                        <where start-index="94" stop-index="117" literal-start-index="94" literal-stop-index="117">
                            <expr>
                                <binary-operation-expression start-index="100" stop-index="117"
                                                             literal-start-index="100" literal-stop-index="117">
                                    <left>
                                        <column name="department_id" start-index="100" stop-index="112"
                                                literal-start-index="100" literal-stop-index="112"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="80" start-index="116" stop-index="117"
                                                            literal-start-index="116" literal-stop-index="117"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </source>
        <expr>
            <binary-operation-expression start-index="129" stop-index="157" literal-start-index="129"
                                         literal-stop-index="157">
                <left>
                    <column name="employee_id" start-index="129" stop-index="141" literal-start-index="129"
                            literal-stop-index="141">
                        <owner name="D" start-index="129" stop-index="129" literal-start-index="129"
                               literal-stop-index="129"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="employee_id" start-index="145" stop-index="157" literal-start-index="145"
                            literal-stop-index="157">
                        <owner name="S" start-index="145" stop-index="145" literal-start-index="145"
                               literal-stop-index="145"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
        <insert>
            <values>
                <value>
                    <assignment-value>
                        <column name="employee_id" start-index="331" stop-index="343">
                            <owner name="S" start-index="331" stop-index="331"/>
                        </column>
                        <binary-operation-expression start-index="67" stop-index="74">
                            <left>
                                <column name="salary" start-index="346" stop-index="353">
                                    <owner name="S" start-index="346" stop-index="346"/>
                                </column>
                            </left>
                            <operator>*</operator>
                            <right>
                                <literal-expression value=".01" start-index="355" stop-index="357"/>
                            </right>
                        </binary-operation-expression>
                    </assignment-value>
                </value>
            </values>
            <where start-index="365" stop-index="388" literal-start-index="365" literal-stop-index="388">
                <expr>
                    <binary-operation-expression start-index="372" stop-index="387" literal-start-index="372"
                                                 literal-stop-index="387">
                        <left>
                            <column name="salary" start-index="372" stop-index="379" literal-start-index="372"
                                    literal-stop-index="379">
                                <owner name="S" start-index="372" stop-index="372" literal-start-index="372"
                                       literal-stop-index="372"/>
                            </column>
                        </left>
                        <operator>&lt;=</operator>
                        <right>
                            <literal-expression value="8000" start-index="384" stop-index="387"
                                                literal-start-index="384" literal-stop-index="387"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </insert>
    </merge>
    <merge sql-case-id="merge_insert_and_update_table" parameters="1, 1, 1, 1, 1, 1, 1, 1, 1, 1">
        <target>
            <simple-table alias="t1" name="t_order" start-index="11" stop-index="20"/>
        </target>
        <source>
            <subquery-table alias="t2" start-index="84" stop-index="193">
                <subquery>
                    <select>
                        <projections start-index="92" stop-index="116">
                            <expression-projection text="1" start-index="92" stop-index="102" alias="userId">
                                <parameter-marker-expression parameter-index="0" start-index="92" stop-index="92"/>
                                <literal-expression value="1" start-index="92" stop-index="92"/>
                            </expression-projection>
                            <expression-projection text="1" start-index="105" stop-index="116" alias="orderId">
                                <parameter-marker-expression parameter-index="1" start-index="105" stop-index="105"/>
                                <literal-expression value="1" start-index="105" stop-index="105"/>
                            </expression-projection>
                        </projections>
                        <from>
                            <simple-table name="DUAL" start-index="186" stop-index="189"/>
                        </from>
                    </select>
                </subquery>
            </subquery-table>
        </source>
        <expr>
            <binary-operation-expression start-index="255" stop-index="305">
                <left>
                    <binary-operation-expression start-index="255" stop-index="276">
                        <left>
                            <column name="user_id" start-index="255" stop-index="264">
                                <owner name="t1" start-index="255" stop-index="256"/>
                            </column>
                        </left>
                        <operator>=</operator>
                        <right>
                            <column name="userId" start-index="268" stop-index="276">
                                <owner name="t2" start-index="268" stop-index="269"/>
                            </column>
                        </right>
                    </binary-operation-expression>
                </left>
                <operator>AND</operator>
                <right>
                    <binary-operation-expression start-index="282" stop-index="305">
                        <left>
                            <column name="order_id" start-index="282" stop-index="292">
                                <owner name="t1" start-index="282" stop-index="283"/>
                            </column>
                        </left>
                        <operator>=</operator>
                        <right>
                            <column name="orderId" start-index="296" stop-index="305">
                                <owner name="t2" start-index="296" stop-index="297"/>
                            </column>
                        </right>
                    </binary-operation-expression>
                </right>
            </binary-operation-expression>
        </expr>
        <insert>
            <columns start-index="453" stop-index="515">
                <column name="order_id" start-index="454" stop-index="461"/>
                <column name="user_id" start-index="464" stop-index="470"/>
                <column name="status" start-index="473" stop-index="478"/>
                <column name="merchant_id" start-index="481" stop-index="491"/>
                <column name="remark" start-index="494" stop-index="499"/>
                <column name="creation_date" start-index="502" stop-index="514"/>
            </columns>
            <values>
                <value>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="2" start-index="585" stop-index="585"/>
                        <literal-expression value="1" start-index="585" stop-index="585"/>
                    </assignment-value>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="3" start-index="588" stop-index="588"/>
                        <literal-expression value="1" start-index="588" stop-index="588"/>
                    </assignment-value>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="4" start-index="591" stop-index="591"/>
                        <literal-expression value="1" start-index="591" stop-index="591"/>
                    </assignment-value>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="5" start-index="594" stop-index="594"/>
                        <literal-expression value="1" start-index="594" stop-index="594"/>
                    </assignment-value>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="6" start-index="597" stop-index="597"/>
                        <literal-expression value="1" start-index="597" stop-index="597"/>
                    </assignment-value>
                    <assignment-value>
                        <literal-expression value="DATE '2017-08-08'" start-index="600" stop-index="616"/>
                    </assignment-value>
                </value>
            </values>
        </insert>
        <update>
            <set start-index="824" stop-index="1000">
                <assignment>
                    <columns name="merchant_id" start-index="824" stop-index="834"/>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="7" start-index="838" stop-index="838"/>
                        <literal-expression value="1" start-index="838" stop-index="838"/>
                    </assignment-value>
                </assignment>
                <assignment>
                    <columns name="remark" start-index="905" stop-index="910"/>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="8" start-index="919" stop-index="919"/>
                        <literal-expression value="1" start-index="919" stop-index="919"/>
                    </assignment-value>
                </assignment>
                <assignment>
                    <columns name="status" start-index="986" stop-index="991"/>
                    <assignment-value>
                        <parameter-marker-expression parameter-index="9" start-index="1000" stop-index="1000"/>
                        <literal-expression value="1" start-index="1000" stop-index="1000"/>
                    </assignment-value>
                </assignment>
            </set>
        </update>
    </merge>
    <merge sql-case-id="merge_into_select">
        <target>
            <subquery-table alias="D" start-index="11" stop-index="60">
                <subquery>
                    <select>
                        <projections start-index="19" stop-index="19">
                            <shorthand-projection start-index="19" stop-index="19"/>
                        </projections>
                        <from>
                            <simple-table name="bonuses" start-index="26" stop-index="32"/>
                        </from>
                        <where start-index="34" stop-index="57">
                            <expr>
                                <binary-operation-expression start-index="40" stop-index="57">
                                    <left>
                                        <column name="department_id" start-index="40" stop-index="52"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="80" start-index="56" stop-index="57"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </target>
        <source>
            <subquery-table alias="S" start-index="68" stop-index="152">
                <subquery>
                    <select>
                        <from>
                            <simple-table name="employees" start-index="116" stop-index="124"/>
                        </from>
                        <projections start-index="76" stop-index="109">
                            <column-projection name="employee_id" start-index="76" stop-index="86"/>
                            <column-projection name="salary" start-index="89" stop-index="94"/>
                            <column-projection name="department_id" start-index="97" stop-index="109"/>
                        </projections>
                        <where start-index="126" stop-index="149">
                            <expr>
                                <binary-operation-expression start-index="132" stop-index="149">
                                    <left>
                                        <column name="department_id" start-index="132" stop-index="144"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="80" start-index="148" stop-index="149"/>
                                    </right>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </source>
        <expr>
            <binary-operation-expression start-index="158" stop-index="186">
                <left>
                    <column name="employee_id" start-index="158" stop-index="170">
                        <owner name="D" start-index="158" stop-index="158"/>
                    </column>
                </left>
                <operator>=</operator>
                <right>
                    <column name="employee_id" start-index="174" stop-index="186">
                        <owner name="S" start-index="174" stop-index="174"/>
                    </column>
                </right>
            </binary-operation-expression>
        </expr>
        <update>
            <set start-index="218" stop-index="249" literal-start-index="218" literal-stop-index="249">
                <assignment>
                    <columns name="bonus" start-index="218" stop-index="224" literal-start-index="218"
                             literal-stop-index="224">
                        <owner name="D" start-index="218" stop-index="218" literal-start-index="218"
                               literal-stop-index="218"/>
                    </columns>
                    <assignment-value>
                        <binary-operation-expression start-index="228" stop-index="249" literal-start-index="228"
                                                     literal-stop-index="249">
                            <left>
                                <binary-operation-expression start-index="228" stop-index="245"
                                                             literal-start-index="228" literal-stop-index="245">
                                    <left>
                                        <column name="bonus" start-index="228" stop-index="234"
                                                literal-start-index="228" literal-stop-index="234">
                                            <owner name="D" start-index="228" stop-index="228" literal-start-index="228"
                                                   literal-stop-index="228"/>
                                        </column>
                                    </left>
                                    <operator>+</operator>
                                    <right>
                                        <column name="salary" start-index="238" stop-index="245"
                                                literal-start-index="238" literal-stop-index="245">
                                            <owner name="S" start-index="238" stop-index="238" literal-start-index="238"
                                                   literal-stop-index="238"/>
                                        </column>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>*</operator>
                            <right>
                                <literal-expression value="0.01" start-index="247" stop-index="249"
                                                    literal-start-index="247" literal-stop-index="249"/>
                            </right>
                        </binary-operation-expression>
                    </assignment-value>
                </assignment>
            </set>
        </update>
        <delete>
            <where start-index="251" stop-index="280">
                <expr>
                    <binary-operation-expression start-index="265" stop-index="279">
                        <left>
                            <column name="salary" start-index="265" stop-index="272">
                                <owner name="S" start-index="265" stop-index="265"/>
                            </column>
                        </left>
                        <operator>&gt;</operator>
                        <right>
                            <literal-expression value="8000" start-index="276" stop-index="279"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </delete>
    </merge>
</sql-parser-test-cases>
