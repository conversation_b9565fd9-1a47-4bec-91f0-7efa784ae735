<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="explicit_table">
        <projections start-index="0" stop-index="0">
            <shorthand-projection start-index="0" stop-index="0"/>
        </projections>
        <from>
            <simple-table name="f" start-index="0" stop-index="6"/>
        </from>
    </select>

    <select sql-case-id="table_with_order_by_limit_offset">
        <projections start-index="0" stop-index="0">
            <shorthand-projection start-index="0" stop-index="0"/>
        </projections>
        <from>
            <simple-table name="t_order" start-index="0" stop-index="12"/>
        </from>
        <order-by>
            <column-item name="order_id" start-index="23" stop-index="30"/>
        </order-by>
        <limit start-index="32" stop-index="47">
            <offset value="2" literal-start-index="47" literal-stop-index="47"/>
            <row-count value="1" start-index="38" stop-index="38"/>
        </limit>
    </select>

    <select sql-case-id="table_union">
        <projections start-index="0" stop-index="0">
            <shorthand-projection start-index="0" stop-index="0"/>
        </projections>
        <from>
            <simple-table name="T1" start-index="0" stop-index="7"/>
        </from>
        <combine combine-type="UNION" start-index="9" stop-index="22">
            <left>
                <projections start-index="0" stop-index="0">
                    <shorthand-projection start-index="0" stop-index="0"/>
                </projections>
                <from>
                    <simple-table name="T1" start-index="0" stop-index="7"/>
                </from>
            </left>
            <right>
                <projections start-index="15" stop-index="15">
                    <shorthand-projection start-index="15" stop-index="15"/>
                </projections>
                <from>
                    <simple-table name="T2" start-index="15" stop-index="22"/>
                </from>
            </right>
        </combine>
    </select>

    <select sql-case-id="select_from_with_table">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table alias="dt" start-index="14" stop-index="29">
                <subquery>
                    <select>
                        <projections start-index="15" stop-index="15">
                            <shorthand-projection start-index="15" stop-index="15"/>
                        </projections>
                        <from>
                            <simple-table name="t0" start-index="15" stop-index="22"/>
                        </from>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>
</sql-parser-test-cases>
