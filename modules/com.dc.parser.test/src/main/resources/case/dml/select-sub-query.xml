<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_with_lateral">
        <from>
            <join-table start-index="14" stop-index="94" join-type="COMMA">
                <left>
                    <join-table start-index="14" stop-index="54" join-type="COMMA">
                        <left>
                            <simple-table start-index="14" stop-index="15" name="t1"/>
                        </left>
                        <right>
                            <subquery-table alias="dt1" start-index="18" stop-index="54">
                                <subquery start-index="26" stop-index="47">
                                    <select>
                                        <projections distinct-row="true" start-index="43" stop-index="46">
                                            <column-projection start-index="43" stop-index="46" name="x">
                                                <owner start-index="43" stop-index="44" name="t1"/>
                                            </column-projection>
                                        </projections>
                                    </select>
                                </subquery>
                            </subquery-table>
                        </right>
                    </join-table>
                </left>
                <right>
                    <subquery-table alias="dt2" start-index="57" stop-index="94">
                        <subquery start-index="65" stop-index="87">
                            <select>
                                <projections distinct-row="true" start-index="82" stop-index="86">
                                    <column-projection start-index="82" stop-index="86" name="x">
                                        <owner start-index="82" stop-index="84" name="dt1"/>
                                    </column-projection>
                                </projections>
                            </select>
                        </subquery>
                    </subquery-table>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="7">
            <expression-projection start-index="7" stop-index="7" text="1">
                <expr>
                    <literal-expression start-index="7" stop-index="7" value="1"/>
                </expr>
            </expression-projection>
        </projections>
        <where start-index="96" stop-index="114">
            <expr>
                <binary-operation-expression start-index="102" stop-index="114">
                    <left>
                        <column start-index="102" stop-index="106" name="x">
                            <owner start-index="102" stop-index="104" name="dt1"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column start-index="110" stop-index="114" name="x">
                            <owner start-index="110" stop-index="112" name="dt2"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_sub_query_with_project">
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="33">
            <column-projection name="order_id" start-index="7" stop-index="14"/>
            <subquery-projection start-index="17" stop-index="26" alias="num" text="(SELECT 1)"
                                 literal-text="(SELECT 1)">
                <subquery>
                    <select>
                        <projections start-index="25" stop-index="25">
                            <expression-projection start-index="25" stop-index="25" text="1"/>
                        </projections>
                    </select>
                </subquery>
            </subquery-projection>
        </projections>
    </select>

    <select sql-case-id="select_sub_query_with_table" parameters="3, 4">
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner start-index="7" stop-index="7" name="t"/>
            </shorthand-projection>
        </projections>
        <from>
            <subquery-table alias="t" start-index="16" stop-index="65">
                <subquery>
                    <select parameters="3, 4">
                        <projections start-index="24" stop-index="24">
                            <shorthand-projection start-index="24" stop-index="24"/>
                        </projections>
                        <from>
                            <simple-table start-index="31" stop-index="37" name="t_order"/>
                        </from>
                        <where start-index="39" stop-index="62">
                            <expr>
                                <in-expression start-index="45" stop-index="62">
                                    <not>false</not>
                                    <left>
                                        <column name="order_id" start-index="45" stop-index="52"/>
                                    </left>
                                    <right>
                                        <list-expression start-index="57" stop-index="62">
                                            <items>
                                                <literal-expression value="3" start-index="58" stop-index="58"/>
                                                <parameter-marker-expression parameter-index="0" start-index="58"
                                                                             stop-index="58"/>
                                            </items>
                                            <items>
                                                <literal-expression value="4" start-index="61" stop-index="61"/>
                                                <parameter-marker-expression parameter-index="1" start-index="61"
                                                                             stop-index="61"/>
                                            </items>
                                        </list-expression>
                                    </right>
                                </in-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_with_equal_subquery">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="85">
            <expr>
                <binary-operation-expression start-index="28" stop-index="85">
                    <left>
                        <column name="user_id" start-index="28" stop-index="34"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <subquery start-index="38" stop-index="85">
                            <select>
                                <from start-index="59" stop-index="70">
                                    <simple-table name="t_order_item" start-index="59" stop-index="70"/>
                                </from>
                                <projections start-index="46" stop-index="52">
                                    <column-projection name="user_id" start-index="46" stop-index="52"/>
                                </projections>
                                <where start-index="72" stop-index="84">
                                    <expr>
                                        <binary-operation-expression start-index="78" stop-index="84">
                                            <left>
                                                <column name="id" start-index="78" stop-index="79"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="10" start-index="83" stop-index="84"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_any_subquery">
        <from>
            <simple-table name="employees" start-index="14" stop-index="22"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="24" stop-index="97">
            <expr>
                <binary-operation-expression start-index="30" stop-index="97">
                    <left>
                        <column name="salary" start-index="30" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <subquery start-index="43" stop-index="97">
                            <select>
                                <from start-index="63" stop-index="71">
                                    <simple-table name="employees" start-index="63" stop-index="71"/>
                                </from>
                                <projections start-index="51" stop-index="56">
                                    <column-projection name="salary" start-index="51" stop-index="56"/>
                                </projections>
                                <where start-index="73" stop-index="96">
                                    <expr>
                                        <binary-operation-expression start-index="79" stop-index="96">
                                            <left>
                                                <column name="department_id" start-index="79" stop-index="91"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="30" start-index="95" stop-index="96"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="employee_id" order-direction="ASC" start-index="108" stop-index="118"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_in_subquery">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="93">
            <expr>
                <in-expression start-index="28" stop-index="93">
                    <not>false</not>
                    <left>
                        <column name="user_id" start-index="28" stop-index="34"/>
                    </left>
                    <right>
                        <subquery start-index="39" stop-index="93">
                            <select>
                                <from>
                                    <simple-table name="t_order_item" start-index="60" stop-index="71"/>
                                </from>
                                <projections start-index="47" stop-index="53">
                                    <column-projection name="user_id" start-index="47" stop-index="53"/>
                                </projections>
                                <where start-index="73" stop-index="92">
                                    <expr>
                                        <in-expression start-index="79" stop-index="92">
                                            <not>false</not>
                                            <left>
                                                <column name="id" start-index="79" stop-index="80"/>
                                            </left>
                                            <right>
                                                <list-expression start-index="85" stop-index="92">
                                                    <items>
                                                        <literal-expression value="10" start-index="86"
                                                                            stop-index="87"/>
                                                    </items>
                                                    <items>
                                                        <literal-expression value="11" start-index="90"
                                                                            stop-index="91"/>
                                                    </items>
                                                </list-expression>
                                            </right>
                                        </in-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_between_subquery" parameters="12">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="22" stop-index="103" literal-stop-index="104">
            <expr>
                <between-expression start-index="28" stop-index="103" literal-stop-index="104">
                    <not>false</not>
                    <left>
                        <column name="user_id" start-index="28" stop-index="34"/>
                    </left>
                    <between-expr>
                        <subquery start-index="44" stop-index="97">
                            <select>
                                <from>
                                    <simple-table name="t_order_item" start-index="65" stop-index="76"/>
                                </from>
                                <projections start-index="52" stop-index="58">
                                    <column-projection name="user_id" start-index="52" stop-index="58"/>
                                </projections>
                                <where start-index="78" stop-index="96">
                                    <expr>
                                        <binary-operation-expression start-index="84" stop-index="96">
                                            <left>
                                                <column name="order_id" start-index="84" stop-index="91"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="10" start-index="95" stop-index="96"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </between-expr>
                    <and-expr>
                        <literal-expression value="12" start-index="103" stop-index="104"/>
                        <parameter-marker-expression parameter-index="0" start-index="103" stop-index="103"/>
                    </and-expr>
                </between-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_exists_sub_query_with_project">
        <projections start-index="7" stop-index="36">
            <subquery-projection start-index="14" stop-index="36" text="EXISTS (SELECT 1 FROM t_order)"
                                 literal-text="EXISTS (SELECT 1 FROM t_order)">
                <subquery start-index="15" stop-index="35">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="29" stop-index="35"/>
                        </from>
                        <projections start-index="22" stop-index="22">
                            <expression-projection start-index="22" stop-index="22" text="1"/>
                        </projections>
                    </select>
                </subquery>
            </subquery-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_join_table_subquery">
        <projections start-index="7" stop-index="68">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="60" stop-index="68">
                <owner start-index="60" stop-index="60" name="u"/>
            </column-projection>
        </projections>
        <from start-index="70" stop-index="90">
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="t_order_federate" start-index="75" stop-index="90"/>
                </left>
                <right>
                    <subquery-table alias="u" start-index="93" stop-index="124">
                        <subquery>
                            <select>
                                <projections start-index="101" stop-index="101">
                                    <shorthand-projection start-index="101" stop-index="101"/>
                                </projections>
                                <from>
                                    <simple-table start-index="108" stop-index="118" name="t_user_info"/>
                                </from>
                            </select>
                        </subquery>
                    </subquery-table>
                </right>
            </join-table>
        </from>
        <where start-index="126" stop-index="167">
            <expr>
                <binary-operation-expression start-index="132" stop-index="167">
                    <left>
                        <column name="user_id" start-index="132" stop-index="155">
                            <owner start-index="132" stop-index="147" name="t_order_federate"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="user_id" start-index="159" stop-index="167">
                            <owner start-index="159" stop-index="159" name="u"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_projection_subquery">
        <projections start-index="7" stop-index="99">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
            <subquery-projection start-index="60" stop-index="99" text="(SELECT COUNT(user_id) FROM t_user_info)">
                <subquery>
                    <select>
                        <projections start-index="68" stop-index="81">
                            <aggregation-projection type="COUNT" expression="COUNT(user_id)" start-index="68"
                                                    stop-index="81"/>
                        </projections>
                        <from start-index="83" stop-index="98">
                            <simple-table name="t_user_info" start-index="88" stop-index="98"/>
                        </from>
                    </select>
                </subquery>
            </subquery-projection>
        </projections>
        <from start-index="101" stop-index="121">
            <simple-table name="t_order_federate" start-index="106" stop-index="121"/>
        </from>
    </select>

    <select sql-case-id="select_with_projection_subquery_and_multiple_parameters">
        <projections start-index="7" stop-index="110">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
            <subquery-projection start-index="60" stop-index="110"
                                 text="(SELECT CONCAT(order_id, user_id) FROM t_user_info)">
                <subquery>
                    <select>
                        <projections start-index="68" stop-index="92">
                            <expression-projection text="CONCAT(order_id, user_id)" start-index="68" stop-index="92"/>
                        </projections>
                        <from start-index="94" stop-index="109">
                            <simple-table name="t_user_info" start-index="99" stop-index="109"/>
                        </from>
                    </select>
                </subquery>
            </subquery-projection>
        </projections>
        <from start-index="112" stop-index="132">
            <simple-table name="t_order_federate" start-index="117" stop-index="132"/>
        </from>
    </select>

    <select sql-case-id="select_with_in_subquery_condition">
        <projections start-index="7" stop-index="57">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
        </projections>
        <from start-index="59" stop-index="79">
            <simple-table name="t_order_federate" start-index="64" stop-index="79"/>
        </from>
        <where start-index="81" stop-index="124">
            <expr>
                <in-expression start-index="87" stop-index="124">
                    <left>
                        <column name="user_id" start-index="87" stop-index="93"/>
                    </left>
                    <right>
                        <subquery start-index="98" stop-index="124">
                            <select>
                                <projections start-index="106" stop-index="106">
                                    <shorthand-projection start-index="106" stop-index="106"/>
                                </projections>
                                <from start-index="108" stop-index="123">
                                    <simple-table name="t_user_info" start-index="113" stop-index="123"/>
                                </from>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_between_and_subquery_condition">
        <projections start-index="7" stop-index="57">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
        </projections>
        <from start-index="59" stop-index="79">
            <simple-table name="t_order_federate" start-index="64" stop-index="79"/>
        </from>
        <where start-index="81" stop-index="230">
            <expr>
                <between-expression start-index="87" stop-index="230">
                    <left>
                        <column name="user_id" start-index="87" stop-index="93"/>
                    </left>
                    <between-expr>
                        <subquery start-index="103" stop-index="164">
                            <select>
                                <projections start-index="111" stop-index="117">
                                    <column-projection name="user_id" start-index="111" stop-index="117"/>
                                </projections>
                                <from start-index="119" stop-index="134">
                                    <simple-table name="t_user_info" start-index="124" stop-index="134"/>
                                </from>
                                <where start-index="136" stop-index="163">
                                    <expr>
                                        <binary-operation-expression start-index="142" stop-index="163">
                                            <left>
                                                <column name="information" start-index="142" stop-index="152"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="before" start-index="156" stop-index="163"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </between-expr>
                    <and-expr>
                        <subquery start-index="170" stop-index="230">
                            <select>
                                <projections start-index="178" stop-index="184">
                                    <column-projection name="user_id" start-index="178" stop-index="184"/>
                                </projections>
                                <from start-index="186" stop-index="201">
                                    <simple-table name="t_user_info" start-index="191" stop-index="201"/>
                                </from>
                                <where start-index="203" stop-index="229">
                                    <expr>
                                        <binary-operation-expression start-index="209" stop-index="229">
                                            <left>
                                                <column name="information" start-index="209" stop-index="219"/>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <literal-expression value="after" start-index="223" stop-index="229"/>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </and-expr>
                </between-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_exist_subquery_condition">
        <projections start-index="7" stop-index="57">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
        </projections>
        <from start-index="59" stop-index="79">
            <simple-table name="t_order_federate" start-index="64" stop-index="79"/>
        </from>
        <where start-index="81" stop-index="173">
            <expr>
                <exists-subquery start-index="87" stop-index="173">
                    <subquery start-index="94" stop-index="173">
                        <select>
                            <projections start-index="102" stop-index="102">
                                <shorthand-projection start-index="102" stop-index="102"/>
                            </projections>
                            <from start-index="104" stop-index="119">
                                <simple-table name="t_user_info" start-index="109" stop-index="119"/>
                            </from>
                            <where start-index="121" stop-index="172">
                                <expr>
                                    <binary-operation-expression start-index="127" stop-index="172">
                                        <left>
                                            <column name="user_id" start-index="127" stop-index="150">
                                                <owner start-index="127" stop-index="142" name="t_order_federate"/>
                                            </column>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <column name="user_id" start-index="154" stop-index="172">
                                                <owner start-index="154" stop-index="164" name="t_user_info"/>
                                            </column>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </exists-subquery>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_not_exist_subquery_condition">
        <projections start-index="7" stop-index="57">
            <column-projection name="order_id" start-index="7" stop-index="31">
                <owner start-index="7" stop-index="22" name="t_order_federate"/>
            </column-projection>
            <column-projection name="user_id" start-index="34" stop-index="57">
                <owner start-index="34" stop-index="49" name="t_order_federate"/>
            </column-projection>
        </projections>
        <from start-index="59" stop-index="79">
            <simple-table name="t_order_federate" start-index="64" stop-index="79"/>
        </from>
        <where start-index="81" stop-index="177">
            <expr>
                <exists-subquery start-index="91" stop-index="177">
                    <not>true</not>
                    <subquery start-index="98" stop-index="177">
                        <select>
                            <projections start-index="106" stop-index="106">
                                <shorthand-projection start-index="106" stop-index="106"/>
                            </projections>
                            <from start-index="108" stop-index="123">
                                <simple-table name="t_user_info" start-index="113" stop-index="123"/>
                            </from>
                            <where start-index="125" stop-index="176">
                                <expr>
                                    <binary-operation-expression start-index="131" stop-index="176">
                                        <left>
                                            <column name="user_id" start-index="131" stop-index="154">
                                                <owner start-index="131" stop-index="146" name="t_order_federate"/>
                                            </column>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <column name="user_id" start-index="158" stop-index="176">
                                                <owner start-index="158" stop-index="168" name="t_user_info"/>
                                            </column>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </exists-subquery>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_with_exist_string_split_subquery">
        <projections start-index="7" stop-index="27">
            <column-projection name="ProductId" start-index="7" stop-index="15"/>
            <column-projection name="Name" start-index="18" stop-index="21"/>
            <column-projection name="Tags" start-index="24" stop-index="27"/>
        </projections>
        <from start-index="34" stop-index="40">
            <simple-table name="Product" start-index="34" stop-index="40"/>
        </from>
        <where start-index="42" stop-index="129">
            <expr>
                <subquery start-index="48" stop-index="129">
                    <select>
                        <projections start-index="63" stop-index="63">
                            <shorthand-projection start-index="63" stop-index="63"/>
                        </projections>
                        <from start-index="70" stop-index="92">
                            <function-table>
                                <table-function function-name="STRING_SPLIT" text="STRING_SPLIT(Tags, ',')"/>
                            </function-table>
                        </from>
                        <where start-index="94" stop-index="128">
                            <expr>
                                <in-expression start-index="100" stop-index="128">
                                    <left>
                                        <column name="value" start-index="100" stop-index="104"/>
                                    </left>
                                    <right>
                                        <list-expression start-index="109" stop-index="128">
                                            <items>
                                                <literal-expression value="clothing" start-index="110"
                                                                    stop-index="119"/>
                                            </items>
                                            <items>
                                                <literal-expression value="road" start-index="122" stop-index="127"/>
                                            </items>
                                        </list-expression>
                                    </right>
                                </in-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_sub_query_with_cast_function">
        <projections start-index="7" stop-index="125">
            <column-projection name="BusinessEntityID" start-delimiter="[" end-delimiter="]" start-index="7"
                               stop-index="53" alias="BusinessEntityID">
                <owner name="T1_1" start-delimiter="[" end-delimiter="]" start-index="7" stop-index="12"/>
            </column-projection>
            <column-projection name="rowguid" start-delimiter="[" end-delimiter="]" start-index="56" stop-index="84"
                               alias="rowguid">
                <owner name="T1_1" start-delimiter="[" end-delimiter="]" start-index="56" stop-index="61"/>
            </column-projection>
            <column-projection name="ModifiedDate" start-delimiter="[" end-delimiter="]" start-index="87"
                               stop-index="125" alias="ModifiedDate">
                <owner name="T1_1" start-delimiter="[" end-delimiter="]" start-index="87" stop-index="92"/>
            </column-projection>
        </projections>
        <from start-index="133" stop-index="378">
            <subquery-table alias="T1_1" start-index="133" stop-index="386">
                <subquery start-index="133" stop-index="378">
                    <select>
                        <projections start-index="141" stop-index="259">
                            <column-projection name="BusinessEntityID" start-delimiter="[" end-delimiter="]"
                                               start-index="141" stop-index="187" alias="BusinessEntityID">
                                <owner name="T2_1" start-delimiter="[" end-delimiter="]" start-index="141"
                                       stop-index="146"/>
                            </column-projection>
                            <column-projection name="rowguid" start-delimiter="[" end-delimiter="]" start-index="190"
                                               stop-index="218" alias="rowguid">
                                <owner name="T2_1" start-delimiter="[" end-delimiter="]" start-index="190"
                                       stop-index="195"/>
                            </column-projection>
                            <column-projection name="ModifiedDate" start-delimiter="[" end-delimiter="]"
                                               start-index="221" stop-index="259" alias="ModifiedDate">
                                <owner name="T2_1" start-delimiter="[" end-delimiter="]" start-index="221"
                                       stop-index="226"/>
                            </column-projection>
                        </projections>
                        <from start-index="266" stop-index="319">
                            <simple-table name="BusinessEntity" start-delimiter="[" end-delimiter="]" start-index="266"
                                          stop-index="319" alias="T2_1">
                                <owner name="Person" start-delimiter="[" end-delimiter="]" start-index="287"
                                       stop-index="294">
                                    <owner name="AdventureWorks2022" start-delimiter="[" end-delimiter="]"
                                           start-index="266" stop-index="285"/>
                                </owner>
                            </simple-table>
                        </from>
                        <where start-index="321" stop-index="377">
                            <expr>
                                <binary-operation-expression start-index="328" stop-index="376">
                                    <left>
                                        <column name="BusinessEntityID" start-delimiter="[" end-delimiter="]"
                                                start-index="328" stop-index="352">
                                            <owner name="T2_1" start-delimiter="[" end-delimiter="]" start-index="328"
                                                   stop-index="333"/>
                                        </column>
                                    </left>
                                    <right>
                                        <function function-name="CAST" text="CAST ((17907) AS INT)" start-index="356"
                                                  stop-index="376">
                                            <parameter>
                                                <literal-expression value="17907" start-index="363" stop-index="367"/>
                                            </parameter>
                                            <parameter>
                                                <data-type value="INT" start-index="373" stop-index="375"/>
                                            </parameter>
                                        </function>
                                    </right>
                                    <operator>=</operator>
                                </binary-operation-expression>
                            </expr>
                        </where>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_sub_query_with_inner_join">
        <projections start-index="7" stop-index="88">
            <column-projection name="BusinessEntityID" start-delimiter="[" end-delimiter="]" start-index="7"
                               stop-index="53" alias="BusinessEntityID">
                <owner name="T1_1" start-index="7" stop-index="12" start-delimiter="[" end-delimiter="]"/>
            </column-projection>
            <column-projection name="AddressID" start-index="56" stop-index="88" start-delimiter="[" end-delimiter="]"
                               alias="AddressID">
                <owner name="T1_1" start-index="56" stop-index="61" start-delimiter="[" end-delimiter="]"/>
            </column-projection>
        </projections>
        <from>
            <subquery-table alias="T1_1" start-index="95" stop-index="385">
                <subquery start-index="95" stop-index="377">
                    <select>
                        <projections start-index="103" stop-index="184">
                            <column-projection name="BusinessEntityID" start-index="103" stop-index="149"
                                               start-delimiter="[" end-delimiter="]" alias="BusinessEntityID">
                                <owner name="T2_2" start-index="103" stop-index="108" start-delimiter="["
                                       end-delimiter="]"/>
                            </column-projection>
                            <column-projection name="AddressID" start-index="152" stop-index="184" start-delimiter="["
                                               end-delimiter="]" alias="AddressID">
                                <owner name="T2_1" start-index="152" stop-index="157" start-delimiter="["
                                       end-delimiter="]"/>
                            </column-projection>
                        </projections>
                        <from>
                            <join-table join-type="INNER">
                                <left>
                                    <simple-table name="BusinessEntityAddress" start-index="191" stop-index="251"
                                                  start-delimiter="[" end-delimiter="]" alias="T2_1">
                                        <owner name="Person" start-index="212" stop-index="219" start-delimiter="["
                                               end-delimiter="]">
                                            <owner name="AdventureWorks2022" start-index="191" stop-index="210"
                                                   start-delimiter="[" end-delimiter="]"/>
                                        </owner>
                                    </simple-table>
                                </left>
                                <right>
                                    <simple-table name="BusinessEntity" start-index="264" stop-index="317"
                                                  start-delimiter="[" end-delimiter="]" alias="T2_2">
                                        <owner name="Person" start-index="285" stop-index="292" start-delimiter="["
                                               end-delimiter="]">
                                            <owner name="AdventureWorks2022" start-index="264" stop-index="283"
                                                   start-delimiter="[" end-delimiter="]"/>
                                        </owner>
                                    </simple-table>
                                </right>
                                <on-condition>
                                    <binary-operation-expression start-index="323" stop-index="375">
                                        <left>
                                            <column name="BusinessEntityID" start-index="323" stop-index="347"
                                                    start-delimiter="[" end-delimiter="]">
                                                <owner name="T2_1" start-index="323" stop-index="328"
                                                       start-delimiter="[" end-delimiter="]"/>
                                            </column>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <column name="BusinessEntityID" start-index="351" stop-index="375"
                                                    start-delimiter="[" end-delimiter="]">
                                                <owner name="T2_2" start-index="351" stop-index="356"
                                                       start-delimiter="[" end-delimiter="]"/>
                                            </column>
                                        </right>
                                    </binary-operation-expression>
                                </on-condition>
                            </join-table>
                        </from>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_sub_query_with_sum">
        <projections start-index="7" stop-index="27">
            <column-projection name="col" start-index="7" stop-index="27" start-delimiter="[" end-delimiter="]"
                               alias="col">
                <owner name="T1_1" start-index="7" stop-index="12" start-delimiter="[" end-delimiter="]"/>
            </column-projection>
        </projections>
        <from>
            <subquery-table alias="T1_1" start-index="34" stop-index="147">
                <subquery>
                    <select>
                        <projections start-index="42" stop-index="72">
                            <aggregation-projection expression="SUM([T2_1].[Quantity])" type="SUM" start-index="42"
                                                    stop-index="63" alias="col"/>
                        </projections>
                        <from>
                            <simple-table name="ProductInventory" start-index="79" stop-index="138" start-delimiter="["
                                          end-delimiter="]" alias="T2_1">
                                <owner name="Production" start-index="100" stop-index="111" start-delimiter="["
                                       end-delimiter="]">
                                    <owner name="AdventureWorks2022" start-index="79" stop-index="98"
                                           start-delimiter="[" end-delimiter="]"/>
                                </owner>
                            </simple-table>
                        </from>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_sub_query_with_rownumber">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from start-index="14" stop-index="110">
            <function-table>
                <subquery-table>
                    <subquery start-index="14" stop-index="110">
                        <select>
                            <projections start-index="22" stop-index="82">
                                <column-projection name="col1" start-index="22" stop-index="25"/>
                                <expression-projection alias="rn"
                                                       text="ROW_NUMBER () OVER (PARTITION by col1 ORDER BY col1)"
                                                       start-index="28" stop-index="82">
                                    <expr>
                                        <function function-name="ROW_NUMBER"
                                                  text="ROW_NUMBER () OVER (PARTITION by col1 ORDER BY col1)"
                                                  start-index="28" stop-index="79"/>
                                    </expr>
                                </expression-projection>
                            </projections>
                            <from start-index="89" stop-index="92">
                                <simple-table name="tab1" start-index="89" stop-index="92"/>
                            </from>
                            <where start-index="94" stop-index="109">
                                <expr>
                                    <binary-operation-expression start-index="100" stop-index="109">
                                        <left>
                                            <column name="col1" start-index="100" stop-index="103"/>
                                        </left>
                                        <right>
                                            <literal-expression value="XYZ" start-index="105" stop-index="109"/>
                                        </right>
                                        <operator>=</operator>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </subquery-table>
            </function-table>
        </from>
        <where start-index="112" stop-index="123">
            <expr>
                <binary-operation-expression start-index="118" stop-index="123">
                    <left>
                        <column name="rn" start-index="118" stop-index="119"/>
                    </left>
                    <right>
                        <literal-expression value="1" start-index="123" stop-index="123"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </select>
</sql-parser-test-cases>
