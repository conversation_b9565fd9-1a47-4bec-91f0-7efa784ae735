<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<sql-parser-test-cases>
    <select sql-case-id="select_abs">
        <projections start-index="7" stop-index="13">
            <expression-projection text="ABS(-2)" start-index="7" stop-index="13">
                <expr>
                    <function function-name="ABS" start-index="7" stop-index="13" text="ABS(-2)">
                        <parameter>
                            <literal-expression value="-2" start-index="11" stop-index="12"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_acos">
        <projections start-index="7" stop-index="13">
            <expression-projection text="ACOS(0)" start-index="7" stop-index="13">
                <expr>
                    <function function-name="ACOS" start-index="7" stop-index="13" text="ACOS(0)">
                        <parameter>
                            <literal-expression value="0" start-index="12" stop-index="12"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_asin">
        <projections start-index="7" stop-index="15">
            <expression-projection text="ASIN(0.2)" start-index="7" stop-index="15">
                <expr>
                    <function function-name="ASIN" start-index="7" stop-index="15" text="ASIN(0.2)">
                        <parameter>
                            <literal-expression value="0.2" start-index="12" stop-index="14"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <sql-case id="select_atan" value="SELECT ATAN(2)" db-types="MySQL"/>
    <select sql-case-id="select_atan">
        <projections start-index="7" stop-index="13">
            <expression-projection text="ATAN(2)" start-index="7" stop-index="13">
                <expr>
                    <function function-name="ATAN" start-index="7" stop-index="13" text="ATAN(2)">
                        <parameter>
                            <literal-expression value="2" start-index="12" stop-index="12"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_interval">
        <projections start-index="7" stop-index="42">
            <expression-projection text="INTERVAL(23, 1, 15, 17, 30, 44, 200)" start-index="7" stop-index="42">
                <expr>
                    <function function-name="INTERVAL" start-index="7" stop-index="42"
                              text="INTERVAL(23, 1, 15, 17, 30, 44, 200)">
                        <parameter>
                            <literal-expression value="23" start-index="16" stop-index="17"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="20" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="15" start-index="23" stop-index="24"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="17" start-index="27" stop-index="28"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="30" start-index="31" stop-index="32"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="44" start-index="35" stop-index="36"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="200" start-index="39" stop-index="41"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_pow">
        <projections start-index="7" stop-index="14">
            <expression-projection text="POW(2,2)" start-index="7" stop-index="14">
                <expr>
                    <function function-name="POW" start-index="7" stop-index="14" text="POW(2,2)">
                        <parameter>
                            <literal-expression value="2" start-index="11" stop-index="11"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="2" start-index="13" stop-index="13"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_power">
        <projections start-index="7" stop-index="16">
            <expression-projection text="POWER(2,2)" start-index="7" stop-index="16">
                <expr>
                    <function function-name="POWER" start-index="7" stop-index="16" text="POWER(2,2)">
                        <parameter>
                            <literal-expression value="2" start-index="13" stop-index="13"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="2" start-index="15" stop-index="15"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_rand">
        <projections start-index="7" stop-index="12">
            <expression-projection text="RAND()" start-index="7" stop-index="12">
                <expr>
                    <function function-name="RAND" start-index="7" stop-index="12" text="RAND()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_radians">
        <projections start-index="7" stop-index="17">
            <expression-projection text="RADIANS(90)" start-index="7" stop-index="17">
                <expr>
                    <function function-name="RADIANS" start-index="7" stop-index="17" text="RADIANS(90)">
                        <parameter>
                            <literal-expression value="90" start-index="15" stop-index="16"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_least">
        <projections start-index="7" stop-index="17">
            <expression-projection start-index="7" stop-index="17" text="LEAST(2, 0)">
                <expr>
                    <function start-index="7" stop-index="17" function-name="LEAST" text="LEAST(2, 0)">
                        <parameter>
                            <literal-expression start-index="13" stop-index="13" value="2"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="16" stop-index="16" value="0"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ln">
        <projections start-index="7" stop-index="11">
            <expression-projection start-index="7" stop-index="11" text="LN(2)">
                <expr>
                    <function start-index="7" stop-index="11" function-name="LN" text="LN(2)">
                        <parameter>
                            <literal-expression start-index="10" stop-index="10" value="2"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
</sql-parser-test-cases>
