<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_into_before_from" parameters="1">
        <from start-index="30" stop-index="36">
            <simple-table name="t_order" start-index="30" stop-index="36"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="12">
            <column-projection name="status" start-index="7" stop-index="12"/>
        </projections>
        <where start-index="38" stop-index="55">
            <expr>
                <binary-operation-expression start-index="44" stop-index="55">
                    <left>
                        <column name="order_id" start-index="44" stop-index="51"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="55" stop-index="55"/>
                        <parameter-marker-expression parameter-index="0" start-index="55" stop-index="55"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_into_after_from" parameters="1">
        <from start-index="19" stop-index="25">
            <simple-table name="t_order" start-index="19" stop-index="25"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="12">
            <column-projection name="status" start-index="7" stop-index="12"/>
        </projections>
        <where start-index="27" stop-index="44">
            <expr>
                <binary-operation-expression start-index="33" stop-index="44">
                    <left>
                        <column name="order_id" start-index="33" stop-index="40"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="44" stop-index="44"/>
                        <parameter-marker-expression parameter-index="0" start-index="44" stop-index="44"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_into_multi_variable" parameters="1">
        <from start-index="28" stop-index="34">
            <simple-table name="t_order" start-index="28" stop-index="34"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="21">
            <column-projection name="user_id" start-index="7" stop-index="13"/>
            <column-projection name="status" start-index="16" stop-index="21"/>
        </projections>
        <where start-index="36" stop-index="53">
            <expr>
                <binary-operation-expression start-index="42" stop-index="53">
                    <left>
                        <column name="order_id" start-index="42" stop-index="49"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="53" stop-index="53"/>
                        <parameter-marker-expression parameter-index="0" start-index="53" stop-index="53"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_into_out_file" parameters="2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <limit start-index="22" stop-index="28">
            <row-count value="2" parameter-index="0" start-index="28" stop-index="28"/>
        </limit>
    </select>

    <select sql-case-id="select_into_out_file_with_charset" parameters="2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <limit start-index="22" stop-index="28">
            <row-count value="2" parameter-index="0" start-index="28" stop-index="28"/>
        </limit>
    </select>

    <select sql-case-id="select_into_out_file_with_fields" parameters="2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <limit start-index="22" stop-index="28">
            <row-count value="2" parameter-index="0" start-index="28" stop-index="28"/>
        </limit>
    </select>

    <select sql-case-id="select_into_out_file_with_fields_and_escaped" parameters="2">
        <from start-index="28" stop-index="34">
            <simple-table name="t_order" start-index="28" stop-index="34"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="21">
            <column-projection name="user_id" start-index="7" stop-index="13"/>
            <column-projection name="status" start-index="16" stop-index="21"/>
        </projections>
        <limit start-index="36" stop-index="42">
            <row-count value="2" parameter-index="0" start-index="42" stop-index="42"/>
        </limit>
    </select>

    <select sql-case-id="select_into_out_file_with_lines" parameters="2">
        <from start-index="14" stop-index="20">
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <limit start-index="22" stop-index="28">
            <row-count value="2" parameter-index="0" start-index="28" stop-index="28"/>
        </limit>
    </select>

    <select sql-case-id="select_into_with_lock_after_into" parameters="1">
        <from start-index="19" stop-index="25">
            <simple-table name="t_order" start-index="19" stop-index="25"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="12">
            <column-projection name="status" start-index="7" stop-index="12"/>
        </projections>
        <where start-index="27" stop-index="44">
            <expr>
                <binary-operation-expression start-index="33" stop-index="44">
                    <left>
                        <column name="order_id" start-index="33" stop-index="40"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="44" stop-index="44"/>
                        <parameter-marker-expression parameter-index="0" start-index="44" stop-index="44"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <lock start-index="57" stop-index="66"/>
    </select>

    <select sql-case-id="select_into_with_lock_before_into" parameters="1">
        <from start-index="19" stop-index="25">
            <simple-table name="t_order" start-index="19" stop-index="25"/>
        </from>
        <projections distinct-row="false" start-index="7" stop-index="12">
            <column-projection name="status" start-index="7" stop-index="12"/>
        </projections>
        <where start-index="27" stop-index="44">
            <expr>
                <binary-operation-expression start-index="33" stop-index="44">
                    <left>
                        <column name="order_id" start-index="33" stop-index="40"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="44" stop-index="44"/>
                        <parameter-marker-expression parameter-index="0" start-index="44" stop-index="44"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <lock start-index="46" stop-index="55"/>
    </select>

    <select sql-case-id="select_into_param_without_at">
        <projections start-index="7" stop-index="7">
            <expression-projection text="1" start-index="7" stop-index="7"/>
        </projections>
    </select>

    <select sql-case-id="select_into_with_variable">
        <projections start-index="7" stop-index="17" literal-start-index="7" literal-stop-index="17">
            <column-projection name="select_list" start-index="7" stop-index="17" literal-start-index="7"
                               literal-stop-index="17"/>
        </projections>
        <from>
            <simple-table name="table_or_view_name" start-index="50" stop-index="67" literal-start-index="50"
                          literal-stop-index="67"/>
        </from>
    </select>

    <select sql-case-id="select_into_table_with_try_cast_function">
        <projections start-index="7" stop-index="108">
            <column-projection name="temperature" start-index="7" stop-index="25">
                <owner name="machine" start-index="7" stop-index="13"/>
            </column-projection>
            <expression-projection
                    text="udf.ASAEdgeUDFDemo_Class1_SquareFunction(try_cast(machine.temperature as bigint))"
                    start-index="28" stop-index="108">
                <expr>
                    <function function-name="udf.ASAEdgeUDFDemo_Class1_SquareFunction"
                              text="udf.ASAEdgeUDFDemo_Class1_SquareFunction(try_cast(machine.temperature as bigint))"
                              start-index="28" stop-index="108">
                        <parameter>
                            <function function-name="try_cast" text="try_cast(machine.temperature as bigint)"
                                      start-index="69" stop-index="107">
                                <parameter>
                                    <column name="temperature" start-index="78" stop-index="96">
                                        <owner name="machine" start-index="78" stop-index="84"/>
                                    </column>
                                </parameter>
                                <parameter>
                                    <data-type value="bigint" start-index="101" stop-index="106"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <into>
            <simple-table name="Output" start-index="115" stop-index="120"/>
        </into>
        <from>
            <simple-table name="Input" start-index="127" stop-index="131"/>
        </from>
    </select>

    <select sql-case-id="select_into_table_before_from">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <into>
            <simple-table name="NewProducts" start-index="14" stop-index="28">
                <owner name="dbo" start-index="14" stop-index="16"/>
            </simple-table>
        </into>
        <from start-index="35" stop-index="52">
            <simple-table name="Product" start-index="35" stop-index="52">
                <owner name="Production" start-index="35" stop-index="44"/>
            </simple-table>
        </from>
        <where start-index="54" stop-index="95">
            <expr>
                <binary-operation-expression start-index="60" stop-index="95">
                    <left>
                        <binary-operation-expression start-index="60" stop-index="74">
                            <left>
                                <column name="ListPrice" start-index="60" stop-index="68"/>
                            </left>
                            <right>
                                <common-expression literal-text="$25" start-index="72" stop-index="74"/>
                            </right>
                            <operator>&gt;</operator>
                        </binary-operation-expression>
                    </left>
                    <right>
                        <binary-operation-expression start-index="80" stop-index="95">
                            <left>
                                <column name="ListPrice" start-index="80" stop-index="88"/>
                            </left>
                            <right>
                                <common-expression literal-text="$100" start-index="92" stop-index="95"/>
                            </right>
                            <operator>&lt;</operator>
                        </binary-operation-expression>
                    </right>
                    <operator>AND</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_into_simple_table">
        <projections start-index="7" stop-index="33">
            <column-projection name="film_id" start-index="7" stop-index="13"/>
            <column-projection name="title" start-index="16" stop-index="20"/>
            <column-projection name="rental_rate" start-index="23" stop-index="33"/>
        </projections>
        <from>
            <simple-table name="film" start-index="58" stop-index="61"/>
        </from>
        <into>
            <simple-table name="film_r" start-index="46" stop-index="51"/>
        </into>
        <where start-index="63" stop-index="104">
            <expr>
                <binary-operation-expression start-index="69" stop-index="104">
                    <left>
                        <binary-operation-expression start-index="69" stop-index="80">
                            <left>
                                <column name="rating" start-index="69" stop-index="74"/>
                            </left>
                            <right>
                                <literal-expression value="R" start-index="78" stop-index="80"/>
                            </right>
                            <operator>=</operator>
                        </binary-operation-expression>
                    </left>
                    <right>
                        <binary-operation-expression start-index="86" stop-index="104">
                            <left>
                                <column name="rental_duration" start-index="86" stop-index="100"/>
                            </left>
                            <right>
                                <literal-expression value="5" start-index="104" stop-index="104"/>
                            </right>
                            <operator>=</operator>
                        </binary-operation-expression>
                    </right>
                    <operator>AND</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="title" start-index="115" stop-index="119" order-direction="ASC"/>
        </order-by>
    </select>

    <select sql-case-id="select_into_temp_table">
        <projections start-index="7" stop-index="28">
            <column-projection name="film_id" start-index="7" stop-index="13"/>
            <column-projection name="title" start-index="16" stop-index="20"/>
            <column-projection name="length" start-index="23" stop-index="28"/>
        </projections>
        <from>
            <simple-table name="film" start-index="62" stop-index="65"/>
        </from>
        <into>
            <simple-table name="short_film" start-index="46" stop-index="55"/>
        </into>
        <where start-index="67" stop-index="83">
            <expr>
                <binary-operation-expression start-index="73" stop-index="83">
                    <left>
                        <column name="length" start-index="73" stop-index="78"/>
                    </left>
                    <right>
                        <literal-expression value="60" start-index="82" stop-index="83"/>
                    </right>
                    <operator>&lt;</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="title" start-index="94" stop-index="98" order-direction="ASC"/>
        </order-by>
    </select>
</sql-parser-test-cases>
