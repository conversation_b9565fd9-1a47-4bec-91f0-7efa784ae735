<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <insert sql-case-id="replace_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="57" stop-index="57"/>
                    <literal-expression value="1" start-index="57" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="60" stop-index="60"/>
                    <literal-expression value="1" start-index="60" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="63" stop-index="63"/>
                    <literal-expression value="init" start-index="63" stop-index="68"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_now_function" parameters="1, 1, 'init'">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="26" stop-index="76">
            <column name="item_id" start-index="27" stop-index="33"/>
            <column name="order_id" start-index="36" stop-index="43"/>
            <column name="user_id" start-index="46" stop-index="52"/>
            <column name="status" start-index="55" stop-index="60"/>
            <column name="creation_date" start-index="63" stop-index="75"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="86" stop-index="86"/>
                    <literal-expression value="1" start-index="86" stop-index="86"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="89" stop-index="89"/>
                    <literal-expression value="1" start-index="89" stop-index="89"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="92" stop-index="92"/>
                    <literal-expression value="init" start-index="92" stop-index="97"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="95" stop-index="103" literal-start-index="100"
                                        literal-stop-index="108"/>
                </assignment-value>
                <assignment-value>
                    <function function-name="now" text="now()" start-index="106" stop-index="110"
                              literal-start-index="111" literal-stop-index="115"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_without_parameters">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0"/>
                    <literal-expression value="1" start-index="57" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1"/>
                    <literal-expression value="1" start-index="60" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2"/>
                    <literal-expression value="replace" start-index="63" stop-index="71"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_multiple_values">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="57" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="60" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="63" stop-index="71"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="76" stop-index="76"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2" start-index="79" stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace2" start-index="82" stop-index="91"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_special_characters">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="13" stop-index="21"/>
        <columns start-index="23" stop-index="55">
            <column name="order_id" start-delimiter="`" end-delimiter="`" start-index="24" stop-index="33"/>
            <column name="user_id" start-delimiter="`" end-delimiter="`" start-index="36" stop-index="44"/>
            <column name="status" start-delimiter="`" end-delimiter="`" start-index="47" stop-index="54"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1" start-index="65" stop-index="65"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="68" stop-index="68"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="71" stop-index="79"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_all_placeholders_for_table_identifier" parameters="1, 1, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="71">
            <column name="order_id" start-index="22" stop-index="37">
                <owner name="t_order" start-index="22" stop-index="28"/>
            </column>
            <column name="user_id" start-index="40" stop-index="54">
                <owner name="t_order" start-index="40" stop-index="46"/>
            </column>
            <column name="status" start-index="57" stop-index="70">
                <owner name="t_order" start-index="57" stop-index="63"/>
            </column>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="81" stop-index="81"/>
                    <literal-expression value="1" start-index="81" stop-index="81"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="84" stop-index="84"/>
                    <literal-expression value="1" start-index="84" stop-index="84"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="87" stop-index="87"/>
                    <literal-expression value="init" start-index="87" stop-index="92"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_without_columns_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="20"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="29" stop-index="29"/>
                    <literal-expression value="1" start-index="29" stop-index="29"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="32" stop-index="32"/>
                    <literal-expression value="1" start-index="32" stop-index="32"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="35" stop-index="35"/>
                    <literal-expression value="init" start-index="35" stop-index="40"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_set_with_all_placeholders" parameters="1, 1, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <set start-index="21" stop-index="61" literal-stop-index="66">
            <assignment>
                <column name="order_id" start-index="25" stop-index="32"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="36" stop-index="36"/>
                    <literal-expression value="1" literal-start-index="36" literal-stop-index="36"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="39" stop-index="45"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="49" stop-index="49"/>
                    <literal-expression value="1" literal-start-index="49" literal-stop-index="49"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="52" stop-index="57"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="61" stop-index="61"/>
                    <literal-expression value="init" literal-start-index="61" literal-stop-index="66"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="replace_with_partial_placeholders" parameters="1, 1">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="57" stop-index="57"/>
                    <literal-expression value="1" start-index="57" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="60" stop-index="60"/>
                    <literal-expression value="1" start-index="60" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="63" stop-index="71"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_set_with_partial_placeholders" parameters="1, 1">
        <table name="t_order" start-index="13" stop-index="19"/>
        <set start-index="21" stop-index="69">
            <assignment>
                <column name="order_id" start-index="25" stop-index="32"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="36" stop-index="36"/>
                    <literal-expression value="1" start-index="36" stop-index="36"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="39" stop-index="45"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="49" stop-index="49"/>
                    <literal-expression value="1" start-index="49" stop-index="49"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="52" stop-index="57"/>
                <assignment-value>
                    <literal-expression value="replace" start-index="61" stop-index="69"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="replace_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="75">
            <column name="item_id" start-index="26" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
            <column name="status" start-index="54" stop-index="59"/>
            <column name="creation_date" start-index="62" stop-index="74"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="85" stop-index="85"/>
                    <literal-expression value="10000" start-index="85" stop-index="89"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="88" stop-index="88"/>
                    <literal-expression value="1000" start-index="92" stop-index="95"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="91" stop-index="91"/>
                    <literal-expression value="10" start-index="98" stop-index="99"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="94" stop-index="102" literal-start-index="102"
                                        literal-stop-index="110"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="105" stop-index="116" literal-start-index="113"
                                        literal-stop-index="124"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="66">
            <column name="order_id" start-index="26" stop-index="33"/>
            <column name="user_id" start-index="36" stop-index="42"/>
            <column name="status" start-index="45" stop-index="50"/>
            <column name="creation_date" start-index="53" stop-index="65"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="76" stop-index="76"/>
                    <literal-expression value="1000" start-index="76" stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="79" stop-index="79"/>
                    <literal-expression value="10" start-index="82" stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="82" stop-index="90" literal-start-index="86"
                                        literal-stop-index="94"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="93" stop-index="104" literal-start-index="97"
                                        literal-stop-index="108"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_set_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <set start-index="26" stop-index="115" literal-stop-index="123">
            <assignment>
                <column name="item_id" start-index="30" stop-index="36"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="40" stop-index="40"/>
                    <literal-expression value="10000" start-index="40" stop-index="44"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="order_id" start-index="43" stop-index="50" literal-start-index="47"
                        literal-stop-index="54"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="54" stop-index="54"/>
                    <literal-expression value="1000" start-index="58" stop-index="61"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="57" stop-index="63" literal-start-index="64"
                        literal-stop-index="70"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="67" stop-index="67"/>
                    <literal-expression value="10" start-index="74" stop-index="75"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="70" stop-index="75" literal-start-index="78"
                        literal-stop-index="83"/>
                <assignment-value>
                    <literal-expression value="replace" start-index="79" stop-index="87" literal-start-index="87"
                                        literal-stop-index="95"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="creation_date" start-index="90" stop-index="102" literal-start-index="98"
                        literal-stop-index="110"/>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="104" stop-index="115" literal-start-index="112"
                                        literal-stop-index="123"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="replace_set_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <set start-index="26" stop-index="102" literal-stop-index="106">
            <assignment>
                <column name="order_id" start-index="30" stop-index="37"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="41" stop-index="41"/>
                    <literal-expression value="1000" start-index="41" stop-index="44"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="44" stop-index="50" literal-start-index="47"
                        literal-stop-index="53"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="54" stop-index="54"/>
                    <literal-expression value="10" start-index="57" stop-index="58"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="57" stop-index="62" literal-start-index="61"
                        literal-stop-index="66"/>
                <assignment-value>
                    <literal-expression value="replace" start-index="66" stop-index="74" literal-start-index="70"
                                        literal-stop-index="78"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="creation_date" start-index="77" stop-index="89" literal-start-index="81"
                        literal-stop-index="93"/>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="91" stop-index="102" literal-start-index="95"
                                        literal-stop-index="106"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="replace_set_with_all_placeholders_for_table_identifier" parameters="1, 1, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <set start-index="21" stop-index="85" literal-stop-index="90">
            <assignment>
                <column name="order_id" start-index="25" stop-index="40">
                    <owner name="t_order" start-index="25" stop-index="31"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="44" stop-index="44"/>
                    <literal-expression value="1" start-index="44" stop-index="44"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="user_id" start-index="47" stop-index="61">
                    <owner name="t_order" start-index="47" stop-index="53"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="65" stop-index="65"/>
                    <literal-expression value="1" start-index="65" stop-index="65"/>
                </assignment-value>
            </assignment>
            <assignment>
                <column name="status" start-index="68" stop-index="81">
                    <owner name="t_order" start-index="68" stop-index="74"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="85" stop-index="85"/>
                    <literal-expression value="init" start-index="85" stop-index="90"/>
                </assignment-value>
            </assignment>
        </set>
    </insert>

    <insert sql-case-id="replace_with_batch" parameters="1000, 10, 'init', 1100, 11, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="57" stop-index="57"/>
                    <literal-expression value="1000" start-index="57" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="60" stop-index="60"/>
                    <literal-expression value="10" start-index="63" stop-index="64"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="63" stop-index="63"/>
                    <literal-expression value="init" start-index="67" stop-index="72"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="68" stop-index="68"/>
                    <literal-expression value="1100" start-index="77" stop-index="80"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="71" stop-index="71"/>
                    <literal-expression value="11" start-index="83" stop-index="84"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="5" start-index="74" stop-index="74"/>
                    <literal-expression value="init" start-index="87" stop-index="92"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_batch_and_irregular_parameters" parameters="1, 2, 2, 'init'">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="57" stop-index="57"/>
                    <literal-expression value="1" start-index="57" stop-index="57"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="1" start-index="60" stop-index="60"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="63" stop-index="71"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="76" stop-index="76"/>
                    <literal-expression value="2" start-index="76" stop-index="76"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="79" stop-index="79"/>
                    <literal-expression value="2" start-index="79" stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="82" stop-index="82"/>
                    <literal-expression value="init" start-index="82" stop-index="87"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <!--<replace sql-case-id="replace_with_batch_and_composite_expression" parameters="1, 1, 'init', 2, 2, 'init'">-->
    <!--<table name="t_order" start-index="13" stop-index="19" />-->
    <!--</replace>-->

    <insert sql-case-id="replace_with_batch_and_with_generate_key_column" parameters="10000, 1000, 10, 10010, 1001, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="75">
            <column name="item_id" start-index="26" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
            <column name="status" start-index="54" stop-index="59"/>
            <column name="creation_date" start-index="62" stop-index="74"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="85" stop-index="85"/>
                    <literal-expression value="10000" start-index="85" stop-index="89"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="88" stop-index="88"/>
                    <literal-expression value="1000" start-index="92" stop-index="95"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="91" stop-index="91"/>
                    <literal-expression value="10" start-index="98" stop-index="99"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="94" stop-index="102" literal-start-index="102"
                                        literal-stop-index="110"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="105" stop-index="116" literal-start-index="113"
                                        literal-stop-index="124"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="121" stop-index="121"/>
                    <literal-expression value="10010" start-index="129" stop-index="133"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="124" stop-index="124"/>
                    <literal-expression value="1001" start-index="136" stop-index="139"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="5" start-index="127" stop-index="127"/>
                    <literal-expression value="10" start-index="142" stop-index="143"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="130" stop-index="138" literal-start-index="146"
                                        literal-stop-index="154"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="141" stop-index="152" literal-start-index="157"
                                        literal-stop-index="168"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_batch_and_without_generate_key_column" parameters="1000, 10, 1001, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="66">
            <column name="order_id" start-index="26" stop-index="33"/>
            <column name="user_id" start-index="36" stop-index="42"/>
            <column name="status" start-index="45" stop-index="50"/>
            <column name="creation_date" start-index="53" stop-index="65"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="76" stop-index="76"/>
                    <literal-expression value="1000" start-index="76" stop-index="79"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="79" stop-index="79"/>
                    <literal-expression value="10" start-index="82" stop-index="83"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="82" stop-index="90" literal-start-index="86"
                                        literal-stop-index="94"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="93" stop-index="104" literal-start-index="97"
                                        literal-stop-index="108"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="109" stop-index="109"/>
                    <literal-expression value="1001" start-index="113" stop-index="116"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="112" stop-index="112"/>
                    <literal-expression value="10" start-index="119" stop-index="120"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="115" stop-index="123" literal-start-index="123"
                                        literal-stop-index="131"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="126" stop-index="137" literal-start-index="134"
                                        literal-stop-index="145"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_without_columns_and_with_generate_key_column" parameters="10000, 1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="25"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="33" stop-index="33"/>
                    <literal-expression value="10000" literal-start-index="33" literal-stop-index="37"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="36" stop-index="36"/>
                    <literal-expression value="1000" start-index="36" stop-index="41" literal-start-index="40"
                                        literal-stop-index="43"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="39" stop-index="39"/>
                    <literal-expression value="10" start-index="39" stop-index="42" literal-start-index="46"
                                        literal-stop-index="47"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="42" stop-index="50" literal-start-index="50"
                                        literal-stop-index="58"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="53" stop-index="64" literal-start-index="61"
                                        literal-stop-index="72"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_without_columns_and_without_generate_key_column" parameters="1000, 10">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="25"/>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="33" stop-index="33"/>
                    <literal-expression value="1000" start-index="33" stop-index="36"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="36" stop-index="36"/>
                    <literal-expression value="10" start-index="39" stop-index="40"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="replace" start-index="39" stop-index="47" literal-start-index="43"
                                        literal-stop-index="51"/>
                </assignment-value>
                <assignment-value>
                    <literal-expression value="2017-08-08" start-index="50" stop-index="61" literal-start-index="54"
                                        literal-stop-index="65"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <!-- // TODO
    <replace sql-case-id="assertInsertSelect" sql="REPLACE INTO `order` ('order_id', 'state') (SELECT 1, 'RUNNING' FROM dual UNION ALL SELECT 2, 'RUNNING' FROM dual )"">
        <table name="order" />
        <condition-contexts>
           <condition-context/>
        </condition-contexts>
    </replace>
    -->

    <insert sql-case-id="replace_with_one_auto_increment_column">
        <table name="t_auto_increment_table" start-index="13" stop-index="34"/>
        <columns start-index="35" stop-index="35"/>
        <values>
            <value/>
        </values>
    </insert>

    <insert sql-case-id="replace_with_double_value">
        <table name="t_double_test" start-index="13" stop-index="25"/>
        <columns start-index="26" stop-index="31">
            <column name="col1" start-index="27" stop-index="30"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="1.22" start-index="40" stop-index="43"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_null_value">
        <table name="t_null_value_test" start-index="13" stop-index="29"/>
        <columns start-index="30" stop-index="35">
            <column name="col1" start-index="31" stop-index="34"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="null" start-index="44" stop-index="47"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_blob_value">
        <table name="t_blob_value_test" start-index="13" stop-index="29"/>
        <columns start-index="30" stop-index="35">
            <column name="col1" start-index="31" stop-index="34"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <common-expression text="_BINARY'This is a binary value.'" start-index="44" stop-index="75"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_function" parameters="1000, 10">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="52">
            <column name="present_date" start-index="21" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="curdate" text="curdate()" start-index="62" stop-index="70"
                              literal-start-index="62" literal-stop-index="70"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="73" stop-index="73"/>
                    <literal-expression value="1000" start-index="73" stop-index="76"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="76" stop-index="76"/>
                    <literal-expression value="10" start-index="79" stop-index="80"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_unix_timestamp_function" parameters="'2019-10-19', 1000, 10">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="46">
            <column name="status" start-index="21" stop-index="26"/>
            <column name="order_id" start-index="29" stop-index="36"/>
            <column name="user_id" start-index="39" stop-index="45"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="unix_timestamp" text="unix_timestamp(?)"
                              literal-text="unix_timestamp('2019-10-19')" start-index="56" stop-index="72"
                              literal-start-index="56" literal-stop-index="83">
                        <parameter>
                            <literal-expression literal-start-index="71" literal-stop-index="82" value="2019-10-19"/>
                            <parameter-marker-expression parameter-index="0" start-index="71" stop-index="71"/>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="75" stop-index="75"/>
                    <literal-expression value="1000" start-index="86" stop-index="89"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="78" stop-index="78"/>
                    <literal-expression value="10" start-index="92" stop-index="93"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_str_to_date" parameters="'2019-12-10', 1, 1">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="52">
            <column name="present_date" start-index="21" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="90" stop-index="90"/>
                    <function function-name="str_to_date" text="str_to_date(?, '%Y-%m-%d')"
                              literal-text="str_to_date('2019-12-10', '%Y-%m-%d')" start-index="62" stop-index="87"
                              literal-start-index="62" literal-stop-index="98">
                        <parameter>
                            <parameter-marker-expression parameter-index="0" start-index="74" stop-index="74"/>
                            <literal-expression value="2019-12-10" start-index="77" stop-index="77"
                                                literal-start-index="74" literal-stop-index="85"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="%Y-%m-%d" start-index="77" stop-index="86"
                                                literal-start-index="88" literal-stop-index="97"/>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="90" stop-index="90"/>
                    <literal-expression value="1" start-index="101" stop-index="101"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="93" stop-index="93"/>
                    <literal-expression value="1" start-index="104" stop-index="104"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_with_str_date_add" parameters="1, 1, 1">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="52">
            <column name="present_date" start-index="21" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <function function-name="date_add" text="date_add(now(),interval ? second)"
                              literal-text="date_add(now(),interval 1 second)" start-index="62" stop-index="94"
                              literal-start-index="62" literal-stop-index="94">
                        <parameter>
                            <function function-name="now" text="now()" start-index="71" stop-index="75"/>
                        </parameter>
                        <parameter>
                            <function function-name="interval" text="interval" start-index="77" stop-index="84">
                                <parameter>
                                    <parameter-marker-expression parameter-index="0" start-index="86" stop-index="86"/>
                                    <literal-expression value="1" start-index="86" stop-index="86"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="second" start-index="88" stop-index="93"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="1" start-index="97" stop-index="97"/>
                    <literal-expression value="1" start-index="97" stop-index="97"/>
                </assignment-value>
                <assignment-value>
                    <parameter-marker-expression parameter-index="2" start-index="100" stop-index="100"/>
                    <literal-expression value="1" start-index="100" stop-index="100"/>
                </assignment-value>
            </value>
        </values>
    </insert>

    <insert sql-case-id="replace_select_with_all_columns" parameters="100">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="21" stop-index="47">
            <column name="order_id" start-index="22" stop-index="29"/>
            <column name="user_id" start-index="32" stop-index="38"/>
            <column name="status" start-index="41" stop-index="46"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order" start-index="87" stop-index="93"/>
            </from>
            <projections start-index="56" stop-index="80">
                <column-projection name="order_id" start-index="56" stop-index="63"/>
                <column-projection name="user_id" start-index="66" stop-index="72"/>
                <column-projection name="status" start-index="75" stop-index="80"/>
            </projections>
            <where start-index="95" stop-index="112" literal-stop-index="114">
                <expr>
                    <binary-operation-expression start-index="101" stop-index="112" literal-stop-index="114">
                        <left>
                            <column name="order_id" start-index="101" stop-index="108"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="112" stop-index="114"/>
                            <parameter-marker-expression parameter-index="0" start-index="112" stop-index="112"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="replace_select_without_columns" parameters="100">
        <table name="t_order" start-index="13" stop-index="19"/>
        <columns start-index="20" stop-index="20"/>
        <select parameters="100">
            <from>
                <simple-table name="t_order" start-index="59" stop-index="65"/>
            </from>
            <projections start-index="28" stop-index="52">
                <column-projection name="order_id" start-index="28" stop-index="35"/>
                <column-projection name="user_id" start-index="38" stop-index="44"/>
                <column-projection name="status" start-index="47" stop-index="52"/>
            </projections>
            <where start-index="67" stop-index="84" literal-stop-index="86">
                <expr>
                    <binary-operation-expression start-index="73" stop-index="84" literal-stop-index="86">
                        <left>
                            <column name="order_id" start-index="73" stop-index="80"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="84" stop-index="86"/>
                            <parameter-marker-expression parameter-index="0" start-index="84" stop-index="84"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="replace_select_with_generate_key_column" parameters="100">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="75">
            <column name="item_id" start-index="26" stop-index="32"/>
            <column name="order_id" start-index="35" stop-index="42"/>
            <column name="user_id" start-index="45" stop-index="51"/>
            <column name="status" start-index="54" stop-index="59"/>
            <column name="creation_date" start-index="62" stop-index="74"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order_item" start-index="140" stop-index="151"/>
            </from>
            <projections start-index="84" stop-index="133">
                <column-projection name="item_id" start-index="84" stop-index="90"/>
                <column-projection name="order_id" start-index="93" stop-index="100"/>
                <column-projection name="user_id" start-index="103" stop-index="109"/>
                <expression-projection text="insert" start-index="112" stop-index="119"/>
                <expression-projection text="2017-08-08" start-index="122" stop-index="133"/>
            </projections>
            <where start-index="153" stop-index="169" literal-stop-index="171">
                <expr>
                    <binary-operation-expression start-index="159" stop-index="169" literal-stop-index="171">
                        <left>
                            <column name="item_id" start-index="159" stop-index="165"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="169" stop-index="171"/>
                            <parameter-marker-expression parameter-index="0" start-index="169" stop-index="169"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>

    <insert sql-case-id="replace_select_without_generate_key_column" parameters="100">
        <table name="t_order_item" start-index="13" stop-index="24"/>
        <columns start-index="25" stop-index="66">
            <column name="order_id" start-index="26" stop-index="33"/>
            <column name="user_id" start-index="36" stop-index="42"/>
            <column name="status" start-index="45" stop-index="50"/>
            <column name="creation_date" start-index="53" stop-index="65"/>
        </columns>
        <select parameters="100">
            <from>
                <simple-table name="t_order_item" start-index="122" stop-index="133"/>
            </from>
            <projections start-index="75" stop-index="115">
                <column-projection name="order_id" start-index="75" stop-index="82"/>
                <column-projection name="user_id" start-index="85" stop-index="91"/>
                <expression-projection text="insert" start-index="94" stop-index="101"/>
                <expression-projection text="2017-08-08" start-index="104" stop-index="115"/>
            </projections>
            <where start-index="135" stop-index="152" literal-stop-index="154">
                <expr>
                    <binary-operation-expression start-index="141" stop-index="152" literal-stop-index="154">
                        <left>
                            <column name="order_id" start-index="141" stop-index="148"/>
                        </left>
                        <operator>=</operator>
                        <right>
                            <literal-expression value="100" start-index="152" stop-index="154"/>
                            <parameter-marker-expression parameter-index="0" start-index="152" stop-index="152"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </where>
        </select>
    </insert>
    <insert sql-case-id="replace_returning_expressions">
        <table name="t2" start-index="13" stop-index="14"/>
        <columns start-index="16" stop-index="19">
            <column name="id" start-index="17" stop-index="18"/>
        </columns>
        <values>
            <value>
                <assignment-value>
                    <literal-expression value="2" start-index="29" stop-index="29"/>
                </assignment-value>
            </value>
            <value>
                <assignment-value>
                    <literal-expression value="3" start-index="33" stop-index="33"/>
                </assignment-value>
            </value>
        </values>
        <returning start-index="36" stop-index="51">
            <projections start-index="46" stop-index="51">
                <column-projection name="id" start-index="46" stop-index="47"/>
                <expression-projection start-index="49" stop-index="51" text="t&amp;t">
                    <left>
                        <column name="t" start-index="49" stop-index="49"/>
                    </left>
                    <operator>&amp;</operator>
                    <right>
                        <column name="t" start-index="51" stop-index="51"/>
                    </right>
                </expression-projection>
            </projections>
        </returning>
    </insert>
</sql-parser-test-cases>
