<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_group_concat_with_order_by">
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
        <projections start-index="7" stop-index="42">
            <expression-projection text="GROUP_CONCAT(status ORDER BY status)" start-index="7" stop-index="42">
                <expr>
                    <function function-name="GROUP_CONCAT" start-index="7" stop-index="42"
                              text="GROUP_CONCAT(status ORDER BY status)">
                        <parameter>
                            <column name="status" start-index="20" stop-index="25"/>
                        </parameter>
                        <parameter>
                            <column name="status" start-index="36" stop-index="41"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_window_function">
        <from>
            <simple-table name="t_order" start-index="42" stop-index="48"/>
        </from>
        <projections start-index="7" stop-index="35">
            <column-projection name="order_id" start-index="7" stop-index="14"/>
            <expression-projection text="ROW_NUMBER() OVER()" start-index="17" stop-index="35">
                <expr>
                    <function function-name="ROW_NUMBER" start-index="17" stop-index="35" text="ROW_NUMBER() OVER()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_cast_function">
        <projections start-index="7" stop-index="27">
            <expression-projection text="CAST('1' AS UNSIGNED)" start-index="7" stop-index="27">
                <expr>
                    <function function-name="CAST" start-index="7" stop-index="27" text="CAST('1' AS UNSIGNED)">
                        <parameter>
                            <literal-expression value="1" start-index="12" stop-index="14"/>
                        </parameter>
                        <parameter>
                            <data-type value="UNSIGNED" start-index="19" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_cast">
        <projections start-index="7" stop-index="44">
            <expression-projection text="CAST(c AT TIME ZONE 'UTC' AS DATETIME)" start-index="7" stop-index="44">
                <expr>
                    <function function-name="CAST" start-index="7" stop-index="44"
                              text="CAST(c AT TIME ZONE 'UTC' AS DATETIME)">
                        <parameter>
                            <column name="c" start-index="12" stop-index="12"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="UTC" start-index="27" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <data-type value="DATETIME" start-index="36" stop-index="43"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_cast_multiset">
        <projections start-index="7" stop-index="119">
            <expression-projection
                    text="CAST(MULTISET(SELECT cust_address FROM customers c WHERE c.customer_id = cd.customer_id) as cust_address_tab_typ)"
                    start-index="7" stop-index="119">
                <expr>
                    <function function-name="CAST" start-index="7" stop-index="119"
                              text="CAST(MULTISET(SELECT cust_address FROM customers c WHERE c.customer_id = cd.customer_id) as cust_address_tab_typ)">
                        <parameter>
                            <subquery start-index="20" stop-index="94">
                                <select>
                                    <from start-index="46" stop-index="54">
                                        <simple-table name="customers" alias="c" start-index="46" stop-index="56"/>
                                    </from>
                                    <projections start-index="28" stop-index="39">
                                        <column-projection name="cust_address" start-index="28" stop-index="39"/>
                                    </projections>
                                    <where start-index="58" stop-index="93">
                                        <expr>
                                            <binary-operation-expression start-index="64" stop-index="93">
                                                <left>
                                                    <column name="customer_id" start-index="64" stop-index="76">
                                                        <owner start-index="64" stop-index="64" name="c"/>
                                                    </column>
                                                </left>
                                                <operator>=</operator>
                                                <right>
                                                    <column name="customer_id" start-index="80" stop-index="93">
                                                        <owner start-index="80" stop-index="81" name="cd"/>
                                                    </column>
                                                </right>
                                            </binary-operation-expression>
                                        </expr>
                                    </where>
                                </select>
                            </subquery>
                        </parameter>
                        <parameter>
                            <data-type value="cust_address_tab_typ" start-index="99" stop-index="118"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="customer" start-index="126" stop-index="133"/>
        </from>
    </select>
    <select sql-case-id="select_convert_function">
        <projections start-index="7" stop-index="33">
            <expression-projection text="CONVERT('2020-10-01', DATE)" start-index="7" stop-index="33">
                <expr>
                    <function function-name="CONVERT" start-index="7" stop-index="33"
                              text="CONVERT('2020-10-01', DATE)">
                        <parameter>
                            <literal-expression value="2020-10-01" start-index="15" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_position">
        <projections start-index="7" stop-index="36">
            <expression-projection text="POSITION('bar' IN 'foobarbar')" start-index="7" stop-index="36">
                <expr>
                    <function function-name="POSITION" start-index="7" stop-index="36"
                              text="POSITION('bar' IN 'foobarbar')">
                        <parameter>
                            <literal-expression value="bar" start-index="16" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="foobarbar" start-index="25" stop-index="35"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_substring">
        <projections start-index="7" stop-index="35">
            <expression-projection text="SUBSTRING('foobarbar' from 4)" start-index="7" stop-index="35">
                <expr>
                    <function function-name="SUBSTRING" start-index="7" stop-index="35"
                              text="SUBSTRING('foobarbar' from 4)">
                        <parameter>
                            <literal-expression value="foobarbar" start-index="17" stop-index="27"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="34" stop-index="34"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_substr">
        <projections start-index="7" stop-index="32">
            <expression-projection text="SUBSTR('foobarbar' from 4)" start-index="7" stop-index="32">
                <expr>
                    <function function-name="SUBSTR" start-index="7" stop-index="32" text="SUBSTR('foobarbar' from 4)">
                        <parameter>
                            <literal-expression value="foobarbar" start-index="14" stop-index="24"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="31" stop-index="31"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_extract">
        <projections start-index="7" stop-index="37">
            <expression-projection text="EXTRACT(YEAR FROM '2019-07-02')" start-index="7" stop-index="37">
                <expr>
                    <function function-name="EXTRACT" start-index="7" stop-index="37"
                              text="EXTRACT(YEAR FROM '2019-07-02')">
                        <parameter>
                            <literal-expression value="YEAR" start-index="15" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="2019-07-02" start-index="25" stop-index="36"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_extract_from_column">
        <projections start-index="7" stop-index="40">
            <expression-projection text="EXTRACT(YEAR FROM o.creation_date)" start-index="7" stop-index="40">
                <expr>
                    <function function-name="EXTRACT" start-index="7" stop-index="40"
                              text="EXTRACT(YEAR FROM o.creation_date)">
                        <parameter>
                            <literal-expression value="YEAR" start-index="15" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <column name="creation_date" start-index="25" stop-index="39">
                                <owner name="o" start-index="25" stop-index="25"/>
                            </column>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="47" stop-index="55" alias="o"/>
        </from>
    </select>
    <select sql-case-id="select_char">
        <projections start-index="7" stop-index="29">
            <expression-projection text="CHAR(77,121,83,81,'76')" start-index="7" stop-index="29">
                <expr>
                    <function function-name="CHAR" start-index="7" stop-index="29" text="CHAR(77,121,83,81,'76')">
                        <parameter>
                            <literal-expression value="77" start-index="12" stop-index="13"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="121" start-index="15" stop-index="17"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="83" start-index="19" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="81" start-index="22" stop-index="23"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="76" start-index="25" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_chr_using_nchar_cs">
        <from>
            <simple-table name="DUAL" start-index="37" stop-index="40"/>
        </from>
        <projections start-index="7" stop-index="30">
            <expression-projection text="CHR (196 USING NCHAR_CS)" start-index="7" stop-index="30">
                <expr>
                    <function function-name="CHR" start-index="7" stop-index="30" text="CHR (196 USING NCHAR_CS)">
                        <parameter>
                            <literal-expression value="196" start-index="12" stop-index="14"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_trim">
        <projections start-index="7" stop-index="22">
            <expression-projection text="TRIM('  bar   ')" start-index="7" stop-index="22">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="22" text="TRIM('  bar   ')">
                        <parameter>
                            <literal-expression value="  bar   " start-index="12" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_trim_with_both">
        <projections start-index="7" stop-index="33">
            <expression-projection text="TRIM(BOTH ' ' from ' bar ')" start-index="7" stop-index="33">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="33" text="TRIM(BOTH ' ' from ' bar ')">
                        <parameter>
                            <literal-expression value="BOTH" start-index="12" stop-index="15"/>
                        </parameter>
                        <parameter>
                            <literal-expression value=" " start-index="17" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <literal-expression value=" bar " start-index="26" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_with_trim_expr">
        <projections start-index="7" stop-index="27">
            <expression-projection text="TRIM('#' FROM `name`)" start-index="7" stop-index="27">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="27" text="TRIM('#' FROM `name`)">
                        <parameter>
                            <literal-expression value="#" start-index="12" stop-index="14"/>
                        </parameter>
                        <parameter>
                            <column name="name" start-delimiter="`" end-delimiter="`" start-index="21" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="34" stop-index="40"/>
        </from>
    </select>
    <select sql-case-id="select_with_trim_expr_and_both">
        <projections start-index="7" stop-index="32">
            <expression-projection text="TRIM(BOTH '#' FROM `name`)" start-index="7" stop-index="32">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="32" text="TRIM(BOTH '#' FROM `name`)">
                        <parameter>
                            <literal-expression value="BOTH" start-index="12" stop-index="15"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="#" start-index="17" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <column name="name" start-delimiter="`" end-delimiter="`" start-index="26" stop-index="31"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-delimiter="`" end-delimiter="`" start-index="39" stop-index="47"/>
        </from>
    </select>
    <select sql-case-id="select_with_trim_expr_from_expr">
        <projections start-index="7" stop-index="33">
            <expression-projection text="TRIM(remove_name FROM name)" start-index="7" stop-index="33">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="33" text="TRIM(remove_name FROM name)">
                        <parameter>
                            <column name="remove_name" start-index="12" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <column name="name" start-index="29" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="40" stop-index="46"/>
        </from>
    </select>
    <select sql-case-id="select_with_trim_expr_from_expr_and_both">
        <projections start-index="7" stop-index="42">
            <expression-projection text="TRIM(BOTH `remove_name` FROM `name`)" start-index="7" stop-index="42">
                <expr>
                    <function function-name="TRIM" start-index="7" stop-index="42"
                              text="TRIM(BOTH `remove_name` FROM `name`)">
                        <parameter>
                            <literal-expression value="BOTH" start-index="12" stop-index="15"/>
                        </parameter>
                        <parameter>
                            <column name="remove_name" start-delimiter="`" end-delimiter="`" start-index="17"
                                    stop-index="29"/>
                        </parameter>
                        <parameter>
                            <column name="name" start-delimiter="`" end-delimiter="`" start-index="36" stop-index="41"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-delimiter="`" end-delimiter="`" start-index="49" stop-index="57"/>
        </from>
    </select>
    <select sql-case-id="select_weight_string">
        <projections start-index="7" stop-index="26">
            <expression-projection text="WEIGHT_STRING('bar')" start-index="7" stop-index="26">
                <expr>
                    <function function-name="WEIGHT_STRING" start-index="7" stop-index="26" text="WEIGHT_STRING('bar')">
                        <parameter>
                            <literal-expression value="bar" start-index="21" stop-index="25"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_values">
        <from>
            <simple-table name="t_order" start-index="29" stop-index="35"/>
        </from>
        <projections start-index="7" stop-index="22">
            <expression-projection text="VALUES(order_id)" start-index="7" stop-index="22">
                <expr>
                    <function function-name="VALUES" start-index="7" stop-index="22" text="VALUES(order_id)">
                        <parameter>
                            <column name="order_id" start-index="14" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_current_user_brackets">
        <projections start-index="7" stop-index="20">
            <expression-projection text="CURRENT_USER()" start-index="7" stop-index="20">
                <expr>
                    <function function-name="CURRENT_USER" start-index="7" stop-index="20" text="CURRENT_USER()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_extract_function">
        <projections start-index="7" stop-index="56">
            <expression-projection text="EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')" start-index="7"
                                   stop-index="56">
                <expr>
                    <function function-name="EXTRACT" start-index="7" stop-index="56"
                              text="EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')">
                        <parameter>
                            <extract-arg start-index="15" stop-index="18" text="YEAR"/>
                        </parameter>
                        <parameter>
                            <type-cast-expression>
                                <expression>
                                    <literal-expression value="2001-02-16 20:38:40" start-index="35" stop-index="55"/>
                                </expression>
                                <data-type>TIMESTAMP</data-type>
                            </type-cast-expression>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_extract_function_week">
        <projections start-index="7" stop-index="56">
            <expression-projection text="EXTRACT(WEEK FROM TIMESTAMP '2001-02-16 20:38:40')" start-index="7"
                                   stop-index="56">
                <expr>
                    <function function-name="EXTRACT" start-index="7" stop-index="56"
                              text="EXTRACT(WEEK FROM TIMESTAMP '2001-02-16 20:38:40')">
                        <parameter>
                            <extract-arg start-index="15" stop-index="18" text="WEEK"/>
                        </parameter>
                        <parameter>
                            <type-cast-expression>
                                <expression>
                                    <literal-expression value="2001-02-16 20:38:40" start-index="35" stop-index="55"/>
                                </expression>
                                <data-type>TIMESTAMP</data-type>
                            </type-cast-expression>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_extract_function_quarter">
        <projections start-index="7" stop-index="59">
            <expression-projection text="EXTRACT(QUARTER FROM TIMESTAMP '2001-02-16 20:38:40')" start-index="7"
                                   stop-index="59">
                <expr>
                    <function function-name="EXTRACT" start-index="7" stop-index="59"
                              text="EXTRACT(QUARTER FROM TIMESTAMP '2001-02-16 20:38:40')">
                        <parameter>
                            <extract-arg start-index="15" stop-index="21" text="QUARTER"/>
                        </parameter>
                        <parameter>
                            <type-cast-expression>
                                <expression>
                                    <literal-expression value="2001-02-16 20:38:40" start-index="38" stop-index="58"/>
                                </expression>
                                <data-type>TIMESTAMP</data-type>
                            </type-cast-expression>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_extract_function_for_oracle">
        <projections start-index="7" stop-index="56" literal-start-index="7" literal-stop-index="56">
            <expression-projection text="EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')" start-index="7"
                                   stop-index="56" literal-start-index="7" literal-stop-index="56">
                <literalText>EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')</literalText>
                <expr>
                    <function function-name="EXTRACT" text="EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')"
                              start-index="7" stop-index="56" literal-start-index="7" literal-stop-index="56">
                        <parameter>
                            <common-expression text="TIMESTAMP '2001-02-16 20:38:40'" start-index="25" stop-index="55"
                                               literal-start-index="25" literal-stop-index="55"/>
                        </parameter>
                        <literalText>EXTRACT(YEAR FROM TIMESTAMP '2001-02-16 20:38:40')</literalText>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="DUAL" start-index="63" stop-index="66" literal-start-index="63"
                          literal-stop-index="66"/>
        </from>
    </select>

    <select sql-case-id="select_mod_function">
        <projections start-index="7" stop-index="22">
            <expression-projection text="MOD(order_id, 1)" start-index="7" stop-index="22">
                <expr>
                    <function function-name="MOD" start-index="7" stop-index="22" text="MOD(order_id, 1)">
                        <parameter>
                            <column name="order_id" start-index="11" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="21" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="29" stop-index="35"/>
        </from>
    </select>

    <select sql-case-id="select_with_lead_and_lag_function">
        <projections start-index="7" stop-index="123" literal-start-index="7" literal-stop-index="123">
            <column-projection name="hire_date" start-index="7" stop-index="15" literal-start-index="7"
                               literal-stop-index="15"/>
            <expression-projection text="LAG(hire_date, 1) OVER (ORDER BY hire_date)" alias="LAG1" start-index="18"
                                   stop-index="68" literal-start-index="18" literal-stop-index="68">
                <literalText>LAG(hire_date, 1) OVER (ORDER BY hire_date)</literalText>
                <expr>
                    <function function-name="LAG" text="LAG(hire_date, 1) OVER (ORDER BY hire_date)" start-index="18"
                              stop-index="60" literal-start-index="18" literal-stop-index="60">
                        <parameter>
                            <column name="hire_date" start-index="22" stop-index="30" literal-start-index="22"
                                    literal-stop-index="30"/>
                        </parameter>
                        <literalText>LAG(hire_date, 1) OVER (ORDER BY hire_date)</literalText>
                    </function>
                </expr>
            </expression-projection>
            <expression-projection text="LEAD(hire_date, 1) OVER (ORDER BY hire_date)" alias="LEAD1" start-index="71"
                                   stop-index="123" literal-start-index="71" literal-stop-index="123">
                <literalText>LEAD(hire_date, 1) OVER (ORDER BY hire_date)</literalText>
                <expr>
                    <function function-name="LEAD" text="LEAD(hire_date, 1) OVER (ORDER BY hire_date)" start-index="71"
                              stop-index="114" literal-start-index="71" literal-stop-index="114">
                        <parameter>
                            <column name="hire_date" start-index="76" stop-index="84" literal-start-index="76"
                                    literal-stop-index="84"/>
                        </parameter>
                        <literalText>LEAD(hire_date, 1) OVER (ORDER BY hire_date)</literalText>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="employees" start-index="130" stop-index="138" literal-start-index="130"
                          literal-stop-index="138"/>
        </from>
        <where start-index="140" stop-index="163" literal-start-index="140" literal-stop-index="163">
            <expr>
                <binary-operation-expression start-index="146" stop-index="163" literal-start-index="146"
                                             literal-stop-index="163">
                    <left>
                        <column name="department_id" start-index="146" stop-index="158" literal-start-index="146"
                                literal-stop-index="158"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="30" start-index="162" stop-index="163" literal-start-index="162"
                                            literal-stop-index="163"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="hire_date" order-direction="ASC" start-index="174" stop-index="182"
                         literal-start-index="174" literal-stop-index="182"/>
        </order-by>
    </select>

    <select sql-case-id="select_sys_xml_agg">
        <projections start-index="7" stop-index="46">
            <expression-projection text="SYS_XMLAGG(SYS_XMLGEN(last_name))" alias="XMLAGG" start-index="7"
                                   stop-index="46">
                <expr>
                    <function function-name="SYS_XMLAGG" start-index="7" stop-index="39"
                              text="SYS_XMLAGG(SYS_XMLGEN(last_name))">
                        <parameter>
                            <function function-name="SYS_XMLGEN" start-index="18" stop-index="38"
                                      text="SYS_XMLGEN(last_name)">
                                <parameter>
                                    <column name="last_name" start-index="29" stop-index="37"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="employees" start-index="53" stop-index="61"/>
        </from>
        <where start-index="63" stop-index="87">
            <expr>
                <binary-operation-expression start-index="69" stop-index="87">
                    <left>
                        <column name="last_name" start-index="69" stop-index="77"/>
                    </left>
                    <operator>LIKE</operator>
                    <right>
                        <list-expression start-index="84" stop-index="87">
                            <items>
                                <literal-expression value="R%" start-index="84" stop-index="87"/>
                            </items>
                        </list-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="xmlagg" order-direction="ASC" start-index="98" stop-index="103"/>
        </order-by>
    </select>

    <select sql-case-id="select_set_function">
        <projections start-index="7" stop-index="49">
            <column-projection name="customer_id" start-index="7" stop-index="17"/>
            <expression-projection text="SET(cust_address_ntab)" alias="address" start-index="20" stop-index="49">
                <expr>
                    <function function-name="SET" start-index="20" stop-index="41" text="SET(cust_address_ntab)">
                        <parameter>
                            <column name="cust_address_ntab" start-index="24" stop-index="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="customers_demo" start-index="56" stop-index="69"/>
        </from>
        <order-by>
            <column-item name="customer_id" order-direction="ASC" start-index="80" stop-index="90"
                         literal-start-index="80" literal-stop-index="90"/>
        </order-by>
    </select>

    <select sql-case-id="select_pivot">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <subquery-table start-index="14" stop-index="34">
                <subquery>
                    <select>
                        <projections start-index="22" stop-index="22">
                            <shorthand-projection start-index="22" stop-index="22"/>
                        </projections>
                        <from start-index="29" stop-index="33">
                            <simple-table name="sales" start-index="29" stop-index="33"/>
                        </from>
                    </select>
                </subquery>
            </subquery-table>
        </from>
    </select>

    <select sql-case-id="select_string_split_function">
        <from>
            <join-table join-type="CROSS">
                <left>
                    <simple-table name="Product" start-index="59" stop-index="65"/>
                </left>
                <right>
                    <function-table>
                        <table-function function-name="STRING_SPLIT" text="STRING_SPLIT(Tags, ',')"/>
                    </function-table>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="52">
            <column-projection name="value" alias="tag" start-index="7" stop-index="18"/>
            <aggregation-projection type="COUNT" expression="COUNT(*)" start-index="21" stop-index="28"
                                    alias="number_of_articles"/>
        </projections>
        <group-by>
            <column-item name="value" start-index="112" stop-index="116"/>
        </group-by>
        <having start-index="118" stop-index="136">
            <expr>
                <binary-operation-expression start-index="125" stop-index="136">
                    <left>
                        <aggregation-projection type="COUNT" expression="COUNT(*)" start-index="125" stop-index="132"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression start-index="136" stop-index="136" value="2"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <expression-item expression="COUNT(*)" order-direction="DESC" start-index="147" stop-index="154"/>
        </order-by>
    </select>

    <select sql-case-id="select_from_open_json_function">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from start-index="14" stop-index="101">
            <function-table start-index="14" stop-index="102" table-alias="months">
                <table-function
                        text="OPENJSON(@array) WITH (  month VARCHAR(3), temp int, month_id tinyint '$.sql:identity()')"
                        function-name="OPENJSON">
                    <parameter>
                        <column name="@array" start-index="23" stop-index="28"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_from_open_json_function_with_path">
        <projections start-index="7" stop-index="18">
            <column-projection name="key" start-index="7" stop-index="11" start-delimiter='[' end-delimiter=']'/>
            <column-projection name="value" start-index="14" stop-index="18"/>
        </projections>
        <from start-index="25" stop-index="64">
            <function-table start-index="25" stop-index="64">
                <table-function text="OPENJSON(@json,'$.path.to.&quot;sub-object&quot;')" function-name="OPENJSON">
                    <parameter>
                        <column name="@json" start-index="34" stop-index="38"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="$.path.to.&quot;sub-object&quot;" start-index="40" stop-index="63"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_from_open_row_set_with_provider_name">
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner start-index="7" stop-index="7" name="a"/>
            </shorthand-projection>
        </projections>
        <from start-index="16" stop-index="110">
            <function-table start-index="16" stop-index="110" table-alias="a">
                <table-function function-name="OPENROWSET"
                                text="OPENROWSET('Microsoft.Jet.OLEDB.4.0', 'C:\SAMPLES\Northwind.mdb';'admin';'password', Customers)">
                    <parameter>
                        <literal-expression value="Microsoft.Jet.OLEDB.4.0" start-index="27" stop-index="51"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="C:\SAMPLES\Northwind.mdb" start-index="54" stop-index="79"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="admin" start-index="81" stop-index="87"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="password" start-index="89" stop-index="98"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="Customers" start-index="101" stop-index="109"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_from_open_row_set_with_provider_string">
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner start-index="7" stop-index="7" name="d"/>
            </shorthand-projection>
        </projections>
        <from start-index="6" stop-index="123">
            <function-table start-index="16" stop-index="123" table-alias="d">
                <table-function function-name="OPENROWSET"
                                text="OPENROWSET('SQLNCLI','Server=Seattle1;Trusted_Connection=yes;',AdventureWorks2022.HumanResources.Department)">
                    <parameter>
                        <literal-expression value="SQLNCLI" start-index="27" stop-index="35"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="Server=Seattle1;Trusted_Connection=yes;" start-index="37"
                                            stop-index="77"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="AdventureWorks2022.HumanResources.Department" start-index="79"
                                            stop-index="122"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_json_object_simple_key_value">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43" text="JSON_OBJECT('name':'value', 'type':1)">
                <expr>
                    <function function-name="JSON_OBJECT" text="JSON_OBJECT('name':'value', 'type':1)" start-index="7"
                              stop-index="43">
                        <parameter>
                            <key-value start-index="19" stop-index="32" text="'name':'value'">
                                <key>
                                    <literal-expression start-index="19" stop-index="24" value="name"/>
                                </key>
                                <value>
                                    <literal-expression start-index="26" stop-index="32" value="value"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value start-index="35" stop-index="42" text="'type':1">
                                <key>
                                    <literal-expression start-index="35" stop-index="40" value="type"/>
                                </key>
                                <value>
                                    <literal-expression start-index="42" stop-index="42" value="1"/>
                                </value>
                            </key-value>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_nest_json_object">
        <projections start-index="7" stop-index="78">
            <expression-projection text="JSON_OBJECT('name':'value', 'type':JSON_OBJECT('type_id':1, 'name':'a'))"
                                   start-index="7" stop-index="78">
                <expr>
                    <function function-name="JSON_OBJECT"
                              text="JSON_OBJECT('name':'value', 'type':JSON_OBJECT('type_id':1, 'name':'a'))"
                              start-index="7" stop-index="78">
                        <parameter>
                            <key-value text="'name':'value'" start-index="19" stop-index="32">
                                <key>
                                    <literal-expression value="name" start-index="19" stop-index="24"/>
                                </key>
                                <value>
                                    <literal-expression value="value" start-index="26" stop-index="32"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value text="'type':JSON_OBJECT('type_id':1,'name':'a')" start-index="35"
                                       stop-index="77">
                                <key>
                                    <literal-expression value="type" start-index="35" stop-index="40"/>
                                </key>
                                <value>
                                    <function function-name="JSON_OBJECT" text="JSON_OBJECT('type_id':1, 'name':'a')"
                                              start-index="42" stop-index="77">
                                        <parameter>
                                            <key-value text="'type_id':1" start-index="54" stop-index="64">
                                                <key>
                                                    <literal-expression value="type_id" start-index="54"
                                                                        stop-index="62"/>
                                                </key>
                                                <value>
                                                    <literal-expression value="1" start-index="64" stop-index="64"/>
                                                </value>
                                            </key-value>
                                        </parameter>
                                        <parameter>
                                            <key-value text="'name':'a'" start-index="67" stop-index="76">
                                                <key>
                                                    <literal-expression value="name" start-index="67" stop-index="72"/>
                                                </key>
                                                <value>
                                                    <literal-expression value="a" start-index="74" stop-index="76"/>
                                                </value>
                                            </key-value>
                                        </parameter>
                                    </function>
                                </value>
                            </key-value>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_object_with_json_array">
        <projections start-index="7" stop-index="58">
            <expression-projection text="JSON_OBJECT('name':'value', 'type':JSON_ARRAY(1, 2))" start-index="7"
                                   stop-index="58">
                <expr>
                    <function function-name="JSON_OBJECT" text="JSON_OBJECT('name':'value', 'type':JSON_ARRAY(1, 2))"
                              start-index="7" stop-index="58">
                        <parameter>
                            <key-value text="'name':'value'" start-index="19" stop-index="32">
                                <key>
                                    <literal-expression value="name" start-index="19" stop-index="24"/>
                                </key>
                                <value>
                                    <literal-expression value="value" start-index="26" stop-index="32"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value text="'type':JSON_ARRAY(1,2)" start-index="35" stop-index="57">
                                <key>
                                    <literal-expression value="type" start-index="35" stop-index="40"/>
                                </key>
                                <value>
                                    <function function-name="JSON_ARRAY" text="JSON_ARRAY(1, 2)" start-index="42"
                                              stop-index="57">
                                        <parameter>
                                            <literal-expression value="1" start-index="53" stop-index="53"/>
                                        </parameter>
                                        <parameter>
                                            <literal-expression value="2" start-index="56" stop-index="56"/>
                                        </parameter>
                                    </function>
                                </value>
                            </key-value>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_array">
        <projections start-index="7" stop-index="49">
            <expression-projection text="JSON_ARRAY(1, 'abc', NULL, TRUE, CURTIME())" start-index="7" stop-index="49">
                <expr>
                    <function function-name="JSON_ARRAY" text="JSON_ARRAY(1, 'abc', NULL, TRUE, CURTIME())"
                              start-index="7" stop-index="49">
                        <parameter>
                            <literal-expression value="1" start-index="18" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="abc" start-index="21" stop-index="25"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="null" start-index="28" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="true" start-index="34" stop-index="37"/>
                        </parameter>
                        <parameter>
                            <function function-name="CURTIME" text="CURTIME()" start-index="40" stop-index="48"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_array_append">
        <projections start-index="7" stop-index="60">
            <expression-projection
                    text="JSON_ARRAY_APPEND('[&quot;a&quot;, [&quot;b&quot;, &quot;c&quot;], &quot;d&quot;]', '$[1]', 1)"
                    start-index="7" stop-index="60">
                <expr>
                    <function function-name="JSON_ARRAY_APPEND"
                              text="JSON_ARRAY_APPEND('[&quot;a&quot;, [&quot;b&quot;, &quot;c&quot;], &quot;d&quot;]', '$[1]', 1)"
                              start-index="7" stop-index="60">
                        <parameter>
                            <literal-expression value="[&quot;a&quot;, [&quot;b&quot;, &quot;c&quot;], &quot;d&quot;]"
                                                start-index="25" stop-index="48"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$[1]" start-index="51" stop-index="56"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="59" stop-index="59"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_array_insert">
        <projections start-index="7" stop-index="68">
            <expression-projection
                    text="JSON_ARRAY_INSERT('[&quot;a&quot;, {&quot;b&quot;: [1, 2]}, [3, 4]]', '$[1]', 'x')"
                    start-index="7" stop-index="68">
                <expr>
                    <function function-name="JSON_ARRAY_INSERT"
                              text="JSON_ARRAY_INSERT('[&quot;a&quot;, {&quot;b&quot;: [1, 2]}, [3, 4]]', '$[1]', 'x')"
                              start-index="7" stop-index="68">
                        <parameter>
                            <literal-expression value="[&quot;a&quot;, {&quot;b&quot;: [1, 2]}, [3, 4]]"
                                                start-index="25" stop-index="54"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$[1]" start-index="57" stop-index="62"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="x" start-index="65" stop-index="67"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_arrayagg">
        <projections start-index="7" stop-index="52">
            <column-projection name="o_id" start-index="7" stop-index="10"/>
            <expression-projection text="JSON_ARRAYAGG(attribute)" alias="attributes" start-index="13" stop-index="52">
                <expr>
                    <function function-name="JSON_ARRAYAGG" text="JSON_ARRAYAGG(attribute)" start-index="13"
                              stop-index="36">
                        <parameter>
                            <column name="attribute" start-index="27" stop-index="35"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from start-index="59" stop-index="60">
            <simple-table name="t3" start-index="59" stop-index="60"/>
        </from>
        <group-by start-index="62" stop-index="74">
            <column-item name="o_id" order-direction="ASC" start-index="71" stop-index="74"/>
        </group-by>
    </select>

    <select sql-case-id="select_json_contains">
        <projections start-index="7" stop-index="66">
            <expression-projection
                    text="JSON_CONTAINS('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', '1', '$.a')"
                    start-index="7" stop-index="66">
                <expr>
                    <function function-name="JSON_CONTAINS"
                              text="JSON_CONTAINS('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', '1', '$.a')"
                              start-index="7" stop-index="66">
                        <parameter>
                            <literal-expression
                                    value="{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}"
                                    start-index="21" stop-index="53"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="56" stop-index="58"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.a" start-index="61" stop-index="65"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_contains_one">
        <projections start-index="7" stop-index="80">
            <expression-projection
                    text="JSON_CONTAINS_PATH('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', 'one', '$.a', '$.e')"
                    start-index="7" stop-index="80">
                <expr>
                    <function function-name="JSON_CONTAINS_PATH"
                              text="JSON_CONTAINS_PATH('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', 'one', '$.a', '$.e')"
                              start-index="7" stop-index="80">
                        <parameter>
                            <literal-expression
                                    value="{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}"
                                    start-index="26" stop-index="58"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="one" start-index="61" stop-index="65"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.a" start-index="68" stop-index="72"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.e" start-index="75" stop-index="79"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_contains_all">
        <projections start-index="7" stop-index="80">
            <expression-projection
                    text="JSON_CONTAINS_PATH('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', 'all', '$.a', '$.e')"
                    start-index="7" stop-index="80">
                <expr>
                    <function function-name="JSON_CONTAINS_PATH"
                              text="JSON_CONTAINS_PATH('{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}', 'all', '$.a', '$.e')"
                              start-index="7" stop-index="80">
                        <parameter>
                            <literal-expression
                                    value="{&quot;a&quot;: 1, &quot;b&quot;: 2, &quot;c&quot;: {&quot;d&quot;: 4}}"
                                    start-index="26" stop-index="58"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="all" start-index="61" stop-index="65"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.a" start-index="68" stop-index="72"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.e" start-index="75" stop-index="79"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_depth">
        <projections start-index="7" stop-index="35">
            <expression-projection text="JSON_DEPTH('[10, {&quot;a&quot;: 20}]')" start-index="7" stop-index="35">
                <expr>
                    <function function-name="JSON_DEPTH" text="JSON_DEPTH('[10, {&quot;a&quot;: 20}]')" start-index="7"
                              stop-index="35">
                        <parameter>
                            <literal-expression value="[10, {&quot;a&quot;: 20}]" start-index="18" stop-index="34"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_extract">
        <projections start-index="7" stop-index="56">
            <expression-projection text="JSON_EXTRACT('[10, 20, [30, 40]]', '$[1]', '$[0]')" start-index="7"
                                   stop-index="56">
                <expr>
                    <function function-name="JSON_EXTRACT" text="JSON_EXTRACT('[10, 20, [30, 40]]', '$[1]', '$[0]')"
                              start-index="7" stop-index="56">
                        <parameter>
                            <literal-expression value="[10, 20, [30, 40]]" start-index="20" stop-index="39"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$[1]" start-index="42" stop-index="47"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$[0]" start-index="50" stop-index="55"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_keys">
        <projections start-index="7" stop-index="43">
            <expression-projection text="JSON_KEYS('{&quot;a&quot;: 1, &quot;b&quot;: {&quot;c&quot;: 30}}')"
                                   start-index="7" stop-index="43">
                <expr>
                    <function function-name="JSON_KEYS"
                              text="JSON_KEYS('{&quot;a&quot;: 1, &quot;b&quot;: {&quot;c&quot;: 30}}')" start-index="7"
                              stop-index="43">
                        <parameter>
                            <literal-expression value="{&quot;a&quot;: 1, &quot;b&quot;: {&quot;c&quot;: 30}}"
                                                start-index="17" stop-index="42"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_length">
        <projections start-index="7" stop-index="37">
            <expression-projection text="JSON_LENGTH('[1, 2, {&quot;a&quot;: 3}]')" start-index="7" stop-index="37">
                <expr>
                    <function function-name="JSON_LENGTH" text="JSON_LENGTH('[1, 2, {&quot;a&quot;: 3}]')"
                              start-index="7" stop-index="37">
                        <parameter>
                            <literal-expression value="[1, 2, {&quot;a&quot;: 3}]" start-index="19" stop-index="36"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_merge">
        <projections start-index="7" stop-index="43">
            <expression-projection text="JSON_MERGE('[1, 2]', '[true, false]')" start-index="7" stop-index="43">
                <expr>
                    <function function-name="JSON_MERGE" text="JSON_MERGE('[1, 2]', '[true, false]')" start-index="7"
                              stop-index="43">
                        <parameter>
                            <literal-expression value="[1, 2]" start-index="18" stop-index="25"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="[true, false]" start-index="28" stop-index="42"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_merge_patch">
        <projections start-index="7" stop-index="49">
            <expression-projection text="JSON_MERGE_PATCH('[1, 2]', '[true, false]')" start-index="7" stop-index="49">
                <expr>
                    <function function-name="JSON_MERGE_PATCH" text="JSON_MERGE_PATCH('[1, 2]', '[true, false]')"
                              start-index="7" stop-index="49">
                        <parameter>
                            <literal-expression value="[1, 2]" start-index="24" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="[true, false]" start-index="34" stop-index="48"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_merge_preserve">
        <projections start-index="7" stop-index="52">
            <expression-projection text="JSON_MERGE_PRESERVE('[1, 2]', '[true, false]')" start-index="7"
                                   stop-index="52">
                <expr>
                    <function function-name="JSON_MERGE_PRESERVE" text="JSON_MERGE_PRESERVE('[1, 2]', '[true, false]')"
                              start-index="7" stop-index="52">
                        <parameter>
                            <literal-expression value="[1, 2]" start-index="27" stop-index="34"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="[true, false]" start-index="37" stop-index="51"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_object_absent_not_null">
        <projections start-index="7" stop-index="61">
            <expression-projection text="JSON_OBJECT('name':'value', 'type':NULL ABSENT ON NULL)" start-index="7"
                                   stop-index="61">
                <expr>
                    <function function-name="JSON_OBJECT" text="JSON_OBJECT('name':'value', 'type':NULL ABSENT ON NULL)"
                              start-index="7" stop-index="61">
                        <parameter>
                            <key-value start-index="19" stop-index="32" text="'name':'value'">
                                <key>
                                    <literal-expression start-index="19" stop-index="24" value="name"/>
                                </key>
                                <value>
                                    <literal-expression start-index="26" stop-index="32" value="value"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value start-index="35" stop-index="45" text="'type':NULL">
                                <key>
                                    <literal-expression start-index="35" stop-index="40" value="type"/>
                                </key>
                                <value>
                                    <literal-expression start-index="42" stop-index="45" value="null"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <json-null-clause-expression text="ABSENT ON NULL" start-index="47" stop-index="60"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_object_with_subquery">
        <projections start-index="7" stop-index="84">
            <expression-projection text="JSON_OBJECT('user_name':USER_NAME(), @id_key:@id_value, 'sid':(SELECT @@SPID))"
                                   start-index="7" stop-index="84">
                <expr>
                    <function function-name="JSON_OBJECT"
                              text="JSON_OBJECT('user_name':USER_NAME(), @id_key:@id_value, 'sid':(SELECT @@SPID))"
                              start-index="7" stop-index="84">
                        <parameter>
                            <key-value text="'user_name':USER_NAME()" start-index="19" stop-index="41">
                                <key>
                                    <literal-expression value="user_name" start-index="19" stop-index="29"/>
                                </key>
                                <value>
                                    <function function-name="USER_NAME" text="USER_NAME()" start-index="31"
                                              stop-index="41"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value text="@id_key:@id_value" start-index="44" stop-index="60">
                                <key>
                                    <column name="@id_key" start-index="44" stop-index="50"/>
                                </key>
                                <value>
                                    <column name="@id_value" start-index="52" stop-index="60"/>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value text="'sid':(SELECT@@SPID)" start-index="63" stop-index="83">
                                <key>
                                    <literal-expression value="sid" start-index="63" stop-index="67"/>
                                </key>
                                <value>
                                    <subquery start-index="69" stop-index="83">
                                        <select>
                                            <projections start-index="77" stop-index="82">
                                                <column-projection name="@@SPID" start-index="77" stop-index="82"/>
                                            </projections>
                                        </select>
                                    </subquery>
                                </value>
                            </key-value>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_dm_exec_sessions_with_json_object_function">
        <projections start-index="7" stop-index="109">
            <column-projection name="session_id" start-index="7" stop-index="18">
                <owner name="s" start-index="7" stop-index="7"/>
            </column-projection>
            <expression-projection
                    text="JSON_OBJECT('security_id':s.security_id, 'login':s.login_name, 'status':s.status)"
                    alias="info" start-index="21" stop-index="109">
                <expr>
                    <function function-name="JSON_OBJECT"
                              text="JSON_OBJECT('security_id':s.security_id, 'login':s.login_name, 'status':s.status)"
                              start-index="21" stop-index="101">
                        <parameter>
                            <key-value text="'security_id':s.security_id" start-index="33" stop-index="59">
                                <key>
                                    <literal-expression value="security_id" start-index="33" stop-index="45"/>
                                </key>
                                <value>
                                    <column name="security_id" start-index="47" stop-index="59">
                                        <owner name="s" start-index="47" stop-index="47"/>
                                    </column>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value text="'login':s.login_name" start-index="62" stop-index="81">
                                <key>
                                    <literal-expression value="login" start-index="62" stop-index="68"/>
                                </key>
                                <value>
                                    <column name="login_name" start-index="70" stop-index="81">
                                        <owner name="s" start-index="70" stop-index="70"/>
                                    </column>
                                </value>
                            </key-value>
                        </parameter>
                        <parameter>
                            <key-value
                                    text="JSON_OBJECT('security_id':s.security_id, 'login':s.login_name, 'status':s.status)"
                                    start-index="84" stop-index="100">
                                <key>
                                    <literal-expression value="status" start-index="84" stop-index="91"/>
                                </key>
                                <value>
                                    <column name="status" start-index="93" stop-index="100">
                                        <owner name="s" start-index="93" stop-index="93"/>
                                    </column>
                                </value>
                            </key-value>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from start-index="116" stop-index="140">
            <simple-table name="dm_exec_sessions" alias="s" start-index="116" stop-index="140">
                <owner name="sys" start-index="116" stop-index="118"/>
            </simple-table>
        </from>
        <where start-index="142" stop-index="168">
            <expr>
                <binary-operation-expression start-index="148" stop-index="168" text="s.is_user_process = 1">
                    <left>
                        <column name="is_user_process" start-index="148" stop-index="164">
                            <owner name="s" start-index="148" stop-index="148"/>
                        </column>
                    </left>
                    <right>
                        <literal-expression value="1" start-index="168" stop-index="168"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </select>

    <select sql-case-id="select_first_last_value_function">
        <projections start-index="7" stop-index="493">
            <column-projection name="BusinessEntityID" start-index="7" stop-index="22"/>
            <expression-projection text="DATEPART(QUARTER, QuotaDate)" alias="Quarter" start-index="25" stop-index="63">
                <expr>
                    <function function-name="DATEPART" text="DATEPART(QUARTER, QuotaDate)" start-index="25"
                              stop-index="52">
                        <parameter>
                            <column name="QUARTER" start-index="34" stop-index="40"/>
                        </parameter>
                        <parameter>
                            <column name="QuotaDate" start-index="43" stop-index="51"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <expression-projection text="YEAR(QuotaDate)" start-index="66" stop-index="93" alias="SalesYear">
                <expr>
                    <function function-name="YEAR" text="YEAR(QuotaDate)" start-index="66" stop-index="80">
                        <parameter>
                            <column name="QuotaDate" start-index="71" stop-index="79"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <column-projection name="SalesQuota" alias="QuotaThisQuarter" start-index="96" stop-index="125"/>
            <expression-projection
                    text="SalesQuota - FIRST_VALUE(SalesQuota) OVER (PARTITION BY BusinessEntityID, YEAR(QuotaDate) ORDER BY DATEPART(QUARTER, QuotaDate))"
                    alias="DifferenceFromFirstQuarter" start-index="128" stop-index="285">
                <expr>
                    <binary-operation-expression start-index="128" stop-index="255">
                        <left>
                            <column name="SalesQuota" start-index="128" stop-index="137"/>
                        </left>
                        <right>
                            <function function-name="FIRST_VALUE"
                                      text="FIRST_VALUE(SalesQuota) OVER (PARTITION BY BusinessEntityID, YEAR(QuotaDate) ORDER BY DATEPART(QUARTER, QuotaDate))"
                                      start-index="141" stop-index="255">
                                <parameter>
                                    <column name="SalesQuota" start-index="153" stop-index="162"/>
                                </parameter>
                            </function>
                        </right>
                        <operator>-</operator>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
            <expression-projection start-index="288" stop-index="493"
                                   text="SalesQuota - LAST_VALUE(SalesQuota) OVER (PARTITION BY BusinessEntityID, YEAR(QuotaDate) ORDER BY DATEPART(QUARTER, QuotaDate) RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING)"
                                   alias="DifferenceFromLastQuarter">
                <expr>
                    <binary-operation-expression start-index="288" stop-index="464">
                        <left>
                            <column name="SalesQuota" start-index="288" stop-index="297"/>
                        </left>
                        <right>
                            <function function-name="LAST_VALUE"
                                      text="LAST_VALUE(SalesQuota) OVER (PARTITION BY BusinessEntityID, YEAR(QuotaDate) ORDER BY DATEPART(QUARTER, QuotaDate) RANGE BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING)"
                                      start-index="301" stop-index="464">
                                <parameter>
                                    <column name="SalesQuota" start-index="312" stop-index="321"/>
                                </parameter>
                            </function>
                        </right>
                        <operator>-</operator>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="SalesPersonQuotaHistory" start-index="500" stop-index="528">
                <owner name="Sales" start-index="500" stop-index="504"/>
            </simple-table>
        </from>
        <where start-index="530" stop-index="598">
            <expr>
                <binary-operation-expression start-index="536" stop-index="598"
                                             text="YEAR(QuotaDate) > 2005 AND BusinessEntityID BETWEEN 274 AND 275">
                    <left>
                        <binary-operation-expression start-index="536" stop-index="557" text="YEAR(QuotaDate) > 2005">
                            <left>
                                <function function-name="YEAR" text="YEAR(QuotaDate)" start-index="536"
                                          stop-index="550">
                                    <parameter>
                                        <column name="QuotaDate" start-index="541" stop-index="549"/>
                                    </parameter>
                                </function>
                            </left>
                            <right>
                                <literal-expression value="2005" start-index="554" stop-index="557"/>
                            </right>
                            <operator>></operator>
                        </binary-operation-expression>
                    </left>
                    <right>
                        <between-expression start-index="563" stop-index="598">
                            <not>false</not>
                            <left>
                                <column name="BusinessEntityID" start-index="563" stop-index="578"/>
                            </left>
                            <between-expr>
                                <literal-expression value="274" start-index="588" stop-index="590"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="275" start-index="596" stop-index="598"/>
                            </and-expr>
                        </between-expression>
                    </right>
                    <operator>AND</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="BusinessEntityID" order-direction="ASC" start-index="609" stop-index="624"/>
            <column-item name="SalesYear" order-direction="ASC" start-index="627" stop-index="635"/>
            <column-item name="Quarter" order-direction="ASC" start-index="638" stop-index="644"/>
        </order-by>
    </select>

    <select sql-case-id="select_approx_percentile_cont_function">
        <projections start-index="7" stop-index="148">
            <column-projection start-index="7" stop-index="12" name="DeptId"/>
            <expression-projection start-index="14" stop-index="80"
                                   text="APPROX_PERCENTILE_CONT(0.10) WITHIN GROUP(ORDER BY Salary)" alias="P10">
                <expr>
                    <function function-name="APPROX_PERCENTILE_CONT"
                              text="APPROX_PERCENTILE_CONT(0.10) WITHIN GROUP(ORDER BY Salary)" start-index="14"
                              stop-index="71">
                        <parameter>
                            <literal-expression value="0.10" start-index="37" stop-index="40"/>
                        </parameter>
                        <parameter>
                            <column name="Salary" start-index="65" stop-index="70"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <expression-projection text="APPROX_PERCENTILE_CONT(0.90) WITHIN GROUP(ORDER BY Salary)" start-index="82"
                                   stop-index="148" alias="P90">
                <expr>
                    <function function-name="APPROX_PERCENTILE_CONT"
                              text="APPROX_PERCENTILE_CONT(0.90) WITHIN GROUP(ORDER BY Salary)" start-index="82"
                              stop-index="139">
                        <parameter>
                            <literal-expression value="0.90" start-index="105" stop-index="108"/>
                        </parameter>
                        <parameter>
                            <column name="Salary" start-index="133" stop-index="138"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="tblEmployee" start-index="155" stop-index="165"/>
        </from>
        <group-by start-index="167" stop-index="181">
            <column-item name="DeptId" order-direction="ASC" start-index="176" stop-index="181"/>
        </group-by>
    </select>

    <select sql-case-id="select_wm_concat_function_with_schema">
        <projections start-index="7" stop-index="49">
            <expression-projection start-index="7" stop-index="49" text="TO_CHAR(WMSYS.WM_CONCAT(DISTINCT o.status))">
                <expr>
                    <function function-name="TO_CHAR" text="TO_CHAR(WMSYS.WM_CONCAT(DISTINCT o.status))" start-index="7"
                              stop-index="49">
                        <parameter>
                            <function function-name="WM_CONCAT" text="WMSYS.WM_CONCAT(DISTINCT o.status)"
                                      start-index="15" stop-index="48">
                                <parameter>
                                    <column name="status" start-index="40" stop-index="47">
                                        <owner start-index="40" stop-index="40" name="o"/>
                                    </column>
                                </parameter>
                                <owner start-index="15" stop-index="19" name="WMSYS"/>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="56" stop-index="64" alias="o"/>
        </from>
        <group-by start-index="66" stop-index="84">
            <column-item name="order_id" order-direction="ASC" start-index="75" stop-index="84">
                <owner start-index="75" stop-index="75" name="o"/>
            </column-item>
        </group-by>
    </select>

    <select sql-case-id="select_add_date">
        <projections start-index="7" stop-index="31">
            <expression-projection text="ADDDATE('2008-01-02', 31)" start-index="7" stop-index="31">
                <expr>
                    <function function-name="ADDDATE" start-index="7" stop-index="31" text="ADDDATE('2008-01-02', 31)">
                        <parameter>
                            <literal-expression value="2008-01-02" start-index="15" stop-index="26"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="31" start-index="29" stop-index="30"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_add_time">
        <projections start-index="7" stop-index="61">
            <expression-projection text="ADDTIME('2007-12-31 23:59:59.999999', '1 1:1:1.000002')" start-index="7"
                                   stop-index="61">
                <expr>
                    <function function-name="ADDTIME" start-index="7" stop-index="61"
                              text="ADDTIME('2007-12-31 23:59:59.999999', '1 1:1:1.000002')">
                        <parameter>
                            <literal-expression value="2007-12-31 23:59:59.999999" start-index="15" stop-index="42"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1 1:1:1.000002" start-index="45" stop-index="60"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_aes_decrypt">
        <projections start-index="7" stop-index="43">
            <expression-projection text="AES_DECRYPT('encrypt_text','key_str')" start-index="7" stop-index="43">
                <expr>
                    <function function-name="AES_DECRYPT" start-index="7" stop-index="43"
                              text="AES_DECRYPT('encrypt_text','key_str')">
                        <parameter>
                            <literal-expression value="encrypt_text" start-index="19" stop-index="32"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="key_str" start-index="34" stop-index="42"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_any_value">
        <from>
            <simple-table name="t" start-index="27" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="20">
            <expression-projection text="ANY_VALUE(age)" start-index="7" stop-index="20">
                <expr>
                    <function function-name="ANY_VALUE" start-index="7" stop-index="20" text="ANY_VALUE(age)">
                        <parameter>
                            <column name="age" start-index="17" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ascii">
        <projections start-index="7" stop-index="16">
            <expression-projection text="ASCII('2')" start-index="7" stop-index="16">
                <expr>
                    <function function-name="ASCII" start-index="7" stop-index="16" text="ASCII('2')">
                        <parameter>
                            <literal-expression value="2" start-index="13" stop-index="15"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bin">
        <projections start-index="7" stop-index="13">
            <expression-projection start-index="7" stop-index="13" text="BIN(12)">
                <expr>
                    <function function-name="BIN" text="BIN(12)" start-index="7" stop-index="13">
                        <parameter>
                            <literal-expression value="12" start-index="11" stop-index="12"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bin_uuid">
        <projections start-index="7" stop-index="70">
            <expression-projection start-index="7" stop-index="70"
                                   text="BIN_TO_UUID(UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db'))">
                <expr>
                    <function function-name="BIN_TO_UUID"
                              text="BIN_TO_UUID(UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db'))" start-index="7"
                              stop-index="70">
                        <parameter>
                            <function function-name="UUID_TO_BIN"
                                      text="UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db')" start-index="19"
                                      stop-index="69">
                                <parameter>
                                    <literal-expression value="6ccd780c-baba-1026-9564-5b8c656024db" start-index="31"
                                                        stop-index="68"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bit_length">
        <projections start-index="7" stop-index="19">
            <expression-projection start-index="7" stop-index="19" text="BIT_LENGTH(1)">
                <expr>
                    <function function-name="BIT_LENGTH" text="BIT_LENGTH(1)" start-index="7" stop-index="19">
                        <parameter>
                            <literal-expression value="1" start-index="18" stop-index="18"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_bit_count">
        <projections start-index="7" stop-index="18">
            <expression-projection start-index="7" stop-index="18" text="BIT_COUNT(1)">
                <expr>
                    <function function-name="BIT_COUNT" text="BIT_COUNT(1)" start-index="7" stop-index="18">
                        <parameter>
                            <literal-expression value="1" start-index="17" stop-index="17"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ceil">
        <projections start-index="7" stop-index="15">
            <expression-projection start-index="7" stop-index="15" text="CEIL(1.1)">
                <expr>
                    <function function-name="CEIL" text="CEIL(1.1)" start-index="7" stop-index="15">
                        <parameter>
                            <literal-expression value="1.1" start-index="12" stop-index="14"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ceiling">
        <projections start-index="7" stop-index="18">
            <expression-projection start-index="7" stop-index="18" text="CEILING(1.1)">
                <expr>
                    <function function-name="CEILING" text="CEILING(1.1)" start-index="7" stop-index="18">
                        <parameter>
                            <literal-expression value="1.1" start-index="15" stop-index="17"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_uuid_function">
        <projections start-index="7" stop-index="12">
            <expression-projection text="UUID()" start-index="7" stop-index="12">
                <expr>
                    <function function-name="UUID" start-index="7" stop-index="12" text="UUID()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_uuid_short_function">
        <projections start-index="7" stop-index="18">
            <expression-projection text="UUID_SHORT()" start-index="7" stop-index="18">
                <expr>
                    <function function-name="UUID_SHORT" start-index="7" stop-index="18" text="UUID_SHORT()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_uuid_to_bin_function">
        <projections start-index="7" stop-index="60">
            <expression-projection text="UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db', 0)" start-index="7"
                                   stop-index="60">
                <expr>
                    <function function-name="UUID_TO_BIN" start-index="7" stop-index="60"
                              text="UUID_TO_BIN('6ccd780c-baba-1026-9564-5b8c656024db', 0)">
                        <parameter>
                            <literal-expression value="6ccd780c-baba-1026-9564-5b8c656024db" start-index="19"
                                                stop-index="56"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="0" start-index="59" stop-index="59"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_validate_password_strength_function">
        <projections start-index="7" stop-index="50">
            <expression-projection text="VALIDATE_PASSWORD_STRENGTH('N0Tweak$_@123!')" start-index="7" stop-index="50">
                <expr>
                    <function function-name="VALIDATE_PASSWORD_STRENGTH" start-index="7" stop-index="50"
                              text="VALIDATE_PASSWORD_STRENGTH('N0Tweak$_@123!')">
                        <parameter>
                            <literal-expression value="N0Tweak$_@123!" start-index="34" stop-index="49"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_var_pop_function">
        <from>
            <simple-table name="sales_view" start-index="28" stop-index="39" alias="s"/>
        </from>
        <projections start-index="7" stop-index="21">
            <expression-projection text="VAR_POP(s.year)" start-index="7" stop-index="21">
                <expr>
                    <function function-name="VAR_POP" start-index="7" stop-index="21" text="VAR_POP(s.year)">
                        <parameter>
                            <column name="year" start-index="15" stop-index="20">
                                <owner name="s" start-index="15" stop-index="15"/>
                            </column>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_var_samp_function">
        <from>
            <simple-table name="sales_view" start-index="29" stop-index="40" alias="s"/>
        </from>
        <projections start-index="7" stop-index="22">
            <expression-projection text="VAR_SAMP(s.year)" start-index="7" stop-index="22">
                <expr>
                    <function function-name="VAR_SAMP" start-index="7" stop-index="22" text="VAR_SAMP(s.year)">
                        <parameter>
                            <column name="year" start-index="16" stop-index="21">
                                <owner name="s" start-index="16" stop-index="16"/>
                            </column>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_variance_function">
        <from>
            <simple-table name="sales_view" start-index="29" stop-index="40" alias="s"/>
        </from>
        <projections start-index="7" stop-index="22">
            <expression-projection text="VARIANCE(s.year)" start-index="7" stop-index="22">
                <expr>
                    <function function-name="VARIANCE" start-index="7" stop-index="22" text="VARIANCE(s.year)">
                        <parameter>
                            <column name="year" start-index="16" stop-index="21">
                                <owner name="s" start-index="16" stop-index="16"/>
                            </column>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_version_function">
        <projections start-index="7" stop-index="15">
            <expression-projection text="VERSION()" start-index="7" stop-index="15">
                <expr>
                    <function function-name="VERSION" start-index="7" stop-index="15" text="VERSION()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_wait_for_executed_gtid_set_function">
        <projections start-index="7" stop-index="76">
            <expression-projection text="WAIT_FOR_EXECUTED_GTID_SET('3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5')"
                                   start-index="7" stop-index="76">
                <expr>
                    <function function-name="WAIT_FOR_EXECUTED_GTID_SET" start-index="7" stop-index="76"
                              text="WAIT_FOR_EXECUTED_GTID_SET('3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5')">
                        <parameter>
                            <literal-expression value="3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5" start-index="34"
                                                stop-index="75"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_wait_until_sql_thread_after_gtids_function">
        <projections start-index="7" stop-index="83">
            <expression-projection text="WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS('3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5')"
                                   start-index="7" stop-index="83">
                <expr>
                    <function function-name="WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS" start-index="7" stop-index="83"
                              text="WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS('3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5')">
                        <parameter>
                            <literal-expression value="3E11FA47-71CA-11E1-9E33-C80AA9429562:1-5" start-index="41"
                                                stop-index="82"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_week_function">
        <projections start-index="7" stop-index="26">
            <expression-projection text="WEEK('2018-02-20',1)" start-index="7" stop-index="26">
                <expr>
                    <function function-name="WEEK" start-index="7" stop-index="26" text="WEEK('2018-02-20',1)">
                        <parameter>
                            <literal-expression value="2018-02-20" start-index="12" stop-index="23"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="25" stop-index="25"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_weekday_function">
        <projections start-index="7" stop-index="36">
            <expression-projection text="WEEKDAY('2017-02-03 22:23:00')" start-index="7" stop-index="36">
                <expr>
                    <function function-name="WEEKDAY" start-index="7" stop-index="36"
                              text="WEEKDAY('2017-02-03 22:23:00')">
                        <parameter>
                            <literal-expression value="2017-02-03 22:23:00" start-index="15" stop-index="35"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_weekofyear_function">
        <projections start-index="7" stop-index="30">
            <expression-projection text="WEEKOFYEAR('2019-02-20')" start-index="7" stop-index="30">
                <expr>
                    <function function-name="WEEKOFYEAR" start-index="7" stop-index="30"
                              text="WEEKOFYEAR('2019-02-20')">
                        <parameter>
                            <literal-expression value="2019-02-20" start-index="18" stop-index="29"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_xor_function">
        <projections start-index="7" stop-index="16">
            <expression-projection text="1 XOR NULL" start-index="7" stop-index="16">
                <expr>
                    <binary-operation-expression start-index="7" stop-index="16">
                        <left>
                            <literal-expression value="1" start-index="7" stop-index="7"/>
                        </left>
                        <operator>XOR</operator>
                        <right>
                            <literal-expression value="null" start-index="13" stop-index="16"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_year_function">
        <projections start-index="7" stop-index="24">
            <expression-projection text="YEAR('1999-09-09')" start-index="7" stop-index="24">
                <expr>
                    <function function-name="YEAR" start-index="7" stop-index="24" text="YEAR('1999-09-09')">
                        <parameter>
                            <literal-expression value="1999-09-09" start-index="12" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_yearweek_function">
        <projections start-index="7" stop-index="28">
            <expression-projection text="YEARWEEK('1988-08-08')" start-index="7" stop-index="28">
                <expr>
                    <function function-name="YEARWEEK" start-index="7" stop-index="28" text="YEARWEEK('1988-08-08')">
                        <parameter>
                            <literal-expression value="1988-08-08" start-index="16" stop-index="27"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_with_weight_string_function">
        <projections start-index="7" stop-index="33">
            <expression-projection text="HEX(WEIGHT_STRING('MySQL'))" start-index="7" stop-index="33">
                <expr>
                    <function function-name="HEX" start-index="7" stop-index="33" text="HEX(WEIGHT_STRING('MySQL'))">
                        <parameter>
                            <function function-name="WEIGHT_STRING" start-index="11" stop-index="32"
                                      text="WEIGHT_STRING('MySQL')">
                                <parameter>
                                    <literal-expression value="MySQL" start-index="25" stop-index="31"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_row_number">
        <projections start-index="7" stop-index="25">
            <expression-projection text="ROW_NUMBER() OVER w" start-index="7" stop-index="25">
                <expr>
                    <function function-name="ROW_NUMBER" start-index="7" stop-index="25" text="ROW_NUMBER() OVER w"/>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="32" stop-index="38"/>
        </from>
    </select>

    <select sql-case-id="select_rpad">
        <projections start-index="7" stop-index="28">
            <expression-projection text="RPAD('Hello', 10, '*')" start-index="7" stop-index="28">
                <expr>
                    <function function-name="RPAD" start-index="7" stop-index="28" text="RPAD('Hello', 10, '*')">
                        <parameter>
                            <literal-expression value="Hello" start-index="12" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="21" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="*" start-index="25" stop-index="27"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_rtrim">
        <projections start-index="7" stop-index="24">
            <expression-projection text="RTRIM('  Hello  ')" start-index="7" stop-index="24">
                <expr>
                    <function function-name="RTRIM" start-index="7" stop-index="24" text="RTRIM('  Hello  ')">
                        <parameter>
                            <literal-expression value="  Hello  " start-index="13" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_schema">
        <projections start-index="7" stop-index="14">
            <expression-projection text="SCHEMA()" start-index="7" stop-index="14">
                <expr>
                    <function function-name="SCHEMA" start-index="7" stop-index="14" text="SCHEMA()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sec_to_time">
        <projections start-index="7" stop-index="23">
            <expression-projection text="SEC_TO_TIME(3600)" start-index="7" stop-index="23">
                <expr>
                    <function function-name="SEC_TO_TIME" start-index="7" stop-index="23" text="SEC_TO_TIME(3600)">
                        <parameter>
                            <literal-expression value="3600" start-index="19" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_second">
        <projections start-index="7" stop-index="35">
            <expression-projection text="SECOND('2020-06-10 12:34:56')" start-index="7" stop-index="35">
                <expr>
                    <function function-name="SECOND" start-index="7" stop-index="35"
                              text="SECOND('2020-06-10 12:34:56')">
                        <parameter>
                            <literal-expression value="2020-06-10 12:34:56" start-index="14" stop-index="34"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_session_user">
        <projections start-index="7" stop-index="20">
            <expression-projection text="SESSION_USER()" start-index="7" stop-index="20">
                <expr>
                    <function function-name="SESSION_USER" start-index="7" stop-index="20" text="SESSION_USER()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sha">
        <projections start-index="7" stop-index="24">
            <expression-projection text="SHA('test_string')" start-index="7" stop-index="24">
                <expr>
                    <function function-name="SHA" start-index="7" stop-index="24" text="SHA('test_string')">
                        <parameter>
                            <literal-expression value="test_string" start-index="11" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sign">
        <projections start-index="7" stop-index="14">
            <expression-projection text="SIGN(-5)" start-index="7" stop-index="14">
                <expr>
                    <function function-name="SIGN" start-index="7" stop-index="14" text="SIGN(-5)">
                        <parameter>
                            <literal-expression value="-5" start-index="12" stop-index="13"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sin">
        <projections start-index="7" stop-index="14">
            <expression-projection text="SIN(0.5)" start-index="7" stop-index="14">
                <expr>
                    <function function-name="SIN" start-index="7" stop-index="14" text="SIN(0.5)">
                        <parameter>
                            <literal-expression value="0.5" start-index="11" stop-index="13"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sleep">
        <projections start-index="7" stop-index="14">
            <expression-projection text="SLEEP(5)" start-index="7" stop-index="14">
                <expr>
                    <function function-name="SLEEP" start-index="7" stop-index="14" text="SLEEP(5)">
                        <parameter>
                            <literal-expression value="5" start-index="13" stop-index="13"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_char_length">
        <projections start-index="7" stop-index="24">
            <expression-projection start-index="7" stop-index="24" text="CHAR_LENGTH('str')">
                <expr>
                    <function function-name="CHAR_LENGTH" text="CHAR_LENGTH('str')" start-index="7" stop-index="24">
                        <parameter>
                            <literal-expression value="str" start-index="19" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_character_length">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="CHARACTER_LENGTH('str')">
                <expr>
                    <function function-name="CHARACTER_LENGTH" text="CHARACTER_LENGTH('str')" start-index="7"
                              stop-index="29">
                        <parameter>
                            <literal-expression value="str" start-index="24" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_charset">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="CHARSET('str')">
                <expr>
                    <function function-name="CHARSET" text="CHARSET('str')" start-index="7" stop-index="20">
                        <parameter>
                            <literal-expression value="str" start-index="15" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_coalesce">
        <projections start-index="7" stop-index="23">
            <expression-projection start-index="7" stop-index="23" text="COALESCE(null, 1)">
                <expr>
                    <function function-name="COALESCE" text="COALESCE(null, 1)" start-index="7" stop-index="23">
                        <parameter>
                            <literal-expression value="null" start-index="16" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="22" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_coercibility">
        <projections start-index="7" stop-index="25">
            <expression-projection start-index="7" stop-index="25" text="COERCIBILITY('str')">
                <expr>
                    <function function-name="COERCIBILITY" text="COERCIBILITY('str')" start-index="7" stop-index="25">
                        <parameter>
                            <literal-expression value="str" start-index="20" stop-index="24"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_collation">
        <projections start-index="7" stop-index="22">
            <expression-projection start-index="7" stop-index="22" text="COLLATION('str')">
                <expr>
                    <function function-name="COLLATION" text="COLLATION('str')" start-index="7" stop-index="22">
                        <parameter>
                            <literal-expression value="str" start-index="17" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_compress">
        <projections start-index="7" stop-index="21">
            <expression-projection start-index="7" stop-index="21" text="COMPRESS('str')">
                <expr>
                    <function function-name="COMPRESS" text="COMPRESS('str')" start-index="7" stop-index="21">
                        <parameter>
                            <literal-expression value="str" start-index="16" stop-index="20"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_concat">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="CONCAT('My', 'S', 'QL')">
                <expr>
                    <function function-name="CONCAT" text="CONCAT('My', 'S', 'QL')" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="My" start-index="14" stop-index="17"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="S" start-index="20" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="QL" start-index="25" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_concat_ws">
        <projections start-index="7" stop-index="62">
            <expression-projection start-index="7" stop-index="62"
                                   text="CONCAT_WS(',', 'First name', 'Second name', 'Last Name')">
                <expr>
                    <function function-name="CONCAT_WS" text="CONCAT_WS(',', 'First name', 'Second name', 'Last Name')"
                              start-index="7" stop-index="62">
                        <parameter>
                            <literal-expression value="," start-index="17" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="First name" start-index="22" stop-index="33"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Second name" start-index="36" stop-index="48"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Last Name" start-index="51" stop-index="61"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_connection_id">
        <projections start-index="7" stop-index="21">
            <expression-projection start-index="7" stop-index="21" text="CONNECTION_ID()">
                <expr>
                    <function function-name="CONNECTION_ID" text="CONNECTION_ID()" start-index="7" stop-index="21"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_conv">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="CONV('a',16,2)">
                <expr>
                    <function function-name="CONV" text="CONV('a',16,2)" start-index="7" stop-index="20">
                        <parameter>
                            <literal-expression value="a" start-index="12" stop-index="14"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="16" start-index="16" stop-index="17"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="2" start-index="19" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_convert_function_using_character_set">
        <projections start-index="7" stop-index="34">
            <expression-projection text="CONVERT('abc' USING utf8mb4)" start-index="7" stop-index="34">
                <expr>
                    <function function-name="CONVERT" start-index="7" stop-index="34"
                              text="CONVERT('abc' USING utf8mb4)">
                        <parameter>
                            <literal-expression value="abc" start-index="15" stop-index="19"/>
                            <using-character-set text="USING utf8mb4" start-index="20" stop-index="34"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_convert_tz_function">
        <projections start-index="7" stop-index="51">
            <expression-projection text="CONVERT_TZ('2004-01-01 12:00:00','GMT','MET')" start-index="7" stop-index="51">
                <expr>
                    <function function-name="CONVERT_TZ" start-index="7" stop-index="51"
                              text="CONVERT_TZ('2004-01-01 12:00:00','GMT','MET')">
                        <parameter>
                            <literal-expression value="2004-01-01 12:00:00" start-index="18" stop-index="38"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="GMT" start-index="40" stop-index="44"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="MET" start-index="46" stop-index="50"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_cos_function">
        <projections start-index="7" stop-index="15">
            <expression-projection text="COS(PI())" start-index="7" stop-index="15">
                <expr>
                    <function function-name="COS" start-index="7" stop-index="15" text="COS(PI())">
                        <parameter>
                            <function function-name="PI" start-index="11" stop-index="14" text="PI()">
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_cot_function">
        <projections start-index="7" stop-index="13">
            <expression-projection text="COT(12)" start-index="7" stop-index="13">
                <expr>
                    <function function-name="COT" start-index="7" stop-index="13" text="COT(12)">
                        <parameter>
                            <literal-expression value="12" start-index="11" stop-index="12"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_crc32_function">
        <projections start-index="7" stop-index="20">
            <expression-projection text="CRC32('MySQL')" start-index="7" stop-index="20">
                <expr>
                    <function function-name="CRC32" start-index="7" stop-index="20" text="CRC32('MySQL')">
                        <parameter>
                            <literal-expression value="MySQL" start-index="13" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_window_with_cume_dist_function">
        <from>
            <simple-table name="numbers" start-index="36" stop-index="42"/>
        </from>
        <projections start-index="7" stop-index="29">
            <column-projection name="val" start-index="7" stop-index="9"/>
            <expression-projection text="CUME_DIST() OVER()" start-index="12" stop-index="29">
                <expr>
                    <function function-name="CUME_DIST" start-index="12" stop-index="29" text="CUME_DIST() OVER()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_curdate_function">
        <projections start-index="7" stop-index="15">
            <expression-projection text="CURDATE()" start-index="7" stop-index="15">
                <expr>
                    <function function-name="CURDATE" start-index="7" stop-index="15" text="CURDATE()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_current_date_function">
        <projections start-index="7" stop-index="20">
            <expression-projection text="CURRENT_DATE()" start-index="7" stop-index="20">
                <expr>
                    <function function-name="CURRENT_DATE" start-index="7" stop-index="20" text="CURRENT_DATE()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_current_time_function">
        <projections start-index="7" stop-index="20">
            <expression-projection text="CURRENT_TIME()" start-index="7" stop-index="20">
                <expr>
                    <function function-name="CURRENT_TIME" start-index="7" stop-index="20" text="CURRENT_TIME()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_current_timestamp_function">
        <projections start-index="7" stop-index="25">
            <expression-projection text="CURRENT_TIMESTAMP()" start-index="7" stop-index="25">
                <expr>
                    <function function-name="CURRENT_TIMESTAMP" start-index="7" stop-index="25"
                              text="CURRENT_TIMESTAMP()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_curtime_function">
        <projections start-index="7" stop-index="15">
            <expression-projection text="CURTIME()" start-index="7" stop-index="15">
                <expr>
                    <function function-name="CURTIME" start-index="7" stop-index="15" text="CURTIME()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_date_function">
        <projections start-index="7" stop-index="33">
            <expression-projection text="DATE('2003-12-31 01:02:03')" start-index="7" stop-index="33">
                <expr>
                    <function function-name="DATE" start-index="7" stop-index="33" text="DATE('2003-12-31 01:02:03')">
                        <parameter>
                            <literal-expression value="2003-12-31 01:02:03" start-index="12" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_date_add_function">
        <projections start-index="7" stop-index="43">
            <expression-projection text="DATE_ADD('2018-05-01',INTERVAL 1 DAY)" start-index="7" stop-index="43">
                <expr>
                    <function function-name="DATE_ADD" start-index="7" stop-index="43"
                              text="DATE_ADD('2018-05-01',INTERVAL 1 DAY)">
                        <parameter>
                            <literal-expression value="2018-05-01" start-index="16" stop-index="27"/>
                        </parameter>
                        <parameter>
                            <function function-name="INTERVAL" text="INTERVAL" start-index="29" stop-index="36">
                                <parameter>
                                    <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                                    <literal-expression value="1" start-index="38" stop-index="38"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="DAY" start-index="40" stop-index="42"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_date_format_function">
        <projections start-index="7" stop-index="52">
            <expression-projection text="DATE_FORMAT('2009-10-04 22:23:00', '%W %M %Y')" start-index="7"
                                   stop-index="52">
                <expr>
                    <function function-name="DATE_FORMAT" start-index="7" stop-index="52"
                              text="DATE_FORMAT('2009-10-04 22:23:00', '%W %M %Y')">
                        <parameter>
                            <literal-expression value="2009-10-04 22:23:00" start-index="19" stop-index="39"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="%W %M %Y" start-index="42" stop-index="51"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_date_sub_function">
        <projections start-index="7" stop-index="43">
            <expression-projection text="DATE_SUB('2018-05-01',INTERVAL 1 DAY)" start-index="7" stop-index="43">
                <expr>
                    <function function-name="DATE_SUB" start-index="7" stop-index="43"
                              text="DATE_SUB('2018-05-01',INTERVAL 1 DAY)">
                        <parameter>
                            <literal-expression value="2018-05-01" start-index="16" stop-index="27"/>
                        </parameter>
                        <parameter>
                            <function function-name="INTERVAL" text="INTERVAL" start-index="29" stop-index="36">
                                <parameter>
                                    <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                                    <literal-expression value="1" start-index="38" stop-index="38"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="DAY" start-index="40" stop-index="42"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_datediff_function">
        <projections start-index="7" stop-index="50">
            <expression-projection text="DATEDIFF('2007-12-31 23:59:59','2007-12-30')" start-index="7" stop-index="50">
                <expr>
                    <function function-name="DATEDIFF" start-index="7" stop-index="50"
                              text="DATEDIFF('2007-12-31 23:59:59','2007-12-30')">
                        <parameter>
                            <literal-expression value="2007-12-31 23:59:59" start-index="16" stop-index="36"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="2007-12-30" start-index="38" stop-index="49"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_day_function">
        <projections start-index="7" stop-index="23">
            <expression-projection text="DAY('2007-02-03')" start-index="7" stop-index="23">
                <expr>
                    <function function-name="DAY" start-index="7" stop-index="23" text="DAY('2007-02-03')">
                        <parameter>
                            <literal-expression value="2007-02-03" start-index="11" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_dayname_function">
        <projections start-index="7" stop-index="27">
            <expression-projection text="DAYNAME('2007-02-03')" start-index="7" stop-index="27">
                <expr>
                    <function function-name="DAYNAME" start-index="7" stop-index="27" text="DAYNAME('2007-02-03')">
                        <parameter>
                            <literal-expression value="2007-02-03" start-index="15" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_dayofmonth_function">
        <projections start-index="7" stop-index="30">
            <expression-projection text="DAYOFMONTH('2007-02-03')" start-index="7" stop-index="30">
                <expr>
                    <function function-name="DAYOFMONTH" start-index="7" stop-index="30"
                              text="DAYOFMONTH('2007-02-03')">
                        <parameter>
                            <literal-expression value="2007-02-03" start-index="18" stop-index="29"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_dayofweek_function">
        <projections start-index="7" stop-index="29">
            <expression-projection text="DAYOFWEEK('2007-02-03')" start-index="7" stop-index="29">
                <expr>
                    <function function-name="DAYOFWEEK" start-index="7" stop-index="29" text="DAYOFWEEK('2007-02-03')">
                        <parameter>
                            <literal-expression value="2007-02-03" start-index="17" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_dayofyear_function">
        <projections start-index="7" stop-index="29">
            <expression-projection text="DAYOFYEAR('2007-02-03')" start-index="7" stop-index="29">
                <expr>
                    <function function-name="DAYOFYEAR" start-index="7" stop-index="29" text="DAYOFYEAR('2007-02-03')">
                        <parameter>
                            <literal-expression value="2007-02-03" start-index="17" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_default_function">
        <projections start-index="7" stop-index="18">
            <expression-projection text="DEFAULT(val)" start-index="7" stop-index="18">
                <expr>
                    <function function-name="DEFAULT" start-index="7" stop-index="18" text="DEFAULT(val)">
                        <parameter>
                            <column name="val" start-index="15" stop-index="17"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="numbers" start-index="25" stop-index="31"/>
        </from>
    </select>

    <select sql-case-id="select_degrees_function">
        <projections start-index="7" stop-index="19">
            <expression-projection text="DEGREES(PI())" start-index="7" stop-index="19">
                <expr>
                    <function function-name="DEGREES" start-index="7" stop-index="19" text="DEGREES(PI())">
                        <parameter>
                            <function function-name="PI" start-index="15" stop-index="18" text="PI()">
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_window_with_dense_rank_function">
        <from>
            <simple-table name="numbers" start-index="37" stop-index="43"/>
        </from>
        <projections start-index="7" stop-index="30">
            <column-projection name="val" start-index="7" stop-index="9"/>
            <expression-projection text="DENSE_RANK() OVER()" start-index="12" stop-index="30">
                <expr>
                    <function function-name="DENSE_RANK" start-index="12" stop-index="30" text="DENSE_RANK() OVER()"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_elt">
        <projections start-index="7" stop-index="24">
            <expression-projection start-index="7" stop-index="24" text="ELT(1, 'Aa', 'Bb')">
                <expr>
                    <function function-name="ELT" text="ELT(1, 'Aa', 'Bb')" start-index="7" stop-index="24">
                        <parameter>
                            <literal-expression value="1" start-index="11" stop-index="11"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Aa" start-index="14" stop-index="17"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Bb" start-index="20" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_exp">
        <projections start-index="7" stop-index="12">
            <expression-projection start-index="7" stop-index="12" text="EXP(2)">
                <expr>
                    <function function-name="EXP" text="EXP(2)" start-index="7" stop-index="12">
                        <parameter>
                            <literal-expression value="2" start-index="11" stop-index="11"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_export_set">
        <projections start-index="7" stop-index="33">
            <expression-projection start-index="7" stop-index="33" text="EXPORT_SET(5,'Y','N',',',4)">
                <expr>
                    <function function-name="EXPORT_SET" text="EXPORT_SET(5,'Y','N',',',4)" start-index="7"
                              stop-index="33">
                        <parameter>
                            <literal-expression value="5" start-index="18" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Y" start-index="20" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="N" start-index="24" stop-index="26"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="," start-index="28" stop-index="30"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="32" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_extractvalue">
        <projections start-index="7" stop-index="41">
            <expression-projection start-index="7" stop-index="41"
                                   text="ExtractValue('&lt;a&gt;&lt;b/&gt;&lt;/a&gt;', '/a/b')">
                <expr>
                    <function function-name="ExtractValue" text="ExtractValue('&lt;a&gt;&lt;b/&gt;&lt;/a&gt;', '/a/b')"
                              start-index="7" stop-index="41">
                        <parameter>
                            <literal-expression value="&lt;a&gt;&lt;b/&gt;&lt;/a&gt;" start-index="20" stop-index="32"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="/a/b" start-index="35" stop-index="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_field">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="FIELD('Bb', 'Aa', 'Bb')">
                <expr>
                    <function function-name="FIELD" text="FIELD('Bb', 'Aa', 'Bb')" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="Bb" start-index="13" stop-index="16"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Aa" start-index="19" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Bb" start-index="25" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_find_in_set">
        <projections start-index="7" stop-index="32">
            <expression-projection start-index="7" stop-index="32" text="FIND_IN_SET('b','a,b,c,d')">
                <expr>
                    <function function-name="FIND_IN_SET" text="FIND_IN_SET('b','a,b,c,d')" start-index="7"
                              stop-index="32">
                        <parameter>
                            <literal-expression value="b" start-index="19" stop-index="21"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="a,b,c,d" start-index="23" stop-index="31"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_first_value">
        <projections start-index="7" stop-index="42">
            <expression-projection start-index="7" stop-index="42" text="FIRST_VALUE(name) OVER (ORDER BY id)">
                <expr>
                    <function function-name="FIRST_VALUE" text="FIRST_VALUE(name) OVER (ORDER BY id)" start-index="7"
                              stop-index="42">
                        <parameter>
                            <column name="name" start-index="19" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
    </select>

    <select sql-case-id="select_floor">
        <projections start-index="7" stop-index="17">
            <expression-projection start-index="7" stop-index="17" text="FLOOR(1.23)">
                <expr>
                    <function function-name="FLOOR" text="FLOOR(1.23)" start-index="7" stop-index="17">
                        <parameter>
                            <literal-expression value="1.23" start-index="13" stop-index="16"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_format">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="FORMAT(12332.123456, 4)">
                <expr>
                    <function function-name="FORMAT" text="FORMAT(12332.123456, 4)" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="12332.123456" start-index="14" stop-index="25"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="28" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_format_bytes">
        <projections start-index="7" stop-index="23">
            <expression-projection start-index="7" stop-index="23" text="FORMAT_BYTES(512)">
                <expr>
                    <function function-name="FORMAT_BYTES" text="FORMAT_BYTES(512)" start-index="7" stop-index="23">
                        <parameter>
                            <literal-expression value="512" start-index="20" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_parse">
        <projections start-index="7" stop-index="41">
            <expression-projection start-index="7" stop-index="41"
                                   text="JSON_PARSE('{&quot;k1&quot;:&quot;v31&quot;,&quot;k2&quot;:300}')">
                <expr>
                    <function function-name="JSON_PARSE"
                              text="JSON_PARSE('{&quot;k1&quot;:&quot;v31&quot;,&quot;k2&quot;:300}')" start-index="7"
                              stop-index="41">
                        <parameter>
                            <literal-expression value="{&quot;k1&quot;:&quot;v31&quot;,&quot;k2&quot;:300}"
                                                start-index="18" stop-index="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_parse_error_to_null">
        <projections start-index="7" stop-index="32">
            <expression-projection start-index="7" stop-index="32" text="JSON_PARSE('invalid json')">
                <expr>
                    <function function-name="JSON_PARSE" text="JSON_PARSE('invalid json')" start-index="7"
                              stop-index="32">
                        <parameter>
                            <literal-expression value="invalid json" start-index="18" stop-index="31"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_parse_error_to_value">
        <projections start-index="7" stop-index="38">
            <expression-projection start-index="7" stop-index="38" text="JSON_PARSE('invalid json', '{}')">
                <expr>
                    <function function-name="JSON_PARSE" text="JSON_PARSE('invalid json', '{}')" start-index="7"
                              stop-index="38">
                        <parameter>
                            <literal-expression value="invalid json" start-index="18" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="{}" start-index="34" stop-index="37"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_std">
        <projections start-index="7" stop-index="12">
            <expression-projection start-index="7" stop-index="12" text="STD(1)">
                <expr>
                    <function function-name="STD" text="STD(1)" start-index="7" stop-index="12">
                        <parameter>
                            <literal-expression value="1" start-index="11" stop-index="11"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_stddev">
        <projections start-index="7" stop-index="15">
            <expression-projection start-index="7" stop-index="15" text="STDDEV(1)">
                <expr>
                    <function function-name="STDDEV" text="STDDEV(1)" start-index="7" stop-index="15">
                        <parameter>
                            <literal-expression value="1" start-index="14" stop-index="14"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_stddev_pop">
        <projections start-index="7" stop-index="19">
            <expression-projection start-index="7" stop-index="19" text="STDDEV_POP(1)">
                <expr>
                    <function function-name="STDDEV_POP" text="STDDEV_POP(1)" start-index="7" stop-index="19">
                        <parameter>
                            <literal-expression value="1" start-index="18" stop-index="18"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_stddev_samp">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="STDDEV_SAMP(1)">
                <expr>
                    <function function-name="STDDEV_SAMP" text="STDDEV_SAMP(1)" start-index="7" stop-index="20">
                        <parameter>
                            <literal-expression value="1" start-index="19" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_str_to_date">
        <projections start-index="7" stop-index="41">
            <expression-projection start-index="7" stop-index="41" text="STR_TO_DATE('01,5,2013','%d,%m,%Y')">
                <expr>
                    <function function-name="STR_TO_DATE" text="STR_TO_DATE('01,5,2013','%d,%m,%Y')" start-index="7"
                              stop-index="41">
                        <parameter>
                            <literal-expression value="01,5,2013" start-index="19" stop-index="29"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="%d,%m,%Y" start-index="31" stop-index="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_strcmp">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="STRCMP('text', 'text2')">
                <expr>
                    <function function-name="STRCMP" text="STRCMP('text', 'text2')" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="text" start-index="14" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="text2" start-index="22" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_subdate">
        <projections start-index="7" stop-index="40">
            <expression-projection start-index="7" stop-index="40" text="SUBDATE('2008-01-02 12:00:00', 31)">
                <expr>
                    <function function-name="SUBDATE" text="SUBDATE('2008-01-02 12:00:00', 31)" start-index="7"
                              stop-index="40">
                        <parameter>
                            <literal-expression value="2008-01-02 12:00:00" start-index="15" stop-index="35"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="31" start-index="38" stop-index="39"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_subtime">
        <projections start-index="7" stop-index="60">
            <expression-projection start-index="7" stop-index="60"
                                   text="SUBTIME('2007-12-31 23:59:59.999999','1 1:1:1.000002')">
                <expr>
                    <function function-name="SUBTIME" text="SUBTIME('2007-12-31 23:59:59.999999','1 1:1:1.000002')"
                              start-index="7" stop-index="60">
                        <parameter>
                            <literal-expression value="2007-12-31 23:59:59.999999" start-index="15" stop-index="42"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1 1:1:1.000002" start-index="44" stop-index="59"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sysdate">
        <projections start-index="7" stop-index="15">
            <expression-projection start-index="7" stop-index="15" text="SYSDATE()">
                <expr>
                    <function function-name="SYSDATE" text="SYSDATE()" start-index="7" stop-index="15">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_format_pico_time">
        <projections start-index="7" stop-index="28">
            <expression-projection start-index="7" stop-index="28" text="FORMAT_PICO_TIME(3501)">
                <expr>
                    <function function-name="FORMAT_PICO_TIME" text="FORMAT_PICO_TIME(3501)" start-index="7"
                              stop-index="28">
                        <parameter>
                            <literal-expression value="3501" start-index="24" stop-index="27"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_found_rows">
        <projections start-index="7" stop-index="18">
            <expression-projection start-index="7" stop-index="18" text="FOUND_ROWS()">
                <expr>
                    <function function-name="FOUND_ROWS" text="FOUND_ROWS()" start-index="7" stop-index="18"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_from_base64">
        <projections start-index="7" stop-index="35">
            <expression-projection start-index="7" stop-index="35" text="FROM_BASE64(TO_BASE64('abc'))">
                <expr>
                    <function function-name="FROM_BASE64" text="FROM_BASE64(TO_BASE64('abc'))" start-index="7"
                              stop-index="35">
                        <parameter>
                            <function function-name="TO_BASE64" text="TO_BASE64('abc')" start-index="19"
                                      stop-index="34">
                                <parameter>
                                    <literal-expression value="abc" start-index="29" stop-index="33"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_from_days">
        <projections start-index="7" stop-index="23">
            <expression-projection start-index="7" stop-index="23" text="FROM_DAYS(730669)">
                <expr>
                    <function function-name="FROM_DAYS" text="FROM_DAYS(730669)" start-index="7" stop-index="23">
                        <parameter>
                            <literal-expression value="730669" start-index="17" stop-index="22"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_from_unixtime">
        <projections start-index="7" stop-index="31">
            <expression-projection start-index="7" stop-index="31" text="FROM_UNIXTIME(1447430881)">
                <expr>
                    <function function-name="FROM_UNIXTIME" text="FROM_UNIXTIME(1447430881)" start-index="7"
                              stop-index="31">
                        <parameter>
                            <literal-expression value="1447430881" start-index="21" stop-index="30"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_get_format">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="GET_FORMAT(DATE, 'EUR')">
                <function function-name="GET_FORMAT" text="GET_FORMAT(DATE, 'EUR')" start-index="7" stop-index="29">
                    <parameter>
                        <literal-expression value="DATE" start-index="18" stop-index="21"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="EUR" start-index="24" stop-index="28"/>
                    </parameter>
                </function>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_get_lock">
        <projections start-index="7" stop-index="26">
            <expression-projection start-index="7" stop-index="26" text="GET_LOCK('lock1',10)">
                <expr>
                    <function function-name="GET_LOCK" text="GET_LOCK('lock1',10)" start-index="7" stop-index="26">
                        <parameter>
                            <literal-expression value="lock1" start-index="16" stop-index="22"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="24" stop-index="25"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_greatest">
        <projections start-index="7" stop-index="19">
            <expression-projection start-index="7" stop-index="19" text="GREATEST(2,0)">
                <expr>
                    <function function-name="GREATEST" text="GREATEST(2,0)" start-index="7" stop-index="19">
                        <parameter>
                            <literal-expression value="2" start-index="16" stop-index="16"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="0" start-index="18" stop-index="18"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_grouping">
        <from>
            <simple-table name="t_order" start-index="29" stop-index="35"/>
        </from>
        <projections start-index="7" stop-index="22">
            <expression-projection text="GROUPING(status)" start-index="7" stop-index="22"/>
        </projections>
        <order-by>
            <column-item name="status" start-index="46" stop-index="51"/>
        </order-by>
    </select>

    <select sql-case-id="select_gtid_subset">
        <projections start-index="7" stop-index="105">
            <expression-projection start-index="7" stop-index="105"
                                   text="GTID_SUBSET('3E11FA47-71CA-11E1-9E33-C80AA9429562:23','3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57')">
                <expr>
                    <function function-name="GTID_SUBSET"
                              text="GTID_SUBSET('3E11FA47-71CA-11E1-9E33-C80AA9429562:23','3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57')"
                              start-index="7" stop-index="105">
                        <parameter>
                            <literal-expression value="3E11FA47-71CA-11E1-9E33-C80AA9429562:23" start-index="19"
                                                stop-index="59"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57" start-index="61"
                                                stop-index="104"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_gtid_subtract">
        <projections start-index="7" stop-index="107">
            <expression-projection start-index="7" stop-index="107"
                                   text="GTID_SUBTRACT('3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57','3E11FA47-71CA-11E1-9E33-C80AA9429562:21')">
                <expr>
                    <function start-index="7" stop-index="107" function-name="GTID_SUBTRACT"
                              text="GTID_SUBTRACT('3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57','3E11FA47-71CA-11E1-9E33-C80AA9429562:21')">
                        <parameter>
                            <literal-expression start-index="21" stop-index="64"
                                                value="3E11FA47-71CA-11E1-9E33-C80AA9429562:21-57"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="66" stop-index="106"
                                                value="3E11FA47-71CA-11E1-9E33-C80AA9429562:21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_hex">
        <projections start-index="7" stop-index="16">
            <expression-projection start-index="7" stop-index="16" text="HEX('abc')">
                <expr>
                    <function function-name="HEX" text="HEX('abc')" start-index="7" stop-index="16">
                        <parameter>
                            <literal-expression start-index="11" stop-index="15" value="abc"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_hour">
        <projections start-index="7" stop-index="22">
            <expression-projection start-index="7" stop-index="22" text="HOUR('10:05:03')">
                <expr>
                    <function function-name="HOUR" text="HOUR('10:05:03')" start-index="7" stop-index="22">
                        <parameter>
                            <literal-expression start-index="12" stop-index="21" value="10:05:03"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_ipv4">
        <projections start-index="7" stop-index="25">
            <expression-projection start-index="7" stop-index="25" text="IS_IPV4('********')">
                <expr>
                    <function function-name="IS_IPV4" text="IS_IPV4('********')" start-index="7" stop-index="25">
                        <parameter>
                            <literal-expression start-index="15" stop-index="24" value="********"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_ipv4_compat">
        <projections start-index="7" stop-index="46">
            <expression-projection start-index="7" stop-index="46" text="IS_IPV4_COMPAT(INET6_ATON('::********'))">
                <expr>
                    <function function-name="IS_IPV4_COMPAT" text="IS_IPV4_COMPAT(INET6_ATON('::********'))"
                              start-index="7" stop-index="46">
                        <parameter>
                            <function function-name="INET6_ATON" text="INET6_ATON('::********')" start-index="22"
                                      stop-index="45">
                                <parameter>
                                    <literal-expression start-index="33" stop-index="44" value="::********"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_ipv4_mapped">
        <projections start-index="7" stop-index="46">
            <expression-projection start-index="7" stop-index="46" text="IS_IPV4_MAPPED(INET6_ATON('::********'))">
                <expr>
                    <function function-name="IS_IPV4_MAPPED" text="IS_IPV4_MAPPED(INET6_ATON('::********'))"
                              start-index="7" stop-index="46">
                        <parameter>
                            <function function-name="INET6_ATON" text="INET6_ATON('::********')" start-index="22"
                                      stop-index="45">
                                <parameter>
                                    <literal-expression start-index="33" stop-index="44" value="::********"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_ipv6">
        <projections start-index="7" stop-index="25">
            <expression-projection start-index="7" stop-index="25" text="IS_IPV6('********')">
                <expr>
                    <function function-name="IS_IPV6" text="IS_IPV6('********')" start-index="7" stop-index="25">
                        <parameter>
                            <literal-expression start-index="15" stop-index="24" value="********"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_free_lock">
        <projections start-index="7" stop-index="27">
            <expression-projection start-index="7" stop-index="27" text="IS_FREE_LOCK('lock1')">
                <expr>
                    <function function-name="IS_FREE_LOCK" text="IS_FREE_LOCK('lock1')" start-index="7" stop-index="27">
                        <parameter>
                            <literal-expression start-index="20" stop-index="26" value="lock1"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_used_lock">
        <projections start-index="7" stop-index="27">
            <expression-projection start-index="7" stop-index="27" text="IS_USED_LOCK('lock1')">
                <expr>
                    <function function-name="IS_USED_LOCK" text="IS_USED_LOCK('lock1')" start-index="7" stop-index="27">
                        <parameter>
                            <literal-expression start-index="20" stop-index="26" value="lock1"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_null">
        <projections start-index="7" stop-index="17">
            <expression-projection start-index="7" stop-index="17" text="ISNULL(1+1)">
                <expr>
                    <function function-name="ISNULL" text="ISNULL(1+1)" start-index="7" stop-index="17">
                        <parameter>
                            <binary-operation-expression start-index="14" stop-index="16" literal-start-index="14"
                                                         literal-stop-index="16">
                                <left>
                                    <literal-expression value="1" start-index="14" stop-index="14"
                                                        literal-start-index="14" literal-stop-index="14"/>
                                </left>
                                <operator>+</operator>
                                <right>
                                    <literal-expression value="1" start-index="16" stop-index="16"
                                                        literal-start-index="16" literal-stop-index="16"/>
                                </right>
                            </binary-operation-expression>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_not_null">
        <projections start-index="7" stop-index="19">
            <expression-projection text="1 IS NOT NULL" start-index="7" stop-index="19">
                <expr>
                    <binary-operation-expression start-index="7" stop-index="19">
                        <left>
                            <literal-expression value="1" start-index="7" stop-index="7"/>
                        </left>
                        <operator>IS</operator>
                        <right>
                            <literal-expression value="NOT NULL" start-index="12" stop-index="19"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_is_uuid">
        <projections start-index="7" stop-index="53">
            <expression-projection start-index="7" stop-index="53"
                                   text="IS_UUID('6ccd780c-baba-1026-9564-5b8c656024db')">
                <expr>
                    <function function-name="IS_UUID" text="IS_UUID('6ccd780c-baba-1026-9564-5b8c656024db')"
                              start-index="7" stop-index="53">
                        <parameter>
                            <literal-expression start-index="15" stop-index="52"
                                                value="6ccd780c-baba-1026-9564-5b8c656024db"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_random_bytes">
        <projections start-index="7" stop-index="22">
            <expression-projection start-index="7" stop-index="22" text="RANDOM_BYTES(16)">
                <expr>
                    <function function-name="RANDOM_BYTES" text="RANDOM_BYTES(16)" start-index="7" stop-index="22">
                        <parameter>
                            <literal-expression value="16" start-index="20" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_rank">
        <projections start-index="7" stop-index="12">
            <expression-projection start-index="7" stop-index="12" text="RANK()">
                <expr>
                    <function function-name="RANK" start-index="7" stop-index="12" text="RANK()">
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="numbers" start-index="19" stop-index="25"/>
        </from>
    </select>

    <select sql-case-id="select_regexp_instr">
        <projections start-index="7" stop-index="40">
            <expression-projection start-index="7" stop-index="40" text="REGEXP_INSTR('dog cat dog', 'dog')">
                <expr>
                    <function function-name="REGEXP_INSTR" text="REGEXP_INSTR('dog cat dog', 'dog')" start-index="7"
                              stop-index="40">
                        <parameter>
                            <literal-expression value="dog cat dog" start-index="20" stop-index="32"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="dog" start-index="35" stop-index="39"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_regexp_like">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43" text="REGEXP_LIKE('CamelCase', 'CAMELCASE')">
                <expr>
                    <function function-name="REGEXP_LIKE" text="REGEXP_LIKE('CamelCase', 'CAMELCASE')" start-index="7"
                              stop-index="43">
                        <parameter>
                            <literal-expression value="CamelCase" start-index="19" stop-index="29"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="CAMELCASE" start-index="32" stop-index="42"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_regexp_replace">
        <projections start-index="7" stop-index="39">
            <expression-projection start-index="7" stop-index="39" text="REGEXP_REPLACE('a b c', 'b', 'X')">
                <expr>
                    <function function-name="REGEXP_REPLACE" text="REGEXP_REPLACE('a b c', 'b', 'X')" start-index="7"
                              stop-index="39">
                        <parameter>
                            <literal-expression value="a b c" start-index="22" stop-index="28"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="b" start-index="31" stop-index="33"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="X" start-index="36" stop-index="38"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_regexp_substr">
        <projections start-index="7" stop-index="44">
            <expression-projection start-index="7" stop-index="44" text="REGEXP_SUBSTR('abc def ghi', '[a-z]+')">
                <expr>
                    <function function-name="REGEXP_SUBSTR" text="REGEXP_SUBSTR('abc def ghi', '[a-z]+')"
                              start-index="7" stop-index="44">
                        <parameter>
                            <literal-expression value="abc def ghi" start-index="21" stop-index="33"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="[a-z]+" start-index="36" stop-index="43"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_release_lock">
        <projections start-index="7" stop-index="27">
            <expression-projection start-index="7" stop-index="27" text="RELEASE_LOCK('lock1')">
                <expr>
                    <function function-name="RELEASE_LOCK" text="RELEASE_LOCK('lock1')" start-index="7" stop-index="27">
                        <parameter>
                            <literal-expression value="lock1" start-index="20" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_release_all_locks">
        <projections start-index="7" stop-index="25">
            <expression-projection start-index="7" stop-index="25" text="RELEASE_ALL_LOCKS()">
                <expr>
                    <function function-name="RELEASE_ALL_LOCKS" text="RELEASE_ALL_LOCKS()" start-index="7"
                              stop-index="25">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_repeat">
        <projections start-index="7" stop-index="24">
            <expression-projection start-index="7" stop-index="24" text="REPEAT('MySQL', 3)">
                <expr>
                    <function function-name="REPEAT" text="REPEAT('MySQL', 3)" start-index="7" stop-index="24">
                        <parameter>
                            <literal-expression value="MySQL" start-index="14" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="3" start-index="23" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_replace">
        <projections start-index="7" stop-index="41">
            <expression-projection start-index="7" stop-index="41" text="REPLACE('www.mysql.com', 'w', 'Ww')">
                <expr>
                    <function function-name="REPLACE" text="REPLACE('www.mysql.com', 'w', 'Ww')" start-index="7"
                              stop-index="41">
                        <parameter>
                            <literal-expression value="www.mysql.com" start-index="15" stop-index="29"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="w" start-index="32" stop-index="34"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="Ww" start-index="37" stop-index="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_reverse">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="REVERSE('abc')">
                <expr>
                    <function function-name="REVERSE" text="REVERSE('abc')" start-index="7" stop-index="20">
                        <parameter>
                            <literal-expression value="abc" start-index="15" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_right" db-types="MySQL, Doris">
        <projections start-index="7" stop-index="27">
            <expression-projection start-index="7" stop-index="27" text="RIGHT('foobarbar', 4)">
                <expr>
                    <function function-name="RIGHT" text="RIGHT('foobarbar', 4)" start-index="7" stop-index="27">
                        <parameter>
                            <literal-expression value="foobarbar" start-index="13" stop-index="23"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="26" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_roles_graphml">
        <projections start-index="7" stop-index="21">
            <expression-projection start-index="7" stop-index="21" text="ROLES_GRAPHML()">
                <expr>
                    <function function-name="ROLES_GRAPHML" text="ROLES_GRAPHML()" start-index="7" stop-index="21">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_round">
        <projections start-index="7" stop-index="17">
            <expression-projection start-index="7" stop-index="17" text="ROUND(1.58)">
                <expr>
                    <function function-name="ROUND" text="ROUND(1.58)" start-index="7" stop-index="17">
                        <parameter>
                            <literal-expression value="1.58" start-index="13" stop-index="16"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_row_count">
        <projections start-index="7" stop-index="17">
            <expression-projection start-index="7" stop-index="17" text="ROW_COUNT()">
                <expr>
                    <function function-name="ROW_COUNT" text="ROW_COUNT()" start-index="7" stop-index="17">
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_md5">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="MD5('testing')">
                <expr>
                    <function function-name="MD5" text="MD5('testing')" start-index="7" stop-index="20">
                        <parameter>
                            <literal-expression value="testing" start-index="11" stop-index="19"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_member_of">
        <projections start-index="7" stop-index="49">
            <expression-projection start-index="7" stop-index="49"
                                   text="'ab' MEMBER OF('[23, &quot;abc&quot;, 17, &quot;ab&quot;, 10]')">
                <expr>
                    <binary-operation-expression start-index="7" stop-index="49">
                        <left>
                            <literal-expression value="ab" start-index="7" stop-index="10"/>
                        </left>
                        <operator>MEMBER OF</operator>
                        <right>
                            <expression-projection text="'[23, &quot;abc&quot;, 17, &quot;ab&quot;, 10]'"
                                                   start-index="22" stop-index="48"/>
                        </right>
                    </binary-operation-expression>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_microsecond">
        <projections start-index="7" stop-index="36">
            <expression-projection start-index="7" stop-index="36" text="MICROSECOND('12:00:00.123456')">
                <expr>
                    <function function-name="MICROSECOND" text="MICROSECOND('12:00:00.123456')" start-index="7"
                              stop-index="36">
                        <parameter>
                            <literal-expression value="12:00:00.123456" start-index="19" stop-index="35"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_mid">
        <projections start-index="7" stop-index="29">
            <expression-projection text="MID('foobarbar' from 4)" start-index="7" stop-index="29">
                <expr>
                    <function function-name="MID" start-index="7" stop-index="29" text="MID('foobarbar' from 4)">
                        <parameter>
                            <literal-expression value="foobarbar" start-index="11" stop-index="21"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="28" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_minute">
        <projections start-index="7" stop-index="35">
            <expression-projection start-index="7" stop-index="35" text="MINUTE('2008-02-03 10:05:03')">
                <expr>
                    <function function-name="MINUTE" text="MINUTE('2008-02-03 10:05:03')" start-index="7"
                              stop-index="35">
                        <parameter>
                            <literal-expression value="2008-02-03 10:05:03" start-index="14" stop-index="34"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_mod">
        <projections start-index="7" stop-index="18">
            <expression-projection start-index="7" stop-index="18" text="MOD(234, 10)">
                <expr>
                    <function function-name="MOD" text="MOD(234, 10)" start-index="7" stop-index="18">
                        <parameter>
                            <literal-expression value="234" start-index="11" stop-index="13"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="16" stop-index="17"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_month">
        <projections start-index="7" stop-index="25">
            <expression-projection start-index="7" stop-index="25" text="MONTH('2008-02-03')">
                <expr>
                    <function function-name="MONTH" text="MONTH('2008-02-03')" start-index="7" stop-index="25">
                        <parameter>
                            <literal-expression value="2008-02-03" start-index="13" stop-index="24"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_monthname">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="MONTHNAME('2008-02-03')">
                <expr>
                    <function function-name="MONTHNAME" text="MONTHNAME('2008-02-03')" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="2008-02-03" start-index="17" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_multilinestring">
        <projections start-index="7" stop-index="102">
            <expression-projection start-index="7" stop-index="102"
                                   text="MultiLineString(ST_GeomFromText('LineString(1 1, 2 2)'),ST_GeomFromText('LineString(1 1, 2 2)'))">
                <expr>
                    <function function-name="MultiLineString"
                              text="MultiLineString(ST_GeomFromText('LineString(1 1, 2 2)'),ST_GeomFromText('LineString(1 1, 2 2)'))"
                              start-index="7" stop-index="102">
                        <parameter>
                            <function function-name="ST_GeomFromText" text="ST_GeomFromText('LineString(1 1, 2 2)')"
                                      start-index="23" stop-index="61">
                                <parameter>
                                    <literal-expression value="LineString(1 1, 2 2)" start-index="39" stop-index="60"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function function-name="ST_GeomFromText" text="ST_GeomFromText('LineString(1 1, 2 2)')"
                                      start-index="63" stop-index="101">
                                <parameter>
                                    <literal-expression value="LineString(1 1, 2 2)" start-index="79" stop-index="100"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_multipoint">
        <projections start-index="7" stop-index="39">
            <expression-projection start-index="7" stop-index="39" text="MultiPoint(point(1,1),point(1,1))">
                <expr>
                    <function function-name="MultiPoint" text="MultiPoint(point(1,1),point(1,1))" start-index="7"
                              stop-index="39">
                        <parameter>
                            <function function-name="point" text="point(1,1)" start-index="18" stop-index="27">
                                <parameter>
                                    <literal-expression value="1" start-index="24" stop-index="24"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="1" start-index="26" stop-index="26"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function function-name="point" text="point(1,1)" start-index="29" stop-index="38">
                                <parameter>
                                    <literal-expression value="1" start-index="35" stop-index="35"/>
                                </parameter>
                                <parameter>
                                    <literal-expression value="1" start-index="37" stop-index="37"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_multipolygon">
        <projections start-index="7" stop-index="127">
            <expression-projection start-index="7" stop-index="127"
                                   text="MultiPolygon(ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))'),ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))'))">
                <expr>
                    <function function-name="MultiPolygon"
                              text="MultiPolygon(ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))'),ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))'))"
                              start-index="7" stop-index="127">
                        <parameter>
                            <function function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))')" start-index="20"
                                      stop-index="72">
                                <parameter>
                                    <literal-expression value="Polygon((0 0, 1 0, 1 1, 0 1, 0 0))" start-index="36"
                                                        stop-index="71"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))')" start-index="74"
                                      stop-index="126">
                                <parameter>
                                    <literal-expression value="Polygon((0 0, 1 0, 1 1, 0 1, 0 0))" start-index="90"
                                                        stop-index="125"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_polygon">
        <projections start-index="7" stop-index="59">
            <expression-projection start-index="7" stop-index="59"
                                   text="ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))')">
                <expr>
                    <function function-name="ST_GeomFromText"
                              text="ST_GeomFromText('Polygon((0 0, 1 0, 1 1, 0 1, 0 0))')" start-index="7"
                              stop-index="59">
                        <parameter>
                            <literal-expression value="Polygon((0 0, 1 0, 1 1, 0 1, 0 0))" start-index="23"
                                                stop-index="58"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_point">
        <projections start-index="7" stop-index="16">
            <expression-projection start-index="7" stop-index="16" text="Point(1,1)">
                <expr>
                    <function function-name="Point" text="Point(1,1)" start-index="7" stop-index="16">
                        <parameter>
                            <literal-expression value="1" start-index="13" stop-index="13"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="1" start-index="15" stop-index="15"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ps_current_thread_id">
        <projections start-index="7" stop-index="28">
            <expression-projection start-index="7" stop-index="28" text="PS_CURRENT_THREAD_ID()">
                <expr>
                    <function function-name="PS_CURRENT_THREAD_ID" text="PS_CURRENT_THREAD_ID()" start-index="7"
                              stop-index="28"/>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_ps_thread_id">
        <projections start-index="7" stop-index="21">
            <expression-projection start-index="7" stop-index="21" text="PS_THREAD_ID(5)">
                <expr>
                    <function function-name="PS_THREAD_ID" text="PS_THREAD_ID(5)" start-index="7" stop-index="21">
                        <parameter>
                            <literal-expression value="5" start-index="20" stop-index="20"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_quote">
        <projections start-index="7" stop-index="22">
            <expression-projection start-index="7" stop-index="22" text="QUOTE('Don\'t!')">
                <expr>
                    <function function-name="QUOTE" text="QUOTE('Don\'t!')" start-index="7" stop-index="22">
                        <parameter>
                            <literal-expression value="Don\'t!" start-index="13" stop-index="21"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>


    <select sql-case-id="select_json_objectagg">
        <projections start-index="7" stop-index="38">
            <expression-projection start-index="7" stop-index="38" text="JSON_OBJECTAGG(attribute, value)">
                <expr>
                    <function function-name="JSON_OBJECTAGG" text="JSON_OBJECTAGG(attribute, value)" start-index="7"
                              stop-index="38">
                        <parameter>
                            <column name="attribute" start-index="22" stop-index="30"/>
                        </parameter>
                        <parameter>
                            <column name="value" start-index="33" stop-index="37"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t" start-index="45" stop-index="45"/>
        </from>
        <group-by>
            <column-item name="id" start-index="56" stop-index="57"/>
        </group-by>
    </select>

    <select sql-case-id="select_json_overlaps">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43"
                                   text="JSON_OVERLAPS(&quot;[1,3,5,7]&quot;, &quot;[2,5,7]&quot;)">
                <expr>
                    <function function-name="JSON_OVERLAPS"
                              text="JSON_OVERLAPS(&quot;[1,3,5,7]&quot;, &quot;[2,5,7]&quot;)" start-index="7"
                              stop-index="43">
                        <parameter>
                            <literal-expression value="[1,3,5,7]" start-index="21" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="[2,5,7]" start-index="34" stop-index="42"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_pretty">
        <projections start-index="7" stop-index="24">
            <expression-projection start-index="7" stop-index="24" text="JSON_PRETTY('123')">
                <expr>
                    <function function-name="JSON_PRETTY" text="JSON_PRETTY('123')" start-index="7" stop-index="24">
                        <parameter>
                            <literal-expression value="123" start-index="19" stop-index="23"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_quote">
        <projections start-index="7" stop-index="21">
            <expression-projection start-index="7" stop-index="21" text="JSON_QUOTE('a')">
                <expr>
                    <function function-name="JSON_QUOTE" text="JSON_QUOTE('a')" start-index="7" stop-index="21">
                        <parameter>
                            <literal-expression value="a" start-index="18" stop-index="20"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_remove">
        <projections start-index="7" stop-index="44">
            <expression-projection start-index="7" stop-index="44"
                                   text="JSON_REMOVE('[&quot;a&quot;, &quot;b&quot;, &quot;d&quot;]', '$[0]')">
                <expr>
                    <function function-name="JSON_REMOVE"
                              text="JSON_REMOVE('[&quot;a&quot;, &quot;b&quot;, &quot;d&quot;]', '$[0]')"
                              start-index="7" stop-index="44">
                        <parameter>
                            <literal-expression value="[&quot;a&quot;, &quot;b&quot;, &quot;d&quot;]" start-index="19"
                                                stop-index="35"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$[0]" start-index="38" stop-index="43"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_replace">
        <projections start-index="7" stop-index="52">
            <expression-projection start-index="7" stop-index="52"
                                   text="JSON_REPLACE('{ &quot;a&quot;: 1, &quot;b&quot;: &quot;2&quot;}', '$.a', 10)">
                <expr>
                    <function function-name="JSON_REPLACE"
                              text="JSON_REPLACE('{ &quot;a&quot;: 1, &quot;b&quot;: &quot;2&quot;}', '$.a', 10)"
                              start-index="7" stop-index="52">
                        <parameter>
                            <literal-expression value="{ &quot;a&quot;: 1, &quot;b&quot;: &quot;2&quot;}"
                                                start-index="20" stop-index="40"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.a" start-index="43" stop-index="47"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="50" stop-index="51"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_schema_valid">
        <projections start-index="7" stop-index="98">
            <expression-projection start-index="7" stop-index="98"
                                   text="JSON_SCHEMA_VALID('{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}','{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}')">
                <expr>
                    <function function-name="JSON_SCHEMA_VALID"
                              text="JSON_SCHEMA_VALID('{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}','{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}')"
                              start-index="7" stop-index="98">
                        <parameter>
                            <literal-expression
                                    value="{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}"
                                    start-index="25" stop-index="71"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}"
                                                start-index="73" stop-index="97"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_schema_validation_report">
        <projections start-index="7" stop-index="110">
            <expression-projection start-index="7" stop-index="110"
                                   text="JSON_SCHEMA_VALIDATION_REPORT('{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}','{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}')">
                <expr>
                    <function function-name="JSON_SCHEMA_VALIDATION_REPORT"
                              text="JSON_SCHEMA_VALIDATION_REPORT('{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}','{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}')"
                              start-index="7" stop-index="110">
                        <parameter>
                            <literal-expression
                                    value="{&quot;type&quot;:&quot;object&quot;,&quot;required&quot;:[&quot;name&quot;,&quot;value&quot;]}"
                                    start-index="37" stop-index="83"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="{&quot;name&quot;:&quot;a&quot;,&quot;value&quot;:10}"
                                                start-index="85" stop-index="109"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_search">
        <projections start-index="7" stop-index="54">
            <expression-projection start-index="7" stop-index="54"
                                   text="JSON_SEARCH('[&quot;abc&quot;,{&quot;x&quot;:&quot;abc&quot;}]', 'one', 'abc')">
                <expr>
                    <function function-name="JSON_SEARCH"
                              text="JSON_SEARCH('[&quot;abc&quot;,{&quot;x&quot;:&quot;abc&quot;}]', 'one', 'abc')"
                              start-index="7" stop-index="54">
                        <parameter>
                            <literal-expression value="[&quot;abc&quot;,{&quot;x&quot;:&quot;abc&quot;}]"
                                                start-index="19" stop-index="39"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="one" start-index="42" stop-index="46"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="abc" start-index="49" stop-index="53"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_set">
        <projections start-index="7" stop-index="44">
            <expression-projection start-index="7" stop-index="44"
                                   text="JSON_SET('{&quot;a&quot;:1,&quot;b&quot;:[2,3]}','$.c',10)">
                <expr>
                    <function start-index="7" stop-index="44" function-name="JSON_SET"
                              text="JSON_SET('{&quot;a&quot;:1,&quot;b&quot;:[2,3]}','$.c',10)">
                        <parameter>
                            <literal-expression value="{&quot;a&quot;:1,&quot;b&quot;:[2,3]}" start-index="16"
                                                stop-index="34"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="$.c" start-index="36" stop-index="40"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="42" stop-index="43"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_storage_free">
        <projections start-index="7" stop-index="33">
            <expression-projection start-index="7" stop-index="33" text="JSON_STORAGE_FREE(json_col)">
                <expr>
                    <function start-index="7" stop-index="33" function-name="JSON_STORAGE_FREE"
                              text="JSON_STORAGE_FREE(json_col)">
                        <parameter>
                            <column name="json_col" start-index="25" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t" start-index="40" stop-index="40"/>
        </from>
    </select>

    <select sql-case-id="select_json_storage_size">
        <projections start-index="7" stop-index="33">
            <expression-projection start-index="7" stop-index="33" text="JSON_STORAGE_SIZE(json_col)">
                <expr>
                    <function start-index="7" stop-index="33" function-name="JSON_STORAGE_SIZE"
                              text="JSON_STORAGE_SIZE(json_col)">
                        <parameter>
                            <column name="json_col" start-index="25" stop-index="32"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
        <from>
            <simple-table name="t" start-index="40" stop-index="40"/>
        </from>
    </select>

    <select sql-case-id="select_json_table">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from start-index="14" stop-index="100">
            <function-table start-index="14" stop-index="100" table-alias="t">
                <table-function function-name="JSON_TABLE"
                                text="JSON_TABLE('[{&quot;name&quot;: 2}]','$[*]' COLUMNS( name INT PATH '$.name' error on empty))">
                    <parameter>
                        <literal-expression value="[{&quot;name&quot;: 2}]" start-index="25" stop-index="39"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="$[*]" start-index="41" stop-index="46"/>
                    </parameter>
                    <parameter>
                        <literal-expression value="COLUMNS( name INT PATH '$.name' error on empty)" start-index="48"
                                            stop-index="94"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>

    <select sql-case-id="select_json_type">
        <projections start-index="7" stop-index="26">
            <expression-projection start-index="7" stop-index="26" text="JSON_TYPE('[1,2,3]')">
                <expr>
                    <function start-index="7" stop-index="26" function-name="JSON_TYPE" text="JSON_TYPE('[1,2,3]')">
                        <parameter>
                            <literal-expression value="[1,2,3]" start-index="17" stop-index="25"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_unquote">
        <projections start-index="7" stop-index="27">
            <expression-projection start-index="7" stop-index="27" text="JSON_UNQUOTE('&quot;abc&quot;')">
                <expr>
                    <function start-index="7" stop-index="27" function-name="JSON_UNQUOTE"
                              text="JSON_UNQUOTE('&quot;abc&quot;')">
                        <parameter>
                            <literal-expression value="&quot;abc&quot;" start-index="20" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_valid">
        <projections start-index="7" stop-index="28">
            <expression-projection start-index="7" stop-index="28" text="JSON_VALID('{&quot;a&quot;: 1}')">
                <expr>
                    <function start-index="7" stop-index="28" function-name="JSON_VALID"
                              text="JSON_VALID('{&quot;a&quot;: 1}')">
                        <parameter>
                            <literal-expression value="{&quot;a&quot;: 1}" start-index="18" stop-index="27"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_json_value">
        <projections start-index="7" stop-index="105">
            <expression-projection start-index="7" stop-index="105"
                                   text="JSON_VALUE('{&quot;price&quot;: &quot;49.95&quot;}', '$.price' RETURNING DECIMAL(4,1) null on empty default 0 on error)">
                <expr>
                    <function start-index="7" stop-index="105" function-name="JSON_VALUE"
                              text="JSON_VALUE('{&quot;price&quot;: &quot;49.95&quot;}', '$.price' RETURNING DECIMAL(4,1) null on empty default 0 on error)">
                        <parameter>
                            <literal-expression value="{&quot;price&quot;: &quot;49.95&quot;}" start-index="18"
                                                stop-index="37"/>
                        </parameter>
                        <parameter>
                            <list-expression start-index="40" stop-index="104">
                                <items>
                                    <literal-expression value="'$.price'" start-index="40" stop-index="48"/>
                                </items>
                                <items>
                                    <literal-expression value="RETURNING" start-index="50" stop-index="58"/>
                                </items>
                                <items>
                                    <data-type value="DECIMAL" start-index="60" stop-index="71"/>
                                </items>
                            </list-expression>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_difference">
        <projections start-index="7" stop-index="43">
            <expression-projection start-index="7" stop-index="43" text="ST_Difference(Point(1,1), Point(2,2))">
                <expr>
                    <function start-index="7" stop-index="43" function-name="ST_Difference"
                              text="ST_Difference(Point(1,1), Point(2,2))">
                        <parameter>
                            <function start-index="21" stop-index="30" function-name="Point" text="Point(1,1)">
                                <parameter>
                                    <literal-expression start-index="27" stop-index="27" value="1"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="29" stop-index="29" value="1"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="33" stop-index="42" function-name="Point" text="Point(2,2)">
                                <parameter>
                                    <literal-expression start-index="39" stop-index="39" value="2"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="41" stop-index="41" value="2"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_dimension">
        <projections start-index="7" stop-index="58">
            <expression-projection start-index="7" stop-index="58"
                                   text="ST_Dimension(ST_GeomFromText('LineString(1 1,2 2)'))">
                <expr>
                    <function start-index="7" stop-index="58" function-name="ST_Dimension"
                              text="ST_Dimension(ST_GeomFromText('LineString(1 1,2 2)'))">
                        <parameter>
                            <function start-index="20" stop-index="57" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LineString(1 1,2 2)')">
                                <parameter>
                                    <literal-expression start-index="36" stop-index="56" value="LineString(1 1,2 2)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_disjoint">
        <projections start-index="7" stop-index="40">
            <expression-projection start-index="7" stop-index="40" text="ST_Disjoint(Point(1,1),Point(2,2))">
                <expr>
                    <function start-index="7" stop-index="40" function-name="ST_Disjoint"
                              text="ST_Disjoint(Point(1,1),Point(2,2))">
                        <parameter>
                            <function start-index="19" stop-index="28" function-name="Point" text="Point(1,1)">
                                <parameter>
                                    <literal-expression start-index="25" stop-index="25" value="1"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="27" stop-index="27" value="1"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="30" stop-index="39" function-name="Point" text="Point(2,2)">
                                <parameter>
                                    <literal-expression start-index="36" stop-index="36" value="2"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="38" stop-index="38" value="2"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_distance">
        <projections start-index="7" stop-index="79">
            <expression-projection start-index="7" stop-index="79"
                                   text="ST_Distance(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(1 1)'))">
                <expr>
                    <function start-index="7" stop-index="79" function-name="ST_Distance"
                              text="ST_Distance(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(1 1)'))">
                        <parameter>
                            <function start-index="19" stop-index="47" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="46" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="50" stop-index="78" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="66" stop-index="77" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_distance_sphere">
        <projections start-index="7" stop-index="86">
            <expression-projection start-index="7" stop-index="86"
                                   text="ST_Distance_Sphere(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(1 1)'))">
                <expr>
                    <function start-index="7" stop-index="86" function-name="ST_Distance_Sphere"
                              text="ST_Distance_Sphere(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(1 1)'))">
                        <parameter>
                            <function start-index="26" stop-index="54" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="42" stop-index="53" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="57" stop-index="85" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="73" stop-index="84" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="doris_select_st_distance_sphere">
        <projections start-index="7" stop-index="42">
            <expression-projection start-index="7" stop-index="42" text="st_distance_sphere(116, 39, 116, 40)">
                <expr>
                    <function start-index="7" stop-index="42" function-name="st_distance_sphere"
                              text="st_distance_sphere(116, 39, 116, 40)">
                        <parameter>
                            <literal-expression start-index="26" stop-index="28" value="116"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="31" stop-index="32" value="39"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="35" stop-index="37" value="116"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="40" stop-index="41" value="40"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_endpoint">
        <projections start-index="7" stop-index="57">
            <expression-projection start-index="7" stop-index="57"
                                   text="ST_EndPoint(ST_GeomFromText('LineString(1 1,2 2)'))">
                <expr>
                    <function start-index="7" stop-index="57" function-name="ST_EndPoint"
                              text="ST_EndPoint(ST_GeomFromText('LineString(1 1,2 2)'))">
                        <parameter>
                            <function start-index="19" stop-index="56" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LineString(1 1,2 2)')">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="55" value="LineString(1 1,2 2)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_envelope">
        <projections start-index="7" stop-index="57">
            <expression-projection start-index="7" stop-index="57"
                                   text="ST_Envelope(ST_GeomFromText('LineString(1 1,2 2)'))">
                <expr>
                    <function start-index="7" stop-index="57" function-name="ST_Envelope"
                              text="ST_Envelope(ST_GeomFromText('LineString(1 1,2 2)'))">
                        <parameter>
                            <function start-index="19" stop-index="56" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LineString(1 1,2 2)')">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="55" value="LineString(1 1,2 2)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_equals">
        <projections start-index="7" stop-index="39">
            <expression-projection start-index="7" stop-index="39" text="ST_Equals(Point(1,1), Point(2,2))">
                <expr>
                    <function start-index="7" stop-index="39" function-name="ST_Equals"
                              text="ST_Equals(Point(1,1), Point(2,2))">
                        <parameter>
                            <function start-index="17" stop-index="26" function-name="Point" text="Point(1,1)">
                                <parameter>
                                    <literal-expression start-index="23" stop-index="23" value="1"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="25" stop-index="25" value="1"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="29" stop-index="38" function-name="Point" text="Point(2,2)">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="35" value="2"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="37" stop-index="37" value="2"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_exteriorring">
        <projections start-index="7" stop-index="94">
            <expression-projection start-index="7" stop-index="94"
                                   text="ST_ExteriorRing(ST_GeomFromText('Polygon((0 0,0 3,3 3,3 0,0 0),(1 1,1 2,2 2,2 1,1 1))'))">
                <expr>
                    <function start-index="7" stop-index="94" function-name="ST_ExteriorRing"
                              text="ST_ExteriorRing(ST_GeomFromText('Polygon((0 0,0 3,3 3,3 0,0 0),(1 1,1 2,2 2,2 1,1 1))'))">
                        <parameter>
                            <function start-index="23" stop-index="93" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('Polygon((0 0,0 3,3 3,3 0,0 0),(1 1,1 2,2 2,2 1,1 1))')">
                                <parameter>
                                    <literal-expression start-index="39" stop-index="92"
                                                        value="Polygon((0 0,0 3,3 3,3 0,0 0),(1 1,1 2,2 2,2 1,1 1))"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_frechetdistance">
        <projections start-index="7" stop-index="120">
            <expression-projection start-index="7" stop-index="120"
                                   text="ST_FrechetDistance(ST_GeomFromText('LINESTRING(0 0,0 0,0 0,0 0)'), ST_GeomFromText('LINESTRING(2 2,2 2,2 2,2 2)'))">
                <expr>
                    <function start-index="7" stop-index="120" function-name="ST_FrechetDistance"
                              text="ST_FrechetDistance(ST_GeomFromText('LINESTRING(0 0,0 0,0 0,0 0)'), ST_GeomFromText('LINESTRING(2 2,2 2,2 2,2 2)'))">
                        <parameter>
                            <function start-index="26" stop-index="71" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LINESTRING(0 0,0 0,0 0,0 0)')">
                                <parameter>
                                    <literal-expression start-index="42" stop-index="70"
                                                        value="LINESTRING(0 0,0 0,0 0,0 0)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="74" stop-index="119" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LINESTRING(2 2,2 2,2 2,2 2)')">
                                <parameter>
                                    <literal-expression start-index="90" stop-index="118"
                                                        value="LINESTRING(2 2,2 2,2 2,2 2)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geohash">
        <projections start-index="7" stop-index="26">
            <expression-projection start-index="7" stop-index="26" text="ST_GeoHash(180,0,10)">
                <expr>
                    <function start-index="7" stop-index="26" function-name="ST_GeoHash" text="ST_GeoHash(180,0,10)">
                        <parameter>
                            <literal-expression start-index="18" stop-index="20" value="180"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="22" stop-index="22" value="0"/>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="24" stop-index="25" value="10"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geomcollfromtext">
        <projections start-index="7" stop-index="74">
            <expression-projection start-index="7" stop-index="74"
                                   text="ST_GeomCollFromText('MULTILINESTRING((10 10, 11 11), (9 9, 10 10))')">
                <expr>
                    <function start-index="7" stop-index="74" function-name="ST_GeomCollFromText"
                              text="ST_GeomCollFromText('MULTILINESTRING((10 10, 11 11), (9 9, 10 10))')">
                        <parameter>
                            <literal-expression start-index="27" stop-index="73"
                                                value="MULTILINESTRING((10 10, 11 11), (9 9, 10 10))"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geometrycollectionfromwkb">
        <projections start-index="7" stop-index="252">
            <expression-projection start-index="7" stop-index="252"
                                   text="ST_GeometryCollectionFromWKB(0x0107000000020000000103000000010000000400000000000000000014400000000000001440000000000000244000000000000014400000000000002440000000000000244000000000000014400000000000001440010100000000000000000024400000000000002440)">
                <expr>
                    <function start-index="7" stop-index="252" function-name="ST_GeometryCollectionFromWKB"
                              text="ST_GeometryCollectionFromWKB(0x0107000000020000000103000000010000000400000000000000000014400000000000001440000000000000244000000000000014400000000000002440000000000000244000000000000014400000000000001440010100000000000000000024400000000000002440)">
                        <parameter>
                            <common-expression start-index="36" stop-index="251"
                                               text="0x0107000000020000000103000000010000000400000000000000000014400000000000001440000000000000244000000000000014400000000000002440000000000000244000000000000014400000000000001440010100000000000000000024400000000000002440"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geometryn">
        <projections start-index="7" stop-index="92">
            <expression-projection start-index="7" stop-index="92"
                                   text="ST_GeometryN(ST_GeomFromText('GeometryCollection(Point(1 1),LineString(2 2, 3 3))'),1)">
                <expr>
                    <function start-index="7" stop-index="92" function-name="ST_GeometryN"
                              text="ST_GeometryN(ST_GeomFromText('GeometryCollection(Point(1 1),LineString(2 2, 3 3))'),1)">
                        <parameter>
                            <function start-index="20" stop-index="89" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('GeometryCollection(Point(1 1),LineString(2 2, 3 3))')">
                                <parameter>
                                    <literal-expression start-index="36" stop-index="88"
                                                        value="GeometryCollection(Point(1 1),LineString(2 2, 3 3))"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="91" stop-index="91" value="1"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geometrytype">
        <projections start-index="7" stop-index="52">
            <expression-projection start-index="7" stop-index="52"
                                   text="ST_GeometryType(ST_GeomFromText('POINT(1 1)'))">
                <expr>
                    <function start-index="7" stop-index="52" function-name="ST_GeometryType"
                              text="ST_GeometryType(ST_GeomFromText('POINT(1 1)'))">
                        <parameter>
                            <function start-index="23" stop-index="51" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="39" stop-index="50" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_geomfromgeojson">
        <projections start-index="7" stop-index="71">
            <expression-projection start-index="7" stop-index="71"
                                   text="ST_GeomFromGeoJSON('{&quot;type&quot;:&quot;Point&quot;,&quot;coordinates&quot;:[102.0, 0.0]}')">
                <expr>
                    <function start-index="7" stop-index="71" function-name="ST_GeomFromGeoJSON"
                              text="ST_GeomFromGeoJSON('{&quot;type&quot;:&quot;Point&quot;,&quot;coordinates&quot;:[102.0, 0.0]}')">
                        <parameter>
                            <literal-expression start-index="26" stop-index="70"
                                                value="{&quot;type&quot;:&quot;Point&quot;,&quot;coordinates&quot;:[102.0, 0.0]}"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_soundex">
        <projections start-index="7" stop-index="22">
            <expression-projection start-index="7" stop-index="22" text="SOUNDEX('Hello')">
                <expr>
                    <function start-index="7" stop-index="22" function-name="SOUNDEX" text="SOUNDEX('Hello')">
                        <parameter>
                            <literal-expression start-index="15" stop-index="21" value="Hello"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_space">
        <projections start-index="7" stop-index="14">
            <expression-projection start-index="7" stop-index="14" text="SPACE(6)">
                <expr>
                    <function start-index="7" stop-index="14" function-name="SPACE" text="SPACE(6)">
                        <parameter>
                            <literal-expression start-index="13" stop-index="13" value="6"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_sqrt">
        <projections start-index="7" stop-index="13">
            <expression-projection start-index="7" stop-index="13" text="SQRT(4)">
                <expr>
                    <function start-index="7" stop-index="13" function-name="SQRT" text="SQRT(4)">
                        <parameter>
                            <literal-expression start-index="12" stop-index="12" value="4"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_area">
        <projections start-index="7" stop-index="78">
            <expression-projection start-index="7" stop-index="78"
                                   text="ST_Area(ST_GeomFromText('Polygon((0 0,0 3,3 0,0 0),(1 1,1 2,2 1,1 1))'))">
                <expr>
                    <function start-index="7" stop-index="78" function-name="ST_Area"
                              text="ST_Area(ST_GeomFromText('Polygon((0 0,0 3,3 0,0 0),(1 1,1 2,2 1,1 1))'))">
                        <parameter>
                            <function start-index="15" stop-index="77" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('Polygon((0 0,0 3,3 0,0 0),(1 1,1 2,2 1,1 1))')">
                                <parameter>
                                    <literal-expression start-index="31" stop-index="76"
                                                        value="Polygon((0 0,0 3,3 0,0 0),(1 1,1 2,2 1,1 1))"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_asbinary">
        <projections start-index="7" stop-index="30">
            <expression-projection start-index="7" stop-index="30" text="ST_AsBinary(POINT(1, 1))">
                <expr>
                    <function start-index="7" stop-index="30" function-name="ST_AsBinary"
                              text="ST_AsBinary(POINT(1, 1))">
                        <parameter>
                            <function start-index="19" stop-index="29" function-name="POINT" text="POINT(1, 1)">
                                <parameter>
                                    <literal-expression start-index="25" stop-index="25" value="1"/>
                                </parameter>
                                <parameter>
                                    <literal-expression start-index="28" stop-index="28" value="1"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_asgeojson">
        <projections start-index="7" stop-index="65">
            <expression-projection start-index="7" stop-index="65"
                                   text="ST_AsGeoJSON(ST_GeomFromText('POINT(11.11111 12.22222)'),2)">
                <expr>
                    <function start-index="7" stop-index="65" function-name="ST_AsGeoJSON"
                              text="ST_AsGeoJSON(ST_GeomFromText('POINT(11.11111 12.22222)'),2)">
                        <parameter>
                            <function start-index="20" stop-index="62" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(11.11111 12.22222)')">
                                <parameter>
                                    <literal-expression start-index="36" stop-index="61"
                                                        value="POINT(11.11111 12.22222)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="64" stop-index="64" value="2"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_astext">
        <projections start-index="7" stop-index="59">
            <expression-projection start-index="7" stop-index="59"
                                   text="ST_AsText(ST_GeomFromText('LineString(1 1,2 2,3 3)'))">
                <expr>
                    <function start-index="7" stop-index="59" function-name="ST_AsText"
                              text="ST_AsText(ST_GeomFromText('LineString(1 1,2 2,3 3)'))">
                        <parameter>
                            <function start-index="17" stop-index="58" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('LineString(1 1,2 2,3 3)')">
                                <parameter>
                                    <literal-expression start-index="33" stop-index="57"
                                                        value="LineString(1 1,2 2,3 3)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_buffer">
        <projections start-index="7" stop-index="49">
            <expression-projection start-index="7" stop-index="49" text="ST_Buffer(ST_GeomFromText('POINT(0 0)'), 0)">
                <expr>
                    <function start-index="7" stop-index="49" function-name="ST_Buffer"
                              text="ST_Buffer(ST_GeomFromText('POINT(0 0)'), 0)">
                        <parameter>
                            <function start-index="17" stop-index="45" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(0 0)')">
                                <parameter>
                                    <literal-expression start-index="33" stop-index="44" value="POINT(0 0)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <literal-expression start-index="48" stop-index="48" value="0"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_buffer_strategy">
        <projections start-index="7" stop-index="36">
            <expression-projection start-index="7" stop-index="36" text="ST_Buffer_Strategy('end_flat')">
                <expr>
                    <function start-index="7" stop-index="36" function-name="ST_Buffer_Strategy"
                              text="ST_Buffer_Strategy('end_flat')">
                        <parameter>
                            <literal-expression start-index="26" stop-index="35" value="end_flat"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_centroid">
        <projections start-index="7" stop-index="68">
            <expression-projection start-index="7" stop-index="68"
                                   text="ST_Centroid(ST_GeomFromText('POLYGON((0 0,0 3,3 3,3 0,0 0))'))">
                <expr>
                    <function start-index="7" stop-index="68" function-name="ST_Centroid"
                              text="ST_Centroid(ST_GeomFromText('POLYGON((0 0,0 3,3 3,3 0,0 0))'))">
                        <parameter>
                            <function start-index="19" stop-index="67" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POLYGON((0 0,0 3,3 3,3 0,0 0))')">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="66"
                                                        value="POLYGON((0 0,0 3,3 3,3 0,0 0))"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_contains">
        <projections start-index="7" stop-index="79">
            <expression-projection start-index="7" stop-index="79"
                                   text="ST_Contains(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(2 1)'))">
                <expr>
                    <function start-index="7" stop-index="79" function-name="ST_Contains"
                              text="ST_Contains(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(2 1)'))">
                        <parameter>
                            <function start-index="19" stop-index="47" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="35" stop-index="46" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="50" stop-index="78" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(2 1)')">
                                <parameter>
                                    <literal-expression start-index="66" stop-index="77" value="POINT(2 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_convexhull">
        <projections start-index="7" stop-index="72">
            <expression-projection start-index="7" stop-index="72"
                                   text="ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,25 0,15 10,15 25)'))">
                <expr>
                    <function start-index="7" stop-index="72" function-name="ST_ConvexHull"
                              text="ST_ConvexHull(ST_GeomFromText('MULTIPOINT(5 0,25 0,15 10,15 25)'))">
                        <parameter>
                            <function start-index="21" stop-index="71" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('MULTIPOINT(5 0,25 0,15 10,15 25)')">
                                <parameter>
                                    <literal-expression start-index="37" stop-index="70"
                                                        value="MULTIPOINT(5 0,25 0,15 10,15 25)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_st_crosses">
        <projections start-index="7" stop-index="78">
            <expression-projection start-index="7" stop-index="78"
                                   text="ST_Crosses(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(2 2)'))">
                <expr>
                    <function start-index="7" stop-index="78" function-name="ST_Crosses"
                              text="ST_Crosses(ST_GeomFromText('POINT(1 1)'), ST_GeomFromText('POINT(2 2)'))">
                        <parameter>
                            <function start-index="18" stop-index="46" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(1 1)')">
                                <parameter>
                                    <literal-expression start-index="34" stop-index="45" value="POINT(1 1)"/>
                                </parameter>
                            </function>
                        </parameter>
                        <parameter>
                            <function start-index="49" stop-index="77" function-name="ST_GeomFromText"
                                      text="ST_GeomFromText('POINT(2 2)')">
                                <parameter>
                                    <literal-expression start-index="65" stop-index="76" value="POINT(2 2)"/>
                                </parameter>
                            </function>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_from_table_function">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <function-table>
                <table-function function-name="GENERATE_SERIES" text="GENERATE_SERIES(1, name)">
                    <parameter>
                        <literal-expression value="1" start-index="30" stop-index="30"/>
                        <column name="name" start-index="33" stop-index="36"/>
                    </parameter>
                </table-function>
            </function-table>
        </from>
    </select>
    <select sql-case-id="select_bitxor" db-types="Doris">
        <projections start-index="7" stop-index="17">
            <expression-projection text="BITXOR(3,5)" start-index="7" stop-index="17">
                <expr>
                    <function function-name="BITXOR" start-index="7" stop-index="17" text="BITXOR(3,5)">
                        <parameter>
                            <literal-expression value="3" start-index="14" stop-index="14"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="5" start-index="16" stop-index="16"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_instr" db-types="Doris">
        <projections start-index="7" stop-index="27">
            <expression-projection text="INSTR('foobar','bar')" start-index="7" stop-index="27">
                <expr>
                    <function function-name="INSTR" start-index="7" stop-index="27" text="INSTR('foobar','bar')">
                        <parameter>
                            <literal-expression value="'foobar'" start-index="13" stop-index="20"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="'bar'" start-index="22" stop-index="26"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_strright" db-types="Doris">
        <projections start-index="7" stop-index="29">
            <expression-projection start-index="7" stop-index="29" text="STRRIGHT('foobarbar',4)">
                <expr>
                    <function function-name="STRRIGHT" text="STRRIGHT('foobarbar',4)" start-index="7" stop-index="29">
                        <parameter>
                            <literal-expression value="foobarbar" start-index="16" stop-index="26"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="4" start-index="28" stop-index="28"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_extract_url_parameter" db-types="Doris">
        <projections start-index="7" stop-index="60">
            <expression-projection start-index="7" stop-index="60"
                                   text="EXTRACT_URL_PARAMETER('http://foo.com/?bar=baz','bar')">
                <expr>
                    <function function-name="EXTRACT_URL_PARAMETER"
                              text="EXTRACT_URL_PARAMETER('http://foo.com/?bar=baz','bar')" start-index="7"
                              stop-index="60">
                        <parameter>
                            <literal-expression value="'http://foo.com/?bar=baz'" start-index="29" stop-index="53"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="'bar'" start-index="55" stop-index="59"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
    <select sql-case-id="select_lcase_function">
        <projections start-index="7" stop-index="28">
            <expression-projection start-index="7" stop-index="28" text="LCASE('QUADRATICALLY')">
                <expr>
                    <function start-index="7" stop-index="28" function-name="LCASE" text="LCASE('QUADRATICALLY')">
                        <parameter>
                            <literal-expression start-index="13" stop-index="27" value="QUADRATICALLY"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_lower_function">
        <projections start-index="7" stop-index="28">
            <expression-projection start-index="7" stop-index="28" text="LOWER('QUADRATICALLY')">
                <expr>
                    <function start-index="7" stop-index="28" function-name="LOWER" text="LOWER('QUADRATICALLY')">
                        <parameter>
                            <literal-expression start-index="13" stop-index="27" value="QUADRATICALLY"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_length">
        <projections start-index="7" stop-index="20">
            <expression-projection start-index="7" stop-index="20" text="LENGTH('TEXT')">
                <expr>
                    <function start-index="7" stop-index="20" function-name="LENGTH" text="LENGTH('TEXT')">
                        <parameter>
                            <literal-expression start-index="14" stop-index="19" value="TEXT"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>

    <select sql-case-id="select_locate">
        <projections start-index="7" stop-index="31">
            <expression-projection start-index="7" stop-index="31" text="LOCATE('bar','foobarbar')">
                <expr>
                    <function start-index="7" stop-index="31" function-name="LOCATE" text="LOCATE('bar','foobarbar')">
                        <parameter>
                            <literal-expression value="bar" start-index="14" stop-index="18"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="foobarbar" start-index="20" stop-index="30"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
        </projections>
    </select>
</sql-parser-test-cases>
