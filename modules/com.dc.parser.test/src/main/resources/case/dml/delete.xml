<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <delete sql-case-id="delete_with_sharding_value" parameters="1000, 1001, 'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <where start-index="20" stop-index="66" literal-stop-index="77">
            <expr>
                <binary-operation-expression start-index="26" stop-index="66" literal-stop-index="77">
                    <left>
                        <binary-operation-expression start-index="26" stop-index="53" literal-stop-index="59">
                            <left>
                                <binary-operation-expression start-index="26" stop-index="37" literal-stop-index="40">
                                    <left>
                                        <column name="order_id" start-index="26" stop-index="33"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1000" start-index="37" stop-index="40"/>
                                        <parameter-marker-expression parameter-index="0" start-index="37"
                                                                     stop-index="37"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="43" stop-index="53" literal-start-index="46"
                                                             literal-stop-index="59">
                                    <left>
                                        <column name="user_id" start-index="43" stop-index="49" literal-start-index="46"
                                                literal-stop-index="52"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1001" start-index="56" stop-index="59"/>
                                        <parameter-marker-expression parameter-index="1" start-index="53"
                                                                     stop-index="53"/>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="59" stop-index="66" literal-start-index="65"
                                                     literal-stop-index="77">
                            <left>
                                <column name="status" start-index="59" stop-index="64" literal-start-index="65"
                                        literal-stop-index="70"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="init" start-index="72" stop-index="77"/>
                                <parameter-marker-expression parameter-index="2" start-index="66" stop-index="66"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_without_sharding_value" parameters="'init'">
        <table name="t_order" start-index="12" stop-index="18"/>
        <where start-index="20" stop-index="33" literal-stop-index="38">
            <expr>
                <binary-operation-expression start-index="26" stop-index="33" literal-stop-index="38">
                    <left>
                        <column name="status" start-index="26" stop-index="31"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="init" start-index="33" stop-index="38"/>
                        <parameter-marker-expression parameter-index="0" start-index="33" stop-index="33"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_special_character_without_sharding_value">
        <table name="t_order" start-delimiter="`" end-delimiter="`" start-index="12" stop-index="20"/>
        <where start-index="22" stop-index="42">
            <expr>
                <binary-operation-expression start-index="28" stop-index="42">
                    <left>
                        <column name="status" start-delimiter="`" end-delimiter="`" start-index="28" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="init" start-index="37" stop-index="42"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_alias" parameters="'init'">
        <table name="t_order" alias="o" start-index="12" stop-index="23"/>
        <where start-index="25" stop-index="38" literal-stop-index="43">
            <expr>
                <binary-operation-expression start-index="31" stop-index="38" literal-stop-index="43">
                    <left>
                        <column name="status" start-index="31" stop-index="36"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="init" start-index="38" stop-index="43"/>
                        <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_alias_without_as">
        <table name="product_price_history" alias="pp" start-index="7" stop-index="30"/>
        <where start-index="32" stop-index="211">
            <expr>
                <in-expression start-index="38" stop-index="211">
                    <not>false</not>
                    <left>
                        <list-expression start-index="38" stop-index="85" literal-start-index="38"
                                         literal-stop-index="85">
                            <items>
                                <column name="product_id" start-index="39" stop-index="48" literal-start-index="39"
                                        literal-stop-index="48"/>
                            </items>
                            <items>
                                <column name="currency_code" start-index="51" stop-index="63" literal-start-index="51"
                                        literal-stop-index="63"/>
                            </items>
                            <items>
                                <column name="effective_from_date" start-index="66" stop-index="84"
                                        literal-start-index="66" literal-stop-index="84"/>
                            </items>
                        </list-expression>
                    </left>
                    <right>
                        <subquery start-index="90" stop-index="211">
                            <select>
                                <from start-index="155" stop-index="175">
                                    <simple-table name="product_price_history" start-index="155" stop-index="175"/>
                                </from>
                                <projections start-index="98" stop-index="148">
                                    <column-projection name="product_id" start-index="98" stop-index="107"/>
                                    <column-projection name="currency_code" start-index="110" stop-index="122"/>
                                    <aggregation-projection type="MAX" expression="MAX(effective_from_date)"
                                                            start-index="125" stop-index="148"/>
                                </projections>
                                <group-by>
                                    <column-item name="product_id" start-index="186" stop-index="195"/>
                                    <column-item name="currency_code" start-index="198" stop-index="210"/>
                                </group-by>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_order_by_row_count" parameters="1000, 1001, 'init', 10">
        <table name="t_order" start-index="12" stop-index="18"/>
        <where start-index="20" stop-index="66" literal-stop-index="77">
            <expr>
                <binary-operation-expression start-index="26" stop-index="66" literal-stop-index="77">
                    <left>
                        <binary-operation-expression start-index="26" stop-index="53" literal-stop-index="59">
                            <left>
                                <binary-operation-expression start-index="26" stop-index="37" literal-stop-index="40">
                                    <left>
                                        <column name="order_id" start-index="26" stop-index="33"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1000" start-index="37" stop-index="40"/>
                                        <parameter-marker-expression parameter-index="0" start-index="37"
                                                                     stop-index="37"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>AND</operator>
                            <right>
                                <binary-operation-expression start-index="43" stop-index="53" literal-start-index="46"
                                                             literal-stop-index="59">
                                    <left>
                                        <column name="user_id" start-index="43" stop-index="49" literal-start-index="46"
                                                literal-stop-index="52"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1001" start-index="56" stop-index="59"/>
                                        <parameter-marker-expression parameter-index="1" start-index="53"
                                                                     stop-index="53"/>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="59" stop-index="66" literal-start-index="65"
                                                     literal-stop-index="77">
                            <left>
                                <column name="status" start-index="59" stop-index="64" literal-start-index="65"
                                        literal-stop-index="70"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="init" start-index="72" stop-index="77"/>
                                <parameter-marker-expression parameter-index="2" start-index="66" stop-index="66"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="order_id" start-index="77" stop-index="84" literal-start-index="88"
                         literal-stop-index="95"/>
        </order-by>
        <limit start-index="86" stop-index="92" literal-start-index="97" literal-stop-index="104">
            <row-count value="10" parameter-index="3" start-index="92" stop-index="92" literal-start-index="103"
                       literal-stop-index="104"/>
        </limit>
    </delete>

    <delete sql-case-id="delete_with_output_clause" parameters="1000">
        <table name="t_order" start-index="12" stop-index="18"/>
        <output start-index="20" stop-index="106">
            <output-columns start-index="27" stop-index="59">
                <column-projection name="order_id" start-index="27" stop-index="42"/>
                <column-projection name="user_id" start-index="45" stop-index="59"/>
            </output-columns>
            <output-table name="@MyTableVar" start-index="66" stop-index="76"/>
            <output-table-columns start-index="78" stop-index="106">
                <column name="temp_order_id" start-index="79" stop-index="91"/>
                <column name="temp_user_id" start-index="94" stop-index="105"/>
            </output-table-columns>
        </output>
        <where start-index="108" stop-index="125" literal-stop-index="128">
            <expr>
                <binary-operation-expression start-index="114" stop-index="125" literal-stop-index="128">
                    <left>
                        <column name="order_id" start-index="114" stop-index="121"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="125" stop-index="128"/>
                        <parameter-marker-expression parameter-index="0" start-index="125" stop-index="125"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_output_clause_without_output_table_columns" parameters="1000">
        <table name="t_order" start-index="12" stop-index="18"/>
        <output start-index="20" stop-index="76">
            <output-columns start-index="27" stop-index="59">
                <column-projection name="order_id" start-index="27" stop-index="42"/>
                <column-projection name="user_id" start-index="45" stop-index="59"/>
            </output-columns>
            <output-table name="@MyTableVar" start-index="66" stop-index="76"/>
        </output>
        <where start-index="78" stop-index="95" literal-stop-index="98">
            <expr>
                <binary-operation-expression start-index="84" stop-index="95" literal-stop-index="98">
                    <left>
                        <column name="order_id" start-index="84" stop-index="91"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="95" stop-index="98"/>
                        <parameter-marker-expression parameter-index="0" start-index="95" stop-index="95"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_output_clause_without_output_table" parameters="1000">
        <table name="t_order" start-index="12" stop-index="18"/>
        <output start-index="20" stop-index="59">
            <output-columns start-index="27" stop-index="59">
                <column-projection name="order_id" start-index="27" stop-index="42"/>
                <column-projection name="user_id" start-index="45" stop-index="59"/>
            </output-columns>
        </output>
        <where start-index="61" stop-index="78" literal-stop-index="81">
            <expr>
                <binary-operation-expression start-index="67" stop-index="78" literal-stop-index="81">
                    <left>
                        <column name="order_id" start-index="67" stop-index="74"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="78" stop-index="81"/>
                        <parameter-marker-expression parameter-index="0" start-index="78" stop-index="78"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_output_clause_column_shorthand" parameters="1000">
        <table name="t_order" start-index="12" stop-index="18"/>
        <output start-index="20" stop-index="35"/>
        <where start-index="37" stop-index="54" literal-stop-index="57">
            <expr>
                <binary-operation-expression start-index="43" stop-index="54" literal-stop-index="57">
                    <left>
                        <column name="order_id" start-index="43" stop-index="50"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="54" stop-index="57"/>
                        <parameter-marker-expression parameter-index="0" start-index="54" stop-index="54"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_top" parameters="1000">
        <table name="t_order" start-index="20" stop-index="26"/>
        <where start-index="28" stop-index="45" literal-stop-index="48">
            <expr>
                <binary-operation-expression start-index="34" stop-index="45" literal-stop-index="48">
                    <left>
                        <column name="order_id" start-index="34" stop-index="41"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="45" stop-index="48"/>
                        <parameter-marker-expression parameter-index="0" start-index="45" stop-index="45"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_top_percent" parameters="1000">
        <table name="t_order" start-index="28" stop-index="34"/>
        <where start-index="36" stop-index="53" literal-stop-index="56">
            <expr>
                <binary-operation-expression start-index="42" stop-index="53" literal-stop-index="56">
                    <left>
                        <column name="order_id" start-index="42" stop-index="49"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="53" stop-index="56"/>
                        <parameter-marker-expression parameter-index="0" start-index="53" stop-index="53"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_with_clause">
        <with start-index="0" stop-index="70">
            <common-table-expression name="cte" start-index="5" stop-index="70">
                <column name="order_id" start-index="10" stop-index="17"/>
                <column name="user_id" start-index="20" stop-index="26"/>
                <subquery-expression start-index="33" stop-index="69">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="63" stop-index="69"/>
                        </from>
                        <projections start-index="40" stop-index="56">
                            <column-projection name="order_id" start-index="40" stop-index="47"/>
                            <column-projection name="user_id" start-index="50" stop-index="56"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <table name="t_order" start-index="79" stop-index="85"/>
        <where start-index="96" stop-index="132">
            <expr>
                <binary-operation-expression start-index="102" stop-index="132">
                    <left>
                        <column name="order_id" start-index="102" stop-index="117">
                            <owner name="t_order" start-index="102" stop-index="108"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="order_id" start-index="121" stop-index="132">
                            <owner name="cte" start-index="121" stop-index="123"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_without_columns_with_with_clause">
        <with start-index="0" stop-index="50">
            <common-table-expression name="cte" start-index="5" stop-index="50">
                <subquery-expression start-index="13" stop-index="49">
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="43" stop-index="49"/>
                        </from>
                        <projections start-index="20" stop-index="36">
                            <column-projection name="order_id" start-index="20" stop-index="27"/>
                            <column-projection name="user_id" start-index="30" stop-index="36"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <table name="t_order" start-index="59" stop-index="65"/>
        <where start-index="76" stop-index="112">
            <expr>
                <binary-operation-expression start-index="82" stop-index="112">
                    <left>
                        <column name="order_id" start-index="82" stop-index="97">
                            <owner name="t_order" start-index="82" stop-index="88"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="order_id" start-index="101" stop-index="112">
                            <owner name="cte" start-index="101" stop-index="103"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_multi_tables" parameters="1">
        <where start-index="56" stop-index="124">
            <expr>
                <binary-operation-expression start-index="62" stop-index="124">
                    <left>
                        <binary-operation-expression start-index="62" stop-index="101">
                            <left>
                                <column name="order_id" start-index="62" stop-index="77">
                                    <owner name="t_order" start-index="62" stop-index="68"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="81" stop-index="101">
                                    <owner name="t_order_item" start-index="81" stop-index="92"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>and</operator>
                    <right>
                        <binary-operation-expression start-index="107" stop-index="124">
                            <left>
                                <column name="status" start-index="107" stop-index="120">
                                    <owner name="t_order" start-index="107" stop-index="113"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="124" stop-index="124"/>
                                <parameter-marker-expression parameter-index="0" start-index="124" stop-index="124"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <table name="t_order" start-index="7" stop-index="13"/>
        <table name="t_order_item" start-index="16" stop-index="27"/>
    </delete>

    <delete sql-case-id="delete_multi_tables_with_using" parameters="1">
        <where start-index="115" stop-index="138">
            <expr>
                <binary-operation-expression start-index="121" stop-index="138">
                    <left>
                        <column name="status" start-index="121" stop-index="134">
                            <owner name="t_order" start-index="121" stop-index="127"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="138" stop-index="138"/>
                        <parameter-marker-expression parameter-index="0" start-index="138" stop-index="138"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <table name="t_order" start-index="12" stop-index="18"/>
        <table name="t_order_item" start-index="21" stop-index="32"/>
    </delete>

    <delete sql-case-id="delete_with_query_hint" parameters="1000">
        <table name="t_order" start-index="12" stop-index="18"/>
        <where start-index="20" stop-index="37" literal-stop-index="40">
            <expr>
                <binary-operation-expression start-index="26" stop-index="37" literal-stop-index="40">
                    <left>
                        <column name="order_id" start-index="26" stop-index="33"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1000" start-index="37" stop-index="40"/>
                        <parameter-marker-expression parameter-index="0" start-index="37" stop-index="37"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_simple_table">
        <table name="product_descriptions" start-index="12" stop-index="31"/>
        <where start-index="33" stop-index="56">
            <expr>
                <binary-operation-expression start-index="39" stop-index="56">
                    <left>
                        <column name="language_id" start-index="39" stop-index="49"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="AR" start-index="53" stop-index="56"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_simple_subquery">
        <subquery-table start-index="12" stop-index="36">
            <subquery>
                <select>
                    <from start-index="27" stop-index="35">
                        <simple-table name="employees" start-index="27" stop-index="35"/>
                    </from>
                    <projections start-index="20" stop-index="20">
                        <shorthand-projection start-index="20" stop-index="20"/>
                    </projections>
                </select>
            </subquery>
        </subquery-table>
        <where start-index="38" stop-index="85">
            <expr>
                <binary-operation-expression start-index="44" stop-index="85">
                    <left>
                        <binary-operation-expression start-index="44" stop-index="60">
                            <left>
                                <column name="job_id" start-index="44" stop-index="49"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="SA_REP" start-index="53" stop-index="60"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="66" stop-index="85">
                            <left>
                                <column name="commission_pct" start-index="66" stop-index="79"/>
                            </left>
                            <operator>&lt;</operator>
                            <right>
                                <literal-expression value="0.2" start-index="83" stop-index="85"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_simple_subquery_without_from">
        <subquery-table start-index="7" stop-index="43">
            <subquery>
                <select>
                    <from start-index="27" stop-index="35">
                        <simple-table name="product_price_history" start-index="22" stop-index="42"/>
                    </from>
                    <projections start-index="15" stop-index="15">
                        <shorthand-projection start-index="15" stop-index="15"/>
                    </projections>
                </select>
            </subquery>
        </subquery-table>
        <where start-index="45" stop-index="71">
            <expr>
                <binary-operation-expression start-index="51" stop-index="71">
                    <left>
                        <column name="currency_code" start-index="51" stop-index="63"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="EUR" start-index="67" stop-index="71"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_remote_database_table">
        <table name="locations" start-index="12" stop-index="30">
            <owner name="hr" start-index="12" stop-index="13"/>
        </table>
        <where start-index="32" stop-index="55">
            <expr>
                <binary-operation-expression start-index="38" stop-index="55">
                    <left>
                        <column name="location_id" start-index="38" stop-index="48"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="3000" start-index="52" stop-index="55"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_returning_into">
        <table name="employees" start-index="12" stop-index="20"/>
        <where start-index="22" stop-index="93">
            <expr>
                <binary-operation-expression start-index="28" stop-index="93">
                    <left>
                        <binary-operation-expression start-index="28" stop-index="44">
                            <left>
                                <column name="job_id" start-index="28" stop-index="33"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="SA_REP" start-index="37" stop-index="44"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="50" stop-index="93">
                            <left>
                                <binary-operation-expression start-index="50" stop-index="83">
                                    <left>
                                        <column name="hire_date" start-index="50" stop-index="58"/>
                                    </left>
                                    <operator>+</operator>
                                    <right>
                                        <function function-name="TO_YMINTERVAL" text="TO_YMINTERVAL('01-00')"
                                                  start-index="62" stop-index="83">
                                            <parameter>
                                                <literal-expression value="01-00" start-index="76" stop-index="82"/>
                                            </parameter>
                                        </function>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>&lt;</operator>
                            <right>
                                <column name="SYSDATE" start-index="87" stop-index="93"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_partition">
        <table name="sales" start-index="12" stop-index="16"/>
        <where start-index="44" stop-index="67">
            <expr>
                <binary-operation-expression start-index="50" stop-index="67">
                    <left>
                        <column name="amount_sold" start-index="50" stop-index="60"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression value="1000" start-index="64" stop-index="67"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_table">
        <table name="product_price_history" start-index="7" stop-index="27"/>
    </delete>

    <delete sql-case-id="delete_with_schema">
        <table name="t_order" start-index="12" stop-index="22">
            <owner name="db1" start-index="12" stop-index="14"/>
        </table>
    </delete>

    <delete sql-case-id="delete_with_simple_condition">
        <table name="Q1_2000_sales" start-index="12" stop-index="24"/>
        <where start-index="26" stop-index="46">
            <expr>
                <binary-operation-expression start-index="32" stop-index="46">
                    <left>
                        <column name="amount_sold" start-index="32" stop-index="42"/>
                    </left>
                    <operator>&lt;</operator>
                    <right>
                        <literal-expression value="0" start-index="46" stop-index="46"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>

    <delete sql-case-id="delete_with_output_clause_with_compress_function">
        <table name="player" start-index="12" stop-index="17"/>
        <output start-index="19" stop-index="134">
            <output-columns start-index="26" stop-index="109">
                <column-projection name="id" start-index="26" stop-index="35"/>
                <column-projection name="name" start-index="37" stop-index="48"/>
                <column-projection name="surname" start-index="51" stop-index="65"/>
                <column-projection name="datemodifier" start-index="67" stop-index="86"/>
                <expression-projection text="COMPRESS(deleted.info)" start-index="88" stop-index="109">
                    <function function-name="COMPRESS" text="COMPRESS(deleted.info)" start-index="88" stop-index="109">
                        <parameter>
                            <column name="info" start-index="97" stop-index="108">
                                <owner name="deleted" start-index="97" stop-index="103"/>
                            </column>
                        </parameter>
                    </function>
                </expression-projection>
            </output-columns>
            <output-table name="inactivePlayers" start-index="116" stop-index="134">
                <owner name="dbo" start-index="116" stop-index="118"/>
            </output-table>
        </output>
        <where start-index="136" stop-index="168">
            <expr>
                <binary-operation-expression start-index="142" stop-index="168">
                    <left>
                        <column name="datemodified" start-index="142" stop-index="153"/>
                    </left>
                    <right>
                        <column name="@startOfYear" start-index="157" stop-index="168"/>
                    </right>
                    <operator>&lt;</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </delete>
    <delete sql-case-id="delete_returning_expressions">
        <table name="t2" start-index="12" stop-index="13"/>
        <where start-index="15" stop-index="26">
            <expr>
                <binary-operation-expression start-index="21" stop-index="26">
                    <left>
                        <column name="id" start-index="21" stop-index="22"/>
                    </left>
                    <right>
                        <literal-expression value="2" start-index="26" stop-index="26"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <returning start-index="29" stop-index="44">
            <projections start-index="39" stop-index="44">
                <column-projection name="id" start-index="39" stop-index="40"/>
                <expression-projection start-index="42" stop-index="44" text="t&amp;t">
                    <left>
                        <column name="t" start-index="42" stop-index="42"/>
                    </left>
                    <operator>&amp;</operator>
                    <right>
                        <column name="t" start-index="44" stop-index="44"/>
                    </right>
                </expression-projection>
            </projections>
        </returning>
    </delete>
</sql-parser-test-cases>
