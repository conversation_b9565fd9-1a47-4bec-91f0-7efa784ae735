<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_group_by_with_sum">
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
        <projections start-index="7" stop-index="42">
            <aggregation-projection type="SUM" alias="orders_sum" expression="SUM(order_id)" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" start-index="36" stop-index="42"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="66" stop-index="72"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="83" stop-index="89"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_sum_where">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="sales" alias="s" start-index="65" stop-index="71"/>
                </left>
                <right>
                    <simple-table name="products" alias="p" start-index="74" stop-index="83"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="58">
            <column-projection name="prod_subcategory" start-index="7" stop-index="24">
                <owner start-index="7" stop-index="7" name="p"/>
            </column-projection>
            <aggregation-projection type="SUM" alias="sum_amount" expression="SUM(s.amount_sold)" start-index="27"
                                    stop-index="44"/>
        </projections>
        <where start-index="85" stop-index="111">
            <expr>
                <binary-operation-expression start-index="91" stop-index="111">
                    <left>
                        <column name="prod_id" start-index="91" stop-index="99">
                            <owner name="s" start-index="91" stop-index="91"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="prod_id" start-index="103" stop-index="111">
                            <owner name="p" start-index="103" stop-index="103"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="prod_subcategory" start-index="122" stop-index="139">
                <owner name="p" start-index="122" stop-index="122"/>
            </column-item>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_count">
        <from>
            <simple-table name="t_order" start-index="53" stop-index="59"/>
        </from>
        <projections start-index="7" stop-index="46">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(order_id)" start-index="7"
                                    stop-index="21"/>
            <column-projection name="user_id" start-index="40" stop-index="46"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="70" stop-index="76"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="87" stop-index="93"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_max">
        <from>
            <simple-table name="t_order" start-index="51" stop-index="57"/>
        </from>
        <projections start-index="7" stop-index="44">
            <aggregation-projection type="MAX" alias="max_order_id" expression="MAX(order_id)" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" start-index="38" stop-index="44"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="68" stop-index="74"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="85" stop-index="91"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_min">
        <from>
            <simple-table name="t_order" start-index="51" stop-index="57"/>
        </from>
        <projections start-index="7" stop-index="44">
            <aggregation-projection type="MIN" alias="min_order_id" expression="MIN(order_id)" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" start-index="38" stop-index="44"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="68" stop-index="74"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="85" stop-index="91"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_avg">
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
        <projections start-index="7" stop-index="42">
            <aggregation-projection type="AVG" alias="orders_avg" expression="AVG(order_id)" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" start-index="36" stop-index="42"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="66" stop-index="72"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="83" stop-index="89"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_column_avg">
        <projections start-index="7" stop-index="57">
            <column-projection name="prod_subcategory" start-index="7" stop-index="24">
                <owner start-index="7" stop-index="7" name="p"/>
            </column-projection>
            <aggregation-projection type="AVG" expression="AVG(s.amount_sold)" alias="avg_sales" start-index="27"
                                    stop-index="44"/>
        </projections>
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="sales" alias="s" start-index="65" stop-index="71"/>
                </left>
                <right>
                    <simple-table name="products" alias="p" start-index="74" stop-index="83"/>
                </right>
            </join-table>
        </from>
        <where start-index="85" stop-index="111">
            <expr>
                <binary-operation-expression start-index="91" stop-index="111">
                    <left>
                        <column name="prod_id" start-index="91" stop-index="99">
                            <owner name="s" start-index="91" stop-index="91"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="prod_id" start-index="103" stop-index="111">
                            <owner name="p" start-index="103" stop-index="103"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="prod_subcategory" start-index="122" stop-index="139">
                <owner name="p" start-index="122" stop-index="122"/>
            </column-item>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_order_by_desc">
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
        <projections start-index="7" stop-index="42">
            <aggregation-projection type="SUM" expression="SUM(order_id)" alias="orders_sum" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" start-index="36" stop-index="42"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="66" stop-index="72"/>
        </group-by>
        <order-by>
            <column-item name="orders_sum" order-direction="DESC" start-index="83" stop-index="92"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_without_grouped_column" parameters="1, 2, 9, 10">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="36" stop-index="44"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="51" stop-index="64"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="69" stop-index="117">
                        <left>
                            <binary-operation-expression start-index="69" stop-index="89">
                                <left>
                                    <column name="user_id" start-index="69" stop-index="77">
                                        <owner name="o" start-index="69" stop-index="69"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="81" stop-index="89">
                                        <owner name="i" start-index="81" stop-index="81"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="95" stop-index="117">
                                <left>
                                    <column name="order_id" start-index="95" stop-index="104">
                                        <owner name="o" start-index="95" stop-index="95"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="108" stop-index="117">
                                        <owner name="i" start-index="108" stop-index="108"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="29">
            <aggregation-projection type="COUNT" expression="count(*)" alias="items_count" start-index="7"
                                    stop-index="14"/>
        </projections>
        <where start-index="119" stop-index="174" literal-stop-index="175">
            <expr>
                <binary-operation-expression start-index="125" stop-index="174" literal-stop-index="175">
                    <left>
                        <in-expression start-index="125" stop-index="143">
                            <not>false</not>
                            <left>
                                <column name="user_id" start-index="125" stop-index="133">
                                    <owner name="o" start-index="125" stop-index="125"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="138" stop-index="143">
                                    <items>
                                        <literal-expression value="1" start-index="139" stop-index="139"/>
                                        <parameter-marker-expression parameter-index="0" start-index="139"
                                                                     stop-index="139"/>
                                    </items>
                                    <items>
                                        <literal-expression value="2" start-index="142" stop-index="142"/>
                                        <parameter-marker-expression parameter-index="1" start-index="142"
                                                                     stop-index="142"/>
                                    </items>
                                </list-expression>
                            </right>
                        </in-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <between-expression start-index="149" stop-index="174" literal-stop-index="175">
                            <not>false</not>
                            <left>
                                <column name="order_id" start-index="149" stop-index="158">
                                    <owner name="o" start-index="149" stop-index="149"/>
                                </column>
                            </left>
                            <between-expr>
                                <literal-expression value="9" start-index="168" stop-index="168"/>
                                <parameter-marker-expression parameter-index="2" start-index="168" stop-index="168"/>
                            </between-expr>
                            <and-expr>
                                <literal-expression value="10" start-index="174" stop-index="175"/>
                                <parameter-marker-expression parameter-index="3" start-index="174" stop-index="174"/>
                            </and-expr>
                        </between-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="user_id" start-index="185" stop-index="193" literal-start-index="186"
                         literal-stop-index="194">
                <owner name="o" start-index="185" stop-index="185" literal-start-index="186" literal-stop-index="186"/>
            </column-item>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_limit" parameters="5">
        <from>
            <simple-table name="t_order" start-index="20" stop-index="26"/>
        </from>
        <projections start-index="7" stop-index="13">
            <column-projection name="user_id" start-index="7" stop-index="13"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="37" stop-index="43"/>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="54" stop-index="60"/>
        </order-by>
        <limit start-index="62" stop-index="68">
            <row-count value="5" parameter-index="0" start-index="68" stop-index="68"/>
        </limit>
    </select>

    <select sql-case-id="select_group_by_with_order_by_and_limit" parameters="5">
        <from>
            <simple-table name="t_order" start-index="49" stop-index="55"/>
        </from>
        <projections start-index="7" stop-index="42">
            <column-projection name="user_id" start-index="7" stop-index="13"/>
            <aggregation-projection type="SUM" expression="SUM(order_id)" alias="orders_sum" start-index="16"
                                    stop-index="28"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="66" stop-index="72"/>
        </group-by>
        <order-by>
            <expression-item expression="SUM(order_id)" start-index="83" stop-index="95"/>
        </order-by>
        <limit start-index="97" stop-index="103">
            <row-count value="5" parameter-index="0" start-index="103" stop-index="103"/>
        </limit>
    </select>

    <select sql-case-id="select_with_item_alias_match_order_by_and_group_by_items">
        <from>
            <simple-table name="t_order" alias="o" start-index="26" stop-index="34"/>
        </from>
        <projections start-index="7" stop-index="19">
            <column-projection name="user_id" alias="uid" start-index="7" stop-index="19">
                <owner name="o" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="45" stop-index="53">
                <owner name="o" start-index="45" stop-index="45"/>
            </column-item>
        </group-by>
        <order-by>
            <column-item name="user_id" start-index="64" stop-index="72">
                <owner name="o" start-index="64" stop-index="64"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_date_function" parameters="1000, 1100">
        <from>
            <simple-table name="t_order_item" start-delimiter="`" end-delimiter="`" start-index="91" stop-index="104"/>
        </from>
        <projections start-index="7" stop-index="84">
            <expression-projection text="date_format(creation_date,  '%y-%m-%d')" alias="creation_date" start-index="7"
                                   stop-index="62">
                <expr>
                    <function function-name="date_format" alias="creation_date" start-index="7" stop-index="45"
                              text="date_format(creation_date,  '%y-%m-%d')">
                        <parameter>
                            <column name="creation_date" start-index="19" stop-index="31"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="%y-%m-%d" start-index="35" stop-index="44"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <aggregation-projection type="COUNT" expression="count(*)" alias="c_number" start-index="65"
                                    stop-index="72"/>
        </projections>
        <where start-index="106" stop-index="129" literal-stop-index="135">
            <expr>
                <in-expression start-index="112" stop-index="129" literal-stop-index="135">
                    <not>false</not>
                    <left>
                        <column name="order_id" start-index="112" stop-index="119"/>
                    </left>
                    <right>
                        <list-expression start-index="124" stop-index="129" literal-stop-index="135">
                            <items>
                                <literal-expression value="1000" start-index="125" stop-index="128"/>
                                <parameter-marker-expression parameter-index="0" start-index="125" stop-index="125"/>
                            </items>
                            <items>
                                <literal-expression value="1100" start-index="131" stop-index="134"/>
                                <parameter-marker-expression parameter-index="1" start-index="128" stop-index="128"/>
                            </items>
                        </list-expression>
                    </right>
                </in-expression>
            </expr>
        </where>
        <group-by>
            <expression-item expression="date_format(creation_date, '%y-%m-%d')" start-index="140" stop-index="177"
                             literal-start-index="146" literal-stop-index="183"/>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_keyword_alias">
        <from>
            <simple-table name="t_order" start-index="58" stop-index="64"/>
        </from>
        <projections start-index="7" stop-index="51">
            <aggregation-projection type="SUM" expression="SUM(order_id)" alias="orders_sum" start-index="7"
                                    stop-index="19"/>
            <column-projection name="user_id" alias="key" start-index="36" stop-index="51"/>
        </projections>
        <group-by>
            <column-item name="key" start-delimiter="`" end-delimiter="`" start-index="75" stop-index="79"/>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_count_without_column_name">
        <from>
            <simple-table name="t_order" start-index="53" stop-index="59"/>
        </from>
        <projections start-index="7" stop-index="46">
            <aggregation-projection expression="COUNT(order_id)" type="COUNT" alias="orders_count" start-index="7"
                                    stop-index="21"/>
            <column-projection name="user_id" start-index="40" stop-index="46"/>
        </projections>
        <group-by>
            <index-item index="2" start-index="70" stop-index="70"/>
        </group-by>
        <order-by>
            <index-item index="2" start-index="81" stop-index="81"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_having">
        <from>
            <simple-table name="t_order" start-index="53" stop-index="59"/>
        </from>
        <projections start-index="7" stop-index="46">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(order_id)" start-index="7"
                                    stop-index="21"/>
            <column-projection name="user_id" start-index="40" stop-index="46"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="70" stop-index="76"/>
        </group-by>
        <having start-index="78" stop-index="100">
            <expr>
                <binary-operation-expression start-index="85" stop-index="100">
                    <left>
                        <column name="orders_count" start-index="85" stop-index="96"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression start-index="100" stop-index="100" value="0"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
    </select>

    <select sql-case-id="select_group_by_with_having_count">
        <from>
            <simple-table name="t_order" start-index="37" stop-index="43"/>
        </from>
        <projections start-index="7" stop-index="30">
            <aggregation-projection type="COUNT" expression="COUNT(order_id)" start-index="7" stop-index="21"/>
            <column-projection name="user_id" start-index="24" stop-index="30"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="54" stop-index="60"/>
        </group-by>
        <having start-index="62" stop-index="87">
            <expr>
                <binary-operation-expression start-index="69" stop-index="87">
                    <left>
                        <aggregation-projection type="COUNT" expression="COUNT(order_id)" start-index="69"
                                                stop-index="83"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression start-index="87" stop-index="87" value="0"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
    </select>

    <select sql-case-id="select_group_by_with_having_and_window">
        <from>
            <simple-table name="t_order" start-index="53" stop-index="59"/>
        </from>
        <projections start-index="7" stop-index="46">
            <aggregation-projection type="COUNT" alias="orders_count" expression="COUNT(order_id)" start-index="7"
                                    stop-index="21"/>
            <column-projection name="user_id" start-index="40" stop-index="46"/>
        </projections>
        <group-by>
            <column-item name="user_id" start-index="70" stop-index="76"/>
        </group-by>
        <having start-index="78" stop-index="100">
            <expr>
                <binary-operation-expression start-index="85" stop-index="100">
                    <left>
                        <column name="orders_count" start-index="85" stop-index="96"/>
                    </left>
                    <operator>&gt;</operator>
                    <right>
                        <literal-expression start-index="100" stop-index="100" value="0"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
        <window start-index="102" stop-index="135">
            <window-item start-index="109" stop-index="135"/>
        </window>
    </select>

    <select sql-case-id="select_group_by_cube">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="employees" alias="e" start-index="218" stop-index="228"/>
                </left>
                <right>
                    <simple-table name="departments" alias="d" start-index="231" stop-index="243"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="211">
            <expression-projection text="DECODE(GROUPING(department_name), 1, 'All Departments', department_name)"
                                   alias="department_name" start-index="7" stop-index="97"/>
            <expression-projection text="DECODE(GROUPING(job_id), 1, 'All Jobs', job_id)" alias="job_id"
                                   start-index="100" stop-index="156"/>
            <aggregation-projection type="COUNT" alias="Total Empl" expression="COUNT(*)" start-index="159"
                                    stop-index="166"/>
            <expression-projection text="AVG(salary) * 12" alias="Average Sal" start-index="182" stop-index="211"/>
        </projections>
        <where start-index="245" stop-index="283">
            <expr>
                <binary-operation-expression start-index="251" stop-index="283">
                    <left>
                        <column name="department_id" start-index="251" stop-index="265">
                            <owner name="d" start-index="251" stop-index="251"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="department_id" start-index="269" stop-index="283">
                            <owner name="e" start-index="269" stop-index="269"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="department_name" start-index="300" stop-index="314"/>
            <column-item name="job_id" start-index="317" stop-index="322"/>
        </group-by>
        <order-by>
            <column-item name="department_name" start-index="334" stop-index="348"/>
            <column-item name="job_id" start-index="351" stop-index="356"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_grouping_sets">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <join-table join-type="COMMA">
                        <left>
                            <join-table join-type="COMMA">
                                <left>
                                    <join-table join-type="COMMA">
                                        <left>
                                            <simple-table name="sales" start-index="113" stop-index="117"/>
                                        </left>
                                        <right>
                                            <simple-table name="customers" start-index="120" stop-index="128"/>
                                        </right>
                                    </join-table>
                                </left>
                                <right>
                                    <simple-table name="times" start-index="131" stop-index="135"/>
                                </right>
                            </join-table>
                        </left>
                        <right>
                            <simple-table name="channels" start-index="138" stop-index="145"/>
                        </right>
                    </join-table>
                </left>
                <right>
                    <simple-table name="countries" alias="co" start-index="148" stop-index="159"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="106">
            <column-projection name="channel_desc" start-index="7" stop-index="18"/>
            <column-projection name="calendar_month_desc" start-index="21" stop-index="39"/>
            <column-projection name="country_id" start-index="42" stop-index="54">
                <owner name="co" start-index="42" stop-index="43"/>
            </column-projection>
            <expression-projection text="TO_CHAR(SUM(amount_sold) , '9,999,999,999')" alias="SALES$" start-index="57"
                                   stop-index="106"/>
        </projections>
        <where start-index="161" stop-index="193">
            <expr>
                <binary-operation-expression start-index="167" stop-index="193">
                    <left>
                        <column name="time_id" start-index="167" stop-index="179">
                            <owner name="sales" start-index="167" stop-index="171"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <column name="time_id" start-index="181" stop-index="193">
                            <owner name="times" start-index="181" stop-index="185"/>
                        </column>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <expression-item expression="(channel_desc,calendar_month_desc,co.country_id)" start-index="218"
                             stop-index="265"/>
            <expression-item expression="(channel_desc,co.country_id)" start-index="268" stop-index="295"/>
            <expression-item expression="(calendar_month_desc,co.country_id)" start-index="298" stop-index="332"/>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_with_having_with_order_by">
        <from>
            <simple-table name="employees" start-index="52" stop-index="60"/>
        </from>
        <projections start-index="7" stop-index="45">
            <column-projection name="department_id" start-index="7" stop-index="19"/>
            <aggregation-projection type="MIN" expression="MIN(salary)" start-index="22" stop-index="32"/>
            <aggregation-projection type="MAX" expression="MAX(salary)" start-index="35" stop-index="45"/>
        </projections>
        <group-by>
            <column-item name="department_id" start-index="71" stop-index="83"/>
        </group-by>
        <having start-index="85" stop-index="109">
            <expr>
                <binary-operation-expression start-index="92" stop-index="109">
                    <left>
                        <aggregation-projection type="MIN" expression="MIN(salary)" start-index="92" stop-index="102"/>
                    </left>
                    <operator>&lt;</operator>
                    <right>
                        <literal-expression value="5000" start-index="106" stop-index="109"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <column-item name="department_id" start-index="120" stop-index="132"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_with_having_with_subquery">
        <from>
            <simple-table name="employees" start-index="38" stop-index="46"/>
        </from>
        <projections start-index="7" stop-index="31">
            <column-projection name="department_id" start-index="7" stop-index="19"/>
            <column-projection name="manager_id" start-index="22" stop-index="31"/>
        </projections>
        <group-by>
            <column-item name="department_id" start-index="57" stop-index="69"/>
            <column-item name="manager_id" start-index="72" stop-index="81"/>
        </group-by>
        <having start-index="83" stop-index="219">
            <expr>
                <in-expression start-index="90" stop-index="219">
                    <not>false</not>
                    <left>
                        <list-expression start-index="90" stop-index="116" literal-start-index="90"
                                         literal-stop-index="116">
                            <items>
                                <column name="department_id" start-index="91" stop-index="103" literal-start-index="91"
                                        literal-stop-index="103"/>
                            </items>
                            <items>
                                <column name="manager_id" start-index="106" stop-index="115" literal-start-index="106"
                                        literal-stop-index="115"/>
                            </items>
                        </list-expression>
                    </left>
                    <right>
                        <subquery start-index="121" stop-index="219">
                            <select>
                                <from start-index="160" stop-index="170">
                                    <simple-table name="employees" alias="x" start-index="160" stop-index="170"/>
                                </from>
                                <projections start-index="129" stop-index="153">
                                    <column-projection name="department_id" start-index="129" stop-index="141"/>
                                    <column-projection name="manager_id" start-index="144" stop-index="153"/>
                                </projections>
                                <where start-index="172" stop-index="218">
                                    <expr>
                                        <binary-operation-expression start-index="178" stop-index="218">
                                            <left>
                                                <column name="department_id" start-index="178" stop-index="192">
                                                    <owner name="x" start-index="178" stop-index="178"/>
                                                </column>
                                            </left>
                                            <operator>=</operator>
                                            <right>
                                                <column name="department_id" start-index="196" stop-index="218">
                                                    <owner name="employees" start-index="196" stop-index="204"/>
                                                </column>
                                            </right>
                                        </binary-operation-expression>
                                    </expr>
                                </where>
                            </select>
                        </subquery>
                    </right>
                </in-expression>
            </expr>
        </having>
        <order-by>
            <column-item name="department_id" start-index="230" stop-index="242"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_case_when_then_in_group_by_item_and_order_by_item">
        <from>
            <simple-table name="t_order" start-index="21" stop-index="27"/>
        </from>
        <projections start-index="7" stop-index="14">
            <column-projection name="order_id" start-index="7" stop-index="14"/>
        </projections>
        <group-by>
            <expression-item
                    expression="CASE WHEN order_id > 0 AND order_id &lt;= 10 THEN '(0,10]' WHEN order_id > 10 THEN '(10,+∞)' ELSE '' END"
                    start-index="38" stop-index="138"/>
        </group-by>
        <order-by>
            <expression-item
                    expression="CASE WHEN order_id > 0 AND order_id &lt;= 10 THEN '(0,10]' WHEN order_id > 10 THEN '(10,+∞)' ELSE '' END"
                    start-index="149" stop-index="249"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_event_group_by_with_having_order_by">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="syscolumns" alias="c" start-index="66" stop-index="85">
                        <owner name="sys" start-index="66" stop-index="68"/>
                    </simple-table>
                </left>
                <right>
                    <simple-table name="sysobjects" alias="o" start-index="92" stop-index="111">
                        <owner name="sys" start-index="92" stop-index="94"/>
                    </simple-table>
                </right>
                <on-condition>
                    <binary-operation-expression text="o.id = c.id" start-index="116" stop-index="126">
                        <left>
                            <column name="id" start-index="116" stop-index="119">
                                <owner name="o" start-index="116" stop-index="116"/>
                            </column>
                        </left>
                        <right>
                            <column name="id" start-index="123" stop-index="126">
                                <owner name="c" start-index="123" stop-index="123"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="59">
            <column-projection name="name" start-index="7" stop-index="12">
                <owner name="c" start-index="7" stop-index="7"/>
            </column-projection>
            <aggregation-projection expression="Count(*)" type="COUNT" alias="Count-Per-Column-Repeated-Name"
                                    start-index="15" stop-index="22"/>
        </projections>
        <where start-index="128" stop-index="171">
            <expr>
                <binary-operation-expression text="o.type = 'V' AND c.name like '%event%'" start-index="134"
                                             stop-index="171">
                    <left>
                        <binary-operation-expression text="o.type = 'V'" start-index="134" stop-index="145">
                            <left>
                                <column name="type" start-index="134" stop-index="139">
                                    <owner name="o" start-index="134" stop-index="134"/>
                                </column>
                            </left>
                            <right>
                                <literal-expression value="V" start-index="143" stop-index="145"/>
                            </right>
                            <operator>=</operator>
                        </binary-operation-expression>
                    </left>
                    <right>
                        <binary-operation-expression text="c.name like '%event%'" start-index="151" stop-index="171">
                            <left>
                                <column name="name" start-index="151" stop-index="156">
                                    <owner name="c" start-index="151" stop-index="151"/>
                                </column>
                            </left>
                            <right>
                                <list-expression start-index="163" stop-index="171">
                                    <items>
                                        <literal-expression value="%event%" start-index="163" stop-index="171"/>
                                    </items>
                                </list-expression>
                            </right>
                            <operator>LIKE</operator>
                        </binary-operation-expression>
                    </right>
                    <operator>AND</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <group-by>
            <column-item name="name" order-direction="ASC" start-index="182" stop-index="187">
                <owner name="c" start-index="182" stop-index="182"/>
            </column-item>
        </group-by>
        <having start-index="189" stop-index="208">
            <expr>
                <binary-operation-expression text="Count(*) >= 3" start-index="196" stop-index="208">
                    <left>
                        <aggregation-projection expression="Count(*)" type="COUNT" start-index="196" stop-index="203"/>
                    </left>
                    <right>
                        <literal-expression value="3" start-index="208" stop-index="208"/>
                    </right>
                    <operator>&gt;=</operator>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <column-item name="name" order-direction="ASC" start-index="219" stop-index="224">
                <owner name="c" start-index="219" stop-index="219"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_with_datepart_group_by_with_order_by">
        <projections start-index="7" stop-index="81">
            <expression-projection text="DATEPART(yyyy,OrderDate)" alias="Year" start-index="7" stop-index="41">
                <expr>
                    <function function-name="DATEPART" text="DATEPART(yyyy,OrderDate)" start-index="7" stop-index="30">
                        <parameter>
                            <column name="yyyy" start-index="16" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <column name="OrderDate" start-index="21" stop-index="29"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <aggregation-projection type="SUM" expression="SUM(TotalDue)" alias="Total Order Amount" start-index="44"
                                    stop-index="56">
                <paramters>
                    <column name="TotalDue" start-index="48" stop-index="55"/>
                </paramters>
            </aggregation-projection>
        </projections>
        <from>
            <simple-table name="SalesOrderHeader" start-index="88" stop-index="109">
                <owner name="Sales" start-index="88" stop-index="92"/>
            </simple-table>
        </from>
        <group-by>
            <expression-item expression="DATEPART(yyyy,OrderDate)" start-index="120" stop-index="143"/>
        </group-by>
        <order-by>
            <expression-item expression="DATEPART(yyyy,OrderDate)" order-direction="ASC" start-index="154"
                             stop-index="177"/>
        </order-by>
    </select>

    <select sql-case-id="select_with_datepart_group_by_with_having_order_by">
        <projections start-index="7" stop-index="81">
            <expression-projection text="DATEPART(yyyy,OrderDate)" alias="Year" start-index="7" stop-index="41">
                <expr>
                    <function function-name="DATEPART" text="DATEPART(yyyy,OrderDate)" start-index="7" stop-index="30">
                        <parameter>
                            <column name="yyyy" start-index="16" stop-index="19"/>
                        </parameter>
                        <parameter>
                            <column name="OrderDate" start-index="21" stop-index="29"/>
                        </parameter>
                    </function>
                </expr>
            </expression-projection>
            <aggregation-projection type="SUM" expression="SUM(TotalDue)" alias="Total Order Amount" start-index="44"
                                    stop-index="56">
                <paramters>
                    <column name="TotalDue" start-index="48" stop-index="55"/>
                </paramters>
            </aggregation-projection>
        </projections>
        <from>
            <simple-table name="SalesOrderHeader" start-index="88" stop-index="109">
                <owner name="Sales" start-index="88" stop-index="92"/>
            </simple-table>
        </from>
        <group-by>
            <expression-item expression="DATEPART(yyyy,OrderDate)" start-index="120" stop-index="143"/>
        </group-by>
        <having start-index="145" stop-index="186">
            <expr>
                <binary-operation-expression text="DATEPART(yyyy,OrderDate) &gt;= N'2003'" start-index="152"
                                             stop-index="186">
                    <left>
                        <function function-name="DATEPART" text="DATEPART(yyyy,OrderDate)" start-index="152"
                                  stop-index="175">
                            <parameter>
                                <column name="yyyy" start-index="161" stop-index="164"/>
                            </parameter>
                            <parameter>
                                <column name="OrderDate" start-index="166" stop-index="174"/>
                            </parameter>
                        </function>
                    </left>
                    <right>
                        <literal-expression value="2003" start-index="180" stop-index="186"/>
                    </right>
                    <operator>&gt;=</operator>
                </binary-operation-expression>
            </expr>
        </having>
        <order-by>
            <expression-item expression="DATEPART(yyyy,OrderDate)" order-direction="ASC" start-index="197"
                             stop-index="220"/>
        </order-by>
    </select>

    <select sql-case-id="select_from_input_table">
        <projections start-index="7" stop-index="14">
            <aggregation-projection type="COUNT" expression="count(*)" start-index="7" stop-index="14"/>
        </projections>
        <from start-index="21" stop-index="25">
            <simple-table name="input" start-index="21" stop-index="25"/>
        </from>
        <group-by>
            <column-item name="PartitionId" order-direction="ASC" start-index="36" stop-index="46"/>
            <column-item name="clusterid" order-direction="ASC" start-index="49" stop-index="57"/>
            <column-item name="tumblingwindow" order-direction="ASC" start-index="60" stop-index="73"/>
        </group-by>
    </select>

    <select sql-case-id="select_group_by_top_column_value">
        <projections start-index="7" stop-index="34">
            <top-projection start-index="7" stop-index="12">
                <top-value value="10" start-index="26" stop-index="26"/>
            </top-projection>
            <column-projection name="hash_unique_bigint_id" start-index="14" stop-index="34"/>
        </projections>
        <from start-index="41" stop-index="55">
            <simple-table name="TelemetryDS" start-index="41" stop-index="55">
                <owner name="dbo" start-index="41" stop-index="43"/>
            </simple-table>
        </from>
        <where start-index="57" stop-index="103">
            <expr>
                <between-expression start-index="63" stop-index="103">
                    <left>
                        <column name="Timestamp" start-index="63" stop-index="71"/>
                    </left>
                    <between-expr>
                        <column name="@StartTime" start-index="81" stop-index="90"/>
                    </between-expr>
                    <and-expr>
                        <column name="@EndTime" start-index="96" stop-index="103"/>
                    </and-expr>
                </between-expression>
            </expr>
        </where>
        <group-by start-index="57" stop-index="103">
            <column-item name="hash_unique_bigint_id" order-direction="ASC" start-index="114" stop-index="134"/>
        </group-by>
        <order-by start-index="136" stop-index="179">
            <expression-item expression="MAX(max_elapsed_time_microsec)" order-direction="DESC" start-index="145"
                             stop-index="174"/>
        </order-by>
    </select>

    <select sql-case-id="select_group_by_count_with_tumblingwindow_function">
        <projections start-index="7" stop-index="14">
            <aggregation-projection expression="count(*)" type="COUNT" start-index="7" stop-index="14"/>
        </projections>
        <from start-index="21" stop-index="25">
            <simple-table name="input" start-index="21" stop-index="25"/>
        </from>
        <group-by start-index="27" stop-index="71">
            <column-item name="clusterid" order-direction="ASC" start-index="36" stop-index="44"/>
            <expression-item expression="tumblingwindow(minutes, 5)" order-direction="ASC" start-index="46"
                             stop-index="71">
                <expr>
                    <function function-name="tumblingwindow" text="tumblingwindow(minutes, 5)" start-index="46"
                              stop-index="71">
                        <parameter>
                            <column name="minutes" start-index="61" stop-index="67"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="5" start-index="70" stop-index="70"/>
                        </parameter>
                    </function>
                </expr>
            </expression-item>
        </group-by>
    </select>
</sql-parser-test-cases>
