<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <update sql-case-id="update_without_alias" parameters="'update', 1, 1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="28" literal-stop-index="35">
            <assignment start-index="19" stop-index="28" literal-stop-index="35">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="update" start-index="28" stop-index="35"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="30" stop-index="63" literal-start-index="37" literal-stop-index="70">
            <expr>
                <binary-operation-expression start-index="36" stop-index="63" literal-start-index="43"
                                             literal-stop-index="70">
                    <left>
                        <binary-operation-expression start-index="36" stop-index="47" literal-start-index="43"
                                                     literal-stop-index="54">
                            <left>
                                <column name="order_id" start-index="36" stop-index="43" literal-start-index="43"
                                        literal-stop-index="50"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="54" stop-index="54"/>
                                <parameter-marker-expression parameter-index="1" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="53" stop-index="63" literal-start-index="60"
                                                     literal-stop-index="70">
                            <left>
                                <column name="user_id" start-index="53" stop-index="59" literal-start-index="60"
                                        literal-stop-index="66"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="70" stop-index="70"/>
                                <parameter-marker-expression parameter-index="2" start-index="63" stop-index="63"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_alias" parameters="'update', 1, 1">
        <table start-index="7" stop-index="18">
            <simple-table name="t_order" alias="o" start-index="7" stop-index="18"/>
        </table>
        <set start-index="20" stop-index="35" literal-stop-index="42">
            <assignment start-index="24" stop-index="35" literal-stop-index="42">
                <column name="status" start-index="24" stop-index="31">
                    <owner name="o" start-index="24" stop-index="24"/>
                </column>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="35" stop-index="35"/>
                    <literal-expression value="update" start-index="35" stop-index="42"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="37" stop-index="74" literal-start-index="44" literal-stop-index="81">
            <expr>
                <binary-operation-expression start-index="43" stop-index="74" literal-start-index="50"
                                             literal-stop-index="81">
                    <left>
                        <binary-operation-expression start-index="43" stop-index="56" literal-start-index="50"
                                                     literal-stop-index="63">
                            <left>
                                <column name="order_id" start-index="43" stop-index="52" literal-start-index="50"
                                        literal-stop-index="59">
                                    <owner name="o" start-index="43" stop-index="43" literal-start-index="50"
                                           literal-stop-index="50"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="63" stop-index="63"/>
                                <parameter-marker-expression parameter-index="1" start-index="56" stop-index="56"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="62" stop-index="74" literal-start-index="69"
                                                     literal-stop-index="81">
                            <left>
                                <column name="user_id" start-index="62" stop-index="70" literal-start-index="69"
                                        literal-stop-index="77">
                                    <owner name="o" start-index="62" stop-index="62" literal-start-index="69"
                                           literal-stop-index="69"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="81" stop-index="81"/>
                                <parameter-marker-expression parameter-index="2" start-index="74" stop-index="74"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_unicode_escape_alias" parameters="'update', 1, 1">
        <table start-index="7" stop-index="18">
            <simple-table name="t_order" alias="u" start-index="7" stop-index="18"/>
        </table>
        <set start-index="20" stop-index="33" literal-stop-index="40">
            <assignment start-index="24" stop-index="33" literal-stop-index="40">
                <column name="status" start-index="24" stop-index="29"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="33" stop-index="33"/>
                    <literal-expression value="update" start-index="33" stop-index="40"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="35" stop-index="72" literal-start-index="42" literal-stop-index="79">
            <expr>
                <binary-operation-expression start-index="41" stop-index="72" literal-start-index="48"
                                             literal-stop-index="79">
                    <left>
                        <binary-operation-expression start-index="41" stop-index="54" literal-start-index="48"
                                                     literal-stop-index="61">
                            <left>
                                <column name="order_id" start-index="41" stop-index="50" literal-start-index="48"
                                        literal-stop-index="57">
                                    <owner name="u" start-index="41" stop-index="41" literal-start-index="48"
                                           literal-stop-index="48"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="61" stop-index="61"/>
                                <parameter-marker-expression parameter-index="1" start-index="54" stop-index="54"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="60" stop-index="72" literal-start-index="67"
                                                     literal-stop-index="79">
                            <left>
                                <column name="user_id" start-index="60" stop-index="68" literal-start-index="67"
                                        literal-stop-index="75">
                                    <owner name="u" start-index="60" stop-index="60" literal-start-index="67"
                                           literal-stop-index="67"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="79" stop-index="79"/>
                                <parameter-marker-expression parameter-index="2" start-index="72" stop-index="72"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_equal_with_geography"
            parameters="'2017-06-07', 100, 200, '{&quot;rule2&quot;:&quot;null2&quot;}', 3, 5, 7, 200">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="168" literal-stop-index="201">
            <assignment start-index="19" stop-index="32" literal-stop-index="43">
                <column name="start_time" start-index="19" stop-index="28"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="32" stop-index="32"/>
                    <literal-expression value="2017-06-07" start-index="32" stop-index="43"/>
                </assignment-value>
            </assignment>
            <assignment start-index="35" stop-index="44" literal-start-index="46" literal-stop-index="55">
                <column name="status" start-index="35" stop-index="40" literal-start-index="46"
                        literal-stop-index="51"/>
                <assignment-value>
                    <literal-expression value="0" start-index="44" stop-index="44" literal-start-index="55"
                                        literal-stop-index="55"/>
                </assignment-value>
            </assignment>
            <assignment start-index="47" stop-index="116" literal-start-index="58" literal-stop-index="131">
                <column name="start_point" start-index="47" stop-index="57" literal-start-index="58"
                        literal-stop-index="68"/>
                <assignment-value>
                    <function function-name="ST_GeographyFromText"
                              text="ST_GeographyFromText('SRID=4326;POINT('||?||' '||?||')')"
                              literal-text="ST_GeographyFromText('SRID=4326;POINT('||100||' '||200||')')"
                              start-index="61" stop-index="116" literal-start-index="72" literal-stop-index="131">
                        <parameter>
                            <common-expression text="'SRID=4326;POINT('||?||' '||?||')'"
                                               literal-text="'SRID=4326;POINT('||100||' '||200||')'"
                                               literal-start-index="93" start-index="82" literal-stop-index="130"
                                               stop-index="115"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
            <assignment start-index="119" stop-index="133" literal-start-index="134" literal-stop-index="166">
                <column name="rule" start-index="119" stop-index="122" literal-start-index="134"
                        literal-stop-index="137"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="3" start-index="126" stop-index="133"/>
                    <common-expression text="'{&quot;rule2&quot;:&quot;null2&quot;}'::jsonb" start-index="141"
                                       stop-index="166"/>
                </assignment-value>
            </assignment>
            <assignment start-index="136" stop-index="152" literal-start-index="169" literal-stop-index="185">
                <column name="discount_type" start-index="136" stop-index="148" literal-start-index="169"
                        literal-stop-index="181"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="4" start-index="152" stop-index="152"/>
                    <literal-expression value="3" start-index="185" stop-index="185"/>
                </assignment-value>
            </assignment>
            <assignment start-index="155" stop-index="168" literal-start-index="188" literal-stop-index="201">
                <column name="order_type" start-index="155" stop-index="164" literal-start-index="188"
                        literal-stop-index="197"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="5" start-index="168" stop-index="168"/>
                    <literal-expression value="5" start-index="201" stop-index="201"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="170" stop-index="203" literal-start-index="203" literal-stop-index="238">
            <expr>
                <binary-operation-expression start-index="176" stop-index="203" literal-start-index="209"
                                             literal-stop-index="238">
                    <left>
                        <binary-operation-expression start-index="176" stop-index="186" literal-start-index="209"
                                                     literal-stop-index="219">
                            <left>
                                <column name="user_id" start-index="176" stop-index="182" literal-start-index="209"
                                        literal-stop-index="215"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="7" start-index="219" stop-index="219"/>
                                <parameter-marker-expression parameter-index="6" start-index="186" stop-index="186"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="192" stop-index="203" literal-start-index="225"
                                                     literal-stop-index="238">
                            <left>
                                <column name="order_id" start-index="192" stop-index="199" literal-start-index="225"
                                        literal-stop-index="232"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="200" start-index="236" stop-index="238"/>
                                <parameter-marker-expression parameter-index="7" start-index="203" stop-index="203"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_without_condition">
        <table start-index="7" stop-index="15">
            <simple-table name="t_order" alias="o" start-index="7" stop-index="15"/>
        </table>
        <set start-index="17" stop-index="41">
            <assignment start-index="21" stop-index="41">
                <column name="status" start-index="21" stop-index="28">
                    <owner name="o" start-index="21" stop-index="21"/>
                </column>
                <assignment-value>
                    <literal-expression value="finished" start-index="32" stop-index="41"/>
                </assignment-value>
            </assignment>
        </set>
    </update>

    <update sql-case-id="update_with_extra_keywords" parameters="'update', 1, 1">
        <table start-index="27" stop-index="33">
            <simple-table name="t_order" start-index="27" stop-index="33"/>
        </table>
        <set start-index="35" stop-index="48" literal-stop-index="55">
            <assignment start-index="39" stop-index="48" literal-start-index="39" literal-stop-index="55">
                <column name="status" start-index="39" stop-index="44"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="48" stop-index="48"/>
                    <literal-expression value="update" start-index="48" stop-index="55"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="50" stop-index="83" literal-start-index="57" literal-stop-index="90">
            <expr>
                <binary-operation-expression start-index="56" stop-index="83" literal-start-index="63"
                                             literal-stop-index="90">
                    <left>
                        <binary-operation-expression start-index="56" stop-index="67" literal-start-index="63"
                                                     literal-stop-index="74">
                            <left>
                                <column name="order_id" start-index="56" stop-index="63" literal-start-index="63"
                                        literal-stop-index="70"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="74" stop-index="74"/>
                                <parameter-marker-expression parameter-index="1" start-index="67" stop-index="67"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="73" stop-index="83" literal-start-index="80"
                                                     literal-stop-index="90">
                            <left>
                                <column name="user_id" start-index="73" stop-index="79" literal-start-index="80"
                                        literal-stop-index="86"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="90" stop-index="90"/>
                                <parameter-marker-expression parameter-index="2" start-index="83" stop-index="83"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_special_character" parameters="'update', 1, 1">
        <table start-index="7" stop-index="15">
            <simple-table name="t_order" start-delimiter="`" end-delimiter="`" start-index="7" stop-index="15"/>
        </table>
        <set start-index="17" stop-index="32" literal-stop-index="39">
            <assignment start-index="21" stop-index="32" literal-stop-index="39">
                <column name="status" start-delimiter="`" end-delimiter="`" start-index="21" stop-index="28"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="32" stop-index="32"/>
                    <literal-expression value="update" start-index="32" stop-index="39"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="34" stop-index="69" literal-start-index="41" literal-stop-index="76">
            <expr>
                <binary-operation-expression start-index="40" stop-index="69" literal-start-index="47"
                                             literal-stop-index="76">
                    <left>
                        <binary-operation-expression start-index="40" stop-index="53" literal-start-index="47"
                                                     literal-stop-index="60">
                            <left>
                                <column name="order_id" start-delimiter="`" end-delimiter="`" start-index="40"
                                        stop-index="49" literal-start-index="47" literal-stop-index="56"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="60" stop-index="60"/>
                                <parameter-marker-expression parameter-index="1" start-index="53" stop-index="53"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="59" stop-index="69" literal-start-index="66"
                                                     literal-stop-index="76">
                            <left>
                                <column name="user_id" start-index="59" stop-index="65" literal-start-index="66"
                                        literal-stop-index="72"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="76" stop-index="76"/>
                                <parameter-marker-expression parameter-index="2" start-index="69" stop-index="69"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_without_parameters">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="35">
            <assignment start-index="19" stop-index="35">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <literal-expression value="update" start-index="28" stop-index="35"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="37" stop-index="74">
            <expr>
                <binary-operation-expression start-index="43" stop-index="74">
                    <left>
                        <binary-operation-expression start-index="43" stop-index="57">
                            <left>
                                <column name="order_id" start-index="43" stop-index="50"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1000" start-index="54" stop-index="57"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="63" stop-index="74">
                            <left>
                                <column name="user_id" start-index="63" stop-index="69"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="10" start-index="73" stop-index="74"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_or" parameters="1000, 0, 10">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="35">
            <assignment start-index="19" stop-index="35">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <literal-expression value="update" start-index="28" stop-index="35"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="37" stop-index="88" literal-stop-index="92">
            <expr>
                <binary-operation-expression start-index="43" stop-index="88" literal-stop-index="92">
                    <left>
                        <binary-operation-expression start-index="44" stop-index="71" literal-stop-index="74">
                            <left>
                                <binary-operation-expression start-index="44" stop-index="55" literal-stop-index="58">
                                    <left>
                                        <column name="order_id" start-index="44" stop-index="51"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="1000" start-index="55" stop-index="58"/>
                                        <parameter-marker-expression parameter-index="0" start-index="55"
                                                                     stop-index="55"/>
                                    </right>
                                </binary-operation-expression>
                            </left>
                            <operator>OR</operator>
                            <right>
                                <binary-operation-expression start-index="60" stop-index="71" literal-start-index="63"
                                                             literal-stop-index="74">
                                    <left>
                                        <column name="order_id" start-index="60" stop-index="67"
                                                literal-start-index="63" literal-stop-index="70"/>
                                    </left>
                                    <operator>=</operator>
                                    <right>
                                        <literal-expression value="0" start-index="74" stop-index="74"/>
                                        <parameter-marker-expression parameter-index="1" start-index="71"
                                                                     stop-index="71"/>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="78" stop-index="88" literal-start-index="81"
                                                     literal-stop-index="92">
                            <left>
                                <column name="user_id" start-index="78" stop-index="84" literal-start-index="81"
                                        literal-stop-index="87"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="10" start-index="91" stop-index="92"/>
                                <parameter-marker-expression parameter-index="2" start-index="88" stop-index="88"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_set_calculation" parameters="1, 2, 3">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="37" literal-stop-index="37">
            <assignment start-index="19" stop-index="37" literal-stop-index="37">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <binary-operation-expression start-index="28" stop-index="37" literal-start-index="28"
                                                 literal-stop-index="37">
                        <left>
                            <column name="status" start-index="28" stop-index="33" literal-start-index="28"
                                    literal-stop-index="33"/>
                        </left>
                        <operator>-</operator>
                        <right>
                            <literal-expression value="1" start-index="37" stop-index="37"/>
                            <parameter-marker-expression parameter-index="0" start-index="37" stop-index="37"
                                                         literal-start-index="37" literal-stop-index="37"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="39" stop-index="72">
            <expr>
                <binary-operation-expression start-index="45" stop-index="72">
                    <left>
                        <binary-operation-expression start-index="45" stop-index="56">
                            <left>
                                <column name="order_id" start-index="45" stop-index="52"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="2" start-index="56" stop-index="56"/>
                                <parameter-marker-expression parameter-index="1" start-index="56" stop-index="56"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="62" stop-index="72">
                            <left>
                                <column name="user_id" start-index="62" stop-index="68"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="3" start-index="72" stop-index="72"/>
                                <parameter-marker-expression parameter-index="2" start-index="72" stop-index="72"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_where_calculation" parameters="1, 2, 3">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="28" literal-stop-index="28">
            <assignment start-index="19" stop-index="28" literal-stop-index="28">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="1" start-index="28" stop-index="28"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="30" stop-index="74">
            <expr>
                <binary-operation-expression start-index="36" stop-index="74">
                    <left>
                        <binary-operation-expression start-index="36" stop-index="58">
                            <left>
                                <column name="order_id" start-index="36" stop-index="43"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <binary-operation-expression start-index="47" stop-index="58">
                                    <left>
                                        <column name="order_id" start-index="47" stop-index="54"/>
                                    </left>
                                    <operator>-</operator>
                                    <right>
                                        <literal-expression value="2" start-index="58" stop-index="58"/>
                                        <parameter-marker-expression parameter-index="1" start-index="58"
                                                                     stop-index="58"/>
                                    </right>
                                </binary-operation-expression>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="64" stop-index="74">
                            <left>
                                <column name="user_id" start-index="64" stop-index="70"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="3" start-index="74" stop-index="74"/>
                                <parameter-marker-expression parameter-index="2" start-index="74" stop-index="74"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_column_equal_column" parameters="1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="54" literal-stop-index="54">
            <assignment start-index="19" stop-index="37" literal-stop-index="37">
                <column name="order_id" start-index="19" stop-index="26"/>
                <assignment-value>
                    <common-expression text="order_id" literal-text="order_id" start-index="30" stop-index="37"
                                       literal-start-index="30" literal-stop-index="37"/>
                </assignment-value>
            </assignment>
            <assignment start-index="40" stop-index="54" literal-stop-index="54">
                <column name="status" start-index="40" stop-index="45"/>
                <assignment-value>
                    <literal-expression value="init" start-index="49" stop-index="54"/>
                    <common-expression text="init" start-index="49" stop-index="54" literal-start-index="49"
                                       literal-stop-index="54"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="56" stop-index="97">
            <expr>
                <binary-operation-expression start-index="62" stop-index="97">
                    <left>
                        <binary-operation-expression start-index="62" stop-index="80">
                            <left>
                                <column name="order_id" start-index="62" stop-index="69"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="73" stop-index="80"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="86" stop-index="97">
                            <left>
                                <column name="order_id" start-index="86" stop-index="93"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="97" stop-index="97"/>
                                <parameter-marker-expression parameter-index="0" start-index="97" stop-index="97"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_case_when"
            parameters="3, 2, 4, 2, 10, 2, 3, 'll', 4, 'll', 10, 'll', 3, '2020-08-10T17:15:25.979+0800', 'jd'">
        <table start-index="7" stop-index="25">
            <simple-table name="stock_freeze_detail" start-index="7" stop-index="25"/>
        </table>
        <set start-index="27" stop-index="230" literal-stop-index="270" literal-start-index="27">
            <assignment start-index="31" stop-index="106" literal-start-index="31" literal-stop-index="107">
                <column name="row_status" start-index="31" stop-index="40"/>
                <assignment-value>
                    <case-when-expression>
                        <when-exprs>
                            <binary-operation-expression start-index="53" stop-index="56" literal-start-index="53"
                                                         literal-stop-index="56">
                                <left>
                                    <column name="id" start-index="53" stop-index="54" literal-start-index="53"
                                            literal-stop-index="54"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="3" start-index="56" stop-index="56"/>
                                    <parameter-marker-expression parameter-index="0" start-index="56" stop-index="56"
                                                                 literal-start-index="56" literal-stop-index="56"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <when-exprs>
                            <binary-operation-expression start-index="72" stop-index="75" literal-start-index="72"
                                                         literal-stop-index="75">
                                <left>
                                    <column name="id" start-index="72" stop-index="73" literal-start-index="72"
                                            literal-stop-index="73"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="4" start-index="75" stop-index="75"/>
                                    <parameter-marker-expression parameter-index="2" start-index="75" stop-index="75"
                                                                 literal-start-index="75" literal-stop-index="75"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <when-exprs>
                            <binary-operation-expression start-index="91" stop-index="94" literal-start-index="91"
                                                         literal-stop-index="95">
                                <left>
                                    <column name="id" start-index="91" stop-index="92" literal-start-index="91"
                                            literal-stop-index="92"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="10" start-index="94" stop-index="94"
                                                        literal-start-index="94" literal-stop-index="95"/>
                                    <parameter-marker-expression parameter-index="4" start-index="94" stop-index="94"
                                                                 literal-start-index="94" literal-stop-index="94"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="2" start-index="64" stop-index="64"/>
                            <parameter-marker-expression parameter-index="1" start-index="64" stop-index="64"
                                                         literal-start-index="64" literal-stop-index="64"/>
                        </then-exprs>
                        <then-exprs>
                            <literal-expression value="2" start-index="83" stop-index="83"/>
                            <parameter-marker-expression parameter-index="3" start-index="83" stop-index="83"
                                                         literal-start-index="83" literal-stop-index="83"/>
                        </then-exprs>
                        <then-exprs>
                            <literal-expression value="2" start-index="103" stop-index="103" literal-start-index="103"
                                                literal-stop-index="103"/>
                            <parameter-marker-expression parameter-index="5" start-index="102" stop-index="102"
                                                         literal-start-index="103" literal-stop-index="103"/>
                        </then-exprs>
                    </case-when-expression>
                </assignment-value>
            </assignment>
            <assignment start-index="113" stop-index="189" literal-start-index="114" literal-stop-index="200">
                <column name="update_user" start-index="113" stop-index="123" literal-start-index="114"
                        literal-stop-index="124"/>
                <assignment-value>
                    <case-when-expression>
                        <when-exprs>
                            <binary-operation-expression start-index="136" stop-index="139" literal-start-index="137"
                                                         literal-stop-index="140">
                                <left>
                                    <column name="id" start-index="136" stop-index="137" literal-start-index="137"
                                            literal-stop-index="138"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="3" start-index="140" stop-index="140"
                                                        literal-start-index="140" literal-stop-index="140"/>
                                    <parameter-marker-expression parameter-index="6" start-index="139" stop-index="139"
                                                                 literal-start-index="139" literal-stop-index="139"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <when-exprs>
                            <binary-operation-expression start-index="155" stop-index="158" literal-start-index="159"
                                                         literal-stop-index="162">
                                <left>
                                    <column name="id" start-index="155" stop-index="156" literal-start-index="159"
                                            literal-stop-index="160"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="4" start-index="162" stop-index="162"
                                                        literal-start-index="162" literal-stop-index="162"/>
                                    <parameter-marker-expression parameter-index="8" start-index="158" stop-index="158"
                                                                 literal-start-index="158" literal-stop-index="158"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <when-exprs>
                            <binary-operation-expression start-index="174" stop-index="177" literal-start-index="181"
                                                         literal-stop-index="185">
                                <left>
                                    <column name="id" start-index="174" stop-index="175" literal-start-index="181"
                                            literal-stop-index="182"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="10" start-index="184" stop-index="185"
                                                        literal-start-index="184" literal-stop-index="185"/>
                                    <parameter-marker-expression parameter-index="10" start-index="177" stop-index="177"
                                                                 literal-start-index="177" literal-stop-index="177"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="ll" start-index="148" stop-index="151" literal-start-index="148"
                                                literal-stop-index="151"/>
                            <parameter-marker-expression parameter-index="7" start-index="147" stop-index="147"
                                                         literal-start-index="147" literal-stop-index="147"/>
                        </then-exprs>
                        <then-exprs>
                            <literal-expression value="ll" start-index="170" stop-index="173" literal-start-index="170"
                                                literal-stop-index="173"/>
                            <parameter-marker-expression parameter-index="9" start-index="166" stop-index="166"
                                                         literal-start-index="166" literal-stop-index="166"/>
                        </then-exprs>
                        <then-exprs>
                            <literal-expression value="ll" start-index="193" stop-index="196" literal-start-index="193"
                                                literal-stop-index="196"/>
                            <parameter-marker-expression parameter-index="11" start-index="185" stop-index="185"
                                                         literal-start-index="185" literal-stop-index="185"/>
                        </then-exprs>
                    </case-when-expression>
                </assignment-value>
            </assignment>
            <assignment start-index="192" stop-index="230" literal-start-index="203" literal-stop-index="270">
                <column name="update_time" start-index="192" stop-index="202" literal-start-index="203"
                        literal-stop-index="213"/>
                <assignment-value>
                    <case-when-expression>
                        <when-exprs>
                            <binary-operation-expression start-index="215" stop-index="218" literal-start-index="226"
                                                         literal-stop-index="229">
                                <left>
                                    <column name="id" start-index="215" stop-index="216" literal-start-index="226"
                                            literal-stop-index="227"/>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <literal-expression value="3" start-index="229" stop-index="229"
                                                        literal-start-index="229" literal-stop-index="229"/>
                                    <parameter-marker-expression parameter-index="12" start-index="218" stop-index="218"
                                                                 literal-start-index="218" literal-stop-index="218"/>
                                </right>
                            </binary-operation-expression>
                        </when-exprs>
                        <then-exprs>
                            <literal-expression value="2020-08-10T17:15:25.979+0800" start-index="237" stop-index="266"
                                                literal-start-index="237" literal-stop-index="266"/>
                            <parameter-marker-expression parameter-index="13" start-index="226" stop-index="226"
                                                         literal-start-index="226" literal-stop-index="226"/>
                        </then-exprs>
                    </case-when-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="232" stop-index="251" literal-start-index="272" literal-stop-index="294">
            <expr>
                <binary-operation-expression start-index="239" stop-index="251" literal-start-index="279"
                                             literal-stop-index="294">
                    <left>
                        <column name="tenant_id" start-index="239" stop-index="247" literal-start-index="279"
                                literal-stop-index="287"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="jd" start-index="291" stop-index="294"/>
                        <parameter-marker-expression parameter-index="14" start-index="251" stop-index="251"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_order_by_row_count" parameters="'update', 1, 1, 10">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="28" literal-stop-index="35">
            <assignment start-index="19" stop-index="28" literal-stop-index="35">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="update" start-index="28" stop-index="35"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="30" stop-index="63" literal-start-index="37" literal-stop-index="70">
            <expr>
                <binary-operation-expression start-index="36" stop-index="63" literal-start-index="43"
                                             literal-stop-index="70">
                    <left>
                        <binary-operation-expression start-index="36" stop-index="47" literal-start-index="43"
                                                     literal-stop-index="54">
                            <left>
                                <column name="order_id" start-index="36" stop-index="43" literal-start-index="43"
                                        literal-stop-index="50"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="54" stop-index="54"/>
                                <parameter-marker-expression parameter-index="1" start-index="47" stop-index="47"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="53" stop-index="63" literal-start-index="60"
                                                     literal-stop-index="70">
                            <left>
                                <column name="user_id" start-index="53" stop-index="59" literal-start-index="60"
                                        literal-stop-index="66"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="1" start-index="70" stop-index="70"/>
                                <parameter-marker-expression parameter-index="2" start-index="63" stop-index="63"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="order_id" start-index="74" stop-index="81" literal-start-index="81"
                         literal-stop-index="88"/>
        </order-by>
        <limit start-index="83" stop-index="89" literal-start-index="90" literal-stop-index="97">
            <row-count value="10" parameter-index="3" start-index="89" stop-index="89" literal-start-index="96"
                       literal-stop-index="97"/>
        </limit>
    </update>

    <update sql-case-id="update_with_number" parameters="1, 1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="30" literal-stop-index="30">
            <assignment start-index="19" stop-index="30" literal-stop-index="30">
                <column name="order_id" start-index="19" stop-index="26"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="30" stop-index="30"/>
                    <literal-expression value="1" start-index="30" stop-index="30"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="32" stop-index="48">
            <expr>
                <binary-operation-expression start-index="38" stop-index="48">
                    <left>
                        <column name="user_id" start-index="38" stop-index="44"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="48" stop-index="48"/>
                        <parameter-marker-expression parameter-index="1" start-index="48" stop-index="48"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_with_clause" parameters="1, 1">
        <with start-index="0" stop-index="86">
            <common-table-expression name="cte" start-index="5" stop-index="86">
                <column name="order_id" start-index="10" stop-index="17"/>
                <column name="user_id" start-index="20" stop-index="26"/>
                <column name="status" start-index="29" stop-index="34"/>
                <subquery-expression>
                    <select>
                        <from>
                            <simple-table name="t_order" start-index="79" stop-index="85"/>
                        </from>
                        <projections start-index="48" stop-index="72">
                            <column-projection name="order_id" start-index="48" stop-index="55"/>
                            <column-projection name="user_id" start-index="58" stop-index="64"/>
                            <column-projection name="status" start-index="67" stop-index="72"/>
                        </projections>
                    </select>
                </subquery-expression>
            </common-table-expression>
        </with>
        <table start-index="95" stop-index="101">
            <simple-table name="t_order" start-index="95" stop-index="101"/>
        </table>
        <set start-index="103" stop-index="116">
            <assignment start-index="107" stop-index="116" literal-stop-index="116">
                <column name="status" start-index="107" stop-index="112"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="116" stop-index="116"/>
                    <literal-expression value="1" start-index="116" stop-index="116"/>
                </assignment-value>
            </assignment>
        </set>
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" start-index="123" stop-index="134" alias="t"/>
                </left>
                <right>
                    <simple-table name="cte" start-index="141" stop-index="148" alias="c"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="153" stop-index="175">
                        <left>
                            <column name="order_id" start-index="153" stop-index="162">
                                <owner name="t" start-index="153" stop-index="153"/>
                            </column>
                        </left>
                        <right>
                            <column name="order_id" start-index="166" stop-index="175">
                                <owner name="c" start-index="166" stop-index="166"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <where start-index="177" stop-index="196">
            <expr>
                <binary-operation-expression start-index="183" stop-index="196">
                    <left>
                        <column name="order_id" start-index="183" stop-index="192">
                            <owner name="c" start-index="183" stop-index="183"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="196" stop-index="196"/>
                        <parameter-marker-expression parameter-index="1" start-index="196" stop-index="196"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_from_clause" parameters="1, 1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="28">
            <assignment start-index="19" stop-index="28">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="1" start-index="28" stop-index="28"/>
                </assignment-value>
            </assignment>
        </set>
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" start-index="35" stop-index="46" alias="t"/>
                </left>
                <right>
                    <simple-table name="t_order_item" start-index="53" stop-index="69" alias="i"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="74" stop-index="96">
                        <left>
                            <column name="order_id" start-index="74" stop-index="83">
                                <owner name="t" start-index="74" stop-index="74"/>
                            </column>
                        </left>
                        <right>
                            <column name="order_id" start-index="87" stop-index="96">
                                <owner name="i" start-index="87" stop-index="87"/>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <where start-index="98" stop-index="117">
            <expr>
                <binary-operation-expression start-index="104" stop-index="117">
                    <left>
                        <column name="order_id" start-index="104" stop-index="113">
                            <owner name="i" start-index="104" stop-index="104"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="117" stop-index="117"/>
                        <parameter-marker-expression parameter-index="1" start-index="117" stop-index="117"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_top" parameters="1, 1">
        <table start-index="15" stop-index="21">
            <simple-table name="t_order" start-index="15" stop-index="21"/>
        </table>
        <set start-index="23" stop-index="38" literal-stop-index="38">
            <assignment start-index="27" stop-index="38" literal-stop-index="38">
                <column name="order_id" start-index="27" stop-index="34"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="38" stop-index="38"/>
                    <literal-expression value="1" start-index="38" stop-index="38"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="40" stop-index="56">
            <expr>
                <binary-operation-expression start-index="46" stop-index="56">
                    <left>
                        <column name="user_id" start-index="46" stop-index="52"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="56" stop-index="56"/>
                        <parameter-marker-expression parameter-index="1" start-index="56" stop-index="56"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_top_percent" parameters="1, 1">
        <table start-index="23" stop-index="30">
            <simple-table name="t_order" start-index="23" stop-index="29"/>
        </table>
        <set start-index="31" stop-index="46" literal-stop-index="46">
            <assignment start-index="35" stop-index="46" literal-stop-index="46">
                <column name="order_id" start-index="35" stop-index="42"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="46" stop-index="46"/>
                    <literal-expression value="1" start-index="46" stop-index="46"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="48" stop-index="64">
            <expr>
                <binary-operation-expression start-index="54" stop-index="64">
                    <left>
                        <column name="user_id" start-index="54" stop-index="60"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="64" stop-index="64"/>
                        <parameter-marker-expression parameter-index="1" start-index="64" stop-index="64"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_query_hint" parameters="'update', 1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="28" literal-stop-index="35">
            <assignment start-index="19" stop-index="28" literal-stop-index="35">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="28" stop-index="28"/>
                    <literal-expression value="update" start-index="28" stop-index="35"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="30" stop-index="47" literal-start-index="37" literal-stop-index="54">
            <expr>
                <binary-operation-expression start-index="36" stop-index="47" literal-start-index="43"
                                             literal-stop-index="54">
                    <left>
                        <column name="order_id" start-index="36" stop-index="43" literal-start-index="43"
                                literal-stop-index="50"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="54" stop-index="54"/>
                        <parameter-marker-expression parameter-index="1" start-index="47" stop-index="47"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_set_null">
        <table start-index="7" stop-index="13">
            <simple-table name="employees" start-index="7" stop-index="15"/>
        </table>
        <set start-index="17" stop-index="41">
            <assignment start-index="21" stop-index="41">
                <column name="commission_pct" start-index="21" stop-index="34"/>
                <assignment-value>
                    <literal-expression value="null" start-index="38" stop-index="41"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="43" stop-index="67">
            <expr>
                <binary-operation-expression start-index="49" stop-index="67">
                    <left>
                        <column name="job_id" start-index="49" stop-index="54"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="SH_CLERK" start-index="58" stop-index="67"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_set_subquery">
        <table start-index="7" stop-index="17">
            <simple-table name="employees" alias="a" start-index="7" stop-index="17"/>
        </table>
        <set start-index="19" stop-index="104">
            <assignment start-index="23" stop-index="104">
                <column name="department_id" start-index="23" stop-index="35"/>
                <assignment-value>
                    <subquery start-index="39" stop-index="104">
                        <select>
                            <from start-index="66" stop-index="76">
                                <simple-table name="departments" start-index="66" stop-index="76"/>
                            </from>
                            <projections start-index="47" stop-index="59">
                                <column-projection name="department_id" start-index="47" stop-index="59"/>
                            </projections>
                            <where start-index="78" stop-index="103">
                                <expr>
                                    <binary-operation-expression start-index="84" stop-index="103">
                                        <left>
                                            <column name="location_id" start-index="84" stop-index="94"/>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <literal-expression value="2100" start-index="98" stop-index="103"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
    </update>

    <update sql-case-id="update_with_subquery_using_interval">
        <table start-index="7" stop-index="17">
            <simple-table name="employees" alias="a" start-index="7" stop-index="17"/>
        </table>
        <set start-index="19" stop-index="140">
            <assignment start-index="23" stop-index="140">
                <column name="salary" start-index="23" stop-index="28"/>
                <assignment-value>
                    <subquery start-index="32" stop-index="140">
                        <select>
                            <from start-index="52" stop-index="60">
                                <simple-table name="employees" start-index="52" stop-index="60"/>
                            </from>
                            <projections start-index="40" stop-index="45">
                                <column-projection name="salary" start-index="40" stop-index="45"/>
                            </projections>
                            <where start-index="115" stop-index="139">
                                <expr>
                                    <binary-operation-expression start-index="121" stop-index="139">
                                        <left>
                                            <column name="last_name" start-index="121" stop-index="129"/>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <literal-expression value="Chung" start-index="133" stop-index="139"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="142" stop-index="166">
            <expr>
                <binary-operation-expression start-index="148" stop-index="166">
                    <left>
                        <column name="last_name" start-index="148" stop-index="156"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="Chung" start-index="160" stop-index="166"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_multiple_set">
        <table start-index="7" stop-index="15">
            <simple-table name="employees" start-index="7" stop-index="15"/>
        </table>
        <set start-index="17" stop-index="73">
            <assignment start-index="21" stop-index="37">
                <column name="job_id" start-index="21" stop-index="26"/>
                <assignment-value>
                    <literal-expression value="SA_MAN" start-index="30" stop-index="37"/>
                </assignment-value>
            </assignment>
            <assignment start-index="40" stop-index="61">
                <column name="salary" start-index="40" stop-index="45"/>
                <assignment-value>
                    <literal-expression value="1000" start-index="49" stop-index="52"/>
                </assignment-value>
            </assignment>
            <assignment start-index="64" stop-index="82">
                <column name="department_id" start-index="55" stop-index="67"/>
                <assignment-value>
                    <literal-expression value="120" start-index="71" stop-index="73"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="75" stop-index="107">
            <expr>
                <binary-operation-expression start-index="81" stop-index="107">
                    <left>
                        <column name="last_name" start-index="81" stop-index="89"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="Douglas Grant" start-index="93" stop-index="107"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_set_value">
        <table start-index="7" stop-index="20">
            <simple-table name="people_demo1" alias="p" start-index="7" stop-index="20"/>
        </table>
        <set start-index="22" stop-index="113">
            <assignment start-index="26" stop-index="113">
                <column name="p" start-index="32" stop-index="32"/>
                <assignment-value>
                    <subquery start-index="37" stop-index="113">
                        <select>
                            <from start-index="59" stop-index="72">
                                <simple-table name="people_demo2" alias="q" start-index="59" stop-index="72"/>
                            </from>
                            <projections start-index="45" stop-index="52" literal-start-index="45"
                                         literal-stop-index="52">
                                <expression-projection text="VALUE(q)" start-index="45" stop-index="52"
                                                       literal-start-index="45" literal-stop-index="52">
                                    <literalText>VALUE(q)</literalText>
                                    <expr>
                                        <function function-name="VALUE" text="VALUE(q)" start-index="45" stop-index="52"
                                                  literal-start-index="45" literal-stop-index="52">
                                            <parameter>
                                                <column name="q" start-index="51" stop-index="51"
                                                        literal-start-index="51" literal-stop-index="51"/>
                                            </parameter>
                                            <literalText>VALUE(q)</literalText>
                                        </function>
                                    </expr>
                                </expression-projection>
                            </projections>
                            <where start-index="74" stop-index="112">
                                <expr>
                                    <binary-operation-expression start-index="80" stop-index="112">
                                        <left>
                                            <column name="department_id" start-index="80" stop-index="94">
                                                <owner name="p" start-index="80" stop-index="80"/>
                                            </column>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <column name="department_id" start-index="98" stop-index="112">
                                                <owner name="q" start-index="98" stop-index="98"/>
                                            </column>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="115" stop-index="140">
            <expr>
                <binary-operation-expression start-index="121" stop-index="140">
                    <left>
                        <column name="department_id" start-index="121" stop-index="135">
                            <owner name="p" start-index="121" stop-index="121"/>
                        </column>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="10" start-index="139" stop-index="140"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_multi_columns">
        <table start-index="7" stop-index="17">
            <simple-table name="employees" alias="a" start-index="7" stop-index="17"/>
        </table>
        <set start-index="19" stop-index="239">
            <assignment start-index="23" stop-index="104">
                <column name="department_id" start-index="23" stop-index="35"/>
                <assignment-value>
                    <subquery start-index="39" stop-index="104">
                        <select>
                            <from start-index="66" stop-index="76">
                                <simple-table name="departments" start-index="66" stop-index="76"/>
                            </from>
                            <projections start-index="47" stop-index="59">
                                <column-projection name="department_id" start-index="47" stop-index="59"/>
                            </projections>
                            <where start-index="78" stop-index="103">
                                <expr>
                                    <binary-operation-expression start-index="84" stop-index="103">
                                        <left>
                                            <column name="location_id" start-index="84" stop-index="94"/>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <literal-expression value="2100" start-index="98" stop-index="103"/>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
            <assignment start-index="107" stop-index="239">
                <columns name="salary" start-index="108" stop-index="113"/>
                <columns name="commission_pct" start-index="116" stop-index="129"/>
                <assignment-value>
                    <subquery start-index="135" stop-index="238">
                        <select>
                            <from start-index="188" stop-index="198">
                                <simple-table name="employees" alias="b" start-index="188" stop-index="198"/>
                            </from>
                            <projections start-index="142" stop-index="181">
                                <expression-projection text="1.1*AVG(salary)" start-index="142" stop-index="156"/>
                                <expression-projection text="1.5*AVG(commission_pct)" start-index="159"
                                                       stop-index="181"/>
                            </projections>
                            <where start-index="200" stop-index="238">
                                <expr>
                                    <binary-operation-expression start-index="206" stop-index="238">
                                        <left>
                                            <column name="department_id" start-index="206" stop-index="220">
                                                <owner name="a" start-index="206" stop-index="206"/>
                                            </column>
                                        </left>
                                        <operator>=</operator>
                                        <right>
                                            <column name="department_id" start-index="224" stop-index="238">
                                                <owner name="b" start-index="224" stop-index="224"/>
                                            </column>
                                        </right>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
    </update>

    <update sql-case-id="update_with_force_index" parameters="'update', 1">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="37" stop-index="50" literal-stop-index="57">
            <assignment start-index="41" stop-index="50" literal-stop-index="57">
                <column name="status" start-index="41" stop-index="46"/>
                <assignment-value>
                    <parameter-marker-expression parameter-index="0" start-index="50" stop-index="50"/>
                    <literal-expression value="update" start-index="50" stop-index="57"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="52" stop-index="69" literal-start-index="59" literal-stop-index="76">
            <expr>
                <binary-operation-expression start-index="58" stop-index="69" literal-start-index="65"
                                             literal-stop-index="76">
                    <left>
                        <column name="order_id" start-index="58" stop-index="65" literal-start-index="65"
                                literal-stop-index="72"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="76" stop-index="76"/>
                        <parameter-marker-expression parameter-index="1" start-index="69" stop-index="69"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_translate_function">
        <table start-index="7" stop-index="19">
            <simple-table name="translate_tab" start-index="7" stop-index="19"/>
        </table>
        <set start-index="21" stop-index="70">
            <assignment start-index="21" stop-index="70">
                <column name="char_col" start-index="25" stop-index="32"/>
                <assignment-value>
                    <function function-name="TRANSLATE" text="TRANSLATE (nchar_col USING CHAR_CS)" start-index="36"
                              stop-index="70">
                        <parameter>
                            <column name="nchar_col" start-index="47" stop-index="55"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="CHAR_CS" start-index="63" stop-index="69"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
    </update>

    <update sql-case-id="update_with_dot_column_name">
        <table start-index="7" stop-index="15">
            <simple-table name="employees" start-index="7" stop-index="15"/>
        </table>
        <set start-index="17" stop-index="40">
            <assignment>
                <columns name="salary" start-index="21" stop-index="26" literal-start-index="21"
                         literal-stop-index="26"/>
                <assignment-value>
                    <binary-operation-expression start-index="29" stop-index="40" literal-start-index="29"
                                                 literal-stop-index="40">
                        <left>
                            <column name="salary" start-index="29" stop-index="35" literal-start-index="29"
                                    literal-stop-index="35"/>
                        </left>
                        <operator>+</operator>
                        <right>
                            <literal-expression value="10" start-index="39" stop-index="40" literal-start-index="39"
                                                literal-stop-index="40"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="43" stop-index="76">
            <expr>
                <between-expression start-index="49" stop-index="76">
                    <left>
                        <column start-index="49" stop-index="59" name="employee_id"/>
                    </left>
                    <between-expr>
                        <literal-expression start-index="69" stop-index="69" value="1"/>
                    </between-expr>
                    <and-expr>
                        <literal-expression start-index="75" stop-index="76" value="10"/>
                    </and-expr>
                </between-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_set_value_clause">
        <table start-index="7" stop-index="9">
            <simple-table name="ot1" start-index="7" stop-index="9"/>
        </table>
        <set start-index="11" stop-index="35">
            <assignment start-index="11" stop-index="35">
                <column name="x" start-index="21" stop-index="25">
                    <owner name="ot1" start-index="21" stop-index="23"/>
                </column>
                <assignment-value>
                    <function start-index="30" stop-index="35" function-name="t1" text="t1(20)">
                        <parameter>
                            <literal-expression value="20" start-index="33" stop-index="34"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="37" stop-index="63">
            <expr>
                <binary-operation-expression start-index="43" stop-index="63">
                    <left>
                        <function start-index="43" stop-index="54" function-name="VALUE" text="VALUE(ot1.x)">
                            <parameter>
                                <column start-index="49" stop-index="53" name="x">
                                    <owner start-index="49" stop-index="51" name="ot1"/>
                                </column>
                            </parameter>
                        </function>
                    </left>
                    <operator>=</operator>
                    <right>
                        <function start-index="58" stop-index="63" function-name="t1" text="t1(10)">
                            <parameter>
                                <literal-expression value="10" start-index="61" stop-index="62"/>
                            </parameter>
                        </function>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_open_row_set_function">
        <table start-index="7" stop-index="7">
            <simple-table name="T" start-index="7" stop-index="7"/>
        </table>
        <set start-index="9" stop-index="106">
            <assignment start-index="13" stop-index="106">
                <column name="XmlCol" start-index="13" stop-index="18"/>
                <assignment-value>
                    <subquery start-index="22" stop-index="106">
                        <select>
                            <projections start-index="31" stop-index="31">
                                <shorthand-projection start-index="31" stop-index="31"/>
                            </projections>
                            <from start-index="38" stop-index="100">
                                <function-table start-index="38" stop-index="100" table-alias="x">
                                    <table-function function-name="OPENROWSET"
                                                    text="OPENROWSET(BULK 'C:\SampleFolder\SampleData3.txt', SINGLE_BLOB)">
                                        <parameter>
                                            <literal-expression value="C:\SampleFolder\SampleData3.txt" start-index="54"
                                                                stop-index="86"/>
                                        </parameter>
                                        <parameter>
                                            <column name="SINGLE_BLOB" start-index="89" stop-index="99"/>
                                        </parameter>
                                    </table-function>
                                </function-table>
                            </from>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="108" stop-index="123">
            <expr>
                <binary-operation-expression start-index="114" stop-index="123">
                    <left>
                        <column name="IntCol" start-index="114" stop-index="119"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="1" start-index="123" stop-index="123"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_point_type">
        <table start-index="7" stop-index="16">
            <simple-table name="Cities" start-index="7" stop-index="16">
                <owner name="dbo" start-index="7" stop-index="9"/>
            </simple-table>
        </table>
        <set start-index="18" stop-index="47">
            <assignment start-index="22" stop-index="47">
                <column name="Location" start-index="22" stop-index="29"/>
                <assignment-value>
                    <function function-name="SetXY" text="SetXY(23.5, 23.5)" start-index="31" stop-index="47">
                        <parameter>
                            <literal-expression value="23.5" start-index="37" stop-index="40"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="23.5" start-index="43" stop-index="46"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="49" stop-index="72">
            <expr>
                <binary-operation-expression start-index="55" stop-index="72">
                    <left>
                        <column name="Name" start-index="55" stop-index="58"/>
                    </left>
                    <right>
                        <literal-expression value="Anchorage" start-index="62" stop-index="72"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_table_hint">
        <table start-index="7" stop-index="24">
            <simple-table name="Product" start-index="7" stop-index="24">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <table-hints start-index="26" stop-index="39">
            <table-hint value="TABLOCK" start-index="32" stop-index="38"/>
        </table-hints>
        <set start-index="41" stop-index="72">
            <assignment start-index="45" stop-index="72">
                <column name="ListPrice" start-index="45" stop-index="53"/>
                <assignment-value>
                    <binary-operation-expression start-index="57" stop-index="72">
                        <left>
                            <column name="ListPrice" start-index="57" stop-index="65"/>
                        </left>
                        <right>
                            <literal-expression value="1.10" start-index="69" stop-index="72"/>
                        </right>
                        <operator>*</operator>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="74" stop-index="104">
            <expr>
                <binary-operation-expression start-index="80" stop-index="104">
                    <left>
                        <column name="ProductNumber" start-index="80" stop-index="92"/>
                    </left>
                    <right>
                        <list-expression start-index="99" stop-index="104">
                            <items>
                                <literal-expression value="BK-%" start-index="99" stop-index="104"/>
                            </items>
                        </list-expression>
                    </right>
                    <operator>LIKE</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_option_hint">
        <table start-index="7" stop-index="24">
            <simple-table name="Product" start-index="7" stop-index="24">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="26" stop-index="57">
            <assignment start-index="30" stop-index="57">
                <column name="ListPrice" start-index="30" stop-index="38"/>
                <assignment-value>
                    <binary-operation-expression start-index="42" stop-index="57">
                        <left>
                            <column name="ListPrice" start-index="42" stop-index="50"/>
                        </left>
                        <right>
                            <literal-expression value="1.10" start-index="54" stop-index="57"/>
                        </right>
                        <operator>*</operator>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="59" stop-index="91">
            <expr>
                <binary-operation-expression start-index="65" stop-index="91">
                    <left>
                        <column name="ProductNumber" start-index="65" stop-index="77"/>
                    </left>
                    <right>
                        <list-expression start-index="84" stop-index="91">
                            <items>
                                <column name="@Product" start-index="84" stop-index="91"/>
                            </items>
                        </list-expression>
                    </right>
                    <operator>LIKE</operator>
                </binary-operation-expression>
            </expr>
        </where>
        <option-hint start-index="93" stop-index="133" text="OPTION (OPTIMIZE FOR (@Product = 'BK-%'))"/>
    </update>

    <update sql-case-id="update_sales_table_with_subquery">
        <table start-index="7" stop-index="22">
            <simple-table name="YearlyTotalSales" start-index="7" stop-index="22"/>
        </table>
        <set start-index="24" stop-index="151">
            <assignment start-index="28" stop-index="151">
                <column name="YearlySalesAmount" start-index="28" stop-index="44"/>
                <assignment-value>
                    <subquery start-index="46" stop-index="151">
                        <select>
                            <projections start-index="54" stop-index="69">
                                <aggregation-projection type="SUM" expression="SUM(SalesAmount)" start-index="54"
                                                        stop-index="69">
                                    <parameter>
                                        <column name="SalesAmount" start-index="58" stop-index="68"/>
                                    </parameter>
                                </aggregation-projection>
                            </projections>
                            <from start-index="76" stop-index="92">
                                <simple-table name="FactInternetSales" start-index="76" stop-index="92"/>
                            </from>
                            <where start-index="94" stop-index="150">
                                <expr>
                                    <binary-operation-expression start-index="100" stop-index="150">
                                        <left>
                                            <binary-operation-expression start-index="100" stop-index="122">
                                                <left>
                                                    <column name="OrderDateKey" start-index="100" stop-index="111"/>
                                                </left>
                                                <right>
                                                    <literal-expression value="20040000" start-index="115"
                                                                        stop-index="122"/>
                                                </right>
                                                <operator>>=</operator>
                                            </binary-operation-expression>
                                        </left>
                                        <right>
                                            <binary-operation-expression start-index="128" stop-index="150">
                                                <left>
                                                    <column name="OrderDateKey" start-index="128" stop-index="139"/>
                                                </left>
                                                <right>
                                                    <literal-expression value="20050000" start-index="143"
                                                                        stop-index="150"/>
                                                </right>
                                                <operator>&lt;</operator>
                                            </binary-operation-expression>
                                        </right>
                                        <operator>AND</operator>
                                    </binary-operation-expression>
                                </expr>
                            </where>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="153" stop-index="167">
            <expr>
                <binary-operation-expression start-index="159" stop-index="167">
                    <left>
                        <column name="Year" start-index="159" stop-index="162"/>
                    </left>
                    <right>
                        <literal-expression value="2004" start-index="164" stop-index="167"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_inner_join_and_database_table_column">
        <table start-index="7" stop-index="16">
            <simple-table name="Table2" start-index="7" stop-index="16">
                <owner name="dbo" start-index="7" stop-index="9"/>
            </simple-table>
        </table>
        <set start-index="18" stop-index="72">
            <assignment start-index="22" stop-index="72">
                <column name="ColB" start-index="22" stop-index="36">
                    <owner name="Table2" start-index="26" stop-index="31">
                        <owner name="dbo" start-index="22" stop-index="24"/>
                    </owner>
                </column>
                <assignment-value>
                    <binary-operation-expression start-index="40" stop-index="72">
                        <left>
                            <column name="ColB" start-index="40" stop-index="54">
                                <owner name="Table2" start-index="44" stop-index="49">
                                    <owner name="dbo" start-index="40" stop-index="42"/>
                                </owner>
                            </column>
                        </left>
                        <right>
                            <column name="ColB" start-index="58" stop-index="72">
                                <owner name="Table1" start-index="62" stop-index="67">
                                    <owner name="dbo" start-index="58" stop-index="60"/>
                                </owner>
                            </column>
                        </right>
                        <operator>+</operator>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <from start-index="80" stop-index="150">
            <join-table join-type="INNER">
                <left>
                    <simple-table name="Table2" start-index="80" stop-index="89">
                        <owner name="dbo" start-index="80" stop-index="82"/>
                    </simple-table>
                </left>
                <right>
                    <simple-table name="Table1" start-index="102" stop-index="111">
                        <owner name="dbo" start-index="102" stop-index="104"/>
                    </simple-table>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="117" stop-index="149">
                        <left>
                            <column name="ColA" start-index="117" stop-index="131">
                                <owner name="Table2" start-index="121" stop-index="126">
                                    <owner name="dbo" start-index="117" stop-index="119"/>
                                </owner>
                            </column>
                        </left>
                        <right>
                            <column name="ColA" start-index="135" stop-index="149">
                                <owner name="Table1" start-index="139" stop-index="144">
                                    <owner name="dbo" start-index="135" stop-index="137"/>
                                </owner>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
    </update>

    <update sql-case-id="update_with_open_query_function">
        <table start-index="7" stop-index="106">
            <function-table start-index="7" stop-index="106">
                <table-function function-name="OPENQUERY"
                                text="OPENQUERY (MyLinkedServer, 'SELECT GroupName FROM HumanResources.Department WHERE DepartmentID = 4')">
                    <parameter>
                        <column name="MyLinkedServer" start-index="18" stop-index="31"/>
                    </parameter>
                    <parameter>
                        <literal-expression
                                value="SELECT GroupName FROM HumanResources.Department WHERE DepartmentID = 4"
                                start-index="34" stop-index="105"/>
                    </parameter>
                </table-function>
            </function-table>
        </table>
        <set start-index="108" stop-index="144">
            <assignment start-index="112" stop-index="144">
                <column name="GroupName" start-index="112" stop-index="120"/>
                <assignment-value>
                    <literal-expression value="Sales and Marketing" start-index="124" stop-index="144"/>
                </assignment-value>
            </assignment>
        </set>
    </update>

    <update sql-case-id="update_with_open_datasource_function">
        <table start-index="7" stop-index="130">
            <function-table start-index="7" stop-index="130">
                <table-function function-name="OPENDATASOURCE"
                                text="OPENDATASOURCE('SQLNCLI', 'Data Source=&lt;server name&gt;;Integrated Security=SSPI').AdventureWorks2022.HumanResources.Department"/>
            </function-table>
        </table>
        <set start-index="132" stop-index="168">
            <assignment>
                <column name="GroupName" start-index="136" stop-index="144"/>
                <assignment-value>
                    <literal-expression value="Sales and Marketing" start-index="148" stop-index="168"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="170" stop-index="191">
            <expr>
                <binary-operation-expression start-index="176" stop-index="191">
                    <left>
                        <column name="DepartmentID" start-index="176" stop-index="187"/>
                    </left>
                    <right>
                        <literal-expression value="4" start-index="191" stop-index="191"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_write_function_and_output_clause">
        <table start-index="7" stop-index="25">
            <simple-table name="Document" start-index="7" stop-index="25">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="27" stop-index="72">
            <assignment start-index="31" stop-index="72">
                <column name="DocumentSummary" start-index="31" stop-index="45"/>
                <assignment-value>
                    <function function-name="WRITE" text="WRITE (N'features',28,10)" start-index="48" stop-index="72">
                        <parameter>
                            <literal-expression value="features" start-index="55" stop-index="65"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="28" start-index="67" stop-index="68"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="10" start-index="70" stop-index="71"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <output start-index="74" stop-index="146">
            <output-table name="@MyTableVar" start-index="136" stop-index="146"/>
            <output-columns start-index="81" stop-index="129">
                <column-projection name="DocumentSummary" start-index="81" stop-index="103"/>
                <column-projection name="DocumentSummary" start-index="106" stop-index="129"/>
            </output-columns>
        </output>
        <where start-index="148" stop-index="200">
            <expr>
                <binary-operation-expression start-index="154" stop-index="200">
                    <left>
                        <column name="Title" start-index="154" stop-index="158"/>
                    </left>
                    <right>
                        <literal-expression value="Front Reflector Bracket Installation" start-index="162"
                                            stop-index="200"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_open_rowset_function_and_subquery">
        <table start-index="7" stop-index="29">
            <simple-table name="ProductPhoto" start-index="7" stop-index="29">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="31" stop-index="118">
            <assignment start-index="35" stop-index="118">
                <column name="ThumbNailPhoto" start-index="35" stop-index="48"/>
                <assignment-value>
                    <subquery start-index="52" stop-index="118">
                        <select>
                            <projections start-index="61" stop-index="61">
                                <shorthand-projection start-index="61" stop-index="61"/>
                            </projections>
                            <from start-index="69" stop-index="111">
                                <function-table table-alias="x" start-index="69" stop-index="111">
                                    <table-function function-name="OPENROWSET"
                                                    text="OPENROWSET(BULK 'c:Tires.jpg', SINGLE_BLOB)">
                                        <parameter>
                                            <literal-expression value="c:Tires.jpg" start-index="85" stop-index="97"/>
                                        </parameter>
                                        <parameter>
                                            <column name="SINGLE_BLOB" start-index="100" stop-index="110"/>
                                        </parameter>
                                    </table-function>
                                </function-table>
                            </from>
                        </select>
                    </subquery>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="120" stop-index="143">
            <expr>
                <binary-operation-expression start-index="126" stop-index="143">
                    <left>
                        <column name="ProductPhotoID" start-index="126" stop-index="139"/>
                    </left>
                    <right>
                        <literal-expression value="1" start-index="143" stop-index="143"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_filestream">
        <table start-index="7" stop-index="25">
            <simple-table name="Records" start-index="7" stop-index="25">
                <owner name="dbo" start-index="15" stop-index="17">
                    <owner name="Archive" start-index="7" stop-index="13"/>
                </owner>
            </simple-table>
        </table>
        <set start-index="27" stop-index="72">
            <assignment start-index="31" stop-index="72">
                <column name="Chart" start-delimiter="[" end-delimiter="]" start-index="31" stop-index="37"/>
                <assignment-value>
                    <function function-name="CAST" text="CAST('Xray 1' as VARBINARY(max))" start-index="41"
                              stop-index="72">
                        <parameter>
                            <literal-expression value="Xray 1" start-index="46" stop-index="53"/>
                        </parameter>
                        <parameter>
                            <data-type value="VARBINARY" start-index="58" stop-index="71"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="74" stop-index="97">
            <expr>
                <binary-operation-expression start-index="80" stop-index="97">
                    <left>
                        <column name="SerialNumber" start-delimiter="[" end-delimiter="]" start-index="80"
                                stop-index="93"/>
                    </left>
                    <right>
                        <literal-expression value="2" start-index="97" stop-index="97"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_production_product_with_like">
        <table start-index="7" stop-index="24">
            <simple-table name="Product" start-index="7" stop-index="24">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="26" stop-index="52">
            <assignment start-index="30" stop-index="52">
                <column name="Color" start-index="30" stop-index="34"/>
                <assignment-value>
                    <literal-expression value="Metallic Red" start-index="38" stop-index="52"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="54" stop-index="100">
            <expr>
                <binary-operation-expression start-index="60" stop-index="100">
                    <left>
                        <binary-operation-expression start-index="60" stop-index="81">
                            <left>
                                <column name="Name" start-index="60" stop-index="63"/>
                            </left>
                            <right>
                                <list-expression start-index="70" stop-index="81">
                                    <items>
                                        <literal-expression value="Road-250%" start-index="70" stop-index="81"/>
                                    </items>
                                </list-expression>
                            </right>
                            <operator>LIKE</operator>
                        </binary-operation-expression>
                    </left>
                    <right>
                        <binary-operation-expression start-index="87" stop-index="100">
                            <left>
                                <column name="Color" start-index="87" stop-index="91"/>
                            </left>
                            <right>
                                <literal-expression value="Red" start-index="95" stop-index="100"/>
                            </right>
                            <operator>=</operator>
                        </binary-operation-expression>
                    </right>
                    <operator>AND</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_current_of">
        <table start-index="7" stop-index="39">
            <simple-table name="EmployeePayHistory" start-index="7" stop-index="39">
                <owner name="HumanResources" start-index="7" stop-index="20"/>
            </simple-table>
        </table>
        <set start-index="41" stop-index="60">
            <assignment start-index="45" stop-index="60">
                <column name="PayFrequency" start-index="45" stop-index="56"/>
                <assignment-value>
                    <literal-expression value="2" start-index="60" stop-index="60"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="62" stop-index="92">
            <expr>
                <common-expression text="CURRENT OF complex_cursor" start-index="68" stop-index="92"/>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_plus_eq_symbol">
        <table start-index="7" stop-index="24">
            <simple-table name="Product" start-index="7" stop-index="24">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="26" stop-index="51">
            <assignment start-index="30" stop-index="51">
                <column name="ListPrice" start-index="30" stop-index="38"/>
                <assignment-value>
                    <column name="@NewPrice" start-index="43" stop-index="51"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="53" stop-index="72">
            <expr>
                <binary-operation-expression start-index="59" stop-index="72">
                    <left>
                        <column name="Color" start-index="59" stop-index="63"/>
                    </left>
                    <right>
                        <literal-expression value="Red" start-index="67" stop-index="72"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_scrapreason_with_between_and">
        <table start-index="7" stop-index="28">
            <simple-table name="ScrapReason" start-index="7" stop-index="28">
                <owner name="Production" start-index="7" stop-index="16"/>
            </simple-table>
        </table>
        <set start-index="30" stop-index="62">
            <assignment start-index="34" stop-index="62">
                <column name="Name" start-index="34" stop-index="37"/>
                <assignment-value>
                    <literal-expression value=" - tool malfunction" start-index="42" stop-index="62"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="64" stop-index="100">
            <expr>
                <between-expression start-index="70" stop-index="100">
                    <left>
                        <column name="ScrapReasonID" start-index="70" stop-index="82"/>
                    </left>
                    <between-expr>
                        <literal-expression value="10" start-index="92" stop-index="93"/>
                    </between-expr>
                    <and-expr>
                        <literal-expression value="12" start-index="99" stop-index="100"/>
                    </and-expr>
                    <not>false</not>
                </between-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_sr_with_join_subquery">
        <table start-index="7" stop-index="8">
            <simple-table name="sr" start-index="7" stop-index="8"/>
        </table>
        <set start-index="10" stop-index="45">
            <assignment start-index="14" stop-index="45">
                <column name="Name" start-index="14" stop-index="20">
                    <owner name="sr" start-index="14" stop-index="15"/>
                </column>
                <assignment-value>
                    <literal-expression value=" - tool malfunction" start-index="25" stop-index="45"/>
                </assignment-value>
            </assignment>
        </set>
        <from start-index="52" stop-index="175">
            <join-table join-type="INNER">
                <left>
                    <simple-table name="ScrapReason" alias="sr" start-index="52" stop-index="79">
                        <owner name="Production" start-index="52" stop-index="61"/>
                    </simple-table>
                </left>
                <right>
                    <simple-table name="WorkOrder" alias="wo" start-index="86" stop-index="111">
                        <owner name="Production" start-index="86" stop-index="95"/>
                    </simple-table>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="116" stop-index="175">
                        <left>
                            <binary-operation-expression start-index="116" stop-index="150">
                                <left>
                                    <column name="ScrapReasonID" start-index="116" stop-index="131">
                                        <owner name="sr" start-index="116" stop-index="117"/>
                                    </column>
                                </left>
                                <right>
                                    <column name="ScrapReasonID" start-index="135" stop-index="150">
                                        <owner name="wo" start-index="135" stop-index="136"/>
                                    </column>
                                </right>
                                <operator>=</operator>
                            </binary-operation-expression>
                        </left>
                        <right>
                            <binary-operation-expression start-index="156" stop-index="175">
                                <left>
                                    <column name="ScrappedQty" start-index="156" stop-index="169">
                                        <owner name="wo" start-index="156" stop-index="157"/>
                                    </column>
                                </left>
                                <right>
                                    <literal-expression value="300" start-index="173" stop-index="175"/>
                                </right>
                                <operator>&gt;</operator>
                            </binary-operation-expression>
                        </right>
                        <operator>AND</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
    </update>

    <update sql-case-id="update_with_nchar">
        <table start-index="7" stop-index="29">
            <simple-table name="Employee" start-index="7" stop-index="29">
                <owner name="HumanResources" start-index="7" stop-index="20"/>
            </simple-table>
        </table>
        <set start-index="31" stop-index="57">
            <assignment start-index="35" stop-index="57">
                <column name="JobTitle" start-index="35" stop-index="42"/>
                <assignment-value>
                    <literal-expression value="Executive" start-index="46" stop-index="57"/>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="59" stop-index="92">
            <expr>
                <binary-operation-expression start-index="65" stop-index="92">
                    <left>
                        <column name="NationalIDNumber" start-index="65" stop-index="80"/>
                    </left>
                    <right>
                        <literal-expression value="123456789" start-index="84" stop-index="92"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_inner_join">
        <table start-index="7" stop-index="16">
            <simple-table name="Table2" start-index="7" stop-index="16">
                <owner name="dbo" start-index="7" stop-index="9"/>
            </simple-table>
        </table>
        <set start-index="18" stop-index="72">
            <assignment start-index="22" stop-index="72">
                <column name="ColB" start-index="22" stop-index="36">
                    <owner name="Table2" start-index="26" stop-index="31">
                        <owner name="dbo" start-index="22" stop-index="24"/>
                    </owner>
                </column>
                <assignment-value>
                    <binary-operation-expression start-index="40" stop-index="72">
                        <left>
                            <column name="ColB" start-index="40" stop-index="54">
                                <owner name="Table2" start-index="44" stop-index="49">
                                    <owner name="dbo" start-index="40" stop-index="42"/>
                                </owner>
                            </column>
                        </left>
                        <right>
                            <column name="ColB" start-index="58" stop-index="72">
                                <owner name="Table1" start-index="62" stop-index="67">
                                    <owner name="dbo" start-index="58" stop-index="60"/>
                                </owner>
                            </column>
                        </right>
                        <operator>+</operator>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <from start-index="79" stop-index="149">
            <join-table join-type="INNER">
                <left>
                    <simple-table name="Table2" start-index="79" stop-index="88">
                        <owner name="dbo" start-index="79" stop-index="81"/>
                    </simple-table>
                </left>
                <right>
                    <simple-table name="Table1" start-index="101" stop-index="110">
                        <owner name="dbo" start-index="101" stop-index="103"/>
                    </simple-table>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="116" stop-index="148">
                        <left>
                            <column name="ColA" start-index="116" stop-index="130">
                                <owner name="Table2" start-index="120" stop-index="125">
                                    <owner name="dbo" start-index="116" stop-index="118"/>
                                </owner>
                            </column>
                        </left>
                        <right>
                            <column name="ColA" start-index="134" stop-index="148">
                                <owner name="Table1" start-index="138" stop-index="143">
                                    <owner name="dbo" start-index="134" stop-index="136"/>
                                </owner>
                            </column>
                        </right>
                        <operator>=</operator>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
    </update>

    <update sql-case-id="update_with_current_of_abc">
        <table start-index="7" stop-index="16">
            <simple-table name="Table1" start-index="7" stop-index="16">
                <owner name="dbo" start-index="7" stop-index="9"/>
            </simple-table>
        </table>
        <set start-index="18" stop-index="33">
            <assignment start-index="22" stop-index="33">
                <column name="c2" start-index="22" stop-index="23"/>
                <assignment-value>
                    <binary-operation-expression start-index="27" stop-index="33">
                        <left>
                            <column name="c2" start-index="27" stop-index="28"/>
                        </left>
                        <right>
                            <column name="d2" start-index="32" stop-index="33"/>
                        </right>
                        <operator>+</operator>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <from start-index="40" stop-index="49">
            <simple-table name="Table2" start-index="40" stop-index="49">
                <owner name="dbo" start-index="40" stop-index="42"/>
            </simple-table>
        </from>
        <where start-index="51" stop-index="70">
            <expr>
                <common-expression text="CURRENT OF abc" start-index="57" stop-index="70"/>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_with_location_setXY">
        <table start-index="7" stop-index="12">
            <simple-table name="Cities" start-index="7" stop-index="12"/>
        </table>
        <set start-index="14" stop-index="43">
            <assignment start-index="14" stop-index="43">
                <column name="Location" start-index="18" stop-index="25"/>
                <assignment-value>
                    <function text="SetXY(23.5, 23.5)" function-name="SetXY" start-index="27" stop-index="43">
                        <parameter>
                            <literal-expression value="23.5" start-index="33" stop-index="36"/>
                        </parameter>
                        <parameter>
                            <literal-expression value="23.5" start-index="39" stop-index="42"/>
                        </parameter>
                    </function>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="45" stop-index="68">
            <expr>
                <binary-operation-expression start-index="51" stop-index="68">
                    <left>
                        <column name="Name" start-index="51" stop-index="54"/>
                    </left>
                    <right>
                        <literal-expression value="Anchorage" start-index="58" stop-index="68"/>
                    </right>
                    <operator>=</operator>
                </binary-operation-expression>
            </expr>
        </where>
    </update>

    <update sql-case-id="update_returning_expressions" parameters="1, 2, 3">
        <table start-index="7" stop-index="13">
            <simple-table name="t_order" start-index="7" stop-index="13"/>
        </table>
        <set start-index="15" stop-index="37" literal-stop-index="37">
            <assignment start-index="19" stop-index="37" literal-stop-index="37">
                <column name="status" start-index="19" stop-index="24"/>
                <assignment-value>
                    <binary-operation-expression start-index="28" stop-index="37" literal-start-index="28"
                                                 literal-stop-index="37">
                        <left>
                            <column name="status" start-index="28" stop-index="33" literal-start-index="28"
                                    literal-stop-index="33"/>
                        </left>
                        <operator>-</operator>
                        <right>
                            <literal-expression value="1" start-index="37" stop-index="37"/>
                            <parameter-marker-expression parameter-index="0" start-index="37" stop-index="37"
                                                         literal-start-index="37" literal-stop-index="37"/>
                        </right>
                    </binary-operation-expression>
                </assignment-value>
            </assignment>
        </set>
        <where start-index="39" stop-index="72">
            <expr>
                <binary-operation-expression start-index="45" stop-index="72">
                    <left>
                        <binary-operation-expression start-index="45" stop-index="56">
                            <left>
                                <column name="order_id" start-index="45" stop-index="52"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="2" start-index="56" stop-index="56"/>
                                <parameter-marker-expression parameter-index="1" start-index="56" stop-index="56"/>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="62" stop-index="72">
                            <left>
                                <column name="user_id" start-index="62" stop-index="68"/>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="3" start-index="72" stop-index="72"/>
                                <parameter-marker-expression parameter-index="2" start-index="72" stop-index="72"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <returning start-index="74" stop-index="89">
            <projections start-index="84" stop-index="89">
                <column-projection name="status" start-index="84" stop-index="89"/>
            </projections>
        </returning>
    </update>
</sql-parser-test-cases>
