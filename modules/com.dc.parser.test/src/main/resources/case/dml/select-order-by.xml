<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <select sql-case-id="select_order_by_asc_and_index_desc">
        <from>
            <simple-table name="t_order" alias="o" start-index="14" stop-index="22"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="33" stop-index="42">
                <owner name="o" start-index="33" stop-index="33"/>
            </column-item>
            <index-item index="2" order-direction="DESC" start-index="45" stop-index="45"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_desc_and_index_asc">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="27" stop-index="40"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="42" stop-index="92">
            <expr>
                <binary-operation-expression start-index="48" stop-index="92">
                    <left>
                        <binary-operation-expression start-index="48" stop-index="70">
                            <left>
                                <column name="order_id" start-index="48" stop-index="57">
                                    <owner name="o" start-index="48" stop-index="48"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="61" stop-index="70">
                                    <owner name="i" start-index="61" stop-index="61"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="76" stop-index="92">
                            <left>
                                <column name="status" start-index="76" stop-index="83">
                                    <owner name="o" start-index="76" stop-index="76"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="init" start-index="87" stop-index="92"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="order_id" order-direction="DESC" start-index="103" stop-index="112">
                <owner name="o" start-index="103" stop-index="103"/>
            </column-item>
            <index-item index="1" start-index="120" stop-index="120"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_ordered_column">
        <from>
            <simple-table name="t_order" alias="o" start-index="40" stop-index="48"/>
        </from>
        <projections start-index="7" stop-index="33">
            <column-projection name="order_id" alias="gen_order_id_" start-index="7" stop-index="33">
                <owner name="o" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="59" stop-index="68">
                <owner name="o" start-index="59" stop-index="59"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_date">
        <from>
            <join-table join-type="COMMA">
                <left>
                    <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="27" stop-index="40"/>
                </right>
            </join-table>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <where start-index="42" stop-index="92">
            <expr>
                <binary-operation-expression start-index="48" stop-index="92">
                    <left>
                        <binary-operation-expression start-index="48" stop-index="70">
                            <left>
                                <column name="order_id" start-index="48" stop-index="57">
                                    <owner name="o" start-index="48" stop-index="48"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <column name="order_id" start-index="61" stop-index="70">
                                    <owner name="i" start-index="61" stop-index="61"/>
                                </column>
                            </right>
                        </binary-operation-expression>
                    </left>
                    <operator>AND</operator>
                    <right>
                        <binary-operation-expression start-index="76" stop-index="92">
                            <left>
                                <column name="status" start-index="76" stop-index="83">
                                    <owner name="o" start-index="76" stop-index="76"/>
                                </column>
                            </left>
                            <operator>=</operator>
                            <right>
                                <literal-expression value="init" start-index="87" stop-index="92"/>
                            </right>
                        </binary-operation-expression>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="creation_date" order-direction="DESC" start-index="103" stop-index="117">
                <owner name="i" start-index="103" stop-index="103"/>
            </column-item>
            <column-item name="order_id" order-direction="DESC" start-index="125" stop-index="134">
                <owner name="o" start-index="125" stop-index="125"/>
            </column-item>
            <column-item name="item_id" start-index="142" stop-index="150">
                <owner name="i" start-index="142" stop-index="142"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_for_nulls_first">
        <from>
            <simple-table name="t_order" alias="o" start-index="40" stop-index="48"/>
        </from>
        <projections start-index="7" stop-index="33">
            <column-projection name="order_id" alias="gen_order_id_" start-index="7" stop-index="33">
                <owner name="o" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="59" stop-index="68" order-direction="ASC">
                <owner name="o" start-index="59" stop-index="59"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_for_nulls_last">
        <from>
            <simple-table name="t_order" alias="o" start-index="40" stop-index="48"/>
        </from>
        <projections start-index="7" stop-index="33">
            <column-projection name="order_id" alias="gen_order_id_" start-index="7" stop-index="33">
                <owner name="o" start-index="7" stop-index="7"/>
            </column-projection>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="59" stop-index="68" order-direction="ASC">
                <owner name="o" start-index="59" stop-index="59"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_multiple_stars">
        <from>
            <simple-table name="t_order" alias="o" start-index="29" stop-index="37"/>
        </from>
        <projections start-index="7" stop-index="22">
            <shorthand-projection start-index="7" stop-index="7"/>
            <shorthand-projection start-index="20" stop-index="22">
                <owner name="o" start-index="20" stop-index="20"/>
            </shorthand-projection>
            <column-projection name="order_id" start-index="10" stop-index="17"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="48" stop-index="57">
                <owner name="o" start-index="48" stop-index="48"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_desc">
        <from>
            <simple-table name="employees" start-index="14" stop-index="22"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <where start-index="24" stop-index="48">
            <expr>
                <binary-operation-expression start-index="30" stop-index="48">
                    <left>
                        <column name="job_id" start-index="30" stop-index="35"/>
                    </left>
                    <operator>=</operator>
                    <right>
                        <literal-expression value="PU_CLERK" start-index="39" stop-index="48"/>
                    </right>
                </binary-operation-expression>
            </expr>
        </where>
        <order-by>
            <column-item name="salary" order-direction="DESC" start-index="59" stop-index="64"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_asc_desc">
        <from>
            <simple-table name="employees" start-index="30" stop-index="38"/>
        </from>
        <projections start-index="7" stop-index="23">
            <column-projection name="salary" start-index="7" stop-index="12"/>
            <column-projection name="last_name" start-index="15" stop-index="23"/>
        </projections>
        <order-by>
            <column-item name="salary" order-direction="ASC" start-index="49" stop-index="54"/>
            <column-item name="last_name" order-direction="DESC" start-index="61" stop-index="69"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_alias_star_alias_name">
        <from>
            <simple-table name="t_order" alias="o" start-index="16" stop-index="24"/>
        </from>
        <projections start-index="7" stop-index="9">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="o" start-index="7" stop-index="7"/>
            </shorthand-projection>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="35" stop-index="44">
                <owner name="o" start-index="35" stop-index="35"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_star_table_alias">
        <from>
            <simple-table name="t_order" alias="o" start-index="14" stop-index="22"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="33" stop-index="40"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_table_star_table_name">
        <from>
            <simple-table name="t_order" start-index="22" stop-index="28"/>
        </from>
        <projections start-index="7" stop-index="15">
            <shorthand-projection start-index="7" stop-index="15">
                <owner name="t_order" start-index="7" stop-index="13"/>
            </shorthand-projection>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="39" stop-index="54">
                <owner name="t_order" start-index="39" stop-index="45"/>
            </column-item>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_star_no_table_alias">
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <order-by>
            <column-item name="order_id" start-index="31" stop-index="38"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_with_table_star_without_table_name">
        <from>
            <join-table join-type="INNER">
                <left>
                    <simple-table name="t_order" alias="o" start-index="21" stop-index="29"/>
                </left>
                <right>
                    <simple-table name="t_order_item" alias="i" start-index="36" stop-index="49"/>
                </right>
                <on-condition>
                    <binary-operation-expression start-index="54" stop-index="102">
                        <left>
                            <binary-operation-expression start-index="54" stop-index="74">
                                <left>
                                    <column name="user_id" start-index="54" stop-index="62">
                                        <owner name="o" start-index="54" stop-index="54"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="user_id" start-index="66" stop-index="74">
                                        <owner name="i" start-index="66" stop-index="66"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </left>
                        <operator>AND</operator>
                        <right>
                            <binary-operation-expression start-index="80" stop-index="102">
                                <left>
                                    <column name="order_id" start-index="80" stop-index="89">
                                        <owner name="o" start-index="80" stop-index="80"/>
                                    </column>
                                </left>
                                <operator>=</operator>
                                <right>
                                    <column name="order_id" start-index="93" stop-index="102">
                                        <owner name="i" start-index="93" stop-index="93"/>
                                    </column>
                                </right>
                            </binary-operation-expression>
                        </right>
                    </binary-operation-expression>
                </on-condition>
            </join-table>
        </from>
        <projections start-index="7" stop-index="14">
            <shorthand-projection start-index="7" stop-index="9">
                <owner name="i" start-index="7" stop-index="7"/>
            </shorthand-projection>
            <shorthand-projection start-index="12" stop-index="14">
                <owner name="o" start-index="12" stop-index="12"/>
            </shorthand-projection>
        </projections>
        <order-by>
            <column-item name="item_id" start-index="113" stop-index="119"/>
        </order-by>
    </select>

    <select sql-case-id="select_order_by_expression_binary_operation">
        <projections start-index="7" stop-index="7">
            <shorthand-projection start-index="7" stop-index="7"/>
        </projections>
        <from>
            <simple-table name="t_order" start-index="14" stop-index="20"/>
        </from>
        <order-by>
            <expression-item expression="1+1" start-index="31" stop-index="33">
                <expr>
                    <binary-operation-expression start-index="31" stop-index="33">
                        <left>
                            <literal-expression value="1" start-index="31" stop-index="31"/>
                        </left>
                        <right>
                            <literal-expression value="1" start-index="33" stop-index="33"/>
                        </right>
                        <operator>+</operator>
                    </binary-operation-expression>
                </expr>
            </expression-item>
        </order-by>
    </select>
</sql-parser-test-cases>
