<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <check-table sql-case-id="check_table">
        <table name="test_table" start-index="12" stop-index="21"/>
    </check-table>
    <check-table sql-case-id="check_tables">
        <table name="t_order" start-index="12" stop-index="18"/>
        <table name="t_order_item" start-index="20" stop-index="31"/>
    </check-table>
    <checksum-table sql-case-id="checksum_table">
        <table name="t_order" start-index="15" stop-index="21"/>
    </checksum-table>
    <checksum-table sql-case-id="checksum_table_quick">
        <table name="t_order" start-index="15" stop-index="21"/>
    </checksum-table>
    <checksum-table sql-case-id="checksum_table_extended">
        <table name="t_order" start-index="15" stop-index="21"/>
    </checksum-table>
    <checksum-table sql-case-id="checksum_tables">
        <table name="t_order" start-index="16" stop-index="22"/>
        <table name="t_order_item" start-index="24" stop-index="35"/>
    </checksum-table>
    <checksum-table sql-case-id="checksum_tables_quick">
        <table name="t_order" start-index="16" stop-index="22"/>
        <table name="t_order_item" start-index="24" stop-index="35"/>
    </checksum-table>
    <checksum-table sql-case-id="checksum_tables_extended">
        <table name="t_order" start-index="16" stop-index="22"/>
        <table name="t_order_item" start-index="24" stop-index="35"/>
    </checksum-table>
    <check-table sql-case-id="check_multi_tables">
        <table name="t_order" start-index="13" stop-index="19"/>
        <table name="t_order_item" start-index="21" stop-index="32"/>
    </check-table>
</sql-parser-test-cases>
