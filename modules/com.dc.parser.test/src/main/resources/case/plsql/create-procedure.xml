<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-procedure sql-case-id="pl_sql_create_procedure">
        <procedure-name name="test_procedure"/>
        <sql-statements>
            <sql-statement start-index="162" stop-index="187" statement-class-simple-name="OracleSavepointStatement"/>
            <sql-statement start-index="205" stop-index="269" statement-class-simple-name="OracleSelectStatement"/>
            <sql-statement start-index="1200" stop-index="1205" statement-class-simple-name="OracleCommitStatement"/>
            <sql-statement start-index="1246" stop-index="1288" statement-class-simple-name="OracleRollbackStatement"/>
            <sql-statement start-index="1403" stop-index="1445" statement-class-simple-name="OracleRollbackStatement"/>
        </sql-statements>
        <procedure-calls>
            <procedure-call name="DBMS_OUTPUT.PUT_LINE"/>
            <procedure-call name="DBMS_OUTPUT.PUT_LINE"/>
        </procedure-calls>
        <procedure-body-end-names>
            <procedure-body-end-name name="cp_SHOPBudget_Gy"/>
        </procedure-body-end-names>
    </create-procedure>
</sql-parser-test-cases>
