<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <grant sql-case-id="grant_user_without_hostname">
        <table name="t_order" start-index="40" stop-index="46"/>
    </grant>

    <grant sql-case-id="grant_user_with_grant">
        <table name="t_order" start-index="40" stop-index="46"/>
    </grant>

    <grant sql-case-id="grant_role"/>
    <grant sql-case-id="grant_user_with_admin"/>
    <grant sql-case-id="grant_user_on_db"/>

    <grant sql-case-id="grant_user_on_table">
        <table name="t_order" start-index="13" stop-index="19"/>
    </grant>

    <grant sql-case-id="grant_user_on_db_and_table">
        <!--  TODO should be 25, because table name should start from real table name, not start from schema name. Need change ID definition of g4 -->
        <table name="t_order" start-index="13" stop-index="31">
            <owner name="sharding_db" start-index="13" stop-index="23"/>
        </table>
    </grant>

    <grant sql-case-id="grant_user_with_hostname">
        <table name="t_order" start-index="24" stop-index="30"/>
    </grant>

    <grant sql-case-id="grant_select"/>

    <grant sql-case-id="grant_select_column">
        <table name="t_order" start-index="27" stop-index="38">
            <owner name="ds_0" start-index="27" stop-index="30"/>
        </table>
        <column name="order_id" start-index="14" stop-index="21"/>
    </grant>

    <grant sql-case-id="grant_select_to_local_user"/>
    <grant sql-case-id="grant_create_to_local_user"/>
    <grant sql-case-id="grant_proxy_to_local_user"/>
    <grant sql-case-id="grant_crud"/>
    <grant sql-case-id="grant_all"/>

    <grant sql-case-id="grant_all_on_table">
        <table name="t_order" start-index="24" stop-index="35">
            <owner name="ds_0" start-index="24" stop-index="27"/>
        </table>
    </grant>

    <grant sql-case-id="grant_role_to_user"/>
    <grant sql-case-id="grant_roles_to_users"/>
    <grant sql-case-id="grant_system_privilege"/>
    <grant sql-case-id="grant_system_privileges"/>
    <grant sql-case-id="grant_all_system_privileges"/>
    <grant sql-case-id="grant_system_privilege_to_users"/>

    <grant sql-case-id="grant_object_privilege">
        <table name="t_order" start-index="16" stop-index="27">
            <owner name="ds_0" start-index="16" stop-index="19"/>
        </table>
    </grant>

    <grant sql-case-id="grant_object_privileges">
        <table name="t_order" start-index="40" stop-index="51">
            <owner name="ds_0" start-index="40" stop-index="43"/>
        </table>
    </grant>

    <grant sql-case-id="grant_all_object_privileges">
        <table name="t_order" start-index="24" stop-index="35">
            <owner name="ds_0" start-index="24" stop-index="27"/>
        </table>
    </grant>

    <grant sql-case-id="grant_object_privilege_to_users">
        <table name="t_order" start-index="16" stop-index="27">
            <owner name="ds_0" start-index="16" stop-index="19"/>
        </table>
    </grant>

    <grant sql-case-id="grant_object_privilege_column">
        <table name="t_order" start-index="27" stop-index="38">
            <owner name="ds_0" start-index="27" stop-index="30"/>
        </table>
    </grant>

    <grant sql-case-id="grant_program"/>
    <grant sql-case-id="grant_roles_to_programs"/>
    <grant sql-case-id="grant_create_rule_to_user"/>

    <grant sql-case-id="grant_all_on_table_to_roles">
        <table name="t_order" start-index="30" stop-index="36"/>
    </grant>

    <grant sql-case-id="grant_all_on_table_to_current_user">
        <table name="t_order" start-index="30" stop-index="36"/>
    </grant>

    <grant sql-case-id="grant_select_on_tables">
        <table name="t_order" start-index="22" stop-index="28"/>
        <table name="t_order_item" start-index="31" stop-index="42"/>
    </grant>

    <grant sql-case-id="grant_select_on_all_tables"/>

    <grant sql-case-id="grant_all_column_on_table">
        <table name="t_order" start-index="41" stop-index="47"/>
    </grant>

    <grant sql-case-id="grant_all_column_on_table_to_roles">
        <table name="t_order" start-index="41" stop-index="47"/>
    </grant>

    <grant sql-case-id="grant_all_column_on_table_to_current_user">
        <table name="t_order" start-index="41" stop-index="47"/>
    </grant>

    <grant sql-case-id="grant_select_column_on_table">
        <table name="t_order" start-index="33" stop-index="39"/>
    </grant>

    <grant sql-case-id="grant_select_column_on_tables">
        <table name="t_order" start-index="33" stop-index="39"/>
        <table name="t_order_item" start-index="42" stop-index="53"/>
    </grant>

    <grant sql-case-id="grant_all_on_sequence"/>
    <grant sql-case-id="grant_all_on_sequence_to_roles"/>
    <grant sql-case-id="grant_all_on_sequence_to_current_user"/>
    <grant sql-case-id="grant_select_on_sequence"/>
    <grant sql-case-id="grant_select_on_sequences"/>
    <grant sql-case-id="grant_select_on_all_sequences"/>
    <grant sql-case-id="grant_all_on_database"/>
    <grant sql-case-id="grant_all_on_database_to_roles"/>
    <grant sql-case-id="grant_all_on_database_to_current_user"/>
    <grant sql-case-id="grant_create_on_database"/>
    <grant sql-case-id="grant_create_on_databases"/>
    <grant sql-case-id="grant_all_on_domain"/>
    <grant sql-case-id="grant_all_on_domain_to_roles"/>
    <grant sql-case-id="grant_all_on_domain_to_current_user"/>
    <grant sql-case-id="grant_usage_on_domain"/>
    <grant sql-case-id="grant_usage_on_domains"/>
    <grant sql-case-id="grant_all_on_foreign_data_wrapper"/>
    <grant sql-case-id="grant_all_on_foreign_data_wrapper_to_roles"/>
    <grant sql-case-id="grant_all_on_foreign_data_wrapper_to_current_user"/>
    <grant sql-case-id="grant_usage_on_foreign_data_wrapper"/>
    <grant sql-case-id="grant_usage_on_foreign_data_wrappers"/>
    <grant sql-case-id="grant_all_on_foreign_server"/>
    <grant sql-case-id="grant_all_on_foreign_server_to_roles"/>
    <grant sql-case-id="grant_all_on_foreign_server_to_current_user"/>
    <grant sql-case-id="grant_usage_on_foreign_server"/>
    <grant sql-case-id="grant_usage_on_foreign_servers"/>
    <grant sql-case-id="grant_all_on_function"/>
    <grant sql-case-id="grant_all_on_function_to_roles"/>
    <grant sql-case-id="grant_all_on_function_to_current_user"/>
    <grant sql-case-id="grant_execute_on_function"/>
    <grant sql-case-id="grant_execute_on_functions"/>
    <grant sql-case-id="grant_execute_on_all_functions"/>
    <grant sql-case-id="grant_all_on_language"/>
    <grant sql-case-id="grant_all_on_language_to_roles"/>
    <grant sql-case-id="grant_all_on_language_to_current_user"/>
    <grant sql-case-id="grant_usage_on_language"/>
    <grant sql-case-id="grant_usage_on_languages"/>
    <grant sql-case-id="grant_all_on_large_object"/>
    <grant sql-case-id="grant_all_on_large_object_to_roles"/>
    <grant sql-case-id="grant_all_on_large_object_to_current_user"/>
    <grant sql-case-id="grant_select_large_object"/>
    <grant sql-case-id="grant_select_on_large_objects"/>
    <grant sql-case-id="grant_all_on_schema"/>
    <grant sql-case-id="grant_all_on_schema_to_roles"/>
    <grant sql-case-id="grant_all_on_schema_to_current_user"/>
    <grant sql-case-id="grant_create_on_schema"/>
    <grant sql-case-id="grant_create_on_schemas"/>
    <grant sql-case-id="grant_all_on_tablespace"/>
    <grant sql-case-id="grant_all_on_tablespace_to_roles"/>
    <grant sql-case-id="grant_all_on_tablespace_to_current_user"/>
    <grant sql-case-id="grant_create_on_tablespace"/>
    <grant sql-case-id="grant_create_on_tablespaces"/>
    <grant sql-case-id="grant_all_on_type"/>
    <grant sql-case-id="grant_all_on_type_to_roles"/>
    <grant sql-case-id="grant_all_on_type_to_current_user"/>
    <grant sql-case-id="grant_usage_on_type"/>
    <grant sql-case-id="grant_usage_on_types"/>
    <grant sql-case-id="grant_roles"/>

    <grant sql-case-id="grant_select_to_users">
        <table name="t_order" start-index="27" stop-index="33"/>
        <column name="order_id" start-index="14" stop-index="21"/>
    </grant>

    <grant sql-case-id="grant_select_on_table_for_postgresql">
        <table name="t_order" start-index="22" stop-index="28"/>
    </grant>

    <grant sql-case-id="grant_select_on_table_for_sqlserver">
        <table name="t_order" start-index="16" stop-index="22"/>
    </grant>

    <grant sql-case-id="grant_all_on_table_with_owner_for_mysql">
        <!-- FIXME should assert schema -->
    </grant>

    <grant sql-case-id="grant_all_on_table_with_owner_for_postgresql">
        <!-- FIXME ds_0 is not table, should be schema -->
        <!--<table name="ds_0" start-index="24" stop-index="27" />-->
    </grant>

    <grant sql-case-id="grant_view_definition_on_availability_group"/>
    <grant sql-case-id="grant_take_ownership_on_availability_group"/>
    <grant sql-case-id="grant_control_on_availability_group"/>
    <grant sql-case-id="grant_create_table_to_user"/>
    <grant sql-case-id="grant_showplan_to_application_role"/>
    <grant sql-case-id="grant_create_view_to_user"/>
    <grant sql-case-id="grant_control_to_database_user"/>
    <grant sql-case-id="grant_control_to_user"/>
    <grant sql-case-id="grant_view_definition_on_role_to_user"/>
    <grant sql-case-id="grant_impersonate_to_application_role"/>
    <grant sql-case-id="grant_view_definition_endpoint_to_sqlserver_login"/>
    <grant sql-case-id="grant_take_ownership_endpoint_to_sqlserver_login"/>
    <grant sql-case-id="grant_control_on_fulltext_catalog"/>
    <grant sql-case-id="grant_view_definition_on_fulltext_stoplist"/>

    <grant sql-case-id="grant_select_on_object_table">
        <table name="order_id" start-index="24" stop-index="33">
            <owner name="t" start-index="24" stop-index="24"/>
        </table>
    </grant>

    <grant sql-case-id="grant_execute_on_object_stored_procedure">
        <table name="sp1" start-index="25" stop-index="32">
            <owner name="ds_0" start-index="25" stop-index="28"/>
        </table>
    </grant>

    <grant sql-case-id="grant_references_on_column">
        <table name="order_id" start-index="39" stop-index="48">
            <owner name="t" start-index="39" stop-index="39"/>
        </table>
        <column name="order_id" start-index="18" stop-index="25"/>
    </grant>

    <grant sql-case-id="grant_execute_on_stored_procedure">
        <table name="sp1" start-index="17" stop-index="24">
            <owner name="ds_0" start-index="17" stop-index="20"/>
        </table>
    </grant>

    <grant sql-case-id="grant_insert_on_schema"/>
    <grant sql-case-id="grant_insert_on_schema_with_grant_option"/>
    <grant sql-case-id="grant_view_definition_on_search_property_list"/>
    <grant sql-case-id="grant_control_server_to_sqlserver_login"/>
    <grant sql-case-id="grant_alter_any_event_notification_to_sqlserver_login"/>
    <grant sql-case-id="grant_alter_any_database_to_role"/>
    <grant sql-case-id="grant_impersonate_on_login"/>
    <grant sql-case-id="grant_alter_on_symmetric_key"/>
    <grant sql-case-id="grant_view_server_state_to_sqlserver_login"/>
    <grant sql-case-id="grant_view_definition_on_type"/>
    <grant sql-case-id="grant_execute_on_xml_schema_collection"/>
    <grant sql-case-id="grant_usage_on_client_master_key"/>
    <grant sql-case-id="grant_usage_on_column_encryption_key"/>
</sql-parser-test-cases>
