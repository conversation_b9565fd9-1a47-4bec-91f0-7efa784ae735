<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-role sql-case-id="alter_no_identified_role"/>
    <alter-role sql-case-id="alter_external_role"/>
    <alter-role sql-case-id="alter_role_globally"/>
    <alter-role sql-case-id="alter_role_with_password"/>
    <alter-role sql-case-id="alter_role_with_container"/>
    <alter-role sql-case-id="alter_role_with_password_postgresql"/>
    <alter-role sql-case-id="alter_role_with_option"/>
    <alter-role sql-case-id="alter_role_with_options"/>
    <alter-role sql-case-id="alter_role_with_rename"/>
    <alter-role sql-case-id="alter_role_set_config"/>
    <alter-role sql-case-id="alter_all_roles_set_config"/>
    <alter-role sql-case-id="alter_role_set_config_in_database"/>
    <alter-role sql-case-id="alter_role_set_config_from_current_user"/>
    <alter-role sql-case-id="alter_role_reset_config"/>
    <alter-role sql-case-id="alter_all_roles_reset_config"/>
    <alter-role sql-case-id="alter_role_reset_config_in_database"/>
    <alter-role sql-case-id="alter_role_reset_all_config"/>
    <alter-role sql-case-id="alter_role_add_member"/>
    <alter-role sql-case-id="alter_role_drop_member"/>
    <alter-role sql-case-id="alter_role_rename"/>
    <alter-role sql-case-id="alter_role_createRole_and_createDB"/>
    <alter-role sql-case-id="alter_role_in_database_set"/>
</sql-parser-test-cases>
