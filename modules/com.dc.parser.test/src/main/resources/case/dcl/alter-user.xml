<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <alter-user sql-case-id="alter_user_identified_without_hostname"/>
    <alter-user sql-case-id="alter_user_identified_with_hostname"/>
    <alter-user sql-case-id="alter_user_account"/>
    <alter-user sql-case-id="alter_username_with_name"/>
    <alter-user sql-case-id="alter_username_rename"/>
    <alter-user sql-case-id="alter_user_passwd_with_equality"/>
    <alter-user sql-case-id="alter_user_passwd_without_equality"/>
    <alter-user sql-case-id="alter_user"/>
    <alter-user sql-case-id="alter_users"/>
    <alter-user sql-case-id="alter_user_if_exists"/>
    <alter-user sql-case-id="alter_local_user"/>
    <alter-user sql-case-id="alter_user_with_password"/>
    <alter-user sql-case-id="alter_user_with_resource_option"/>
    <alter-user sql-case-id="alter_user_with_resource_options"/>
    <alter-user sql-case-id="alter_user_with_password_option"/>
    <alter-user sql-case-id="alter_user_with_password_options"/>
    <alter-user sql-case-id="alter_user_with_lock_option"/>
    <alter-user sql-case-id="alter_user_with_options"/>
    <alter-user sql-case-id="alter_external_user"/>
    <alter-user sql-case-id="alter_global_user"/>
    <alter-user sql-case-id="alter_user_with_tablespace_option"/>
    <alter-user sql-case-id="alter_user_with_container"/>
    <alter-user sql-case-id="alter_user_with_quota_option"/>
    <alter-user sql-case-id="alter_user_password_with_lock_option"/>
    <alter-user sql-case-id="alter_user_expire_with_options"/>
    <alter-user sql-case-id="alter_user_grant_proxy"/>
    <alter-user sql-case-id="alter_user_grant_proxy_with_option"/>
    <alter-user sql-case-id="alter_user_default_role"/>
    <alter-user sql-case-id="alter_user_revoke_proxy"/>
    <alter-user sql-case-id="alter_user_proxys"/>
    <alter-user sql-case-id="alter_user_with_password_postgresql"/>
    <alter-user sql-case-id="alter_current_user_with_password"/>
    <alter-user sql-case-id="alter_user_with_option"/>
    <alter-user sql-case-id="alter_user_with_options_postgresql"/>
    <alter-user sql-case-id="alter_user_set_config"/>
    <alter-user sql-case-id="alter_all_users_set_config"/>
    <alter-user sql-case-id="alter_user_set_config_in_database"/>
    <alter-user sql-case-id="alter_user_set_config_from_current_user"/>
    <alter-user sql-case-id="alter_user_reset_config"/>
    <alter-user sql-case-id="alter_all_users_reset_config"/>
    <alter-user sql-case-id="alter_user_reset_config_in_database"/>
    <alter-user sql-case-id="alter_user_reset_all_config"/>
    <alter-user sql-case-id="alter_user_set_password"/>
    <alter-user sql-case-id="alter_user_set_login"/>
    <alter-user sql-case-id="alter_user_set_default_schema"/>
    <alter-user sql-case-id="alter_user_rename"/>
    <alter-user sql-case-id="alter_user_in_database"/>
    <alter-user sql-case-id="alter_user_set_schema_password_language"/>
    <alter-user sql-case-id="alter_azure_ad_user_with_login"/>
    <alter-user sql-case-id="alter_azure_ad_user_without_login"/>
    <alter-user sql-case-id="alter_user_alias_to_existing_azure_id"/>
    <alter-user sql-case-id="alter_user_identified_with_single_quoted"/>
    <alter-user sql-case-id="alter_user_with_factor"/>
</sql-parser-test-cases>
