<?xml version="1.0" encoding="UTF-8"?>
<sql-parser-test-cases>
    <create-user sql-case-id="create_user_with_hostname"/>
    <create-user sql-case-id="create_user_without_hostname"/>
    <create-user sql-case-id="create_user_identified_by_with_hostname"/>
    <create-user sql-case-id="create_user_identified_by_without_hostname"/>
    <create-user sql-case-id="create_user_identified_by_for"/>
    <create-user sql-case-id="create_user_password"/>
    <create-user sql-case-id="create_user"/>
    <create-user sql-case-id="create_users"/>
    <create-user sql-case-id="create_user_if_not_exists"/>
    <create-user sql-case-id="create_local_user"/>
    <create-user sql-case-id="create_user_with_roles"/>
    <create-user sql-case-id="create_user_with_password_default_role"/>
    <create-user sql-case-id="create_user_with_resource_option"/>
    <create-user sql-case-id="create_user_with_resource_options"/>
    <create-user sql-case-id="create_user_with_password_option"/>
    <create-user sql-case-id="create_user_with_password_options"/>
    <create-user sql-case-id="create_user_with_lock_option"/>
    <create-user sql-case-id="create_user_with_options"/>
    <create-user sql-case-id="create_external_user"/>
    <create-user sql-case-id="create_global_user"/>
    <create-user sql-case-id="create_user_with_password"/>
    <create-user sql-case-id="create_user_with_tablespace"/>
    <create-user sql-case-id="create_user_with_quota_option"/>
    <create-user sql-case-id="create_user_with_password_expire_lock"/>
    <create-user sql-case-id="create_user_only_with_name"/>
    <create-user sql-case-id="create_user_with_role_postgresql"/>
    <create-user sql-case-id="create_user_with_roles_postgresql"/>
    <create-user sql-case-id="create_user_with_password_postgresql"/>
    <create-user sql-case-id="create_user_with_option_postgresql"/>
    <create-user sql-case-id="create_user_with_options_postgresql"/>
    <create-user sql-case-id="create_user_with_login"/>
    <create-user sql-case-id="create_user_with_schema"/>
    <create-user sql-case-id="create_user_with_no_login"/>
    <create-user sql-case-id="create_user_with_certificate"/>
    <create-user sql-case-id="create_user_with_asym_key"/>
    <create-user sql-case-id="create_user_with_sysid"/>
    <create-user sql-case-id="create_user_with_group"/>
    <create-user sql-case-id="create_user_with_password_default_language"/>
    <create-user sql-case-id="create_user_with_domain_login"/>
    <create-user sql-case-id="create_user_with_sid"/>
    <create-user sql-case-id="create_user_to_copy_encrypted_data"/>
    <create-user sql-case-id="create_azure_ad_user_with_login"/>
    <create-user sql-case-id="create_azure_ad_user_as_group_from_login"/>
    <create-user sql-case-id="create_azure_ad_user_without_login"/>
    <create-user sql-case-id="create _user_with_option"/>
    <create-user sql-case-id="create_user_with_user_auth_option"/>
</sql-parser-test-cases>
