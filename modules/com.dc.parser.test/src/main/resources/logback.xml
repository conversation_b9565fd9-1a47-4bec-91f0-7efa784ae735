<?xml version="1.0"?>
<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.dc.parser" level="warn" additivity="true">
        <appender-ref ref="console"/>
    </logger>

    <root>
        <level value="info"/>
        <appender-ref ref="console"/>
    </root>
</configuration>
