<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_inner_join_related_with_alias"
              value="SELECT i.* FROM t_order o INNER JOIN t_order_item i ON o.order_id = i.order_id WHERE o.order_id = ?"/>
    <sql-case id="select_inner_join_related_with_name"
              value="SELECT t_order_item.* FROM t_order JOIN t_order_item ON t_order.order_id = t_order_item.order_id WHERE t_order.order_id = ?"/>
    <sql-case id="select_join_using"
              value="SELECT i.* FROM t_order o JOIN t_order_item i USING(order_id) WHERE o.order_id = ?"
              db-types="MySQL,PostgreSQL,GaussDB"/>
    <sql-case id="select_left_outer_join_related_with_alias"
              value="SELECT d.department_id, e.last_name FROM departments d LEFT OUTER JOIN employees e ON d.department_id = e.department_id ORDER BY d.department_id, e.last_name"
              db-types="MySQL, Oracle"/>
    <sql-case id="select_right_outer_join_related_with_alias"
              value="SELECT d.department_id, e.last_name FROM departments d RIGHT OUTER JOIN employees e ON d.department_id = e.department_id ORDER BY d.department_id, e.last_name"
              db-types="MySQL, Oracle"/>
    <sql-case id="select_full_outer_join_related_with_alias"
              value="SELECT d.department_id AS d_dept_id, e.department_id AS e_dept_id, e.last_name FROM departments d FULL OUTER JOIN employees e ON d.department_id = e.department_id ORDER BY d.department_id, e.last_name"
              db-types="Oracle"/>
    <sql-case id="select_full_outer_join_using_related_with_alias"
              value="SELECT department_id AS d_e_dept_id, e.last_name FROM departments d FULL OUTER JOIN employees e USING (department_id) ORDER BY department_id, e.last_name"
              db-types="Oracle"/>
    <sql-case id="select_cross_apply_join_related_with_alias"
              value="SELECT d.department_name, v.employee_id, v.last_name FROM departments d CROSS APPLY (SELECT * FROM employees e WHERE e.department_id = d.department_id) v WHERE d.department_name IN ('Marketing', 'Operations', 'Public Relations') ORDER BY d.department_name, v.employee_id"
              db-types="Oracle"/>
    <sql-case id="select_natural_join" value="SELECT * FROM t_order o NATURAL JOIN t_order_item i WHERE o.order_id = ?"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="select_natural_inner_join"
              value="SELECT * FROM t_order o NATURAL INNER JOIN t_order_item i WHERE o.order_id = ?"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="select_natural_left_join"
              value="SELECT * FROM t_order o NATURAL LEFT JOIN t_order_item i WHERE o.order_id = ?"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="select_natural_right_join"
              value="SELECT * FROM t_order o NATURAL RIGHT JOIN t_order_item i WHERE o.order_id = ?"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="select_natural_full_join"
              value="SELECT * FROM t_order o NATURAL FULL JOIN t_order_item i WHERE o.order_id = ?"
              db-types="PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="select_with_join_operator"
              value="SELECT * FROM t_order o , t_order_item i WHERE o.order_id(+) = i.order_id" db-types="Oracle"/>
    <sql-case id="select_join_with_quote"
              value="SELECT &quot;u&quot;.*, &quot;o&quot;.* FROM t_user &quot;u&quot; INNER JOIN t_order &quot;o&quot; ON &quot;u&quot;.user_id = &quot;o&quot;.user_id WHERE &quot;u&quot;.user_id = ?"
              db-types="Oracle"/>
    <sql-case id="select_outer_left_join_without_alias" value="SELECT * from t1 LEFT JOIN t2 ON t1.id = t2.id"
              db-types="Oracle"/>
    <sql-case id="select_outer_full_join_without_alias" value="SELECT * FROM t1 FULL JOIN t2 ON t1.id = t2.id"
              db-types="Oracle"/>
    <sql-case id="select_natural_join_with_object_id_function"
              value="SELECT c.name AS column_name,c.column_id,SCHEMA_NAME(t.schema_id) AS type_schema,t.name AS type_name,t.is_user_defined,t.is_assembly_type,c.max_length,c.precision,c.scale FROM sys.columns AS c JOIN sys.types AS t ON c.user_type_id=t.user_type_id WHERE c.object_id = OBJECT_ID('&lt;schema_name.table_name&gt;') ORDER BY c.column_id"
              db-types="SQLServer"/>
    <sql-case id="select_inner_join_with_object_id_function"
              value="SELECT SCHEMA_NAME(schema_id) AS schema_name,o.name AS object_name,o.type_desc,p.parameter_id,p.name AS parameter_name,TYPE_NAME(p.user_type_id) AS parameter_type,p.max_length,p.precision,p.scale,p.is_output FROM sys.objects AS o INNER JOIN sys.parameters AS p ON o.object_id = p.object_id WHERE o.object_id = OBJECT_ID('&lt;schema_name.object_name&gt;') ORDER BY schema_name, object_name, p.parameter_id"
              db-types="SQLServer"/>
    <sql-case id="select_inner_join_from_sys_dm_xe_objects"
              value="SELECT p.name AS [Package-Name],o.object_type,o.name AS [Object-Name],o.description AS [Object-Descr],p.guid AS [Package-Guid] FROM sys.dm_xe_packages AS p INNER JOIN sys.dm_xe_objects AS o ON p.guid = o.package_guid WHERE o.object_type = 'event' AND p.name LIKE '%' AND o.name LIKE '%sql%' ORDER BY p.name, o.object_type, o.name"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_join_string_split"
              value="SELECT ProductId, Name, value FROM Product CROSS APPLY STRING_SPLIT(Tags, ',')"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_join_string_split_with_group_by"
              value="SELECT value as tag, COUNT(*) AS [number_of_articles] FROM Product CROSS APPLY STRING_SPLIT(Tags, ',') GROUP BY value HAVING COUNT(*) > 2 ORDER BY COUNT(*) DESC"
              db-types="SQLServer"/>
    <sql-case id="select_cross_join_schema_table"
              value="SELECT s.ticket, s.customer, r.store FROM dbo.Sales AS s CROSS JOIN dbo.Returns AS r WHERE s.ticket = r.ticket AND s.type = 'toy' AND r.date = '2016-05-11'"
              db-types="SQLServer"/>
    <sql-case id="select_with_multi_join_01"
              value="SELECT tat.transaction_begin_time, getdate() AS 'current time', es.program_name, es.login_time, es.session_id, tst.open_transaction_count, eib.event_info FROM sys.dm_tran_active_transactions tat JOIN sys.dm_tran_session_transactions tst ON tat.transaction_id=tst.transaction_id JOIN sys.dm_exec_sessions es ON tst.session_id=es.session_id CROSS APPLY sys.dm_exec_input_buffer(es.session_id, NULL) eib WHERE es.is_user_process = 1 ORDER BY tat.transaction_begin_time ASC"
              db-types="SQLServer"/>
    <sql-case id="select_with_multi_join_02"
              value="SELECT text, 'DBCC FREEPROCCACHE (0x' + CONVERT(VARCHAR (512), plan_handle, 2) + ')' AS dbcc_freeproc_command FROM sys.dm_exec_cached_plans CROSS APPLY sys.dm_exec_query_plan(plan_handle) CROSS APPLY sys.dm_exec_sql_text(plan_handle) WHERE text LIKE '%person.person%'"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_join_with_open_json_function"
              value="SELECT reason, score, script = JSON_VALUE(details, '$.implementationDetails.script'), planForceDetails.*, estimated_gain = (regressedPlanExecutionCount + recommendedPlanExecutionCount) * (regressedPlanCpuTimeAverage - recommendedPlanCpuTimeAverage)/1000000, error_prone = IIF(regressedPlanErrorCount > recommendedPlanErrorCount, 'YES','NO') FROM sys.dm_db_tuning_recommendations CROSS APPLY OPENJSON (Details, '$.planForceDetails') WITH ([query_id] int '$.queryId', regressedPlanId int '$.regressedPlanId', recommendedPlanId int '$.recommendedPlanId', regressedPlanErrorCount int, recommendedPlanErrorCount int, regressedPlanExecutionCount int, regressedPlanCpuTimeAverage float, recommendedPlanExecutionCount int, recommendedPlanCpuTimeAverage float) AS planForceDetails"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_join_with_sys_dm_exec_function"
              value="SELECT cp.memory_object_address, cp.objtype, refcounts, usecounts, qs.query_plan_hash, qs.query_hash, qs.plan_handle, qs.sql_handle FROM sys.dm_exec_cached_plans AS cp CROSS APPLY sys.dm_exec_sql_text (cp.plan_handle) CROSS APPLY sys.dm_exec_query_plan (cp.plan_handle) INNER JOIN sys.dm_exec_query_stats AS qs ON qs.plan_handle = cp.plan_handle WHERE text LIKE '%usp_SalesByCustomer%'"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_join_with_like_nchar"
              value="SELECT plan_handle, st.text FROM sys.dm_exec_cached_plans CROSS APPLY sys.dm_exec_sql_text(plan_handle) AS st WHERE text LIKE N'SELECT * FROM Person.Address%'"
              db-types="SQLServer"/>
    <sql-case id="select_inner_join_dm_tran_session_transactions"
              value="SELECT [s_tst].[session_id], [database_name] = DB_NAME (s_tdt.database_id), [s_tdt].[database_transaction_begin_time],  [sql_text] = [s_est].[text] FROM sys.dm_tran_database_transactions [s_tdt] INNER JOIN sys.dm_tran_session_transactions [s_tst] ON [s_tst].[transaction_id] = [s_tdt].[transaction_id] INNER JOIN sys.dm_exec_connections [s_ec] ON [s_ec].[session_id] = [s_tst].[session_id] CROSS APPLY sys.dm_exec_sql_text ([s_ec].[most_recent_sql_handle]) AS [s_est]"
              db-types="SQLServer"/>
    <sql-case id="select_case_when_with_multi_join"
              value="SELECT tst.session_id, [database_name] = db_name(s.database_id), tat.transaction_begin_time, transaction_duration_s = datediff(s, tat.transaction_begin_time, sysdatetime()) , transaction_type = CASE tat.transaction_type  WHEN 1 THEN 'Read/write transaction' WHEN 2 THEN 'Read-only transaction' WHEN 3 THEN 'System transaction' WHEN 4 THEN 'Distributed transaction' END, input_buffer = ib.event_info, tat.transaction_uow, transaction_state  = CASE tat.transaction_state WHEN 0 THEN 'The transaction has not been completely initialized yet.' WHEN 1 THEN 'The transaction has been initialized but has not started.' WHEN 2 THEN 'The transaction is active - has not been committed or rolled back.' WHEN 3 THEN 'The transaction has ended. This is used for read-only transactions.' WHEN 4 THEN 'The commit process has been initiated on the distributed transaction.' WHEN 5 THEN 'The transaction is in a prepared state and waiting resolution.' WHEN 6 THEN 'The transaction has been committed.' WHEN 7 THEN 'The transaction is being rolled back.' WHEN 8 THEN 'The transaction has been rolled back.' END, transaction_name = tat.name, request_status = r.status, tst.is_user_transaction, tst.is_local, session_open_transaction_count = tst.open_transaction_count, s.host_name, s.program_name, s.client_interface_name, s.login_name, s.is_user_process FROM sys.dm_tran_active_transactions tat INNER JOIN sys.dm_tran_session_transactions tst  on tat.transaction_id = tst.transaction_id INNER JOIN Sys.dm_exec_sessions s on s.session_id = tst.session_id LEFT OUTER JOIN sys.dm_exec_requests r on r.session_id = s.session_id CROSS APPLY sys.dm_exec_input_buffer(s.session_id, null) AS ib"
              db-types="SQLServer"/>
    <sql-case id="select_inner_join_from_user_table"
              value="SELECT DISTINCT user.FirstName, user.LastName INTO ms_user FROM user INNER JOIN (SELECT * FROM ClickStream WHERE cs.url = 'www.microsoft.com') AS ms ON user.user_ip = ms.user_ip"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_with_substring_nest_case_when"
              value="SELECT req.session_id, req.total_elapsed_time AS duration_ms, req.cpu_time AS cpu_time_ms, req.total_elapsed_time - req.cpu_time AS wait_time, req.logical_reads, SUBSTRING (REPLACE (REPLACE (SUBSTRING (ST.text, (req.statement_start_offset/2) + 1, ((CASE statement_end_offset WHEN -1 THEN DATALENGTH(ST.text)  ELSE req.statement_end_offset END - req.statement_start_offset)/2) + 1) , CHAR(10), ' '), CHAR(13), ' '), 1, 512)  AS statement_text FROM sys.dm_exec_requests AS req CROSS APPLY sys.dm_exec_sql_text(req.sql_handle) AS ST ORDER BY total_elapsed_time DESC"
              db-types="SQLServer"/>
    <sql-case id="select_cross_apply_sys_table_query_status"
              value="SELECT t.text, (qs.total_elapsed_time/1000) / qs.execution_count AS avg_elapsed_time, (qs.total_worker_time/1000) / qs.execution_count AS avg_cpu_time, ((qs.total_elapsed_time/1000) / qs.execution_count ) - ((qs.total_worker_time/1000) / qs.execution_count) AS avg_wait_time, qs.total_logical_reads / qs.execution_count AS avg_logical_reads, qs.total_logical_writes / qs.execution_count AS avg_writes, (qs.total_elapsed_time/1000) AS cumulative_elapsed_time_all_executions FROM sys.dm_exec_query_stats qs CROSS apply sys.Dm_exec_sql_text (sql_handle) t WHERE t.text like '&lt;Your Query&gt;%' ORDER BY (qs.total_elapsed_time / qs.execution_count) DESC"
              db-types="SQLServer"/>
    <sql-case id="select_left_join_sub_query_with_escape_quotes"
              value="SELECT 'DECLARE @node NVARCHAR(512) = N''' + NodeName + '.' + Cluster + '''' FROM (SELECT SUBSTRING(replica_address, 0, CHARINDEX('\', replica_address)) AS NodeName, RIGHT(service_name, CHARINDEX('/', REVERSE(service_name)) - 1) AppName, JoinCol = 1 FROM sys.dm_hadr_fabric_partitions fp INNER JOIN sys.dm_hadr_fabric_replicas fr ON fp.partition_id = fr.partition_id INNER JOIN sys.dm_hadr_fabric_nodes fn ON fr.node_name = fn.node_name WHERE service_name LIKE '%ManagedServer%' AND replica_role = 2) t1 LEFT JOIN (SELECT value AS Cluster, JoinCol = 1 FROM sys.dm_hadr_fabric_config_parameters WHERE parameter_name = 'ClusterName') t2 ON (t1.JoinCol = t2.JoinCol) INNER JOIN (SELECT [value] AS AppName FROM sys.dm_hadr_fabric_config_parameters WHERE section_name = 'SQL' AND parameter_name = 'InstanceName') t3 ON (t1.AppName = t3.AppName)"
              db-types="SQLServer"/>
    <sql-case id="select_objects_with_inner_join"
              value="SELECT SCHEMA_NAME(schema_id) AS schema_name,o.name AS object_name,o.type_desc,p.parameter_id,p.name AS parameter_name,TYPE_NAME(p.user_type_id) AS parameter_type,p.max_length,p.precision,p.scale,p.is_output FROM sys.objects AS o INNER JOIN sys.parameters AS p ON o.object_id = p.object_id WHERE o.object_id = OBJECT_ID('&lt;schema_name.object_name&gt;') ORDER BY schema_name, object_name, p.parameter_id"
              db-types="SQLServer"/>
    <sql-case id="select_json_value_with_cross_apply"
              value="SELECT name,reason,score,JSON_VALUE(details, '$.implementationDetails.script') AS script,details.* FROM sys.dm_db_tuning_recommendations CROSS APPLY OPENJSON(details, '$.planForceDetails') WITH ([query_id] INT '$.queryId',regressed_plan_id INT '$.regressedPlanId',last_good_plan_id INT '$.recommendedPlanId') AS details WHERE JSON_VALUE(STATE, '$.currentValue') = 'Active'"
              db-types="SQLServer"/>
    <sql-case id="select_open_json_with_cross_apply"
              value="SELECT reason,score,script = JSON_VALUE(details, '$.implementationDetails.script'),planForceDetails.*,estimated_gain = (regressedPlanExecutionCount + recommendedPlanExecutionCount) * (regressedPlanCpuTimeAverage - recommendedPlanCpuTimeAverage) / 1000000,error_prone = IIF(regressedPlanErrorCount &gt; recommendedPlanErrorCount, 'YES', 'NO') FROM sys.dm_db_tuning_recommendations CROSS APPLY OPENJSON(Details, '$.planForceDetails') WITH ([query_id] INT '$.queryId',regressedPlanId INT '$.regressedPlanId',recommendedPlanId INT '$.recommendedPlanId',regressedPlanErrorCount INT,recommendedPlanErrorCount INT,regressedPlanExecutionCount INT,regressedPlanCpuTimeAverage FLOAT,recommendedPlanExecutionCount INT,recommendedPlanCpuTimeAverage FLOAT) AS planForceDetails"
              db-types="SQLServer"/>
    <sql-case id="select_sales_order_record_with_cross_apply"
              value="SELECT Tab.Id,SalesOrderJsonData.Customer,SalesOrderJsonData.Date FROM SalesOrderRecord AS Tab CROSS APPLY OPENJSON(Tab.json, N'$.Orders.OrdersArray') WITH (Number VARCHAR(200) N'$.Order.Number',Date DATETIME N'$.Order.Date',Customer VARCHAR(200) N'$.AccountNumber',Quantity INT N'$.Item.Quantity') AS SalesOrderJsonData WHERE JSON_VALUE(Tab.json, '$.Status') = N'Closed' ORDER BY JSON_VALUE(Tab.json, '$.Group'),Tab.DateModified"
              db-types="SQLServer"/>
    <sql-case id="select_sys_databases_join_sys_logins"
              value="SELECT d.name, d.owner_sid, sl.name FROM sys.databases AS d JOIN sys.sql_logins AS sl ON d.owner_sid = sl.sid"
              db-types="SQLServer"/>
    <sql-case id="select_distinct_with_inner_join_subquery"
              value="SELECT DISTINCT user.FirstName, user.LastName INTO ms_user FROM user INNER JOIN (SELECT * FROM ClickStream WHERE cs.url = 'www.microsoft.com') AS ms ON user.user_ip = ms.user_ip"
              db-types="SQLServer"/>
    <sql-case id="select_cross_join_sys_log_info_with_count"
              value="SELECT [name], COUNT(l.database_id) AS 'vlf_count' FROM sys.databases AS s CROSS APPLY sys.dm_db_log_info(s.database_id) AS l GROUP BY [name] HAVING COUNT(l.database_id) &gt; 100"
              db-types="SQLServer"/>
    <sql-case id="select_from_sys_columns_inner_join_sys_types"
              value="SELECT OBJECT_NAME(object_id) AS object_name,c.name AS column_name,SCHEMA_NAME(t.schema_id) AS schema_name,TYPE_NAME(c.user_type_id) AS user_type_name,c.max_length,c.precision,c.scale,c.is_nullable,c.is_computed FROM sys.columns AS c INNER JOIN sys.types AS t ON c.user_type_id = t.user_type_id WHERE c.user_type_id = TYPE_ID('&lt;schema_name.data_type_name&gt;')"
              db-types="SQLServer"/>
    <sql-case id="select_cross_join_sys_dm_exec_requests"
              value="SELECT session_id as SPID, command, a.text AS Query, start_time, percent_complete, dateadd(second,estimated_completion_time/1000, getdate()) as estimated_completion_time FROM sys.dm_exec_requests r CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) a WHERE r.command in ('BACKUP DATABASE','RESTORE DATABASE')"
              db-types="SQLServer"/>
    <sql-case id="select_from_join_with_json_table"
              value="SELECT c1, c2, JSON_EXTRACT(c3, '$.*') FROM t1 AS m JOIN JSON_TABLE(m.c3, '$.*' COLUMNS(at VARCHAR(10) PATH '$.a' DEFAULT '1' ON EMPTY, bt VARCHAR(10) PATH '$.b'DEFAULT '2' ON EMPTY, ct VARCHAR(10) PATH '$.c' DEFAULT '3' ON EMPTY)) AS tt ON m.c1 &gt; tt.at;"
              db-types="MySQL"/>
</sql-cases>
