<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_with_double_quotes"
              value="SELECT * FROM &quot;t_order_item&quot; WHERE &quot;item_id&quot; != ? ORDER BY &quot;item_id&quot;"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="select_escape_with_single_quota" value="SELECT * FROM t_order where status='\''" db-types="MySQL"/>
    <sql-case id="select_escape_with_double_quota" value="SELECT * FROM t_order where status=&quot;\&quot;&quot;"
              db-types="MySQL"/>
    <sql-case id="select_alias_as_single_quote_string" value="SELECT status as 'status' FROM t_order" db-types="MySQL"/>
    <sql-case id="select_alias_as_string_double_quote" value="SELECT status as &quot;status&quot; FROM t_order"
              db-types="MySQL"/>
</sql-cases>
