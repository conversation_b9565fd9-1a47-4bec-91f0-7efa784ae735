<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_into_before_from" value="SELECT status INTO @var1 FROM t_order WHERE order_id = ?"
              db-types="MySQL"/>
    <sql-case id="select_into_after_from" value="SELECT status FROM t_order WHERE order_id = ? INTO @var1"
              db-types="MySQL"/>
    <sql-case id="select_into_multi_variable"
              value="SELECT user_id, status FROM t_order WHERE order_id = ? INTO @var1, @var2" db-types="MySQL"/>
    <sql-case id="select_into_out_file" value="SELECT * FROM t_order LIMIT ? INTO OUTFILE '/tmp/tmp.txt'"
              db-types="MySQL"/>
    <sql-case id="select_into_out_file_with_charset"
              value="SELECT * FROM t_order LIMIT ? INTO OUTFILE '/tmp/tmp.txt' CHARACTER SET utf8" db-types="MySQL"/>
    <sql-case id="select_into_out_file_with_fields"
              value="SELECT * FROM t_order LIMIT ? INTO OUTFILE '/tmp/tmp.txt'  FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '&quot;'"
              db-types="MySQL"/>
    <sql-case id="select_into_out_file_with_fields_and_escaped"
              value="SELECT user_id, status FROM t_order LIMIT ? INTO OUTFILE '/tmp/tmp.txt'  FIELDS TERMINATED BY ',' OPTIONALLY ENCLOSED BY '&quot;' ESCAPED BY '\\'"
              db-types="MySQL"/>
    <sql-case id="select_into_out_file_with_lines"
              value="SELECT * FROM t_order LIMIT ? INTO OUTFILE '/tmp/tmp.txt'  LINES TERMINATED BY '\n'"
              db-types="MySQL"/>
    <sql-case id="select_into_with_lock_after_into"
              value="SELECT status FROM t_order WHERE order_id = ? INTO @var1 FOR UPDATE " db-types="MySQL"/>
    <sql-case id="select_into_with_lock_before_into"
              value="SELECT status FROM t_order WHERE order_id = ? FOR UPDATE INTO @var1" db-types="MySQL"/>
    <sql-case id="select_into_param_without_at" value="SELECT 1 INTO a" db-types="MySQL"/>
    <sql-case id="select_into_with_variable"
              value="SELECT select_list INTO record_variable_name FROM table_or_view_name" db-types="Oracle"/>
    <sql-case id="select_into_table_with_try_cast_function"
              value="SELECT machine.temperature, udf.ASAEdgeUDFDemo_Class1_SquareFunction(try_cast(machine.temperature as bigint)) INTO Output FROM Input;"
              db-types="SQLServer"/>
    <sql-case id="select_into_table_before_from"
              value="SELECT * INTO dbo.NewProducts FROM Production.Product WHERE ListPrice &gt; $25 AND ListPrice &lt; $100;"
              db-types="SQLServer"/>
    <sql-case id="select_into_simple_table"
              value="SELECT film_id, title, rental_rate INTO TABLE film_r FROM film WHERE rating = 'R' AND rental_duration = 5 ORDER BY title"
              db-types="PostgreSQL, GaussDB"/>
    <sql-case id="select_into_temp_table"
              value="SELECT film_id, title, length INTO TEMP TABLE short_film FROM film WHERE length &lt; 60 ORDER BY title"
              db-types="PostgreSQL, GaussDB"/>
</sql-cases>
