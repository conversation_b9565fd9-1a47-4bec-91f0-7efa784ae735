<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="call_without_parameters" value="CALL p" db-types="MySQL"/>
    <sql-case id="call_with_parameters_all_variable" value="CALL p(@order_id, @user_id)" db-types="MySQL"/>
    <sql-case id="call_with_parameters_all_placeholder" value="CALL p(?, ?) " db-types="MySQL"/>
    <sql-case id="call_with_parameters_all_expression" value="CALL p('user', 'order')" db-types="MySQL"/>
    <sql-case id="call_with_parameters_mix" value="CALL p(@order_id, 'user', ?)" db-types="MySQL"/>
    <sql-case id="call_with_named_notation_with_null" value="CALL p(a =&gt; null, b =&gt; 8, c =&gt; 2);"
              db-types="PostgreSQL"/>
    <sql-case id="call_with_named_notation" value="CALL p(b =&gt; 8, c =&gt; 2, a =&gt; 0);" db-types="PostgreSQL"/>
    <sql-case id="call_with_mixed_notation" value="CALL p(null, 7, c =&gt; 2);" db-types="PostgreSQL"/>
    <sql-case id="call_with_mixed_notation_with_null" value="CALL p(null, c =&gt; 4, b =&gt; 11);"
              db-types="PostgreSQL"/>
    <sql-case id="call_with_mixed_notation_with_apos" value="CALL p(10, b =&gt; &apos;Hello&apos;);"
              db-types="PostgreSQL"/>
    <sql-case id="call_with_named_notation_with_apos" value="CALL p(b =&gt; &apos;Hello&apos;, a =&gt; 10);"
              db-types="PostgreSQL"/>
    <sql-case id="call_with_positional_notation_with_expression" value="CALL p(1.0/0.1);" db-types="PostgreSQL"/>
    <sql-case id="call_with_mysql_firewall_group_enlist"
              value="CALL mysql.sp_firewall_group_enlist('fwgrp', 'member2@localhost')" db-types="MySQL"/>
</sql-cases>
