<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="select_with_block_comment"
              value="SELECT * /* this is &#x000D;&#x000A; block comment */ FROM /* this is another &#x000A; block comment */ t_order where status='1'"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle,SQLServer,Firebird"/>
    <sql-case id="select_with_nested_block_comment"
              value="SELECT * /* this is &#x000D;&#x000A; /* this is another &#x000A; block comment */ block comment */ FROM t_order"
              db-types="PostgreSQL"/>
    <sql-case id="select_with_single_comment"
              value="SELECT * -- this is an line comment &#x000D;&#x000A; FROM -- this is another line comment &#x000A; t_order where status='1'"
              db-types="MySQL,PostgreSQL,GaussDB,Firebird"/>
    <sql-case id="select_with_single_comment_without_whitespace"
              value="SELECT * --this is an line comment &#x000D;&#x000A; FROM --this is another line comment &#x000A; t_order where status='1'"
              db-types="PostgreSQL,GaussDB,Oracle,Firebird"/>
</sql-cases>
