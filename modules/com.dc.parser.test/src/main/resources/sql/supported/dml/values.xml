<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="values_with_regexp_replace"
              value="SELECT REGEXP_REPLACE(e, 'pattern', 'xyz')FROM (VALUES ROW('Find pattern'), ROW(NULL), ROW('Find pattern')) AS v(e)"
              db-types="MySQL"/>
    <sql-case id="values_with_row" value="values ROW(1,2)" db-types="MySQL"/>
    <sql-case id="values_with_order_limit"
              value="VALUES ROW(1,-2,3), <PERSON>OW(5,7,9), <PERSON>OW(4,6,8) ORDER BY column_1 desc , column_0 desc limit 10"
              db-types="MySQL"/>
    <sql-case id="values_with_select" value="values row((select 1), 2)" db-types="MySQL"/>
</sql-cases>
