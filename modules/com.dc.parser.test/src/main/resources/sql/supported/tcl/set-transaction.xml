<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="set_transaction" value="SET TRANSACTION ISOLATION LEVEL REPEATABLE READ"
              db-types="MySQL,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="set_global_transaction" value="SET GLOBAL TRANSACTION ISOLATION LEVEL REPEATABLE READ"
              db-types="MySQL"/>
    <sql-case id="set_session_transaction"
              value="SET SESSION CHARACTERISTICS AS TRANSACTION ISOLATION LEVEL READ COMMITTED"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="set_transaction_read_only" value="SET TRANSACTION READ ONLY" db-types="MySQL,Oracle"/>
    <sql-case id="set_transaction_read_write_with_name" value="SET TRANSACTION READ WRITE NAME 'Toronto'"
              db-types="Oracle"/>
    <sql-case id="set_transaction_isolation_level_serializable" value="SET TRANSACTION ISOLATION LEVEL SERIALIZABLE"
              db-types="Oracle, SQLServer"/>
    <sql-case id="set_transaction_isolation_level_read_committed" value="SET TRANSACTION ISOLATION LEVEL READ COMMITTED"
              db-types="Oracle, SQLServer"/>
    <sql-case id="set_transaction_isolation_level_snapshot" value="SET TRANSACTION ISOLATION LEVEL SNAPSHOT"
              db-types="SQLServer"/>
    <sql-case id="set_transaction_use_rollback_segment" value="SET TRANSACTION USE ROLLBACK SEGMENT rbs_ts"
              db-types="Oracle"/>
    <sql-case id="set_transaction_with_name" value="SET TRANSACTION NAME 'comment1'" db-types="Oracle"/>
    <sql-case id="set_transaction_snapshot" value="SET TRANSACTION SNAPSHOT 'snapshot1'"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="xa_recover" value="XA RECOVER" db-types="MySQL"/>
    <sql-case id="xa_start" value="XA start 'abcdef7' join" db-types="MySQL"/>
    <sql-case id="xa_begin" value="XA begin 'abcdef7' join" db-types="MySQL"/>
    <sql-case id="xa_end" value="XA end 'abcdef7'" db-types="MySQL"/>
    <sql-case id="xa_prepare" value="XA prepare 'abcdef7'" db-types="MySQL"/>
    <sql-case id="xa_commit" value="XA commit 'abcdef7'" db-types="MySQL"/>
    <sql-case id="xa_rollback" value="XA rollback 'abcdef7'" db-types="MySQL"/>
</sql-cases>
