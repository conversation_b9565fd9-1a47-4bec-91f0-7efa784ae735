<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="rollback" value="ROLLBACK" db-types="MySQL,Oracle,PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="rollback_transaction" value="ROLLBACK TRAN @TransactionName" db-types="SQLServer"/>
    <sql-case id="rollback_with_name" value="ROLLBACK TRANSACTION transaction1" db-types="SQLServer"/>
    <sql-case id="rollback_to_savepoint" value="ROLLBACK TO savepoint1" db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="rollback_force" value="ROLLBACK FORCE 'transaction1'" db-types="Oracle"/>
    <sql-case id="rollback_prepare" value="ROLLBACK PREPARED 'transaction1'" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="rollback_in_pg" value="ROLLBACK TRANSACTION AND CHAIN" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="rollback_work_force" value="ROLLBACK WORK FORCE 'string1'" db-types="Oracle"/>
    <sql-case id="abort" value="ABORT" db-types="PostgreSQL,GaussDB"/>
</sql-cases>
