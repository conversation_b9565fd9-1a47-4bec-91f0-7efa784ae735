<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="start_transaction" value="START TRANSACTION" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="start_read_committed" value="START TRANSACTION ISOLATION LEVEL READ COMMITTED"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="start_read_only" value="START TRANSACTION READ ONLY" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="start_with_transaction_mode" value="START TRANSACTION ISOLATION LEVEL READ COMMITTED"
              db-types="PostgreSQL,GaussDB"/>
</sql-cases>
