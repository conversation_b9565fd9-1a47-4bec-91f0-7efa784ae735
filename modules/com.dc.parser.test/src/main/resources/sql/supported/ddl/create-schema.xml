<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_schema" value="CREATE SCHEMA test_schema" db-types="SQLServer,PostgreSQL,GaussDB"/>
    <sql-case id="create_schema_with_grant_permissions" value="CREATE SCHEMA test_schema AUTHORIZATION Annik
        CREATE TABLE NineProngs (source int, cost int, partnumber int)
        GRANT SELECT ON SCHEMA::Sprockets TO Mandar
        DENY SELECT ON SCHEMA::Sprockets TO Prasanna" db-types="SQLServer"/>
    <sql-case id="create_schema_with_if_not_exists" value="CREATE SCHEMA IF NOT EXISTS test_schema"
              db-types="PostgreSQL"/>
</sql-cases>
