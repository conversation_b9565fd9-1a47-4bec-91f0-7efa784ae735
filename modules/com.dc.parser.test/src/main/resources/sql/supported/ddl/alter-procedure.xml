<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_procedure_rename" value="ALTER PROCEDURE insert_data(integer, integer) RENAME TO insert_record"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_owner" value="ALTER PROCEDURE insert_data(integer, integer) OWNER TO joe"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_set_schema"
              value="ALTER PROCEDURE insert_data(integer, integer) SET SCHEMA accounting"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_depends_on"
              value="ALTER PROCEDURE insert_data(integer, integer) DEPENDS ON EXTENSION myext"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_set_param"
              value="ALTER PROCEDURE check_password(text) SET search_path = admin,pg_temp"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_reset_param" value="ALTER PROCEDURE check_password(text) RESET search_path"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_procedure_for_oracle" value="ALTER PROCEDURE hr.remove_emp COMPILE" db-types="Oracle"/>
</sql-cases>
