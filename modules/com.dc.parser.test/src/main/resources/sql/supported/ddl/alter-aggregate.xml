<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_aggregate" value="ALTER AGGREGATE alt_agg1(int) RENAME TO alt_agg2;"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_aggregate_owner" value="ALTER AGGREGATE alt_agg2(int) OWNER TO regress_alter_generic_user2;"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_aggregate_set_schema" value="ALTER AGGREGATE alt_agg2(int) SET SCHEMA alt_nsp2;"
              db-types="PostgreSQL,GaussDB"/>
</sql-cases>
