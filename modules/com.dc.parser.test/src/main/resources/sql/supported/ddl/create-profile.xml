<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_mandatory_profile" value="CREATE MANDATORY PROFILE c##cdb_profile LIMIT PASSWORD_VERIFY_FUNCTION my_mandatory_function
                                         CONTAINER = ALL;" db-types="Oracle"/>
    <sql-case id="create_limit_profile" value="CREATE PROFILE prof LIMIT
                                         FAILED_LOGIN_ATTEMPTS 4
                                         PASSWORD_LOCK_TIME 30
                                         PASSWORD_LIFE_TIME 180;" db-types="Oracle"/>
</sql-cases>
