<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_publication" value="CREATE PUBLICATION mypublication FOR TABLE users, departments;"
              db-types="GaussDB, PostgreSQL"/>
    <sql-case id="create_publication_for_all_tables" value="CREATE PUBLICATION alltables FOR ALL TABLES;"
              db-types="GaussDB, PostgreSQL"/>
    <sql-case id="create_publication_with_statement"
              value="CREATE PUBLICATION insert_only FOR TABLE mydata WITH (publish = 'insert');"
              db-types="GaussDB, PostgreSQL"/>
</sql-cases>
