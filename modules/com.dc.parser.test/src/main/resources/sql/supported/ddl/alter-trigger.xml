<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_trigger" value="ALTER TRIGGER Sales.bonus_reminder
        ON Sales.SalesPersonQuotaHistory
        AFTER INSERT
        AS INSERT INTO SalesPersonQuotaHistory VALUES(t1, 100)" db-types="SQLServer"/>
    <sql-case id="alter_trigger_rename" value="ALTER TRIGGER emp_stamp ON emp RENAME TO emp_track_chgs;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_trigger_extension" value="ALTER TRIGGER emp_stamp ON emp DEPENDS ON EXTENSION emplib;"
              db-types="PostgreSQL"/>
    <sql-case id="alter_trigger_disable" value="ALTER TRIGGER update_job_history DISABLE;" db-types="Oracle"/>
    <sql-case id="alter_trigger_enable" value="ALTER TRIGGER update_job_history ENABLE;" db-types="Oracle"/>
</sql-cases>
