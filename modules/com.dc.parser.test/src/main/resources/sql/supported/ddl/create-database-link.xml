<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_database_link" value="CREATE PUBLIC DATABASE LINK remote USING 'remote'" db-types="Oracle"/>
    <sql-case id="create_database_link_connect_to_user"
              value="CREATE DATABASE LINK local CONNECT TO hr IDENTIFIED BY password USING 'local';" db-types="Oracle"/>
    <sql-case id="create_database_link_connect_to_current_user"
              value="CREATE DATABASE LINK remote.us.example.com CONNECT TO CURRENT_USER USING 'remote';"
              db-types="Oracle"/>
</sql-cases>
