<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_spfile" value="CREATE SPFILE FROM MEMORY" db-types="Oracle"/>
    <sql-case id="create_spfile_with_pfile" value="CREATE SPFILE FROM PFILE = '$ORACLE_HOME/work/t_init1.ora';"
              db-types="Oracle"/>
    <sql-case id="create_spfile_with_pfile_spfile"
              value="CREATE SPFILE = 's_params.ora' FROM PFILE = '$ORACLE_HOME/work/t_init1.ora';" db-types="Oracle"/>
    <sql-case id="create_spfile_with_as_copy"
              value="CREATE SPFILE = 's_params.ora' FROM PFILE = '$ORACLE_HOME/work/t_init1.ora' AS COPY;"
              db-types="Oracle"/>
</sql-cases>
