<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_sequence" value="CREATE SEQUENCE seq_id" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_sequence_with_option" value="CREATE TEMPORARY SEQUENCE seq_option"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_sequence_complex" value="CREATE SEQUENCE seq_complex MINVALUE 0 MAXVALUE 100 START 0"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_sequence_with_all_arguments" value="CREATE SEQUENCE Test.DecSeq
        AS decimal(3,0)
        START WITH 125
        INCREMENT BY 25
        MINVALUE 100
        MAXVALUE 200
        CYCLE
        CACHE 3;" db-types="SQLServer"/>
    <sql-case id="create_sequence_with_customers" value="CREATE SEQUENCE customers_seq
        START WITH 1000
        INCREMENT BY 1
        NOCACHE
        NOCYCLE;" db-types="Oracle"/>
    <sql-case id="create_sequence_lab_samples_seq" value="create sequence LAB_SAMPLES_SEQ" db-types="Oracle"/>
</sql-cases>
