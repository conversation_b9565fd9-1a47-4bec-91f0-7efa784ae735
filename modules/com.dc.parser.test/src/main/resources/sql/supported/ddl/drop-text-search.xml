<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="drop_text_search_configuration_if_exists"
              value="DROP TEXT SEARCH CONFIGURATION IF EXISTS no_such_schema.foo" db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_configuration" value="DROP TEXT SEARCH CONFIGURATION no_such_schema"
              db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_dictionary_if_exists"
              value="DROP TEXT SEARCH DICTIONARY IF EXISTS no_such_schema.foo" db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_dictionary" value="DROP TEXT SEARCH DICTIONARY no_such_schema"
              db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_parser_if_exists" value="DROP TEXT SEARCH PARSER IF EXISTS no_such_schema.foo"
              db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_parser" value="DROP TEXT SEARCH PARSER no_such_schema" db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_template_if_exists" value="DROP TEXT SEARCH TEMPLATE IF EXISTS no_such_schema.foo"
              db-types="PostgreSQL"/>
    <sql-case id="drop_text_search_template" value="DROP TEXT SEARCH TEMPLATE no_such_schema;" db-types="PostgreSQL"/>
</sql-cases>
