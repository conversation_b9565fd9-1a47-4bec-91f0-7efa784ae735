<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_lockdown_profile_disable_feature"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE FEATURE = ('LOB_FILE_ACCESS', 'TRACE_VIEW_ACCESS');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_disable_feature_all_except"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE FEATURE ALL EXCEPT = ('COMMON_USER_LOCAL_SCHEMA_ACCESS', 'LOCAL_USER_COMMON_SCHEMA_ACCESS');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_disable_feature_all"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE FEATURE ALL;" db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_enable_feature"
              value="ALTER LOCKDOWN PROFILE hr_prof ENABLE FEATURE = ('UTL_HTTP', 'UTL_SMTP', 'OS_ACCESS');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_enable_feature_all_except"
              value="ALTER LOCKDOWN PROFILE hr_prof ENABLE FEATURE ALL EXCEPT = ('AQ_PROTOCOLS', 'CTX_PROTOCOLS');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_enable_feature_all" value="ALTER LOCKDOWN PROFILE hr_prof ENABLE FEATURE ALL;"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_disable_option"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE OPTION = ('DATABASE QUEUING');" db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_enable_option"
              value="ALTER LOCKDOWN PROFILE hr_prof ENABLE OPTION = ('DATABASE QUEUING');" db-types="Oracle"/>
    <sql-case id="alter_lockdown_profile_enable_option_all" value="ALTER LOCKDOWN PROFILE hr_prof ENABLE OPTION ALL;"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_statement"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER DATABASE');" db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_statement_and_clause"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER SYSTEM') CLAUSE = ('SUSPEND', 'RESUME');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_statement_and_clause_all"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER PLUGGABLE DATABASE') CLAUSE ALL EXCEPT = ('DEFAULT TABLESPACE', 'DEFAULT TEMPORARY TABLESPACE');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_statement_and_clause_and_option"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER SESSION') CLAUSE = ('SET') OPTION = ('COMMIT_WAIT', 'CURSOR_SHARING');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_statement_and_clause_and_option_and_value"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER SYSTEM') CLAUSE = ('SET')  OPTION = ('PDB_FILE_NAME_CONVERT') VALUE = ('cdb1_pdb0', 'cdb1_pdb1');"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_min_value"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER SYSTEM') CLAUSE = ('SET') OPTION = ('CPU_COUNT') MINVALUE = '8';"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_disable_max_value"
              value="ALTER LOCKDOWN PROFILE hr_prof DISABLE STATEMENT = ('ALTER SYSTEM') CLAUSE = ('SET') OPTION = ('CPU_COUNT') MAXVALUE = '2';"
              db-types="Oracle"/>
    <sql-case id="alter_lockdown-profile_enable_statement_all_except"
              value="ALTER LOCKDOWN PROFILE hr_prof ENABLE STATEMENT ALL EXCEPT = ('ALTER DATABASE');"
              db-types="Oracle"/>
</sql-cases>
