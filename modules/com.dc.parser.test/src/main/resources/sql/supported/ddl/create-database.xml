<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_database" value="CREATE DATABASE lusiadas" db-types="PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="create_database_with_back_quote" value="CREATE DATABASE `lusiadas`" db-types="MySQL"/>
    <sql-case id="create_database_owner" value="CREATE DATABASE sales OWNER salesapp TABLESPACE salesspace"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_database_with_location_and_encoding" value="CREATE DATABASE music2
        LC_COLLATE 'sv_SE.iso885915' LC_CTYPE 'sv_SE.iso885915'
        ENCODING LATIN9
        TEMPLATE template0" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_database_with_location" value="CREATE DATABASE music
        LC_COLLATE 'sv_SE.utf8' LC_CTYPE 'sv_SE.utf8'
        TEMPLATE template0" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_database_with_file" value="CREATE DATABASE Sales
        ON ( NAME = Sales_dat,
            FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER\MSSQL\DATA\saledat.mdf',
            SIZE = 10, MAXSIZE = 50, FILEGROWTH = 5 )
        LOG ON ( NAME = Sales_log,
            FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER\MSSQL\DATA\salelog.ldf',
            SIZE = 5MB, MAXSIZE = 25MB, FILEGROWTH = 5MB ) ;" db-types="SQLServer"/>
    <sql-case id="create_database_oracle1" value="CREATE DATABASE sample
        CONTROLFILE REUSE 
        LOGFILE
           GROUP 1 ('diskx:log1.log', 'disky:log1.log') SIZE 50K, 
           GROUP 2 ('diskx:log2.log', 'disky:log2.log') SIZE 50K 
        MAXLOGFILES 5 
        MAXLOGHISTORY 100 
        MAXDATAFILES 10 
        MAXINSTANCES 2 
        ARCHIVELOG 
        CHARACTER SET AL32UTF8
        NATIONAL CHARACTER SET AL16UTF16
        DATAFILE  
           'disk1:df1.dbf' AUTOEXTEND ON,
           'disk2:df2.dbf' AUTOEXTEND ON NEXT 10M MAXSIZE UNLIMITED
        DEFAULT TEMPORARY TABLESPACE temp_ts
        UNDO TABLESPACE undo_ts 
        SET TIME_ZONE = '+02:00'" db-types="Oracle"/>
    <sql-case id="create_database_oracle2" value="CREATE DATABASE newcdb
        USER SYS IDENTIFIED BY sys_password
        USER SYSTEM IDENTIFIED BY system_password
        LOGFILE GROUP 1 ('/u01/logs/my/redo01a.log','/u02/logs/my/redo01b.log')
                   SIZE 100M BLOCKSIZE 512,
                GROUP 2 ('/u01/logs/my/redo02a.log','/u02/logs/my/redo02b.log')
                   SIZE 100M BLOCKSIZE 512,
                GROUP 3 ('/u01/logs/my/redo03a.log','/u02/logs/my/redo03b.log')
                   SIZE 100M BLOCKSIZE 512
        MAXLOGHISTORY 1
        MAXLOGFILES 16
        MAXLOGMEMBERS 3
        MAXDATAFILES 1024
        CHARACTER SET AL32UTF8
        NATIONAL CHARACTER SET AL16UTF16
        EXTENT MANAGEMENT LOCAL
        DATAFILE '/u01/app/oracle/oradata/newcdb/system01.dbf'
          SIZE 700M REUSE AUTOEXTEND ON NEXT 10240K MAXSIZE UNLIMITED
        SYSAUX DATAFILE '/u01/app/oracle/oradata/newcdb/sysaux01.dbf'
          SIZE 550M REUSE AUTOEXTEND ON NEXT 10240K MAXSIZE UNLIMITED
        DEFAULT TABLESPACE deftbs
          DATAFILE '/u01/app/oracle/oradata/newcdb/deftbs01.dbf'
          SIZE 500M REUSE AUTOEXTEND ON MAXSIZE UNLIMITED
        DEFAULT TEMPORARY TABLESPACE tempts1
          TEMPFILE '/u01/app/oracle/oradata/newcdb/temp01.dbf'
          SIZE 20M REUSE AUTOEXTEND ON NEXT 640K MAXSIZE UNLIMITED
        UNDO TABLESPACE undotbs1
          DATAFILE '/u01/app/oracle/oradata/newcdb/undotbs01.dbf'
          SIZE 200M REUSE AUTOEXTEND ON NEXT 5120K MAXSIZE UNLIMITED
        ENABLE PLUGGABLE DATABASE
          SEED
          FILE_NAME_CONVERT = ('/u01/app/oracle/oradata/newcdb/',
                               '/u01/app/oracle/oradata/pdbseed/')
          SYSTEM DATAFILES SIZE 125M AUTOEXTEND ON NEXT 10M MAXSIZE UNLIMITED
          SYSAUX DATAFILES SIZE 100M
        USER_DATA TABLESPACE usertbs
          DATAFILE '/u01/app/oracle/oradata/pdbseed/usertbs01.dbf'
          SIZE 200M REUSE AUTOEXTEND ON MAXSIZE UNLIMITED" db-types="Oracle"/>
</sql-cases>
