<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_language" value="CREATE LANGUAGE alt_lang1 HANDLER plpgsql_call_handler"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_or_replace_language"
              value="CREATE OR REPLACE PROCEDURAL LANGUAGE alt_lang1 HANDLER plpgsql_call_handler"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_language_inline_validator"
              value="CREATE OR REPLACE PROCEDURAL LANGUAGE alt_lang1 HANDLER plpgsql_call_handler INLINE inline_handler VALIDATOR valfunction"
              db-types="PostgreSQL,GaussDB"/>
</sql-cases>
