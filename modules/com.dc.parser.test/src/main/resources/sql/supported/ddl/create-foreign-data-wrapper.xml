<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_foreign_data_wrapper" value="CREATE FOREIGN DATA WRAPPER addr_fdw;" db-types="PostgreSQL"/>
    <sql-case id="create_foreign_data_wrapper_with_validator"
              value="CREATE FOREIGN DATA WRAPPER fdw_heap2 VALIDATOR postgresql_fdw_validator;" db-types="PostgreSQL"/>
    <sql-case id="create_foreign_data_wrapper_with_options"
              value="CREATE FOREIGN DATA WRAPPER foo OPTIONS (&quot;test wrapper&quot; &apos;true&apos;);"
              db-types="PostgreSQL"/>
    <sql-case id="create_foreign_data_wrapper_with_handler"
              value="CREATE FOREIGN DATA WRAPPER test_fdw HANDLER invalid_fdw_handler;" db-types="PostgreSQL"/>
</sql-cases>
