<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="alter_function_rename" value="ALTER FUNCTION sqrt(integer) RENAME TO square_root"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_set" value="ALTER FUNCTION check_password(text) SET search_path = admin,pg_temp"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_owner" value="ALTER FUNCTION sqrt(integer) OWNER TO joe"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_set_schema" value="ALTER FUNCTION sqrt(integer) SET SCHEMA maths"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_reset" value="ALTER FUNCTION check_password(text) RESET search_path"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_depend_on" value="ALTER FUNCTION sqrt(integer) DEPENDS ON EXTENSION mathlib"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="alter_function_compile" value="ALTER FUNCTION oe.get_bal COMPILE;" db-types="Oracle"/>
</sql-cases>
