<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_collation"
              value="CREATE COLLATION collation1 (provider = icu, locale = &apos;@colStrength=secondary&apos;, deterministic = false);"
              db-types="PostgreSQL"/>
    <sql-case id="create_collation_with_apos"
              value="CREATE COLLATION collation1 (provider = icu, locale = &apos;&apos;);" db-types="PostgreSQL"/>
    <sql-case id="create_collation_from" value="CREATE COLLATION collation1 FROM &quot;und-x-icu&quot;;"
              db-types="PostgreSQL"/>
</sql-cases>
