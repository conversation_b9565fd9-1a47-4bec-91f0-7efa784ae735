<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="create_user_with_hostname" value="CREATE USER 'user_dev_new'@'localhost' default role role1"
              db-types="MySQL"/>
    <sql-case id="create_user_without_hostname" value="CREATE USER user_dev_new" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_identified_by_with_hostname"
              value="CREATE USER 'user_dev_new'@'localhost' identified by 'passwd_dev' default role role1"
              db-types="MySQL"/>
    <sql-case id="create_user_identified_by_without_hostname" value="CREATE USER user_dev_new identified by passwd_dev"
              db-types="Oracle"/>
    <sql-case id="create_user_identified_by_for" value="CREATE USER user_dev_new FOR LOGIN login_dev"
              db-types="SQLServer"/>
    <sql-case id="create_user_password" value="CREATE USER user_dev_new PASSWORD 'passwd_dev'"
              db-types="PostgreSQL,GaussDB,Firebird"/>
    <sql-case id="create_user" value="CREATE USER user1 DEFAULT ROLE role1" db-types="MySQL"/>
    <sql-case id="create_users" value="CREATE USER user1, user2 DEFAULT ROLE role1" db-types="MySQL"/>
    <sql-case id="create_user_if_not_exists" value="CREATE USER IF NOT EXISTS user1 DEFAULT ROLE role1"
              db-types="MySQL"/>
    <sql-case id="create_local_user" value="CREATE USER 'user1'@'localhost' DEFAULT ROLE role1" db-types="MySQL"/>
    <sql-case id="create_user_with_roles" value="CREATE USER user1 DEFAULT ROLE role1, role2" db-types="MySQL"/>
    <sql-case id="create_user_with_password_default_role"
              value="CREATE USER user1 IDENTIFIED BY RANDOM password DEFAULT ROLE role1" db-types="MySQL"/>
    <sql-case id="create_user_with_resource_option"
              value="CREATE USER user1 DEFAULT ROLE role1 WITH MAX_QUERIES_PER_HOUR 1" db-types="MySQL"/>
    <sql-case id="create_user_with_resource_options"
              value="CREATE USER user1 DEFAULT ROLE role1 WITH MAX_QUERIES_PER_HOUR 1 MAX_UPDATES_PER_HOUR 1"
              db-types="MySQL"/>
    <sql-case id="create_user_with_password_option" value="CREATE USER user1 DEFAULT ROLE role1 PASSWORD EXPIRE DEFAULT"
              db-types="MySQL"/>
    <sql-case id="create_user_with_password_options"
              value="CREATE USER user1 DEFAULT ROLE role1 PASSWORD EXPIRE DEFAULT PASSWORD HISTORY DEFAULT"
              db-types="MySQL"/>
    <sql-case id="create_user_with_lock_option" value="CREATE USER user1 DEFAULT ROLE role1 ACCOUNT LOCK"
              db-types="MySQL"/>
    <sql-case id="create_user_with_options"
              value="CREATE USER user1 DEFAULT ROLE role1 WITH MAX_QUERIES_PER_HOUR 1 MAX_UPDATES_PER_HOUR 1 PASSWORD EXPIRE DEFAULT PASSWORD HISTORY DEFAULT ACCOUNT LOCK"
              db-types="MySQL"/>
    <sql-case id="create_external_user" value="CREATE USER user1 IDENTIFIED EXTERNALLY" db-types="Oracle"/>
    <sql-case id="create_global_user" value="CREATE USER user1 IDENTIFIED GLOBALLY" db-types="Oracle"/>
    <sql-case id="create_user_with_password" value="CREATE USER user1 IDENTIFIED BY RANDOM password default role role1"
              db-types="H2,MySQL"/>
    <sql-case id="create_user_with_tablespace"
              value="CREATE USER user1 IDENTIFIED BY password DEFAULT TABLESPACE tablespace1" db-types="Oracle"/>
    <sql-case id="create_user_with_quota_option"
              value="CREATE USER user1 IDENTIFIED BY password QUOTA 1M ON tablespace1" db-types="Oracle"/>
    <sql-case id="create_user_with_password_expire_lock"
              value="CREATE USER user1 IDENTIFIED BY RANDOM password default role role1 PASSWORD EXPIRE ACCOUNT LOCK"
              db-types="H2,MySQL"/>
    <sql-case id="create_user_only_with_name" value="CREATE USER user1" db-types="PostgreSQL,GaussDB,SQLServer"/>
    <sql-case id="create_user_with_password_postgresql" value="CREATE USER user1 WITH ENCRYPTED PASSWORD 'password'"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_with_option_postgresql" value="CREATE USER user1 WITH SUPERUSER"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_with_options_postgresql" value="CREATE USER user1 WITH CREATEDB CREATEROLE"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_with_login" value="CREATE USER user1 FROM LOGIN login1" db-types="SQLServer"/>
    <sql-case id="create_user_with_schema" value="CREATE USER user1 WITH DEFAULT_SCHEMA = schema" db-types="SQLServer"/>
    <sql-case id="create_user_with_no_login" value="CREATE USER user1 WITHOUT LOGIN" db-types="SQLServer"/>
    <sql-case id="create_user_with_certificate" value="CREATE USER user1 FROM CERTIFICATE certificate"
              db-types="SQLServer"/>
    <sql-case id="create_user_with_asym_key" value="CREATE USER user1 FROM ASYMMETRIC KEY asym_key"
              db-types="SQLServer"/>
    <sql-case id="create_user_with_sysid" value="CREATE USER user1 WITH SYSID 10000" db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_with_group" value="CREATE USER user1 IN GROUP group1,group2"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="create_user_with_password_default_language"
              value="CREATE USER user1 WITH PASSWORD='RN92piTCh%$!~3K9844 Bl*', DEFAULT_LANGUAGE=[Brazilian], DEFAULT_SCHEMA=[dbo]"
              db-types="SQLServer"/>
    <sql-case id="create_user_with_domain_login" value="CREATE USER [Contoso\Fritz]" db-types="SQLServer"/>
    <sql-case id="create_user_with_sid"
              value="CREATE USER user1 WITH PASSWORD = 'a8ea v*(Rd##+', SID = 0x01050000000000090300000063FF0451A9E7664BA705B10E37DDC4B7"
              db-types="SQLServer"/>
    <sql-case id="create_user_to_copy_encrypted_data"
              value="CREATE USER [User1] WITH DEFAULT_SCHEMA = dbo, ALLOW_ENCRYPTED_VALUE_MODIFICATIONS = ON"
              db-types="SQLServer"/>
    <sql-case id="create_azure_ad_user_with_login" value="CREATE USER [<EMAIL>] FROM LOGIN [<EMAIL>]"
              db-types="SQLServer"/>
    <sql-case id="create_azure_ad_user_as_group_from_login" value="CREATE USER [AAD group] FROM LOGIN [AAD group]"
              db-types="SQLServer"/>
    <sql-case id="create_azure_ad_user_without_login" value="CREATE USER [<EMAIL>] FROM EXTERNAL PROVIDER"
              db-types="SQLServer"/>
    <sql-case id="create _user_with_option" value="CREATE USER 'jon'@'localhost' COMMENT 'Some information about Jon'"
              db-types="MySQL"/>
    <sql-case id="create_user_with_user_auth_option" value="CREATE USER 'mateo'@'localhost' IDENTIFIED BY 'password'
       AND IDENTIFIED WITH authentication_ldap_simple
       AND IDENTIFIED WITH authentication_fido" db-types="MySQL"/>
</sql-cases>
