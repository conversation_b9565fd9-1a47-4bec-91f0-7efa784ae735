<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="pl_sql_create_procedure" db-types="Oracle"
              value="create or replace procedure test_procedure(P_RETVAL out varchar2) is   E_EXCEPTION EXCEPTION;   v_bendate date;   v_enddate date;   v_day     varchar2(30); begin SAVEPOINT cp_SHOPBudget_Gy; P_RETVAL := 0; select upper(trim(to_char(sysdate, 'day'))) into v_day from dual; if v_day = 'MONDAY' then     return; else     case       when v_day = 'MONDAY' then         v_bendate := TRUNC(SYSDATE) - 7;         v_enddate := TRUNC(SYSDATE) - 1; when v_day = 'TUESDAY' then         v_bendate := TRUNC(SYSDATE) - 8;         v_enddate := TRUNC(SYSDATE) - 2; when v_day = 'WEDNESDAY' then         v_bendate := TRUNC(SYSDATE) - 9;         v_enddate := TRUNC(SYSDATE) - 3; when v_day = 'THURSDAY' then         v_bendate := TRUNC(SYSDATE) - 10;         v_enddate := TRUNC(SYSDATE) - 4; when v_day = 'FRIDAY' then         v_bendate := TRUNC(SYSDATE) - 11;         v_enddate := TRUNC(SYSDATE) - 5; when v_day = 'SATURDAY' then         v_bendate := TRUNC(SYSDATE) - 12;         v_enddate := TRUNC(SYSDATE) - 6; when v_day = 'SUNDAY' then         v_bendate := TRUNC(SYSDATE) - 13;         v_enddate := TRUNC(SYSDATE) - 7; else         v_bendate := TRUNC(SYSDATE);         v_enddate := TRUNC(SYSDATE); end case; end if; commit; EXCEPTION   WHEN E_EXCEPTION THEN     ROLLBACK WORK TO SAVEPOINT cp_SHOPBudget_Gy;     DBMS_OUTPUT.PUT_LINE(SQLCODE || '---' || SQLERRM);     P_RETVAL := -1; WHEN OTHERS THEN     P_RETVAL := -1; ROLLBACK WORK TO SAVEPOINT cp_SHOPBudget_Gy; DBMS_OUTPUT.PUT_LINE(SQLCODE || '---' || SQLERRM); end cp_SHOPBudget_Gy;"/>
</sql-cases>
