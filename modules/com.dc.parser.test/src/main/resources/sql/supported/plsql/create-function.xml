<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="pl_sql_create_function" db-types="Oracle" case-types="LITERAL"
              value="CREATE OR REPLACE EDITIONABLE FUNCTION &quot;TEST&quot;.&quot;test_function&quot; ( v_orgid IN NUMBER)&#10;        RETURN NUMBER AS&#10;           v_partAmount NUMBER(9,2);--兼岗人数&#10;           v_factnums NUMBER(9,2);--实际人数&#10;           v_cnt NUMBER;&#10;&#10;        BEGIN&#10;&#10;          v_factnums:=0;&#10;          v_partAmount:=0;&#10;&#10;        BEGIN--1&#10;              --- 部门实际的兼岗人数&#10;              SELECT&#10;                (SELECT  COUNT(b.empid) FROM op_getZusOfOrg(v_orgid) a, md_employee b&#10;                WHERE b.orgid=a.orgid AND b.empkind=8076  AND NVL(b.disabled,0)=0) factnumemps&#10;              into v_factnums FROM dual;&#10;        END;---1&#10;&#10;        BEGIN --2&#10;                --- 部门实际的兼岗人数&#10;              SELECT&#10;                (SELECT COUNT( b.empid)  FROM op_getZusOfOrg(v_orgid) a ,EU_PARTTIME b&#10;                 WHERE  NVL(b.disabled,0)=0  AND b.PTOrgID=a.orgid)  factnumemps&#10;              INTO v_partAmount FROM dual;&#10;        END;--2&#10;        SELECT nvl(v_factnums,0)+nvl(v_partAmount,0) INTO v_factnums FROM dual;&#10;        RETURN v_factnums;&#10;&#10;        END;"/>
</sql-cases>
