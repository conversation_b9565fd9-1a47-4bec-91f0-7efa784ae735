<?xml version="1.0" encoding="UTF-8"?>
<sql-cases>
    <sql-case id="assert_select_with_json_extract_sign_with_parameter_marker"
              value="SELECT * FROM t_order WHERE order_id -&gt; ?" db-types="MySQL"/>
    <sql-case id="assert_select_with_json_unquote_extract_sign_with_parameter_marker"
              value="SELECT * FROM t_order WHERE order_id -&gt;&gt; ?" db-types="MySQL"/>
    <sql-case id="assert_insert_with_first"
              value="INSERT FIRST INTO TABLE_XXX (field1) VALUES (field1) SELECT field1 FROM TABLE_XXX2"
              db-types="Oracle"/>
    <sql-case id="assert_dist_SQL_show_rule_parse_conflict" value="SHOW REPLICA_QUERY RULE FROM schema_name"/>
    <sql-case id="with_select" value="WITH cte AS (SELECT 0 /*! ) */ SELECT * FROM cte a, cte b;" db-types="MySQL"/>
    <sql-case id="with_select_comment" value="WITH cte AS /*! ( */ SELECT 0) SELECT * FROM cte a, cte b;"
              db-types="MySQL"/>
    <sql-case id="create_table_as_select" value="create table agg_data_2k as select g from generate_series(0, 1999) g;"
              db-types="PostgreSQL"/>
    <sql-case id="create_temp_table"
              value="create temp table old_oids as select relname, oid as oldoid, relfilenode as oldfilenode from pg_class where relname like 'at_partitioned%'"
              db-types="PostgreSQL"/>
    <sql-case id="select_case_when"
              value="select relname,c.oid = oldoid as orig_oid,case relfilenode when 0 then 'none' when c.oid then 'own' when oldfilenode then 'orig' else 'OTHER' end as storage, obj_description(c.oid, 'pg_class') as desc from pg_class c left join old_oids using (relname) where relname like 'at_partitioned%' order by relname"
              db-types="PostgreSQL"/>
    <sql-case id="select_like"
              value="select conname, obj_description(oid, 'pg_constraint') as desc from pg_constraint where conname like 'at_partitioned%' order by conname"
              db-types="PostgreSQL"/>
    <sql-case id="select_keyword"
              value="select relname,c.oid = oldoid as orig_oid,case relfilenode when 0 then 'none' when c.oid then 'own' when oldfilenode then 'orig' else 'OTHER' end as storage, obj_description(c.oid, 'pg_class') as desc from pg_class c left join old_oids using (relname) where relname like 'at_partitioned%' order by relname"
              db-types="PostgreSQL"/>
    <sql-case id="unsupported_select_case_for_opengauss_13"
              value="select count(*) from partition_list_tab01 partition for ('20');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_14"
              value="select count(*) from partition_list_tab partition for ('10');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_15"
              value="select count(*) from partition_list_tab partition for ('20');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_16"
              value="select count(*) from partition_list_tab partition for ('30');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_25" value="select ''null''::jsonb;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_26" value="select ''null''::json;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_27"
              value="select &amp;quot;{&amp;quot;name&amp;quot;: [&amp;quot;john&amp;quot;, false],  &amp;quot;age&amp;quot;:    18,  &amp;quot;assress&amp;quot;:  {&amp;quot;country&amp;quot; :&amp;quot;china&amp;quot;, &amp;quot;zip-code&amp;quot;: &amp;quot;10000&amp;quot;},&amp;quot;true&amp;quot;:true}&amp;quot;::JSON;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_28"
              value="select {&amp;quot;name&amp;quot;: [&amp;quot;john&amp;quot;, false],  &amp;quot;age&amp;quot;:    18,  &amp;quot;assress&amp;quot;:  {&amp;quot;country&amp;quot; :&amp;quot;china&amp;quot;, &amp;quot;zip-code&amp;quot;: &amp;quot;10000&amp;quot;},true:true}::JSON;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_29"
              value="select &amp;quot;{&amp;quot;name&amp;quot;,&amp;quot;john&amp;quot;,  &amp;quot;age&amp;quot;}&amp;quot;::JSON;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_30"
              value="select '[null,true,false,&amp;quot;null&amp;quot;,&amp;quot;true&amp;quot;,&amp;quot;false&amp;quot;]'::json -&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_31"
              value="select '[138,0.58,-369,1.25e+6]'::json -&gt; ***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_32"
              value="select '\&amp;quot;$$\&amp;quot;'::json -&gt;***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_33"
              value="select [&amp;quot;www@13^&amp;quot;, 1, {&amp;quot;name&amp;quot;: &amp;quot;john&amp;quot;}, &amp;quot;2&amp;quot;, &amp;quot; &amp;quot;,true,&amp;quot;null&amp;quot;]::JSON;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_34"
              value="select '[&amp;quot;www@13^&amp;quot;, 1, {&amp;quot;name&amp;quot;: &amp;quot;john&amp;quot;}, '2',,true,&amp;quot;null&amp;quot;]'::JSON;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_35"
              value="select jsonb_ge('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_36"
              value="select jsonb_ge('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_37"
              value="select jsonb_ge('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_38"
              value="select jsonb_ge('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_39"
              value="select jsonb_extract_path_op ('[{&amp;quot;a&amp;quot;:&amp;quot;foo&amp;quot;},{&amp;quot;b&amp;quot;:&amp;quot;bar&amp;quot;},{&amp;quot;c&amp;quot;:&amp;quot;baz&amp;quot;}]',{2});"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_40" value="select jsonb_extract_path_op('null',‘{1)’;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_41" value="select jsonb_extract_path_text('null',);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_42"
              value="select jsonb_gt('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_43"
              value="select jsonb_gt('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_44"
              value="select jsonb_gt('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_45"
              value="select jsonb_gt('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_46"
              value="select jsonb_lt('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_47"
              value="select jsonb_lt('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_48"
              value="select jsonb_lt('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_49"
              value="select jsonb_lt('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_50"
              value="select jsonb_extract_path_text_op ('[{&amp;quot;a&amp;quot;:&amp;quot;foo&amp;quot;},{&amp;quot;b&amp;quot;:&amp;quot;bar&amp;quot;},{&amp;quot;c&amp;quot;:&amp;quot;baz&amp;quot;}]',{2});"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_51" value="select jsonb_extract_path_text_op('null',‘{1)’;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_52"
              value="select jsonb_le('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_53"
              value="select jsonb_le('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_54"
              value="select jsonb_le('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_55"
              value="select jsonb_le('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_56"
              value="select jsonb_exists_any('{&amp;quot;a&amp;quot;:1, &amp;quot;b&amp;quot;: [1,2,3],&amp;quot;c&amp;quot;:{&amp;quot;b&amp;quot;:&amp;quot;d&amp;quot;}}', array[{'c':'b'}]);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_57"
              value="select jsonb_exists_any('[&amp;quot;abcdefg&amp;quot;,138,&amp;quot;{\&amp;quot;db\&amp;quot;:\&amp;quot;test\&amp;quot;}&amp;quot;,null,&amp;quot;true&amp;quot;,false]',array{&amp;quot;db&amp;quot;:&amp;quot;test&amp;quot;});"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_58"
              value="select jsonb_exists_any('[&amp;quot;abcdefg&amp;quot;,138,&amp;quot;{\&amp;quot;db\&amp;quot;:\&amp;quot;test\&amp;quot;}&amp;quot;,null,&amp;quot;true&amp;quot;,false]',array[{'db':'test'}]);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_59"
              value="select jsonb_exists_all('{&amp;quot;a&amp;quot;:1, &amp;quot;b&amp;quot;: [1,2,3],&amp;quot;c&amp;quot;:{&amp;quot;b&amp;quot;:&amp;quot;d&amp;quot;}}','{'b','d'}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_60"
              value="select jsonb_exists_all('[null, false, 123,{&amp;quot;a&amp;quot;:true},&amp;quot;test&amp;quot;]',array'[123,'{a}']');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_61"
              value="select sum(score)fromtab136whereclass=2andcourse='数学';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_62"
              value="select max(score)fromtab136whereclass=2andcourse='数学';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_63"
              value="select min(score)fromtab136whereclass=2andcourse='数学';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_64"
              value="select avg(score)fromtab136whereclass=1andcourse='数学';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_65"
              value="select jsonb_cmp('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_66"
              value="select jsonb_cmp('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_67"
              value="select jsonb_cmp('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_68"
              value="select jsonb_cmp('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_69" value="select json_extract_path_text('null',);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_70"
              value="select jsonb_eq('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_71"
              value="select jsonb_eq('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_72"
              value="select jsonb_eq('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_73"
              value="select jsonb_eq('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_74"
              value="select jsonb_ne('[{a:false},{a:true},123,'qwer','null']','{&amp;quot;a&amp;quot;:false}');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_75"
              value="select jsonb_ne('[{a:false},{a:true},123,'qwer','null']','123');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_76"
              value="select jsonb_ne('[{a:false},{a:true},123,'qwer','null']','qwer');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_77"
              value="select jsonb_ne('[{a:false},{a:true},123,'&amp;quot;qwer&amp;quot;','null']','qwer');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_78"
              value="select '[null,true,false,&amp;quot;null&amp;quot;,&amp;quot;true&amp;quot;,&amp;quot;false&amp;quot;]'::jsonb -&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_79"
              value="select '[138,0.58,-369,1.25e+6]'::jsonb -&gt; ***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_80"
              value="select '\&amp;quot;$$\&amp;quot;'::jsonb -&gt;***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_81"
              value="select '{&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: {&amp;quot;a&amp;quot;: 2, &amp;quot;b&amp;quot;: null}}'::jsonb  -&gt;&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_82"
              value="select '{&amp;quot;foo&amp;quot;: [true, &amp;quot;bar&amp;quot;], &amp;quot;tags&amp;quot;: {&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: null}}'::jsonb -&gt;&gt; ***************/25;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_83"
              value="select '\&amp;quot;$$\&amp;quot;'::jsonb -&gt;&gt;***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_84"
              value="select '[{&amp;quot;a&amp;quot;:true}, null] '::  jsonb @&gt; [null];" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_85"
              value="select '{&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: {&amp;quot;a&amp;quot;: 2, &amp;quot;b&amp;quot;: null}}'::json  -&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_86"
              value="select '{&amp;quot;foo&amp;quot;: [true, &amp;quot;bar&amp;quot;], &amp;quot;tags&amp;quot;: {&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: null}}'::json -&gt; ***************/25;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_87"
              value="select '{&amp;quot;a&amp;quot;:null, &amp;quot;bb&amp;quot;: 1, &amp;quot;bb&amp;quot;: &amp;quot;A&amp;quot;,   &amp;quot;cde&amp;quot;: [1,2,   &amp;quot;re&amp;quot;], &amp;quot;abc&amp;quot;: 1}':: -&gt; true;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_88"
              value="select '{&amp;quot;a&amp;quot;:null, &amp;quot;[1,2,\&amp;quot;re\&amp;quot;]&amp;quot;: 1, &amp;quot;bb&amp;quot;: &amp;quot;A&amp;quot;, &amp;quot;cde&amp;quot;: [1,2,   &amp;quot;re&amp;quot;], &amp;quot;abc&amp;quot;: 1}':: -&gt; null;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_89"
              value="select '[null,true,false,&amp;quot;null&amp;quot;,&amp;quot;true&amp;quot;,&amp;quot;false&amp;quot;]'::json -&gt;&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_90"
              value="select '[138,0.58,-369,1.25e+6]'::json -&gt;&gt; ***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_91"
              value="select '\&amp;quot;$$\&amp;quot;'::json -&gt;&gt;***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_92"
              value="select '[{&amp;quot;a&amp;quot;:true}, null] '::  jsonb &lt;@ [null];" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_93"
              value="select '{&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: {&amp;quot;a&amp;quot;: 2, &amp;quot;b&amp;quot;: null}}'::jsonb  -&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_94"
              value="select '{&amp;quot;foo&amp;quot;: [true, &amp;quot;bar&amp;quot;], &amp;quot;tags&amp;quot;: {&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: null}}'::jsonb -&gt; ***************/25;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_95"
              value="select '[null,true,false,&amp;quot;null&amp;quot;,&amp;quot;true&amp;quot;,&amp;quot;false&amp;quot;]'::jsonb -&gt;&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_96"
              value="select '[138,0.58,-369,1.25e+6]'::jsonb -&gt;&gt; ***************/25;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_97"
              value="select '{&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: {&amp;quot;a&amp;quot;: 2, &amp;quot;b&amp;quot;: null}}'::json  -&gt;&gt; 2021-05-31 12:59:08;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_98"
              value="select '{&amp;quot;foo&amp;quot;: [true, &amp;quot;bar&amp;quot;], &amp;quot;tags&amp;quot;: {&amp;quot;a&amp;quot;: 1, &amp;quot;b&amp;quot;: null}}'::json -&gt;&gt; ***************/25;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_105"
              value="select 'a fat cat sat on a mat and ate a fat rat'::tsvector;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_106"
              value="select $$the lexeme '    ' contains spaces$$::tsvector;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_107"
              value="select 'a:1 fat:2 cat:3 sat:4 on:5 a:6 mat:7 and:8 ate:9 a:10 fat:11 rat:12'::tsvector;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_108" value="select 'a:1A fat:2B,4C cat:5D'::tsvector;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_109" value="select ASCII(#);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_110" value="select ASCII(=);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_111" value="select char_length($$jjslf$$);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_112" value="select char_length($$jj' 'slf$$);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_113"
              value="select last(s_name) as name, last(s_id nulls last ) from last06;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_114" value="select NULLIF(,);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_115" value="select NULLIF();" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_116" value="select nullif(#,&amp;) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_117" value="select nullif(#,#) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_118" value="select nullif((,)) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_119" value="select nullif(@,@) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_120" value="select nullif(+,-) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_121" value="select nullif(!,~) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_127"
              value="select row_to_json(row(interval '3' day)) from sys_dummy;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_129" value="select row_to_json(row(B'101')) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_130"
              value="select row_to_json(row('The Fat Rats'::tsvector)) from sys_dummy;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_132" value="select array_prepend(@,array[1,2]) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_133"
              value="select array_cat(array[[1,2,3],[2,5]], @) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_134" value="select array_append(array[1,2],@) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_135"
              value="select array_dims(array[[7,1,@], [1,3,6]]) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_136" value="select array_length(array[1,2,3], ) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_137" value="select array_upper(array[[1,8,3,7]], #) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_138" value="select array_upper(array[[1,8,3,7]], ) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_139" value="select array_lower([1,2,3], 1) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_140" value="select array_lower(array[1,2,3], @) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_141" value="select array_lower(array[1,2,3], ) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_174" value="select bitand(6,) as result from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_175" value="select bitand(,6) as result from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_176"
              value="select first(s_name) as name, first(s_id nulls first ) from first06;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_177" value="select notlike(1, );" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_178" value="select sin(2  2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_179" value="select sin(2)(2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_181" value="select acos(2  2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_182" value="select acos(2)(2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_183" value="select acos 1 as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_184" value="select count(none) from tbc;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_185" value="select reset_unique_sql('global','all',);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_186"
              value="select pg_stat_get_tuples_hot_updated(’87654345888765#￥%……&amp;*‘) from PG_CLASS a where a.relname = 'sales';"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_187" value="select to_hex(@#￥);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_188"
              value="select array_replace(array[1.23,2.25,33,5.0],(select @ -5.0 as result),(select @ 8.0 as result));"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_189"
              value="select array_replace(array[1.23,2.25,33,5.0],(select substrb('string',2,3)),(select @ 8.0 as result));"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_190"
              value="select array_replace(array['string','str','st','ring'],(select @ -5.0 as result),(select repeat('ab', 3)));"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_191" value="select asin(2  2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_192" value="select asin(2)(2) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_193" value="select asin 1 as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_195"
              value="select tsrange('[2021-01-01,2021-03-01)') + '[3,4]'::int4range  as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_197"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') &gt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_198"
              value="select tsrange('[2021-01-01,2028-03-01)') &gt; ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_206"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') &lt;= ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_207"
              value="select tsrange('[2021-01-01,2028-03-01)') &lt;= ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_210"
              value="select tsrange('[2021-01-01,2021-03-01)') *('[2021-03-01,2021-10-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_211"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') *('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_212"
              value="select tsrange('[2021-01-01,2021-03-01)') * ('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_217"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') &lt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_218"
              value="select tsrange('[2021-01-01,2021-03-01)') &lt; ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_224"
              value="select tsrange('[2021-01-01,2021-03-01)') +('[2021-03-01,2021-10-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_225"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') +('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_226"
              value="select tsrange('[2021-01-01,2021-03-01)') + ('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_233"
              value="select lower_inc(tsrange('[2021-01-01,2021-03-01)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_234"
              value="select lower_inc(tsrange('[2013-12-11 pst,2021-03-01 pst)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_235"
              value="select isempty(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_236"
              value="select isempty(bugstatus (create, closed)) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_239"
              value="select tsrange('[2021-01-01,2021-03-01)') -|-('[2021-03-01,2021-10-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_240"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') -|-('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_245"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') &lt;&gt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_249"
              value="select tsrange('[2021-01-01,2021-03-01)') &lt;&gt; ('[2021-01-01,2021-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_255"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') &gt;= ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_256"
              value="select tsrange('[2021-01-01,2028-03-01)') &gt;= ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_259"
              value="select tsrange('[2010-5-11 pst,2011-03-01 pst)') &lt;&lt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_260"
              value="select tsrange('[1999-01-01,2000-05-01)') &lt;&lt; ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_268"
              value="select tsrange('[2010-5-11 pst,2011-03-01 pst)') &gt;&gt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_269"
              value="select tsrange('[1999-01-01,2000-05-01)') &gt;&gt; ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_272"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') @&gt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_273"
              value="select tsrange('[2021-01-01,2028-03-01)') @&gt; ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_279"
              value="select tsrange('[2021-01-01,2021-03-01)') = ('[2021-01-01,2021-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_281"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') = ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_284"
              value="select tsrange('[2021-01-01,2021-03-01)') &amp;&gt; ('[2021-01-01,2021-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_285"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') &amp;&gt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_292"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') &lt;@ ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_293"
              value="select tsrange('[2021-01-01,2028-03-01)') &lt;@ ('[2021-01-01,2021-05-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_295"
              value="select tsrange('[2013-12-11 pst,2025-03-01 pst)') @&gt; '2014-12-11 pst'::timestamp  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_296"
              value="select tsrange('[2021-01-01,2028-03-01)') @&gt; '2026-09-09'::timestamp as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_301"
              value="select tsrange('[2021-01-01,2021-03-01)') &amp;&lt; ('[2021-01-01,2021-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_302"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') &amp;&lt; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_306"
              value="select tsrange('[2021-01-01,2021-03-01)') - '[3,4]'::int4range  as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_315"
              value="select lower(tsrange('[2021-01-01,2021-03-01)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_316"
              value="select lower(tsrange('[2013-12-11 pst,2021-03-01 pst)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_320"
              value="select lower_inf(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_321"
              value="select lower_inf(bugstatus (create, closed)) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_324"
              value="select '2014-12-11 pst'::timestamp &lt;@ tsrange('[2013-12-11 pst,2025-03-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_325"
              value="select '2026-09-09'::timestamp &lt;@ tsrange('[2021-01-01,2028-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_330"
              value="select tsrange('[2021-01-01,2021-03-01)') &amp;&amp; ('[2021-01-01,2021-03-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_331"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') &amp;&amp; ('[2013-12-11 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_338"
              value="select upper_inf(tsrange('(2021-01-01,]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_339"
              value="select upper_inf(tsrange('[,2021-03-01 pst]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_347"
              value="select tsrange('[2021-01-01,2021-03-01)') -('[2021-03-01,2021-10-01)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_348"
              value="select tsrange('[2013-12-11 pst,2021-03-01 pst)') -('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_349"
              value="select tsrange('[2021-01-01,2021-03-01)') - ('[2021-3-01 pst,2021-05-01 pst)') as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_355"
              value="select upper(tsrange('[2021-01-01,2021-03-01)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_356"
              value="select upper(tsrange('[2013-12-11 pst,2021-03-01 pst)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_360"
              value="select upper_inc(tsrange('(2021-01-01,2021-03-01]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_361"
              value="select upper_inc(tsrange('[2013-12-11 pst,2021-03-01 pst]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_362"
              value="select lower_inc(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_363"
              value="select lower_inc(bugstatus (create, closed)) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_364"
              value="select lower(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_365" value="select lower(bugstatus (create, closed)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_367"
              value="select tsrange('[2021-01-01,2021-03-01)') * '[3,4]'::int4range  as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_368"
              value="select upper_inc(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_369"
              value="select upper_inc(bugstatus (create, closed)) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_370"
              value="select upper(macaddr('08:00:2b:01:02:03'::macaddr ,'08:00:2b:01:02:03'::macaddr)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_371" value="select upper(bugstatus (create, closed)) as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_375"
              value="select isempty(tsrange('[2021-01-01,2021-03-01)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_376"
              value="select isempty(tsrange('[2013-12-11 pst,2021-03-01 pst)')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_381"
              value="select lower_inf(tsrange('(2021-01-01,2021-03-01]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_382"
              value="select lower_inf(tsrange('[,2021-03-01 pst]')) as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_383" value="select hll_hash_boolean(@,10);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_384" value="select hll_hash_boolean(true, @#);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_385" value="select hll_hash_boolean(@);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_386" value="select hll_hash_bytea(@##$);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_387" value="select hll_hash_any('08:00:2b:01:02:03'::macaddr);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_388"
              value="select hll_hash_any ('[2010-01-01 14:30, 2010-01-01 15:30)'::tsrange);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_389"
              value="select hll_hash_any ('a fat cat sat on a mat and ate a fat rat'::tsvector);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_390"
              value="select hll_hash_any('08:00:2b:01:02:03'::macaddr, 2147483647);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_391"
              value="select hll_hash_any ('[2010-01-01 14:30, 2010-01-01 15:30)'::tsrange, 10);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_392"
              value="select hll_hash_any ('a fat cat sat on a mat and ate a fat rat'::tsvector, 30);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_393" value="select hll_hash_text(`天天开心`);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_394" value="select trim('2' '298082');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_395"
              value="select trim(leading '1','2' from '1uoiusf8','2iouf899');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_396"
              value="select trim(both '1','2' from '1uoiusf8','2iouf899');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_397" value="select trim(trailing '1','2' from '1009-02-01');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_398" value="select trim(both '2' from );" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_399" value="select trim(leading'2' from );"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_400" value="select trim(trailing '2' from );"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_401" value="select substring(from 4 for 4) as text1;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_406" value="select @clo1, @clo2 from data_01;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_407" value="select @ clo1, @clo2 from data_01;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_410" value="select @ clo1, @ clo2 from data_01;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_416" value="select @clo1 from data_01;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_417" value="select cos(2*pi(),2*pi()));" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_420" value="select |/clo1 from data_01;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_423" value="select |/clo2 from data_01;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_424" value="select |/clo1,|/clo2 from data_01;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_425" value="select  |/clo1 from data_01;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_426" value="select  |/clo2 from data_01;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_433" value="select atan2(,) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_434" value="select atan2('11+11',) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_435" value="select atan2(11 11) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_436" value="select atan2(22,) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_437" value="select B'11110' | B'0000';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_438" value="select B'101' | as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_439" value="select B'101' # as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_440" value="select B'11110' # B'0000';" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_447" value="select atan(11 11) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_448" value="select atan(,) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_451"
              value="select $$the lexeme '    '  , contains spaces$$::tsvector@@ ','::tsquery  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_452"
              value="select setweight(body :: tsvector, 'a') from ts_zhparser;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_455"
              value="select 'fat cats ate'::tsvector || 'a b c'::tsvector;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_458"
              value="select 'a:1 b:2'::tsvector || 'c:1 d:2 b:3'::tsvector as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_459"
              value="select 'a b'::tsvector || 'c:1 d:2 b:3'::tsvector as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_460"
              value="select setweight('fat:2,4 cat:3 rat:5b'::tsvector, 1);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_461"
              value="select setweight('fat:2,4 cat:3 rat:5b'::tsvector, '');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_462"
              value="select $$the lexeme '    '  , contains spaces$$::tsvector || 'c:1 d:2 b:3'::tsvector as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_464" value="select length(1::tsvector);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_465" value="select strip(body :: tsvector) from ts_zhparser;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_466"
              value="select length('a b c'::tsvector  || 'd e f'::tsvector);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_467" value="select length('fat:2,4 cat:3 rat:5a'::tsvector);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_468"
              value="select length($$the lexeme '    ' contains spaces$$::tsvector);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_469" value="select length('fat:2,4 cat:3'::tsvector);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_472" value="select length('fat rat'::tsvector);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_484"
              value="select 'fat cats ate fat rats'::tsvector @@ ('fat'::tsquery  &amp;&amp; 'ate'::tsquery)  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_487"
              value="select 'fat cats ate fat rats'::tsvector @@ ('fat'::tsquery  &amp;&amp; 'ateee'::tsquery)  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_491"
              value="select  to_tsvector('fat cats ate ate' )|| 'a b c'::tsvector  || 'd e f'::tsvector as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_492"
              value="select 'fat cats ate fat rats'::tsvector @@ ('fat'::tsquery ||  'ateee'::tsquery)  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_493"
              value="select  'a:1a '::tsvector|| 'b:1a '::tsvector as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_494"
              value="select 'fat cats ate fat rats'::tsvector @@ to_tsquery('fat &amp; rat')as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_495"
              value="select 'a:1 fat:2 cat:3 sat:4 on:5'::tsvector @@ 'fat'::tsquery  as result;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_497"
              value="select 'fat cats ate fat rats'::tsvector @@ ('fat11'::tsquery ||  'ateee'::tsquery)  as result;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_503" value="select to_char(interval '2' year ,'yyyy','yyyy');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_504" value="select to_char(interval 2 year);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_505" value="select to_char(current_timestamp, 0x5d);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_506" value="select to_char(pg_systimestamp(), #%&amp;^);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_507" value="select to_char(-1239.456::numeric(7,3),''汉字'');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_514" value="select to_char(interval '2' year ,'yyyy');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_515" value="select to_char(interval '3' day ,'ddd');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_516" value="select to_char(interval '3' day ,'dd');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_517" value="select to_char(interval '3' day ,'天');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_518" value="select to_char(interval '2' year ,'dd');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_519" value="select numtoday(2f);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_520" value="select numtoday(@#$);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_521" value="select to_char(156*&amp;%$%^x);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_522" value="select to_number( , '99g999d9s');"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_523"
              value="select to_number(&lt;1*8+9/3-9%3+9/2&gt;, '999999d99');" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_524" value="select to_timestamp(-#&amp;%#^5);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_527" value="select coalesce(,1,null,2);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_528" value="select coalesce();" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_529"
              value="select localtimestamp('skdfhhgh跳或者购买功能') from sys_dummy;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_570" value="select sign(~);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_571" value="select tan(11 11) from tan_T1;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_572" value="select tan(,) from tan_T1;" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_573" value="select mod(154414,,44) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_574" value="select mod(,) from sys_dummy;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_575"
              value="select median(days) from (values(interval '4' day), (interval '5' day), (interval '6' day), (interval '7' day)) as test(days);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_576" value="select every(array[1.1,2.1,3.1]::int[] =[1,2,3]);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_577"
              value="select bool_and(array[1.1,2.1,3.1]::int[] =[1,2,3]);" db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_578" value="select string_agg(name, #) from table_test;"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_579" value="select bool_or(array[1.1,2.1,3.1]::int[] =[1,2,3]);"
              db-types="GaussDB"/>
    <sql-case id="unsupported_select_case_for_opengauss_580" value="select bool_or(@);" db-types="GaussDB"/>
    <sql-case id="assert_distsql_show_rule_parse_conflict" value="SHOW READWRITE_SPLITTING RULE FROM schema_name"
              db-types="PostgreSQL,GaussDB"/>
    <sql-case id="assert_distsql_rollback_migration"
              value="ROLLBACK MIGRATION 10102p0000697d5ccb2e3d960d0gif75c7c7f486fal"
              db-types="MySQL,PostgreSQL,GaussDB,Oracle"/>
    <sql-case id="assert_show_databases_parse_conflict" value="SHOW DATABASES" db-types="Oracle"/>
    <sql-case id="assert_show_schemas_parse_conflict" value="SHOW SCHEMAS" db-types="Oracle"/>
    <sql-case id="assert_show_tables_parse_conflict" value="SHOW TABLES" db-types="Oracle"/>
    <!-- TODO open this case when MySQLStatement change to SEMI_? EOF -->
    <!--    <sql-case id="assert_parse_multi_statements" value="SHOW VARIABLES LIKE 'sql_mode'; SELECT COUNT(*) AS support_ndb FROM information_schema;" db-types="MySQL" />-->
</sql-cases>
