
package com.dc.summer.ext.gaussdb.model;

import com.dc.summer.ext.postgresql.model.PostgreDataSourceInfo;
import com.dc.summer.model.exec.jdbc.JDBCDatabaseMetaData;
import lombok.extern.slf4j.Slf4j;

/**
 * GaussDBDataSourceInfo
 */
@Slf4j
class GaussDBDataSourceInfo extends PostgreDataSourceInfo {

    public GaussDBDataSourceInfo(GaussDBDataSource dataSource, JDBCDatabaseMetaData metaData) {
        super(dataSource, metaData);
    }

}
