

package com.dc.summer.ext.snowflake.model.auth;

import com.dc.summer.DBException;
import com.dc.summer.ext.snowflake.SnowflakeConstants;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNative;
import com.dc.summer.model.impl.auth.AuthModelDatabaseNativeCredentials;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.utils.CommonUtils;

import java.util.Properties;

/**
 * Oracle database native auth model.
 */
public class SnowflakeAuthModelSnowflake extends AuthModelDatabaseNative<AuthModelDatabaseNativeCredentials> {

    public static final String ID = "snowflake_snowflake";

    @Override
    public Object initAuthentication(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration, @NotNull Properties connProperties) throws DBException {
        if (connProperties.getProperty("authenticator") == null) {
            // If "authenticator" is already set by user then do not change it
            String authenticator = getAuthenticator(dataSource, credentials, configuration);
            if (!CommonUtils.isEmpty(authenticator)) {
                connProperties.put("authenticator", authenticator);
            }
        }
        String roleName = configuration.getAuthProperty(SnowflakeConstants.PROP_AUTH_ROLE);
        if (!CommonUtils.isEmpty(roleName)) {
            connProperties.put("role", roleName);
        }

        return super.initAuthentication(monitor, dataSource, credentials, configuration, connProperties);
    }

    @Override
    public void endAuthentication(@NotNull DBPDataSourceContainer dataSource, @NotNull DBPConnectionConfiguration configuration, @NotNull Properties connProperties) {
        super.endAuthentication(dataSource, configuration, connProperties);
    }

    protected String getAuthenticator(DBPDataSource dataSource, AuthModelDatabaseNativeCredentials credentials, DBPConnectionConfiguration configuration) {
        return configuration.getAuthProperty(SnowflakeConstants.PROP_AUTHENTICATOR);
    }

}
