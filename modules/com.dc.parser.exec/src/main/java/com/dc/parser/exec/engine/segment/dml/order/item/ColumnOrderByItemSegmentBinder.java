package com.dc.parser.exec.engine.segment.dml.order.item;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.type.ColumnSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.order.item.ColumnOrderByItemSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Column order by item segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ColumnOrderByItemSegmentBinder {

    /**
     * Bind column order by item segment.
     *
     * @param segment                  column order by item segment
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @param segmentType              segment type
     * @return bound column order by item segment
     */
    public static ColumnOrderByItemSegment bind(final ColumnOrderByItemSegment segment, final SQLStatementBinderContext binderContext,
                                                final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                                final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts, final SegmentType segmentType) {
        return new ColumnOrderByItemSegment(ColumnSegmentBinder.bind(segment.getColumn(), segmentType, binderContext, tableBinderContexts, outerTableBinderContexts), segment.getOrderDirection(),
                segment.getNullsOrderType().orElse(null));
    }
}
