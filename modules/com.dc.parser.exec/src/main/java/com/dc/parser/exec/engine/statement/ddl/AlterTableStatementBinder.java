package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Alter table statement binder.
 */
public final class AlterTableStatementBinder implements SQLStatementBinder<AlterTableStatement> {

    @Override
    public AlterTableStatement bind(final AlterTableStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        AlterTableStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        binderContext.setOperation(SqlConstant.KEY_ALTER);
        result.setTable(SimpleTableSegmentBinder.bind(sqlStatement.getTable(), binderContext, tableBinderContexts));
        sqlStatement.getRenameTable().ifPresent(optional -> result.setRenameTable(SimpleTableSegmentBinder.bind(optional, binderContext, tableBinderContexts)));
        return result;
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static AlterTableStatement copy(final AlterTableStatement sqlStatement) {
        AlterTableStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        // TODO bind column and reference table if kernel need use them
        sqlStatement.getConvertTableDefinition().ifPresent(result::setConvertTableDefinition);
        result.getAddColumnDefinitions().addAll(sqlStatement.getAddColumnDefinitions());
        result.getModifyColumnDefinitions().addAll(sqlStatement.getModifyColumnDefinitions());
        result.getDropColumnDefinitions().addAll(sqlStatement.getDropColumnDefinitions());
        result.getAddConstraintDefinitions().addAll(sqlStatement.getAddConstraintDefinitions());
        result.getValidateConstraintDefinitions().addAll(sqlStatement.getValidateConstraintDefinitions());
        result.getModifyConstraintDefinitions().addAll(sqlStatement.getModifyConstraintDefinitions());
        result.getDropConstraintDefinitions().addAll(sqlStatement.getDropConstraintDefinitions());
        result.getDropIndexDefinitions().addAll(sqlStatement.getDropIndexDefinitions());
        result.getRenameColumnDefinitions().addAll(sqlStatement.getRenameColumnDefinitions());
        result.getRenameIndexDefinitions().addAll(sqlStatement.getRenameIndexDefinitions());
        sqlStatement.getModifyCollectionRetrieval().ifPresent(result::setModifyCollectionRetrieval);
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
