package com.dc.parser.exec.engine.segment.dml.from.context;

import com.dc.parser.model.segment.dml.item.ProjectionSegment;

import java.util.Collection;
import java.util.Optional;

/**
 * Table segment binder context.
 */
public interface TableSegmentBinderContext {

    /**
     * Find projection segment by column label.
     *
     * @param columnLabel column label
     * @return projection segment
     */
    Optional<ProjectionSegment> findProjectionSegmentByColumnLabel(String columnLabel);

    /**
     * Get projection segments.
     *
     * @return projection segments
     */
    Collection<ProjectionSegment> getProjectionSegments();
}
