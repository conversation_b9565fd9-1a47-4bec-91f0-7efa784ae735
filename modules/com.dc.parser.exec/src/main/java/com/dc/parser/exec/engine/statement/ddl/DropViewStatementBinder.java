package com.dc.parser.exec.engine.statement.ddl;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.segment.dml.from.type.SimpleTableSegmentBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinder;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.statement.ddl.DropViewStatement;
import com.dc.summer.parser.sql.constants.SqlConstant;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import lombok.SneakyThrows;

/**
 * Drop view statement binder.
 */
public final class DropViewStatementBinder implements SQLStatementBinder<DropViewStatement> {

    @Override
    public DropViewStatement bind(final DropViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        DropViewStatement result = copy(sqlStatement);
        Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts = LinkedHashMultimap.create();
        sqlStatement.getViews().forEach(each -> result.getViews().add(SimpleTableSegmentBinder.bind(each, binderContext, tableBinderContexts)));
        return result;
    }

    public void extractSqlAuthModel(final DropViewStatement sqlStatement, final SQLStatementBinderContext binderContext) {
        sqlStatement.getViews().forEach(simpleTableSegment -> {
            SqlAuthModel sqlAuthModel = new SqlAuthModel();
            sqlAuthModel.setType(SqlConstant.KEY_VIEW);
            sqlAuthModel.setOperation(SqlConstant.KEY_DROP);
            sqlAuthModel.setName(simpleTableSegment.getTableName().getIdentifier().getValue());
            String schemaName = simpleTableSegment.getOwner().map(ownerSegment -> ownerSegment.getIdentifier().getValue()).orElse(binderContext.getCurrentDatabaseName());
            sqlAuthModel.setSchemaName(schemaName);
            binderContext.addSqlAuthModel(sqlAuthModel);
        });
    }

    @SneakyThrows(ReflectiveOperationException.class)
    private static DropViewStatement copy(final DropViewStatement sqlStatement) {
        DropViewStatement result = sqlStatement.getClass().getDeclaredConstructor().newInstance();
        result.addParameterMarkerSegments(sqlStatement.getParameterMarkerSegments());
        result.getCommentSegments().addAll(sqlStatement.getCommentSegments());
        result.getVariableNames().addAll(sqlStatement.getVariableNames());
        return result;
    }
}
