package com.dc.summer.parser.utils;

import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DSourceToken;
import com.dc.sqlparser.nodes.*;
import com.dc.sqlparser.nodes.mysql.TGroupConcatParam;
import com.dc.sqlparser.stmt.TSelectSqlStatement;
import com.dc.sqlparser.types.EDbType;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ETableSource;
import com.dc.summer.parser.sql.model.ActionColumnEntry;
import com.dc.summer.parser.sql.model.ActionTableEntry;
import com.dc.summer.parser.sql.model.ColumnData;
import com.dc.summer.parser.sql.type.ClauseType;
import com.dc.summer.parser.utils.model.ActionAliasEntry;
import com.dc.summer.parser.utils.model.ActionResultEntry;
import com.dc.type.DatabaseType;
import com.dc.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.List;
import java.util.*;

public class GetQueryFieldsUtil extends GetColumnsUtil {

    private List<ActionAliasEntry> aliases = new ArrayList<>();
    private Map<String, List<ActionResultEntry>> dependMap = new LinkedHashMap<>();
    private Map<String, TCTE> cteMap = new LinkedHashMap<>();
    private Map<String, LinkedHashMap<DCustomSqlStatement, Boolean>> accessMap = new LinkedHashMap<>();
    private Map<DCustomSqlStatement, ClauseType> currentClauseMap = new LinkedHashMap<>();
    private String currentSource = null;
    private DCustomSqlStatement subquery = null;

    private Integer dbType;
    private List<ColumnData> columnDataList = new ArrayList<>();

    private Set<String> sqlSet = new HashSet<>();

    private List<String> FUNCTIONS = Arrays.asList("COUNT", "SUM", "ROW_NUMBER", "ADDDATE", "SUBDATE", "TIME_FORMAT", "DATE_SUB", "DATE_ADD", "DATE_FORMAT", "SUBSTRING", "EXTRACT", "GROUP_CONCAT");

    private List<String> SUB_FUNCTIONS = Arrays.asList("ADDDATE", "SUBDATE", "TIME_FORMAT", "DATE_SUB", "DATE_ADD", "DATE_FORMAT", "SUBSTRING", "EXTRACT");

    public GetQueryFieldsUtil() {
    }

    @Override
    public void setDbType(Integer dbType) {
        this.dbType = dbType;
    }

    public List<ColumnData> getColumnDataList(DCustomSqlStatement tCustomSqlStatement) {
        analyzeSelectStatement(tCustomSqlStatement);
        return columnDataList;
    }

    @Override
    public void analyzeSelectStatement(DCustomSqlStatement select) {
        if (select != null) {
            dependMap.clear();
            aliases.clear();
            currentSource = null;
            cteMap.clear();
            currentClauseMap.clear();
            accessMap.clear();
            columnDataList.clear();

            initCTEMap(select);

            impactSqlFromStatement(select);

        }
    }

    private void initCTEMap(DCustomSqlStatement select) {
        if (select.getStatements() != null && select.getStatements().size() > 0) {
            for (int i = 0; i < select.getStatements().size(); i++) {
                initCTEMap(select.getStatements().get(i));
            }
        }
        if (select.getCteList() != null && select.getCteList().size() > 0) {
            for (int i = 0; i < select.getCteList().size(); i++) {
                TCTE expression = select.getCteList().getCTE(i);
                cteMap.put(removeQuote(expression.getTableName().toString()), expression);
            }
        }
    }

    private void impactSqlFromStatement(DCustomSqlStatement select) {
        if (select instanceof TSelectSqlStatement) {
            TSelectSqlStatement stmt = (TSelectSqlStatement) select;
            if (stmt.getLeftStmt() != null || stmt.getRightStmt() != null) {
                if (stmt.getLeftStmt() != null) {
                    impactSqlFromStatement(stmt.getLeftStmt());
                }
                if (stmt.getRightStmt() != null) {
                    impactSqlFromStatement(stmt.getRightStmt());
                }
            } else {
                if (select.getResultColumnList() != null) {
                    for (int i = 0; i < select.getResultColumnList().size(); i++) {
                        aliases.clear();
                        dependMap.clear();
                        if (skipGetSingleResultColumn(select.getResultColumnList().getResultColumn(i))) {
                            continue;
                        }
                        linkFieldToTables(null, select.getResultColumnList().getResultColumn(i), select, 0);
                        getSingleResultColumn();
                    }
                }
            }
        }
    }

    private boolean skipGetSingleResultColumn(TResultColumn field) {
        switch (field.getExpr().getExpressionType()) {
            case simple_constant_t:
                ColumnData constantColumnData = new ColumnData();
                constantColumnData.setCanDesensitize(false);
                constantColumnData.setConstant(true);
                constantColumnData.setColumnName(CommonUtil.replace(field.getExpr().toString()));
                constantColumnData.setColumnAlias(CommonUtil.replace(field.getExpr().getExprAlias() != null ? field.getExpr().getExprAlias().toString() : field.getExpr().toString()));
                columnDataList.add(constantColumnData);
                return true;
            case arithmetic_t:
            case arithmetic_plus_t:
            case arithmetic_minus_t:
            case arithmetic_times_t:
            case arithmetic_divide_t:
            case arithmetic_modulo_t:
            case arithmetic_compound_operator_t:
            case arithmetic_exponentiation_t:
                ColumnData arithmeticColumnData = new ColumnData();
                arithmeticColumnData.setCanDesensitize(false);
                arithmeticColumnData.setWithinFunc(true);
                arithmeticColumnData.setColumnName(CommonUtil.replace(field.getExpr().toString()));
                arithmeticColumnData.setColumnAlias(CommonUtil.replace(field.getExpr().getExprAlias() != null ? field.getExpr().getExprAlias().toString() : field.getExpr().toString()));
                columnDataList.add(arithmeticColumnData);
                return true;
            default:
                return false;
        }
    }

    private void getSingleResultColumn() {
        aliases.stream()
                .filter(CommonUtils.distinctByColumn(
                        actionAliasEntry -> actionAliasEntry.alias,
                        actionAliasEntry -> actionAliasEntry.column,
                        actionAliasEntry -> actionAliasEntry.columnExpr))
                .forEach(alias -> {
                    ColumnData columnData = new ColumnData();
                    columnData.setColumnAlias(alias.alias != null ? alias.alias.trim() : null);
                    columnDataList.add(columnData);

                    if (dependMap.containsKey(alias.alias)) {
                        List<ActionResultEntry> results = dependMap.get(alias.alias);
                        if (results.isEmpty()) {
                            columnData.setCanDesensitize(false);
                        }

                        for (ActionResultEntry result : results) {
                            if (result.columnObject == null
                                    || result.columnObject.columnName == null
                                    || result.clause != ClauseType.select
                                    || result.targetTable.getFullName() == null) {
                                continue;
                            }

                            String aliasName = alias.alias;
                            if (alias.alias != null) {
                                String[] aliasNames = alias.alias.split("\\.");
                                String aliasNameTemp = aliasNames[aliasNames.length - 1];
                                if ("*".equals(aliasNameTemp)) {
                                    // SELECT a.* FROM (SELECT cy3.*,cy2.* FROM "CY3",cy2) a;
                                    aliasName = aliasNameTemp;
                                }
                            }

                            if ("*".equals(aliasName)) {
                                // SELECT * FROM (SELECT id as id1, name as name1 FROM "CY3");
                                // SELECT * FROM (SELECT cy3.*,cy2.* FROM "CY3",cy2);
                                columnDataList.remove(columnData);
                                ColumnData columnDataInner = new ColumnData();
                                columnDataInner.setColumnAlias(CommonUtil.replace(result.columnObject != null ? result.columnObject.alias : result.targetColumn));
                                buildColumnData(columnDataInner, result, alias);
                                columnDataList.add(columnDataInner);
                                continue;
                            }

                            if (StringUtils.isNotBlank(columnData.getColumnName())) {
                                columnData.setCanDesensitize(false);
                                columnData.setWithinFunc(true);

                                ColumnData columnDataInner = new ColumnData();
                                buildTargetTable(columnDataInner, result);
                                columnData.setColumnDataInner(columnDataInner);

                                break;
                            }

                            buildColumnData(columnData, result, alias);
                        }
                    } else {
                        columnData.setCanDesensitize(false);
                    }
                });
    }

    private void buildColumnData(ColumnData columnData, ActionResultEntry result, ActionAliasEntry alias) {
        columnData.setColumnName(CommonUtil.replace(result.targetColumn));

        if (alias.columnExpr != null && Arrays.asList(EExpressionType.function_t,
                EExpressionType.concatenate_t).contains(alias.columnExpr.getExpressionType())) {
            columnData.setWithinFunc(true);
            columnData.setCanDesensitize(false);
        }

        if (result.targetTable.getFullName() != null) {
            buildTargetTable(columnData, result);
        } else {
            columnData.setCanDesensitize(false);
        }
    }

    public void buildTargetTable(ColumnData columnData, ActionResultEntry result) {
        String splitRegex = CommonUtil.useColonSplit(dbType) ? ":" : "\\.";
        String[] targetTable = result.targetTable.getFullName().split(splitRegex);
        columnData.setTableName(CommonUtil.replace(targetTable[targetTable.length - 1]));

        if (targetTable.length > 1) {
            columnData.setSchemaName(CommonUtil.replace(targetTable[targetTable.length - 2]));
        }
        if (targetTable.length > 2) {
            columnData.setCatalogName(CommonUtil.replace(targetTable[targetTable.length - 3]));
        }
    }

    private boolean linkFieldToTables(ActionAliasEntry parentAlias, TResultColumn field,
                                      DCustomSqlStatement select, int level) {
        if (level == 0) {
            accessMap.clear();
        }

        boolean ret = false;

        // all items in select list was represented by a TLzField Objects
        switch (field.getExpr().getExpressionType()) {
            case simple_object_name_t:
                ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                boolean isPseudoColumn = select.dbvendor == EDbType.dbvoracle && this.isPseudoColumn(column.columnName);
                if (level == 0 || parentAlias != null) {
                    ActionAliasEntry alias = null;
                    if (parentAlias != null) {
                        alias = parentAlias;
                    } else {
                        alias = new ActionAliasEntry();
                        alias.column = removeQuote(field.toString());
                        alias.columnExpr = field.getExpr();
                        alias.alias = removeQuote(field.toString());
                        alias.location = new Point(
                                (int) field.getStartToken().lineNo,
                                (int) field.getStartToken().columnNo);
                        if (field.getAliasClause() != null) {
                            alias.alias = removeQuote(field.getAliasClause().toString());
                            alias.column = removeQuote(field.toString());
                            alias.columnExpr = field.getExpr();
                            DSourceToken startToken = field.getAliasClause().getAliasName().getStartToken();
                            alias.location = new Point(
                                    (int) startToken.lineNo,
                                    (int) startToken.columnNo);
                        }
                        aliases.add(alias);
                    }

                    currentSource = alias.alias;
                    if (!dependMap.containsKey(currentSource)) {
                        dependMap.put(currentSource, new ArrayList<>());
                    }
                }

                if (isPseudoColumn) {
                    break;
                }

                ret = findColumnInTables(column, select, level + 1, null);
                findColumnsFromClauses(select, level + 2);
                break;
            case subquery_t:
                ActionAliasEntry aliasSubQuery = new ActionAliasEntry();
                aliasSubQuery.column = removeQuote(field.toString());
                aliasSubQuery.columnExpr = field.getExpr();
                aliasSubQuery.alias = removeQuote(field.toString());
                aliasSubQuery.location = new Point(
                        (int) field.getStartToken().lineNo,
                        (int) field.getStartToken().columnNo);

                if (field.getAliasClause() != null) {
                    aliasSubQuery.alias = removeQuote(field.getAliasClause().toString());
                    DSourceToken startToken = field.getAliasClause().getAliasName().getStartToken();
                    aliasSubQuery.column = removeQuote(field.toString());
                    aliasSubQuery.columnExpr = field.getExpr();
                    aliasSubQuery.location = new Point(
                            (int) startToken.lineNo,
                            (int) startToken.columnNo);
                }

                if (level == 0) {
                    aliases.add(aliasSubQuery);
                }

                TSelectSqlStatement stmt = field.getExpr().getSubQuery();
                List<TSelectSqlStatement> stmtList = new ArrayList<>();
                getSelectSqlStatements(stmt, stmtList);

                for (TSelectSqlStatement tSelectSqlStatement : stmtList) {
                    linkFieldToTables(aliasSubQuery, tSelectSqlStatement.getResultColumnList().getResultColumn(0), tSelectSqlStatement, Math.max(level + 1, 0));
                }
                break;
            default:
                ActionAliasEntry alias = parentAlias;
                if (level == 0) {
                    alias = new ActionAliasEntry();
                    alias.column = removeQuote(field.toString());
                    alias.columnExpr = field.getExpr();
                    alias.alias = alias.column;
                    alias.location = new Point(
                            (int) field.getStartToken().lineNo,
                            (int) field.getStartToken().columnNo);

                    if (parentAlias == null) {
                        if (field.getAliasClause() != null) {
                            alias.alias = removeQuote(field.getAliasClause().toString());
                            alias.column = removeQuote(field.toString());
                            alias.columnExpr = field.getExpr();
                            DSourceToken startToken = field.getAliasClause().getAliasName().getStartToken();
                            alias.location = new Point(
                                    (int) startToken.lineNo,
                                    (int) startToken.columnNo);
                        }
                        aliases.add(alias);

                        currentSource = alias.alias;
                        if (!dependMap.containsKey(currentSource)) {
                            dependMap.put(currentSource, new ArrayList<ActionResultEntry>());
                        }
                    }
                }

                List<ActionColumnEntry> columns = exprToColumn(field.getExpr(), select, level, true, ClauseType.select, alias);

                for (ActionColumnEntry actionColumnEntry : columns) {
                    if (actionColumnEntry == null) {
                        continue;
                    }
                    findColumnInTables(actionColumnEntry, select, level + 1, null);
                    findColumnsFromClauses(select, level + 2);
                }

                if (field.getExpr().getExpressionType() == EExpressionType.function_t) {
                    TFunctionCall func = field.getExpr().getFunctionCall();

                    // check column in function arguments
                    int argCount = 0;
                    if (func.getArgs() != null) {
                        for (int k = 0; k < func.getArgs().size(); k++) {
                            TExpression expr = func.getArgs().getExpression(k);
                            if ("*".equals(expr.toString().trim())) {
                                continue;
                            }
                            List<ActionColumnEntry> actionColumnEntries = exprToColumn(expr, select,
                                    level + 1, ClauseType.select, parentAlias);
                            for (ActionColumnEntry actionColumnEntry : actionColumnEntries) {
                                findColumnInTables(actionColumnEntry, select, level + 1, null);
                                findColumnsFromClauses(select, level + 2);
                            }
                            argCount++;
                        }
                    }

                    if (argCount == 0) {
                        // adddate(t1,interval 5 day)、subdate(t1,2)、time_FORMAT(t2,'%h-%m-%s %r')
                        TExpression exprFirst = func.getExpr1();
                        if (exprFirst != null) {
                            List<ActionColumnEntry> columnsTemp = exprToColumn(exprFirst, select, level + 1, ClauseType.select, parentAlias);
                            for (ActionColumnEntry columnTemp : columnsTemp) {
                                findColumnInTables(columnTemp, select, level + 1, null);
                                findColumnsFromClauses(select, level + 2);
                            }
                            argCount++;
                        }
                    }

                    if (argCount == 0 && "GROUP_CONCAT".equals(func.getFunctionName().toString().toUpperCase(Locale.ROOT))) {
                        TGroupConcatParam groupConcatParam = func.getGroupConcatParam();
                        if (groupConcatParam != null && groupConcatParam.getExprList() != null) {
                            TExpressionList exprList = groupConcatParam.getExprList();
                            for (TExpression exprTemp : exprList) {
                                if (exprTemp != null) {
                                    List<ActionColumnEntry> columnsTemp = exprToColumn(exprTemp, select, level + 1, ClauseType.select, parentAlias);
                                    for (ActionColumnEntry columnTemp : columnsTemp) {
                                        findColumnInTables(columnTemp, select, level + 1, null);
                                        findColumnsFromClauses(select, level + 2);
                                    }
                                    argCount++;
                                }
                            }
                        }
                    }

                    if (argCount == 0 && !"ROW_NUMBER".equalsIgnoreCase(func.getFunctionName().toString())) {
                        Point point = new Point(
                                (int) func.getEndToken().lineNo,
                                (int) func.getEndToken().columnNo);

                        if (func.getArgs() != null && func.getArgs().size() > 0) {
                            for (int k = 0; k < func.getArgs().size(); k++) {
                                TExpression expr = func.getArgs().getExpression(k);
                                if ("*".equals(expr.toString().trim())) {
                                    point = new Point(
                                            (int) expr.getStartToken().lineNo,
                                            (int) expr.getStartToken().columnNo);
                                    break;
                                }
                            }
                        }

                        if (dependMap.containsKey(currentSource)) {
                            if (currentClauseMap.containsKey(select)) {
                                dependMap.get(currentSource).add(
                                        new ActionResultEntry(select.tables.getTable(0), "*",
                                                (ClauseType) currentClauseMap.get(select), point));
                            } else if (select instanceof TSelectSqlStatement) {
                                dependMap.get(currentSource).add(
                                        new ActionResultEntry(select.tables.getTable(0), "*",
                                                ClauseType.select, point));
                            } else {
                                dependMap.get(currentSource).add(
                                        new ActionResultEntry(select.tables.getTable(0), "*",
                                                ClauseType.undefine, point));
                            }
                        }
                    }

                    findColumnsFromClauses(select, level + 2);

                } else if (field.getExpr().getExpressionType() == EExpressionType.list_t) {
                    TExpressionList exprList = field.getExpr().getExprList();
                    for (TExpression expr : exprList) {
                        if ("*".equals(expr.toString().trim())) {
                            continue;
                        }
                        List<ActionColumnEntry> actionColumnEntries = exprToColumn(expr, select,
                                level + 1, ClauseType.select, parentAlias);
                        for (ActionColumnEntry column1 : actionColumnEntries) {
                            findColumnInTables(column1, select, level + 1, null);
                            findColumnsFromClauses(select, level + 2);
                        }
                    }
                }
                break;
        }

        return ret;
    }

    private void getSelectSqlStatements(TSelectSqlStatement select, List<TSelectSqlStatement> stmtList) {
        if (select.getLeftStmt() != null || select.getRightStmt() != null) {
            if (select.getLeftStmt() != null) {
                getSelectSqlStatements(select.getLeftStmt(), stmtList);
            }
            if (select.getRightStmt() != null) {
                getSelectSqlStatements(select.getRightStmt(), stmtList);
            }
        } else {
            stmtList.add(select);
        }
    }

    @Override
    public ActionColumnEntry attrToColumn(TExpression lcexpr, DCustomSqlStatement stmt,
                                          TExpression expr, boolean collectExpr, ClauseType clause,
                                          ActionAliasEntry parentAlias) {
        ActionColumnEntry column = attrToColumn(lcexpr, stmt, clause);
        if (collectExpr) {
            column.expression = expr.toString().replace("\r\n", "\n").replaceAll("\n+", " ");
            if (column.expression.trim().length() > 0) {
                Stack<TParseTreeNode> tokens = expr.getStartToken().getNodesStartFromThisToken();
                if (tokens != null) {
                    for (int i = 0; i < tokens.size(); i++) {
                        TParseTreeNode node = tokens.get(i);
                        if (node instanceof TResultColumn) {
                            TResultColumn field = (TResultColumn) node;
                            if (field.getAliasClause() != null) {
                                column.alias = field.getAliasClause().toString();
                            }
                        }
                    }
                }
            }
        }
        return column;
    }


    private ActionColumnEntry attrToColumn(TExpression attr, DCustomSqlStatement stmt, ClauseType clauseType) {

        ActionColumnEntry column = new ActionColumnEntry();
        column.clauseType = clauseType;
        if (attr.getObjectOperand() == null) {
            return column;
        }

        column.columnName = removeQuote(attr.getObjectOperand().getEndToken().toString());
        column.location = new Point(
                (int) attr.getObjectOperand().getEndToken().lineNo,
                (int) attr.getEndToken().columnNo);

        Stack<TParseTreeNode> tokens = attr.getObjectOperand().getStartToken().getNodesStartFromThisToken();
        if (tokens != null) {
            for (int i = 0; i < tokens.size(); i++) {
                TParseTreeNode node = tokens.get(i);
                if (node instanceof TResultColumn) {
                    TResultColumn field = (TResultColumn) node;
                    if (field.getAliasClause() != null) {
                        column.alias = field.getAliasClause().toString();
                    }
                }
            }
        }

        if (attr.toString().indexOf(".") > 0) {
            column.columnPrex = removeQuote(attr.toString().substring(0, attr.toString().lastIndexOf(".")));

            String tableName = removeQuote(column.columnPrex);
            if (tableName.indexOf(".") > 0) {
                tableName = removeQuote(tableName.substring(tableName.lastIndexOf(".") + 1));
            }
            if (tableName != null) {
                if (!column.tableNames.contains(tableName)) {
                    column.tableNames.add(tableName);
                    if (!column.tableFullNames.contains(tableName)) {
                        column.tableFullNames.add(tableName);
                    }
                }
            }
        } else {
            TTableList tables = stmt.tables;
            for (int i = 0; i < tables.size(); i++) {
                TTable lztable = tables.getTable(i);
                ActionTableEntry table = TLzTaleToTable(lztable);
                if (table.tableName != null) {
                    if (!column.tableNames.contains(table.tableName)) {
                        column.tableNames.add(table.tableName);
                        if (!column.tableFullNames.contains(lztable.getFullName())) {
                            column.tableFullNames.add(lztable.getFullName());
                        }
                    }
                }
            }
        }

        column.orignColumn = column.columnName;

        return column;
    }


    private DCustomSqlStatement containClause(Map<DCustomSqlStatement, ClauseType> currentClauseMap, DCustomSqlStatement select) {
        if (currentClauseMap.containsKey(select)) {
            return select;
        } else if (select.getParentStmt() != null) {
            return containClause(currentClauseMap, select.getParentStmt());
        } else {
            return null;
        }
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level, ClauseType clauseType) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level, false, clauseType, null);
        c.searchColumn();

        return columns;
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level,
                                                 ClauseType clauseType, ActionAliasEntry parentAlias) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level, false, clauseType, parentAlias);
        c.searchColumn();

        return columns;
    }

    private List<ActionColumnEntry> exprToColumn(TExpression expr, DCustomSqlStatement stmt, int level, boolean collectExpr,
                                                 ClauseType clauseType, ActionAliasEntry parentAlias) {
        List<ActionColumnEntry> columns = new ArrayList<>();

        ColumnsInExpr c = new ColumnsInExpr(this, expr, columns, stmt, level, collectExpr, clauseType, parentAlias);
        c.searchColumn();

        return columns;
    }

    private String getTableAliasName(TTable lzTable) {
        return removeQuote(lzTable.getAliasClause().getAliasName().toString());
    }

    private String getTableName(TTable lzTable) {
        return removeQuote(lzTable.getName());
    }

    private boolean findColumnInSubQuery(TObjectNameList columnList, TSelectSqlStatement select, String columnName, int level,
                                         Point originLocation) {
        boolean ret = false;
        if (accessMap.get(columnName) != null && accessMap.get(columnName).containsKey(select)) {
            return accessMap.get(columnName).get(select);
        } else {
            if (!accessMap.containsKey(columnName)) {
                accessMap.put(columnName, new LinkedHashMap<DCustomSqlStatement, Boolean>());
            }
            Map<DCustomSqlStatement, Boolean> stmts = accessMap.get(columnName);
            stmts.put(select, false);
        }

        if (select.getLeftStmt() != null || select.getRightStmt() != null) {
            boolean left = false;
            boolean right = false;
            if (select.getLeftStmt() != null) {
                left = findColumnInSubQuery(columnList, select.getLeftStmt(), columnName, level, originLocation);
            }
            if (select.getRightStmt() != null) {
                right = findColumnInSubQuery(columnList, select.getRightStmt(), columnName, level, originLocation);
            }

            ret = left && right;
        } else if (select.getResultColumnList() != null) {
            // check column name in select list of subQuery
            TResultColumn columnField = null;
            if (!"*".equals(columnName)) {
                for (int i = 0; i < select.getResultColumnList().size(); i++) {
                    TResultColumn field = select.getResultColumnList().getResultColumn(i);
                    if (field.getAliasClause() != null) {
                        if (field.getAliasClause().toString().equalsIgnoreCase(columnName)) {
                            columnField = field;
                            break;
                        }
                    } else {
                        if (field.getExpr().getExpressionType() == EExpressionType.simple_object_name_t) {
                            ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                            if (columnName != null && columnName.equalsIgnoreCase(column.columnName)) {
                                columnField = field;
                                break;
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < select.getResultColumnList().size(); i++) {
                TResultColumn field = select.getResultColumnList().getResultColumn(i);
                if (columnField != null && !field.equals(columnField)) {
                    continue;
                }
                if (columnList != null) {
                    if (skipGetSingleResultColumn(select.getResultColumnList().getResultColumn(i))) {
                        continue;
                    }
                    ActionAliasEntry alias = new ActionAliasEntry();
                    alias.column = removeQuote(field.toString());
                    alias.columnExpr = field.getExpr();
                    alias.alias = removeQuote(columnList.getObjectName(i).toString());
                    alias.location = new Point(
                            (int) field.getStartToken().lineNo,
                            (int) field.getStartToken().columnNo);
                    aliases.add(alias);
                    linkFieldToTables(alias, select.getResultColumnList().getResultColumn(i), select, level + 1);
                } else if (field.getAliasClause() != null) {
                    ret = "*".equals(columnName) || field.getAliasClause().toString().equalsIgnoreCase(columnName);
                    if (ret) {
                        // check where this column come from
                        linkFieldToTables(null, field, select, level);
                    }
                } else {
                    if (field.getExpr().getExpressionType() == EExpressionType.simple_object_name_t) {
                        ActionColumnEntry column = attrToColumn(field.getExpr(), select, ClauseType.select);
                        ret = "*".equals(columnName) || (columnName != null && columnName.equalsIgnoreCase(column.columnName));
                        if (ret || "*".equals(column.columnName)) {
                            findColumnInTables(column, select, level, !ret ? columnName : null);
                            findColumnsFromClauses(select, level + 1);
                        }
                    }
                }

                if (ret && !"*".equals(columnName)) {
                    break;
                }
            }
        }

        Map<DCustomSqlStatement, Boolean> stmts = accessMap.get(columnName);
        if (stmts != null) {
            stmts.put(select, ret);
        }

        return ret;
    }

    private boolean findColumnInTables(ActionColumnEntry column, String tableName,
                                       DCustomSqlStatement select, int level) {
        return findColumnInTables(column, tableName, select, level, ClauseType.undefine);
    }

    private boolean findColumnInTables(ActionColumnEntry column, String tableName,
                                       DCustomSqlStatement select, int level, ClauseType clause) {
        boolean ret = false;
        TTableList tables = select.tables;

        if (tables.size() == 1) {
            TTable lzTable = tables.getTable(0);
            if ((lzTable.getTableType() == ETableSource.objectname)
                    && (tableName == null
                    || (lzTable.getAliasClause() == null && getTableName(lzTable).equalsIgnoreCase(tableName))
                    || (lzTable.getAliasClause() != null && lzTable.getAliasClause().toString().equalsIgnoreCase(tableName)))) {
                ret = true;

                if (cteMap.containsKey(getTableName(lzTable))) {
                    TCTE tcte = cteMap.get(getTableName(lzTable));
                    ret = findColumnInSubQuery(tcte.getColumnList(), tcte.getSubquery(),
                            column.columnName, level, column.location);
                } else {
                    if (currentSource != null && dependMap.containsKey(currentSource)) {
                        DCustomSqlStatement stmt = containClause(currentClauseMap, select);
                        if (stmt != null) {
                            dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                    column.columnName, currentClauseMap.get(stmt), column.location));
                        } else if (select instanceof TSelectSqlStatement) {
                            if (ClauseType.undefine.equals(clause)) {
                                dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                        column.columnName,
                                        ClauseType.select,
                                        column.location));
                            } else {
                                dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                        column.columnName, clause,
                                        column.location));
                            }
                        } else {
                            dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                    column.columnName,
                                    ClauseType.undefine,
                                    column.location));
                        }
                    }
                }
            } else if (select.getParentStmt() instanceof TSelectSqlStatement) {
                subquery = select;
                ret = findColumnInTables(column, tableName, select.getParentStmt(), level, clause);
                subquery = null;
            }
        }

        if (ret) {
            return ret;
        }

        for (int x = 0; x < tables.size(); x++) {
            TTable lzTable = tables.getTable(x);
            switch (lzTable.getTableType()) {
                case objectname:
                    ActionTableEntry table = TLzTaleToTable(lzTable);
                    String alias = table.tableAlias;
                    if (alias != null) {
                        alias = alias.trim();
                    }
                    boolean tableNameEquals = false;
                    if (dbType != null && DatabaseType.get3FDatabaseIntegerValueList().contains(dbType) && tableName != null && table.tableName != null) {
                        String[] objectNameFirst = tableName.split("\\.");
                        String[] objectNameSecond = table.tableName.split("\\.");

                        if (objectNameFirst.length == 1 && objectNameFirst[objectNameFirst.length - 1].equalsIgnoreCase(objectNameSecond[objectNameSecond.length - 1])) {
                            tableNameEquals = true;
                        }
                    }
                    if (tableName != null
                            && (tableName.equalsIgnoreCase(alias)
                            || tableName.equalsIgnoreCase(table.tableName)
                            || tableNameEquals)) {
                        if (cteMap.containsKey(getTableName(lzTable))) {
                            TCTE tcte = cteMap.get(getTableName(lzTable));
                            ret = findColumnInSubQuery(tcte.getColumnList(), tcte.getSubquery(),
                                    column.columnName, level, column.location);
                        } else {
                            if (dependMap.containsKey(currentSource)) {
                                String columnName = column.orignColumn;
                                if ("*".equals(columnName)) {
                                    columnName = column.columnName;
                                }
                                if (currentClauseMap.containsKey(select)) {
                                    dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                            columnName,
                                            currentClauseMap.get(select),
                                            column.location));
                                } else if (select instanceof TSelectSqlStatement) {
                                    if (ClauseType.undefine.equals(clause)) {
                                        dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                                column.columnName,
                                                ClauseType.select,
                                                column.location));
                                    } else {
                                        dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                                column.columnName, clause,
                                                column.location));
                                    }
                                } else {
                                    dependMap.get(currentSource).add(new ActionResultEntry(lzTable, column,
                                            columnName,
                                            ClauseType.undefine,
                                            column.location));
                                }
                            }
                            ret = true;
                        }
                    }
                    break;
                case subquery:
                    if (column.tableNames.isEmpty()) {
                        ret = findColumnInSubQuery(null,
                                lzTable.getSubquery(),
                                column.columnName,
                                level,
                                column.location);
                    } else {
                        for (int i = 0; i < column.tableNames.size(); i++) {
                            String name = column.tableNames.get(i);
                            TSelectSqlStatement selectStat = lzTable.getSubquery();

                            if (selectStat == subquery) {
                                continue;
                            }
                            if (name == null) {
                                ret = findColumnInSubQuery(null,
                                        selectStat,
                                        column.columnName,
                                        level,
                                        column.location);
                                break;
                            }

                            if (lzTable.getAliasClause() != null && getTableAliasName(lzTable).equalsIgnoreCase(name)) {
                                ret = findColumnInSubQuery(null,
                                        selectStat,
                                        column.columnName,
                                        level,
                                        column.location);
                                break;
                            }

                            boolean flag = false;
                            for (int j = 0; j < selectStat.tables.size(); j++) {
                                if (selectStat.tables.getTable(j)
                                        .getAliasClause() != null) {
                                    if (getTableAliasName(selectStat.tables.getTable(j)).equalsIgnoreCase(name)) {
                                        ret = findColumnInSubQuery(null,
                                                selectStat,
                                                column.columnName,
                                                level,
                                                column.location);
                                        flag = true;
                                        break;
                                    }
                                } else {
                                    if (selectStat.tables.getTable(j).getTableName().toString().equalsIgnoreCase(name)) {
                                        ret = findColumnInSubQuery(null,
                                                selectStat,
                                                column.columnName,
                                                level,
                                                column.location);
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            if (flag) {
                                break;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            if (ret) {
                break;
            }
        }

        if (!ret && select.getParentStmt() instanceof TSelectSqlStatement) {
            subquery = select;
            ret = findColumnInTables(column, tableName, select.getParentStmt(), level, clause);
            subquery = null;
        }

        return ret;
    }

    private boolean findColumnInTables(ActionColumnEntry column, DCustomSqlStatement select, int level, String columnName) {
        boolean ret = false;
        if (column.tableNames.isEmpty()) {
            ret = findColumnInTables(column, null, select, level);
        } else {
            for (String tableName : column.tableNames) {
                if (columnName != null) {
                    column.columnName = columnName;
                }
                ret |= findColumnInTables(column,
                        tableName,
                        select,
                        level);
            }
        }
        return ret;
    }

    private void findColumnsFromClauses(DCustomSqlStatement select, int level) {

        currentClauseMap.put(select, ClauseType.undefine);
        Map<TExpression, ClauseType> clauseTable = new LinkedHashMap<>();
        if (select instanceof TSelectSqlStatement) {
            TSelectSqlStatement statement = (TSelectSqlStatement) select;

            if (statement.getHierarchicalClause() != null && statement.getHierarchicalClause().getConnectByList() != null) {
                for (int i = 0; i < statement.getHierarchicalClause().getConnectByList().size(); i++) {
                    clauseTable.put(statement.getHierarchicalClause().getConnectByList().getElement(i).getCondition(), ClauseType.connectby);
                }
            }

            if (statement.getHierarchicalClause() != null && statement.getHierarchicalClause().getStartWithClause() != null) {
                clauseTable.put(statement.getHierarchicalClause().getStartWithClause(), ClauseType.startwith);
            }

            if (statement.joins != null) {
                for (int i = 0; i < statement.joins.size(); i++) {
                    TJoin join = statement.joins.getJoin(i);
                    if (join.getJoinItems() != null) {
                        for (int j = 0; j < join.getJoinItems().size(); j++) {
                            TJoinItem joinItem = join.getJoinItems().getJoinItem(j);
                            TExpression expr = joinItem.getOnCondition();
                            if (expr != null) {
                                clauseTable.put(expr, ClauseType.join);
                            }
                        }
                    }
                }
            }
        }

        for (TExpression expr : clauseTable.keySet()) {
            currentClauseMap.put(select, clauseTable.get(expr));

            List<ActionColumnEntry> columns = exprToColumn(expr, select, level, clauseTable.get(expr));
            for (ActionColumnEntry actionColumnEntry : columns) {
                for (String tableName : actionColumnEntry.tableNames) {
                    findColumnInTables(actionColumnEntry, tableName, select, level + 2, actionColumnEntry.clauseType);
                }
            }
        }

        currentClauseMap.remove(select);

    }

    @Override
    public void impactSqlFromStatement(DCustomSqlStatement select, int baseLevel) {

        if (select == null) {
            return;
        }

        String sql = select.toString();

        if (!sqlSet.add(sql)) {
            return;
        }

        if (select instanceof TSelectSqlStatement) {
            TSelectSqlStatement stmt = (TSelectSqlStatement) select;

            ArrayDeque<TSelectSqlStatement> deque = new ArrayDeque<>();
            deque.push(stmt);

            while (!deque.isEmpty()) {
                TSelectSqlStatement current = deque.pop();

                // 检查是否是叶子节点
                if (current.getLeftStmt() == null && current.getRightStmt() == null) {

                    for (int i = 0; i < current.getResultColumnList().size(); i++) {
                        linkFieldToTables(null, current.getResultColumnList().getResultColumn(i), current, baseLevel);
                    }

                }

                // 先压入右节点，再压入左节点，这样在遍历时会先处理左子树
                if (current.getRightStmt() != null) {
                    deque.push(current.getRightStmt());
                }
                if (current.getLeftStmt() != null) {
                    deque.push(current.getLeftStmt());
                }
            }

        }
    }

    private ActionTableEntry TLzTaleToTable(TTable lzTable) {
        ActionTableEntry table = new ActionTableEntry();
        if (lzTable.getSubquery() == null && lzTable.getTableName() != null) {
            table.tableName = removeQuote(getTableName(lzTable));
            if (lzTable.getTableName().toString().indexOf(".") > 0) {
                table.prefixName = removeQuote(lzTable.getTableName().toString()
                        .substring(0, lzTable.getFullName().lastIndexOf('.')));
            }
        }

        if (StringUtils.isNotBlank(table.prefixName) && dbType != null && DatabaseType.get3FDatabaseIntegerValueList().contains(dbType)) {
            table.tableName = table.prefixName + "." + table.tableName;
        }

        if (lzTable.getAliasClause() != null) {
            table.tableAlias = removeQuote(lzTable.getAliasClause().toString());
        }
        return table;
    }

    private boolean isPseudoColumn(String column) {
        if (column == null) {
            return false;
        } else if ("rownum".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("rowid".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("nextval".equalsIgnoreCase(column.trim())) {
            return true;
        } else if ("sysdate".equalsIgnoreCase(column.trim())) {
            return true;
        }
        return false;
    }

    private String removeQuote(String string) {
        if (string == null) {
            return string;
        }

        if (string.indexOf('.') != -1 && string.length() < 128) {
            List<String> splits = parseNames(string);
            StringBuilder buffer = new StringBuilder();
            for (int i = 0; i < splits.size(); i++) {
                buffer.append(splits.get(i));
                if (i < splits.size() - 1) {
                    buffer.append(".");
                }
            }
            string = buffer.toString();
        } else {
            if (string.startsWith("\"") && string.endsWith("\"")) {
                return string.substring(1, string.length() - 1);
            }

            if (string.startsWith("[") && string.endsWith("]")) {
                return string.substring(1, string.length() - 1);
            }
        }
        return string;
    }

    public static List<String> parseNames(String nameString) {
        List<String> names = new ArrayList<>();

        String name = nameString.trim();
        String[] splits = nameString.split("\\.");

        if ((name.startsWith("\"") && name.endsWith("\"")) || (name.startsWith("[") && name.endsWith("]"))) {
            for (int i = 0; i < splits.length; i++) {
                String split = splits[i].trim();
                if (split.startsWith("[") && !split.endsWith("]")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("]")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                if (split.startsWith("\"") && !split.endsWith("\"")) {
                    StringBuilder buffer = new StringBuilder();
                    buffer.append(splits[i]);
                    while (!(split = splits[++i].trim()).endsWith("\"")) {
                        buffer.append(".");
                        buffer.append(splits[i]);
                    }

                    buffer.append(".");
                    buffer.append(splits[i]);

                    names.add(buffer.toString());
                    continue;
                }
                names.add(splits[i]);
            }
        } else {
            names.addAll(Arrays.asList(splits));
        }
        return names;
    }

}
