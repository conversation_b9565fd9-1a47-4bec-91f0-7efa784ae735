<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin>

    <extension-point id="com.dc.summer.dataTransfer" name="%extension-point.com.dc.summer.dataTransfer.name" schema="schema/com.dc.summer.dataTransfer.exsd"/>

    <extension point="com.dc.summer.dataTransfer">
        <node type="producer"
              id="database_producer"
              class="com.dc.summer.data.transfer.database.DatabaseTransferProducer"
              icon="icons/formats/table.png"
              label="%dataTransfer.producer.database.name"
              description="%dataTransfer.producer.database.description"
              settings="com.dc.summer.data.transfer.database.DatabaseProducerSettings"
              advanced="true">
            <sourceType type="com.dc.summer.model.struct.DBSDataContainer"/>
<!--            <sourceType type="com.dc.summer.model.struct.DBSObjectContainer"/>-->
        </node>

        <node type="consumer"
            id="database_consumer"
            class="com.dc.summer.data.transfer.database.DatabaseTransferConsumer"
            icon="icons/formats/table.png"
            label="%dataTransfer.consumer.database.name"
            description="%dataTransfer.consumer.database.description"
            settings="com.dc.summer.data.transfer.database.DatabaseConsumerSettings"
            advanced="true">
            <sourceType type="com.dc.summer.model.struct.DBSDataContainer"/>
        </node>

        <node type="producer"
            id="stream_producer"
            class="com.dc.summer.data.transfer.stream.StreamTransferProducer"
            icon="icons/formats/file.png"
            label="%dataTransfer.producer.stream.name"
            description="%dataTransfer.producer.stream.description"
            settings="com.dc.summer.data.transfer.stream.StreamProducerSettings">

            <processor
                id="stream.csv"
                class="com.dc.summer.data.transfer.stream.importer.DataImporterCSV"
                description="%dataTransfer.producer.stream.processor.csv.description"
                icon="icons/formats/csv.png"
                label="%dataTransfer.producer.stream.processor.csv.name"
                contentType="text/plain">
                <propertyGroup label="%dataTransfer.producer.stream.processor.csv.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.producer.stream.processor.csv.property.extension.label" defaultValue="csv,tsv,txt"/>
                    <property id="encoding" label="%dataTransfer.producer.stream.processor.csv.property.encoding.label" defaultValue="utf-8"/>
                    <property id="delimiter" label="%dataTransfer.producer.stream.processor.csv.property.delimiter.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.delimiter.description" defaultValue="," required="true"/>
                    <property id="header" label="%dataTransfer.producer.stream.processor.csv.property.header.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.header.description" defaultValue="top" required="true" validValues="none,top"/>
                    <property id="quoteChar" label="%dataTransfer.producer.stream.processor.csv.property.quoteChar.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.quoteChar.description" defaultValue="&quot;" required="false"/>
                    <property id="escapeChar" label="%dataTransfer.producer.stream.processor.csv.property.escapeChar.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.escapeChar.description" defaultValue="\" required="false"/>
                    <property id="nullString" label="%dataTransfer.producer.stream.processor.csv.property.nullString.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.nullString.description" defaultValue="" required="false"/>
                    <property id="emptyStringNull" label="%dataTransfer.producer.stream.processor.csv.property.emptyStringNull.name" type="boolean" description="%dataTransfer.producer.stream.processor.csv.property.emptyStringNull.description" defaultValue="" required="false"/>
                    <property id="timestampFormat" label="%dataTransfer.producer.stream.processor.csv.property.timestampFormat.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.timestampFormat.description" defaultValue="yyyy-MM-dd[ HH:mm:ss[.SSS]]" required="false"/>
                    <property id="trimWhitespaces" label="%dataTransfer.producer.stream.processor.csv.property.trimWhitespaces.name" type="boolean" description="%dataTransfer.producer.stream.processor.csv.property.trimWhitespaces.description" defaultValue="false" required="false"/>
                    <property id="timestampZone" label="%dataTransfer.producer.stream.processor.csv.property.timestampZone.name" type="string" description="%dataTransfer.producer.stream.processor.csv.property.timestampZone.description" defaultValue="" required="false"/>
                </propertyGroup>
                <propertyGroup label="%dataTransfer.producer.stream.processor.csv.propertyGroup.sampling.label">
                    <property id="columnTypeSamplesCount" label="%dataTransfer.producer.stream.processor.csv.property.columnTypeSamplesCount.name" type="integer" description="%dataTransfer.producer.stream.processor.csv.property.columnTypeSamplesCount.description" defaultValue="100" required="false"/>
                    <property id="columnTypeMinimalLength" label="%dataTransfer.producer.stream.processor.csv.property.columnTypeMinimalLength.name" type="integer" description="%dataTransfer.producer.stream.processor.csv.property.columnTypeMinimalLength.description" defaultValue="50" required="false"/>
                    <property id="columnTypeIsByteLength" label="%dataTransfer.producer.stream.processor.csv.property.columnTypeUseByteLength.name" type="boolean" description="%dataTransfer.producer.stream.processor.csv.property.columnTypeUseByteLength.description" defaultValue="" required="false"/>
                </propertyGroup>
            </processor>
        </node>

        <node type="consumer"
              id="stream_consumer"
              class="com.dc.summer.data.transfer.stream.StreamTransferConsumer"
              icon="icons/formats/file.png"
              label="%dataTransfer.consumer.stream.name"
              description="%dataTransfer.consumer.stream.description"
              settings="com.dc.summer.data.transfer.stream.StreamConsumerSettings">

            <processor
                    id="stream.xml"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterXML"
                    description="%dataTransfer.processor.xml.description"
                    icon="icons/formats/xml.png"
                    label="%dataTransfer.processor.xml.name"
                    contentType="text/xml">
                <propertyGroup label="%dataTransfer.processor.xml.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.xml.property.extension.label" defaultValue="xml"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.dbunit"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterDbUnit"
                    description="%dataTransfer.processor.dbunit.description"
                    icon="icons/formats/xml.png"
                    label="%dataTransfer.processor.dbunit.name"
                    contentType="text/xml">
                <propertyGroup label="%dataTransfer.processor.dbunit.propertyGroup.general.label">
                    <property id="upperCaseTableName" label="%dataTransfer.processor.dbunit.property.uppercase.table.name.label" type="boolean" defaultValue="true"/>
                    <property id="upperCaseColumnNames" label="%dataTransfer.processor.dbunit.property.uppercase.column.names.label" type="boolean" defaultValue="true"/>
                    <property id="extension" label="%dataTransfer.processor.dbunit.property.extension.label" defaultValue="xml"/>
                    <property id="includeNullValues" label="%dataTransfer.processor.dbunit.property.include.null.values.label" type="boolean" defaultValue="true"/>
                    <property id="nullValueString" label="%dataTransfer.processor.dbunit.property.null.value.string.label" defaultValue="[NULL]"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.json"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterJSON"
                    description="%dataTransfer.processor.json.description"
                    icon="icons/formats/json.png"
                    label="%dataTransfer.processor.json.name"
                    contentType="text/json">
                <propertyGroup label="%dataTransfer.processor.json.propertyGroup.general.label">
                    <property id="printTableName" label="%dataTransfer.processor.json.property.printTableName.label" type="boolean" defaultValue="false"/>
                    <property id="formatDateISO" label="%dataTransfer.processor.json.property.formatDateISO.label" type="boolean" defaultValue="true"/>
                    <property id="extension" label="%dataTransfer.processor.json.property.extension.label" defaultValue="json"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.html"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterHTML"
                    description="%dataTransfer.processor.html.description"
                    icon="icons/formats/html.png"
                    label="%dataTransfer.processor.html.name"
                    appFileExtension="html"
                    appName="%dataTransfer.processor.web.name"
                    html="true"
                    order="20"
                    contentType="text/html">
                <propertyGroup label="%dataTransfer.processor.html.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.html.property.extension.label" defaultValue="html"/>
                    <property id="tableHeader" label="%dataTransfer.processor.html.property.header.name" type="boolean" description="%dataTransfer.processor.html.property.header.description" defaultValue="true"/>
                    <property id="columnHeaders" label="%dataTransfer.processor.html.property.columnHeaders.name" type="boolean" description="%dataTransfer.processor.html.property.columnHeaders.description" defaultValue="true"/>
                    <property id="extractImages" label="%dataTransfer.processor.html.property.images.name" type="boolean" description="%dataTransfer.processor.html.property.images.description" defaultValue="true"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.csv"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterCSV"
                    description="%dataTransfer.processor.csv.description"
                    icon="icons/formats/csv.png"
                    label="%dataTransfer.processor.csv.name"
                    contentType="text/plain">
                <propertyGroup label="%dataTransfer.processor.csv.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.csv.property.extension.label" defaultValue="csv"/>
                    <property id="delimiter" label="%dataTransfer.processor.csv.property.delimiter.name" type="string" description="%dataTransfer.processor.csv.property.delimiter.description" defaultValue="," required="true"/>
                    <property id="rowDelimiter" label="%dataTransfer.processor.csv.property.rowDelimiter.name" type="string" description="%dataTransfer.processor.csv.property.rowDelimiter.description" defaultValue="default" validValues="default,\n,\r,\r\n,\n\r" required="true"/>
                    <property id="header" label="%dataTransfer.processor.csv.property.header.name" type="string" description="%dataTransfer.processor.csv.property.header.description" defaultValue="top" required="true" validValues="none,top,bottom" allowCustomValues="false"/>
                    <property id="headerFormat" label="%dataTransfer.processor.csv.property.headerFormat.name" type="string" description="%dataTransfer.processor.csv.property.headerFormat.description" defaultValue="label" required="false" validValues="label,description,both" allowCustomValues="false"/>
                    <property id="escape" label="%dataTransfer.processor.csv.property.escape.name" type="string" description="%dataTransfer.processor.csv.property.escape.description" defaultValue="quotes" required="true" validValues="quotes,escape" allowCustomValues="false"/>
                    <property id="quoteChar" label="%dataTransfer.processor.csv.property.quoteChar.name" type="string" description="%dataTransfer.processor.csv.property.quoteChar.description" defaultValue="&quot;" required="false"/>
                    <property id="quoteAlways" label="%dataTransfer.processor.csv.property.quoteAlways.name" type="string" description="%dataTransfer.processor.csv.property.quoteAlways.description" defaultValue="all" required="false" validValues="disabled,all,strings,all but numbers,all but nulls" allowCustomValues="false"/>
                    <property id="quoteNever" label="%dataTransfer.processor.csv.property.quoteNever.name" type="boolean" description="%dataTransfer.processor.csv.property.quoteNever.description" defaultValue="false" required="false"/>
                    <property id="nullString" label="%dataTransfer.processor.csv.property.nullString.name" type="string" description="%dataTransfer.processor.csv.property.nullString.description" defaultValue="" required="false"/>
                    <property id="formatNumbers" label="%dataTransfer.processor.csv.property.formatNumbers.name" type="boolean" description="%dataTransfer.processor.csv.property.formatNumbers.description" defaultValue="false" required="false"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.markdown.table"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterMarkdownTable"
                    description="%dataTransfer.processor.markdownTable.description"
                    icon="icons/formats/markdown.png"
                    label="%dataTransfer.processor.markdownTable.name"
                contentType="text/plain">
                <propertyGroup label="%dataTransfer.processor.markdownTable.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.markdownTable.property.extension.label" defaultValue="md"/>
                    <property id="nullString" label="%dataTransfer.processor.markdownTable.property.nullString.name" type="string" description="%dataTransfer.processor.markdownTable.property.nullString.description" defaultValue="" required="false"/>
                    <property id="formatNumbers" label="%dataTransfer.processor.markdownTable.property.formatNumbers.name" type="boolean" description="%dataTransfer.processor.markdownTable.property.formatNumbers.description" defaultValue="false" required="false"/>
                    <property id="showHeaderSeparator" label="%dataTransfer.processor.markdownTable.property.showHeaderSeparator.name" type="boolean" description="%dataTransfer.processor.markdownTable.property.showHeaderSeparator.description" defaultValue="true" required="false"/>
                    <property id="confluenceFormat" label="%dataTransfer.processor.markdownTable.property.confluenceFormat.name" type="boolean" description="%dataTransfer.processor.markdownTable.property.confluenceFormat.description" defaultValue="false" required="false"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.sql"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterSQL"
                    description="%dataTransfer.processor.sql.description"
                    icon="icons/formats/sql.png"
                    label="%dataTransfer.processor.sql.name"
                contentType="text/sql">
                <propertyGroup label="%dataTransfer.processor.sql.propertyGroup.general.label">
                    <property id="includeAutoGenerated" label="%dataTransfer.processor.sql.property.includeAutoGenerated.label" description="%dataTransfer.processor.sql.property.includeAutoGenerated.description" type="boolean" defaultValue="false"/>
                    <property id="extension" label="%dataTransfer.processor.sql.property.extension.label" type="string" defaultValue="sql"/>
                    <property id="nativeFormat" label="%dataTransfer.processor.sql.property.nativeFormat.name" type="boolean" description="%dataTransfer.processor.sql.property.nativeFormat.description" defaultValue="true" />
                    <property id="omitSchema" label="%dataTransfer.processor.sql.property.omitSchema.name" type="boolean" description="%dataTransfer.processor.sql.property.omitSchema.description" required="false" defaultValue="false"/>
                    <property id="rowsInStatement" label="%dataTransfer.processor.sql.property.rowsInStatement.name" type="integer" description="%dataTransfer.processor.sql.property.rowsInStatement.description" defaultValue="1" required="true"/>
                </propertyGroup>
                <propertyGroup label="%dataTransfer.processor.sql.propertyGroup.formatting.label">
                    <property id="lineBeforeRows" label="%dataTransfer.processor.sql.property.lineBeforeRows.name" type="boolean" description="%dataTransfer.processor.sql.property.lineBeforeRows.description" defaultValue="true" />
                    <property id="keywordCase" label="%dataTransfer.processor.sql.property.keywordCase.name" type="string" description="%dataTransfer.processor.sql.property.keywordCase.description" defaultValue="upper" required="true" validValues="upper,lower" allowCustomValues="false"/>
                    <property id="identifierCase" label="%dataTransfer.processor.sql.property.identifierCase.name" type="string" description="%dataTransfer.processor.sql.property.identifierCase.description" defaultValue="as is" required="true" validValues="as is,upper,lower" allowCustomValues="false"/>
                    <property id="upsertKeyword" label="%dataTransfer.processor.sql.property.upsertKeyword.name" type="string" description="%dataTransfer.processor.sql.property.upsertKeyword.description" defaultValue="INSERT" required="true" validValues="INSERT,INSERT ALL,UPDATE OR,UPSERT INTO,REPLACE INTO,ON DUPLICATE KEY UPDATE,ON CONFLICT" allowCustomValues="false"/>
                    <property id="insertOnConflict" label="%dataTransfer.processor.sql.property.insertOnConflict.name" type="string" description="%dataTransfer.processor.sql.property.insertOnConflict.description" defaultValue="" required="true"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.txt"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterTXT"
                    description="%dataTransfer.processor.txt.description"
                    icon="icons/formats/text.png"
                    label="%dataTransfer.processor.txt.name"
                    contentType="text/plain">
                <propertyGroup label="%dataTransfer.processor.txt.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.txt.property.extension.label" type="string" defaultValue="txt"/>
                    <property id="batchSize" label="%dataTransfer.processor.txt.property.batchSize.label" defaultValue="1"/>
                    <property id="minColumnLength" label="%dataTransfer.processor.txt.property.minColumnLength.label" defaultValue="3"/>
                    <property id="maxColumnLength" label="%dataTransfer.processor.txt.property.maxColumnLength.label" defaultValue="0"/>
                    <property id="showNulls" label="%dataTransfer.processor.txt.property.showNulls.label" description="%dataTransfer.processor.txt.property.showNulls.tip" type="boolean" defaultValue="false"/>
                    <property id="delimHeader" label="%dataTransfer.processor.txt.property.delimHeader.label" type="boolean" defaultValue="false"/>
                    <property id="delimLeading" label="%dataTransfer.processor.txt.property.delimLeading.label" type="boolean" defaultValue="false"/>
                    <property id="delimTrailing" label="%dataTransfer.processor.txt.property.delimTrailing.label" type="boolean" defaultValue="false"/>
                    <property id="delimBetween" label="%dataTransfer.processor.txt.property.delimBetween.label" type="boolean" defaultValue="false"/>
                    <property id="showHeader" label="%dataTransfer.processor.txt.property.showHeader.label" description="%dataTransfer.processor.txt.property.showHeader.tip" type="boolean" defaultValue="true"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.source.code"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterSourceCode"
                    description="%dataTransfer.processor.source.code.description"
                    icon="icons/formats/code.png"
                    label="%dataTransfer.processor.source.code.name"
                    contentType="text/plain">
                <propertyGroup label="%dataTransfer.processor.source.code.propertyGroup.general.label">
                    <property id="language" label="%dataTransfer.processor.source.code.property.language.name" type="string" description="%dataTransfer.processor.source.code.property.language.description" defaultValue="PHP &lt; 5.4" validValues="PHP &lt; 5.4,PHP 5.4+" required="true"/>
                    <property id="formatDateISOPHP" label="%dataTransfer.processor.source.code.property.formatDateISO.label" type="boolean" defaultValue="true"/>
                    <property id="extension" label="%dataTransfer.processor.source.code.property.extension.label" defaultValue="php"/>
                    <property id="quoteChar" label="%dataTransfer.processor.source.code.property.quoteChar.name" type="string" description="%dataTransfer.processor.source.code.property.quoteChar.description" defaultValue="&quot;" validValues="&quot;,&#39;"/>
                    <property id="rowDelimiter" label="%dataTransfer.processor.source.code.property.rowDelimiter.name" type="string" description="%dataTransfer.processor.source.code.property.rowDelimiter.description" defaultValue="default" validValues="default,\n,\r,\r\n,\n\r"/>
                </propertyGroup>
            </processor>
            <processor
                    id="stream.xlsx"
                    class="com.dc.summer.data.transfer.stream.exporter.DataExporterXLSX"
                    description="%dataTransfer.processor.xlsx.description"
                    icon="icons/excel.png"
                    label="%dataTransfer.processor.xlsx.name"
                    binary="true"
                    contentType="application/vnd.ms-excel"
                    appFileExtension="xlsx"
                    appName="Excel"
                    order="10">
                <propertyGroup label="%dataTransfer.processor.xlsx.propertyGroup.general.label">
                    <property id="extension" label="%dataTransfer.processor.xlsx.property.extension.label" defaultValue="xlsx"/>

                    <property id="rownumber" label="%dataTransfer.processor.xlsx.property.rowNumber.name" type="boolean" description="%dataTransfer.processor.xlsx.property.rowNumber.description" defaultValue="yes" required="false"/>
                    <property id="border" label="%dataTransfer.processor.xlsx.property.border.name" type="string" description="%dataTransfer.processor.xlsx.property.border.description" defaultValue="THIN" required="false" validValues="NONE,THIN,THICK"/>
                    <property id="nullString" label="%dataTransfer.processor.xlsx.property.nullString.name" type="string" description="%dataTransfer.processor.xlsx.property.nullString.description" defaultValue="" required="false"/>
                    <property id="header" label="%dataTransfer.processor.xlsx.property.header.name" type="boolean" description="%dataTransfer.processor.xlsx.property.header.description" defaultValue="true" required="true"/>
                    <property id="headerfont" label="%dataTransfer.processor.xlsx.property.headerFont.name" type="string" description="%dataTransfer.processor.xlsx.property.headerFont.description" defaultValue="BOLD" required="false" validValues="NONE,BOLD,ITALIC,STRIKEOUT,UNDERLINE"/>
                    <property id="trueString" label="%dataTransfer.processor.xlsx.property.boolStringTrue.name" type="string" description="%dataTransfer.processor.xlsx.property.boolStringTrue.description" defaultValue="true" required="false"/>
                    <property id="falseString" label="%dataTransfer.processor.xlsx.property.boolStringFalse.name" type="string" description="%dataTransfer.processor.xlsx.property.boolStringFalse.description" defaultValue="false" required="false"/>
                    <property id="exportSql" label="%dataTransfer.processor.xlsx.property.exportSql.name" type="boolean" description="%dataTransfer.processor.xlsx.property.exportSql.description" defaultValue="false" required="false"/>
                    <property id="splitSqlText" label="%dataTransfer.processor.xlsx.property.splitSqlText.name" type="boolean" description="%dataTransfer.processor.xlsx.property.splitSqlText.description" defaultValue="false" required="false"/>
                    <property id="splitByRowCount" label="%dataTransfer.processor.xlsx.property.splitByRowCount.name" type="integer" description="%dataTransfer.processor.xlsx.property.splitByRowCount.description" defaultValue="1048575" required="false"/>
                    <property id="splitByColNum" label="%dataTransfer.processor.xlsx.property.splitByColNum.name" type="integer" description="%dataTransfer.processor.xlsx.property.splitByColNum.description" defaultValue="0" required="false"/>
                    <property id="dateFormat" label="%dataTransfer.processor.xlsx.property.dateFormat.name" type="string" description="%dataTransfer.processor.xlsx.property.dateFormat.description" defaultValue="m/d/yy" required="false" validValues="m/d/yy,d-mmm-yy,d-mmm,mmm-yy,h:mm AM/PM,h:mm:ss AM/PM,h:mm,h:mm:ss,m/d/yy h:mm"/>
                    <property id="appendStrategy" label="%dataTransfer.processor.xlsx.property.appendStrategy.name" type="string" description="%dataTransfer.processor.xlsx.property.appendStrategy.description" defaultValue="create new sheets" required="true" validValues="create new sheets,use existing sheets" allowCustomValues="false"/>
                </propertyGroup>
            </processor>
        </node>


<!--        <transformer id="none" label="" description="PAss value as is" class="com.dc.summer.data.transfer.transformers.DataTransferTransformerNull"/>-->
        <transformer id="null" label="Set to NULL" description="Set column value to NULL" class="com.dc.summer.data.transfer.transformers.DataTransferTransformerNull"/>

        <transformer id="constant" label="Constant" description="Set column value to a constant value" class="com.dc.summer.data.transfer.transformers.DataTransferTransformerConstant">
            <propertyGroup label="Settings">
                <property id="constant" label="Constant value" type="string" description="Constant value" defaultValue="" required="false"/>
            </propertyGroup>
        </transformer>

        <transformer id="expression" label="Expression" description="Set column value to a result of expression" class="com.dc.summer.data.transfer.transformers.DataTransferTransformerExpression">
            <propertyGroup label="Settings">
                <property id="expression" label="Expression" type="string" description="Expression to evaluate" defaultValue="" required="true"/>
            </propertyGroup>
        </transformer>

        <eventProcessor
                id="showInExplorer"
                label="%dataTransfer.eventProcessor.showInExplorer.label"
                description="%dataTransfer.eventProcessor.showInExplorer.description"
                class="com.dc.summer.data.transfer.processor.ShowInExplorerEventProcessor"
                nodes="streamTransferConsumer"/>

        <eventProcessor
                id="executeCommand"
                label="%dataTransfer.eventProcessor.executeCommand.label"
                description="%dataTransfer.eventProcessor.executeCommand.description"
                class="com.dc.summer.data.transfer.processor.ExecuteCommandEventProcessor"
                nodes="streamTransferConsumer"/>

    </extension>

    <extension point="com.dc.summer.task">
        <category id="common" name="%task.category.name.common" description="%task.category.description.common" icon="platform:/plugin/com.dc.summer.model/icons/tree/task.png"/>

        <task id="dataExport" name="%task.name.export" description="%task.description.export" icon="icons/export.png" type="common" handler="com.dc.summer.data.transfer.task.DTTaskHandlerExport" supportsVariables="true">
            <objectType name="com.dc.summer.model.struct.DBSDataContainer"/>
        </task>
        <task id="dataImport" name="%task.name.import" description="%task.description.import" icon="icons/import.png" type="common" handler="com.dc.summer.data.transfer.task.DTTaskHandlerImport">
            <objectType name="com.dc.summer.model.struct.DBSDataManipulator"/>
        </task>

        <task id="scriptExecute" name="%task.name.sql.script" description="%task.description.sql.script" icon="icons/task_script.png" type="common" handler="com.dc.summer.data.sql.task.SQLScriptExecuteHandler" supportsVariables="true">
            <objectType name="org.eclipse.core.resources.IFile"/>
        </task>
    </extension>

    <extension point="com.dc.summer.serialize">
        <serializer id="databaseTransferProducer" class="com.dc.summer.data.transfer.database.DatabaseTransferProducer$ObjectSerializer"/>
        <serializer id="streamTransferProducer" class="com.dc.summer.data.transfer.stream.StreamTransferProducer$ObjectSerializer"/>

        <serializer id="databaseTransferConsumer" class="com.dc.summer.data.transfer.database.DatabaseTransferConsumerSerializer"/>
        <serializer id="streamTransferConsumer" class="com.dc.summer.data.transfer.stream.StreamTransferConsumer$ObjectSerializer"/>
    </extension>
</plugin>
