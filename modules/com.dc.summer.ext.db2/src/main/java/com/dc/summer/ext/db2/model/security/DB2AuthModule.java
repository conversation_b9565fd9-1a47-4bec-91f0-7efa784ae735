/*
 * DBeaver - Universal Database Manager
 * Copyright (C) 2013-2015 <PERSON> (<EMAIL>)
 * Copyright (C) 2010-2022 DBeaver Corp and others
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.dc.summer.ext.db2.model.security;

import com.dc.summer.DBException;
import com.dc.summer.ext.db2.DB2Constants;
import com.dc.summer.ext.db2.model.module.DB2Module;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.ext.db2.model.DB2Schema;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.CommonUtils;

import java.sql.ResultSet;

/**
 * DB2 Authorisations on Modules
 * 
 * <AUTHOR> Forveille
 */
public class DB2AuthModule extends DB2AuthBase {

    private DB2AuthHeldType execute;

    // -----------------------
    // Constructors
    // -----------------------
    public DB2AuthModule(DBRProgressMonitor monitor, DB2Grantee db2Grantee, DB2Module db2Module, ResultSet resultSet)
        throws DBException
    {
        super(monitor, db2Grantee, db2Module, resultSet);

        this.execute = CommonUtils.valueOf(DB2AuthHeldType.class, JDBCUtils.safeGetString(resultSet, "EXECUTEAUTH"));
    }

    // -----------------
    // Properties
    // -----------------

    @Property(viewable = true, order = 2)
    public DBSObject getObject()
    {
        return super.getObject();
    }

    @Property(viewable = true, order = 1)
    public DB2Schema getObjectSchema()
    {
        return super.getObjectSchema();
    }

    @Property(viewable = true, order = 20, category = DB2Constants.CAT_AUTH)
    public DB2AuthHeldType getExecute()
    {
        return execute;
    }

}
