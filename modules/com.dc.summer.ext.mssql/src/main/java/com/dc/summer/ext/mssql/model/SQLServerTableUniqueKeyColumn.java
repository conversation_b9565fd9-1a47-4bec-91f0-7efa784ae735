
package com.dc.summer.ext.mssql.model;

import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.impl.struct.AbstractTableConstraint;
import com.dc.summer.model.impl.struct.AbstractTableConstraintColumn;
import com.dc.summer.model.meta.Property;

/**
 * SQLServerTableUniqueKeyColumn.
 * No needed? Unique keys use index columns
 */
public class SQLServerTableUniqueKeyColumn extends AbstractTableConstraintColumn
{
    private AbstractTableConstraint<SQLServerTableBase> constraint;
    private SQLServerTableColumn tableColumn;
    private int ordinalPosition;

    public SQLServerTableUniqueKeyColumn(AbstractTableConstraint<SQLServerTableBase> constraint, SQLServerTableColumn tableColumn, int ordinalPosition)
    {
        this.constraint = constraint;
        this.tableColumn = tableColumn;
        this.ordinalPosition = ordinalPosition;
    }

    @Property(viewable = true, order = 1)
    @NotNull
    @Override
    public String getName()
    {
        return tableColumn.getName();
    }

    @NotNull
    @Override
    @Property(viewable = true, order = 1)
    public SQLServerTableColumn getAttribute()
    {
        return tableColumn;
    }

    @Override
    @Property(viewable = false, order = 2)
    public int getOrdinalPosition()
    {
        return ordinalPosition;
    }

    @Nullable
    @Override
    public String getDescription()
    {
        return tableColumn.getDescription();
    }

    @Override
    public AbstractTableConstraint<SQLServerTableBase> getParentObject()
    {
        return constraint;
    }

    @NotNull
    @Override
    public SQLServerDataSource getDataSource()
    {
        return constraint.getTable().getDataSource();
    }

}
