
package com.dc.summer.ext.mssql.model.generic;

import com.dc.function.RuntimeRunnable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.ext.mssql.SQLServerConstants;
import com.dc.summer.ext.mssql.SQLServerUtils;
import com.dc.summer.ext.mssql.model.SQLServerDataSource;
import com.dc.summer.model.DBPDataKind;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.ModelPreferences;
import com.dc.summer.ext.generic.model.GenericDataSource;
import com.dc.summer.ext.mssql.model.SQLServerDialect;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.utils.CommonUtils;
import lombok.SneakyThrows;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

public class SQLServerGenericDataSource extends GenericDataSource {

    private static final Log log = Log.getLog(SQLServerGenericDataSource.class);

    private static final String PROP_ENCRYPT_PASS = "ENCRYPT_PASSWORD";

    public SQLServerGenericDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container)
        throws DBException
    {
        this(monitor, container,
            new SQLServerMetaModel(
                SQLServerUtils.isDriverSqlServer(container.getDriver())
            ));
    }

    SQLServerGenericDataSource(DBRProgressMonitor monitor, DBPDataSourceContainer container, SQLServerMetaModel metaModel)
        throws DBException
    {
        super(monitor, container, metaModel, new SQLServerDialect());
    }

    @Override
    protected Map<String, String> getInternalConnectionProperties(DBRProgressMonitor monitor, DBPDriver driver, JDBCExecutionContext context, String purpose, DBPConnectionConfiguration connectionInfo) throws DBCException {
        Map<String, String> connectionsProps = new HashMap<>();
        if (!getContainer().getPreferenceStore().getBoolean(ModelPreferences.META_CLIENT_NAME_DISABLE)) {
            // App name
            connectionsProps.put(
                SQLServerUtils.isDriverJtds(driver) ? SQLServerConstants.APPNAME_CLIENT_PROPERTY : SQLServerConstants.APPLICATION_NAME_CLIENT_PROPERTY,
                CommonUtils.truncateString(DBUtils.getClientApplicationName(getContainer(), context, purpose), 64));
        }
        if (CommonUtils.toBoolean(connectionInfo.getProviderProperty(SQLServerConstants.PROP_ENCRYPT_PASSWORD))) {
            try {
                DBPPropertyDescriptor[] properties = driver.getDataSourceProvider().getConnectionProperties(monitor, driver, connectionInfo);
                for (DBPPropertyDescriptor descriptor : properties) {
                    if (descriptor.getId().equals(PROP_ENCRYPT_PASS) && descriptor instanceof PropertyDescriptor) {
                        connectionInfo.setProperty(PROP_ENCRYPT_PASS, "true"); // To apply changes
                        context.getDataSource().getContainer().getConnectionConfiguration().setProperty(PROP_ENCRYPT_PASS, "true"); // To see changes in the Driver Properties tab
                        break;
                    }
                }
            } catch (DBException e) {
                log.error("Can't read driver properties", e);
            }
        }
        return connectionsProps;
    }

    @Override
    public Object getDataSourceFeature(String featureId) {
        switch (featureId) {
            case DBPDataSource.FEATURE_LIMIT_AFFECTS_DML:
                return true;
            case DBPDataSource.FEATURE_MAX_STRING_LENGTH:
                return 8000;
        }
        return super.getDataSourceFeature(featureId);
    }

    @NotNull
    @Override
    public DBPDataKind resolveDataKind(@NotNull String typeName, int valueType) {
        if (valueType == Types.VARCHAR) {
            // Workaround for jTDS driver (#3555)
            switch (typeName) {
                case SQLServerConstants.TYPE_DATETIME:
                case SQLServerConstants.TYPE_DATETIME2:
                case SQLServerConstants.TYPE_SMALLDATETIME:
                    return DBPDataKind.DATETIME;
                case SQLServerConstants.TYPE_DATETIMEOFFSET:
                    return DBPDataKind.STRING;
            }
        }
        return super.resolveDataKind(typeName, valueType);
    }

    //////////////////////////////////////////////////////////
    // Databases

    @Override
    protected boolean isPopulateClientAppName() {
        return false;
    }

    @SneakyThrows
    @Override
    public RuntimeRunnable killConnection(DBRProgressMonitor monitor, JDBCExecutionContext defaultContext, String processId) {
        return new SQLServerDataSource(monitor, getContainer()).killConnection(monitor, defaultContext, processId);
    }
}
