package com.dc.parser.ext.oracle.statement.dcl;

import com.dc.parser.ext.oracle.statement.OracleStatement;
import com.dc.parser.model.statement.dcl.CreateRoleStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.RequiredArgsConstructor;

/**
 * Oracle create role statement.
 */
@RequiredArgsConstructor
public final class OracleCreateRoleStatement extends CreateRoleStatement implements OracleStatement {

    private final IdentifierValue roleName;
}
