package com.dc.parser.ext.oracle.check.rule.ddl;

import com.dc.parser.ext.oracle.segment.table.OracleTablePropertiesSegment;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.ddl.PartitionColumnNotNullRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.ModifyColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.AlterTableStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

public class OraclePartitionColumnNotNullRule extends PartitionColumnNotNullRule {

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        if (!(sqlStatement instanceof CreateTableStatement) && !(sqlStatement instanceof AlterTableStatement)) {
            return CheckResult.DEFAULT_SUCCESS_RESULT;
        }

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement checkStatement = (CreateTableStatement) sqlStatement;
            if (checkStatement.getRelationalTable().isPresent()) {
                RelationalTableSegment relationalTable = checkStatement.getRelationalTable().get();

                List<String> partitionColumnList = new ArrayList<>();
                if (relationalTable.getTableProperties() instanceof OracleTablePropertiesSegment) {
                    OracleTablePropertiesSegment oracleTablePropertiesSegment = (OracleTablePropertiesSegment) relationalTable.getTableProperties();
                    if (oracleTablePropertiesSegment.getPartitionProperties() != null) {
                        for (ColumnSegment partitionColumn : oracleTablePropertiesSegment.getPartitionProperties().getPartitionColumns()) {
                            partitionColumnList.add(partitionColumn.getIdentifier().getValue().toUpperCase(Locale.ROOT));
                        }
                        for (ColumnSegment subPartitionColumn : oracleTablePropertiesSegment.getPartitionProperties().getSubPartitionColumns()) {
                            partitionColumnList.add(subPartitionColumn.getIdentifier().getValue().toUpperCase(Locale.ROOT));
                        }
                    }
                }

                if (checkStatement.getRelationalTable().get().getColumnDefinitions() != null) {
                    Collection<ColumnDefinitionSegment> collect = checkStatement.getRelationalTable().get().getColumnDefinitions();
                    for (ColumnDefinitionSegment columnDefinitionSegment : collect) {
                        if (columnDefinitionSegment.getColumnName() != null && columnDefinitionSegment.getColumnName().getIdentifier() != null) {
                            String columnName = columnDefinitionSegment.getColumnName().getIdentifier().getValue();
                            if (partitionColumnList.contains(columnName.toUpperCase(Locale.ROOT)) && !columnDefinitionSegment.isNotNull()) {
                                return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                            }
                        }
                    }
                }
            }
        } else {
            AlterTableStatement checkStatement = (AlterTableStatement) sqlStatement;
            if (checkStatement.getModifyColumnDefinitions() != null && !checkStatement.getModifyColumnDefinitions().isEmpty()) {
                List<String> collect = parameter.getPartitionColumns().stream().map(String::toLowerCase).collect(Collectors.toList());
                for (ModifyColumnDefinitionSegment modifyColumnDefinition : checkStatement.getModifyColumnDefinitions()) {
                    if (modifyColumnDefinition.getColumnDefinition() != null
                            && modifyColumnDefinition.getColumnDefinition().getColumnName() != null
                            && modifyColumnDefinition.getColumnDefinition().getColumnName().getIdentifier() != null) {
                        String value = modifyColumnDefinition.getColumnDefinition().getColumnName().getIdentifier().getValue();
                        if (collect.contains(value.toLowerCase(Locale.ROOT)) && !modifyColumnDefinition.getColumnDefinition().isNotNull()) {
                            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
                        }
                    }
                }
            }
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.ORACLE_.getValue() + super.getType();
    }

    @Override
    public String getQueryPartitionColumnSql(String schemaName, String tableName) {
        return String.format("select\n" +
                "  partition_column,\n" +
                "  subpartition_column\n" +
                "from\n" +
                "  sys.all_part_tables a\n" +
                "  left join (\n" +
                "    select\n" +
                "      owner,\n" +
                "      name as table_name,\n" +
                "      column_name as partition_column\n" +
                "    from\n" +
                "      sys.all_part_key_columns\n" +
                "    where\n" +
                "      trim(object_type) = 'TABLE'\n" +
                "  ) b on a.owner = b.owner\n" +
                "  and a.table_name = b.table_name\n" +
                "  left join (\n" +
                "    select\n" +
                "      owner,\n" +
                "      name as table_name,\n" +
                "      column_name as subpartition_column\n" +
                "    from\n" +
                "      sys.all_subpart_key_columns\n" +
                "    where\n" +
                "      trim(object_type) = 'TABLE'\n" +
                "  ) c on a.owner = c.owner\n" +
                "  and a.table_name = c.table_name\n" +
                "where\n" +
                "  upper(a.owner) = upper('%s')\n" +
                "  and upper(a.table_name) = upper('%s')", schemaName, tableName);
    }

}
