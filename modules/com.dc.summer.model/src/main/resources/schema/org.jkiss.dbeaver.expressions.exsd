<?xml version='1.0' encoding='UTF-8'?>
<schema targetNamespace="com.dc.summer.model" xmlns="http://www.w3.org/2001/XMLSchema">
<annotation>
      <appInfo>
         <meta.schema plugin="com.dc.summer.model" id="com.dc.summer.expressions" name="Expression functions and namespaces"/>
      </appInfo>
      <documentation>
         [Enter description of this extension point.]
      </documentation>
   </annotation>


   <annotation>
      <appInfo>
         <meta.section type="apiinfo"/>
      </appInfo>
      <documentation>
         [Enter API information here.]
      </documentation>
   </annotation>

</schema>
