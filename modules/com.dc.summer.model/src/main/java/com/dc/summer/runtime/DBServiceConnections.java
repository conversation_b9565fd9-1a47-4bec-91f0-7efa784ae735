

package com.dc.summer.runtime;

import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressListener;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * Connections Service
 */
public interface DBServiceConnections {

    void initConnection(DBRProgressMonitor monitor, DBPDataSourceContainer dataSourceContainer, DBRProgressListener onFinish) throws DBException;

}