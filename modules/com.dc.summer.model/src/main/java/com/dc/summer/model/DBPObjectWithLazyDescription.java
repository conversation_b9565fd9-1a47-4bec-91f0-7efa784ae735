
package com.dc.summer.model;

import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

public interface DBPObjectWithLazyDescription extends DBPObjectWithDescription {
    /**
     * Loads description and returns it.
     *
     * @return object description or null
     */
    @Nullable
    String getDescription(DBRProgressMonitor monitor) throws DBException;
}
