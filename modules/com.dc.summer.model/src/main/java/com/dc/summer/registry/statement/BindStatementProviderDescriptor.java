

package com.dc.summer.registry.statement;

import com.dc.code.NotNull;
import com.dc.summer.model.statement.DBPBindStatementProvider;
import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;

/**
 *
 */
public class BindStatementProviderDescriptor extends AbstractDescriptor {
    private final String id;
    private final String implClass;
    private final String description;
    private final ObjectType implType;

    private volatile DBPBindStatementProvider instance;

    public BindStatementProviderDescriptor(IConfigurationElement config) {
        super(config.getContributor().getName());
        this.id = config.getAttribute("id");
        this.implClass = config.getAttribute("class");
        this.description = config.getAttribute("description");
        this.implType = new ObjectType(implClass);
    }

    public String getId() {
        return id;
    }

    public String getImplClass() {
        return implClass;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return id;
    }


    @NotNull
    public DBPBindStatementProvider getInstance() {
        if (instance == null) {
            synchronized (this) {
                if (instance == null) {
                    try {
                        // locate class
                        this.instance = implType.createInstance(DBPBindStatementProvider.class);
                    } catch (Exception ex) {
                        throw new IllegalStateException("Can't initialize external connection provider '" + implType.getImplName() + "'", ex);
                    }
                }
            }

        }
        return instance;
    }
}
