
package com.dc.summer.runtime.jobs;

import com.dc.summer.model.DBPDataSource;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.util.HashSet;
import java.util.Set;

/**
 * EndIdleTransactionsJob
 */
abstract class DataSourceUpdaterJob extends AbstractJob {

    private static final Set<String> activeDataSources = new HashSet<>();

    public static boolean isInProcess(DBPDataSourceContainer ds) {
        synchronized (activeDataSources) {
            return activeDataSources.contains(ds.getId());
        }
    }

    public DataSourceUpdaterJob(String name) {
        super(name);
    }

    public abstract DBPDataSource getDataSource();

    protected abstract IStatus updateDataSource(DBRProgressMonitor monitor);

    @Override
    protected IStatus run(DBRProgressMonitor monitor) {
        if (getDataSource() == null) {
            return Status.CANCEL_STATUS;
        }
        String dsId = getDataSource().getContainer().getId();
        synchronized (activeDataSources) {
            if (activeDataSources.contains(dsId)) {
                return Status.CANCEL_STATUS;
            }
            activeDataSources.add(dsId);
        }
        try {
            return updateDataSource(monitor);
        } finally {
            synchronized (activeDataSources) {
                activeDataSources.remove(dsId);
            }
        }
    }

}
