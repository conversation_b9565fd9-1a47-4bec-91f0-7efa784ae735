

package com.dc.summer.model.runtime.features;

import com.dc.code.NotNull;

/**
 * DBeaver notification description
 */
public class DBRNotificationDescriptor {

    @NotNull
    private DBRNotificationAction action;
    private String soundFile;
    private String shellCommand;

    public DBRNotificationDescriptor() {
        this.action = DBRNotificationAction.NONE;
    }

    public DBRNotificationDescriptor(@NotNull DBRNotificationAction action, String soundFile, String shellCommand) {
        this.action = action;
        this.soundFile = soundFile;
        this.shellCommand = shellCommand;
    }

    @NotNull
    public DBRNotificationAction getAction() {
        return action;
    }

    public void setAction(@NotNull DBRNotificationAction action) {
        this.action = action;
    }

    public String getSoundFile() {
        return soundFile;
    }

    public void setSoundFile(String soundFile) {
        this.soundFile = soundFile;
    }

    public String getShellCommand() {
        return shellCommand;
    }

    public void setShellCommand(String shellCommand) {
        this.shellCommand = shellCommand;
    }
}