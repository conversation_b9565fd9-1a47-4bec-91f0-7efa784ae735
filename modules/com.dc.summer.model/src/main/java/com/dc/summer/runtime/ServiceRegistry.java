
package com.dc.summer.runtime;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.impl.AbstractDescriptor;
import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServiceRegistry {
    private static final Log log = Log.getLog(ServiceRegistry.class);

    public static final String EXTENSION_ID = "com.dc.summer.service"; //$NON-NLS-1$

    private static ServiceRegistry instance = null;

    private class ServiceDescriptor extends AbstractDescriptor {

        private final ObjectType type;
        private final ObjectType impl;
        private final boolean headless;
        private Object instance;

        ServiceDescriptor(IConfigurationElement config) {
            super(config);
            type = new ObjectType(config.getAttribute("name"));
            impl = new ObjectType(config.getAttribute("class"));
            headless = CommonUtils.toBoolean(config.getAttribute("headless"));
        }
    }

    public synchronized static ServiceRegistry getInstance() {
        if (instance == null) {
            instance = new ServiceRegistry(Global.getExtensionRegistry());
        }
        return instance;
    }

    private final Map<String, List<ServiceDescriptor>> services = new HashMap<>();

    private ServiceRegistry(IExtensionRegistry registry) {
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement ext : extElements) {
            ServiceDescriptor service = new ServiceDescriptor(ext);
            List<ServiceDescriptor> descriptors = services.computeIfAbsent(service.type.getImplName(), s -> new ArrayList<>());
            descriptors.add(service);
        }
    }

    @Nullable
    public <T> T getService(@NotNull Class<T> serviceType) {
        List<ServiceDescriptor> descriptors = services.get(serviceType.getName());
        if (!CommonUtils.isEmpty(descriptors)) {
            boolean headlessMode = DBWorkbench.getPlatform().getApplication().isHeadlessMode();
            for (ServiceDescriptor descriptor : descriptors) {
                if (descriptors.size() > 1 && headlessMode != descriptor.headless) {
                    continue;
                }
                if (descriptor.instance == null) {
                    try {
                        descriptor.instance = descriptor.impl.createInstance(Object.class);
                    } catch (DBException e) {
                        log.debug("Error creating service '" + serviceType.getName() + "'", e);
                    }
                }
                return (T) descriptor.instance;
            }
        }
        return null;
    }

}
