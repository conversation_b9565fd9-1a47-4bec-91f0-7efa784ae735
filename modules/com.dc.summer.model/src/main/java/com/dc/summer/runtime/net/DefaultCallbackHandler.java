
package com.dc.summer.runtime.net;

import com.dc.summer.model.connection.DBPAuthInfo;
import com.dc.summer.runtime.DBWorkbench;

import javax.security.auth.callback.Callback;
import javax.security.auth.callback.CallbackHandler;
import javax.security.auth.callback.PasswordCallback;
import javax.security.auth.callback.UnsupportedCallbackException;
import java.io.IOException;

/**
 * Global DefaultCallbackHandler
 */
public class DefaultCallbackHandler implements CallbackHandler {

    private char[] password = null;

    @Override
    public void handle(Callback[] callbacks) throws IOException, UnsupportedCallbackException {
        for (Callback callback : callbacks) {
            if (callback instanceof PasswordCallback) {
                if (password == null) {
                    final DBPAuthInfo authInfo = DBWorkbench.getPlatformUI().promptUserCredentials("Enter password", null, null, true, true);
                    if (authInfo != null) {
                        if (authInfo.isSavePassword()) {
                            password = authInfo.getUserPassword().toCharArray();
                        }
                        ((PasswordCallback) callback).setPassword(authInfo.getUserPassword().toCharArray());
                    }
                } else {
                    ((PasswordCallback) callback).setPassword(password);
                }
            } else {
                throw new UnsupportedCallbackException(callback);
            }
        }
    }
}
