package com.dc.summer.model;

import java.sql.Driver;
import java.util.Map;

/**
 * 扩展连接提供程序
 */
public interface DBPExternalConnectionProvider {

    DBPExternalConnection openExternalConnection(DBPDataSourceContainer container,
                                                 Driver driver,
                                                 Map<String, String> ignoreProperties);

    void closeExternalConnection(DBPDataSourceContainer container);

}
