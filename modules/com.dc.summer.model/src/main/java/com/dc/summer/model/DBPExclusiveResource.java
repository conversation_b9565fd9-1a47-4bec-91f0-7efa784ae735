

package com.dc.summer.model;

import com.dc.code.NotNull;

/**
 * Exclusive resource is an object which can be used only by a single thread.
 * It is needed to avoid low-level synchronization (which may lead to deadlocks).
 */
public interface DBPExclusiveResource
{
    Object TASK_PROCESED = new Object();

    /**
     * Acquires exclusive resource lock. Waits until resource will be available.
     * @return lock object. Caller MUST call this function in pair with releaseExclusiveLock in try/finally block.
     */
    Object acquireExclusiveLock();


    /**
     * Releases exclusive lock. Threads waiting in acquireExclusiveLock now can continue.
     * @param lock lock object obtained in acquireExclusiveLock.
     */
    void releaseExclusiveLock(@NotNull Object lock);

    /**
     * Acquires named resource lock. Waits until resource will be available.
     * Works like {@link #acquireExclusiveLock} but if checkDup=true and lock is already acquired (i.e. task is running)
     * then returns {@link #TASK_PROCESED} as a result.
     */
    Object acquireTaskLock(@NotNull String taskName, boolean checkDup);

    void releaseTaskLock(@NotNull String taskName, @NotNull Object lock);

}
