package com.dc.summer.model;


import java.util.Map;

/**
 * Abstract context with attributes.
 * All attributes will be cleared after context close.
 * Attributes are valid only during implementor Java object live time.
 */
public interface DBPContextWithAttributes {

    /**
     * Returns copy of all context attributes
     */
    Map<String, ?> getContextAttributes();

    /**
     * Returns attribute value by name.
     */
    <T> T getContextAttribute(String attributeName);

    /**
     * Sets context attribute
     */
    <T> void setContextAttribute(String attributeName, T attributeValue);

    /**
     * Removes context attribute
     */
    void removeContextAttribute(String attributeName);

}