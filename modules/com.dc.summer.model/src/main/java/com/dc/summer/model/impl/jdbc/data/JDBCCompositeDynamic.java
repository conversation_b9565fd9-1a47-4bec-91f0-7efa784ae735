
package com.dc.summer.model.impl.jdbc.data;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.data.DBDComposite;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.model.struct.DBSEntityAttribute;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.exec.DBCSession;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Struct;

/**
 * Dynamic struct. Self contained entity.
 */
public class JDBCCompositeDynamic extends JDBCComposite {

    private static final Log log = Log.getLog(JDBCCompositeDynamic.class);
        //public static final int MAX_ITEMS_IN_STRING = 100;


    public JDBCCompositeDynamic(@NotNull JDBCComposite struct, @NotNull DBRProgressMonitor monitor) throws DBCException {
        super(monitor, struct);
    }

    public JDBCCompositeDynamic(@NotNull DBCSession session, @Nullable Struct contents, @Nullable ResultSetMetaData metaData) throws DBCException
    {
        super(contents);

        this.type = new StructType(session.getDataSource());

        // Extract structure data
        try {
            Object[] attrValues = contents == null ? null : contents.getAttributes();
            if (attrValues != null) {
                attributes = new DBSEntityAttribute[attrValues.length];
                values = new Object[attrValues.length];
                if (metaData != null) {
                    // Use meta data to read struct information
                    int attrCount = metaData.getColumnCount();
                    if (attrCount != attrValues.length) {
                        log.warn("Meta column count (" + attrCount + ") differs from value count (" + attrValues.length + ")");
                        attrCount = Math.min(attrCount, attrValues.length);
                    }
                    for (int i = 0; i < attrCount; i++) {
                        Object value = attrValues[i];
                        StructAttribute attr = new StructAttribute(this.type, metaData, i);
                        value = DBUtils.findValueHandler(session, attr).getValueFromObject(session, attr, value, false, modified);
                        attributes[i] = attr;
                        values[i] = value;
                    }
                } else {
                    log.warn("Data type '" + contents.getSQLTypeName() + "' isn't resolved as structured type. Use synthetic attributes.");
                    for (int i = 0, attrValuesLength = attrValues.length; i < attrValuesLength; i++) {
                        Object value = attrValues[i];
                        StructAttribute attr = new StructAttribute(this.type, i, value);
                        value = DBUtils.findValueHandler(session, attr).getValueFromObject(session, attr, value, false, modified);
                        attributes[i] = attr;
                        values[i] = value;
                    }
                }
            } else {
                this.attributes = DBDComposite.EMPTY_ATTRIBUTE;
                this.values = DBDComposite.EMPTY_VALUES;
            }
        } catch (DBException e) {
            throw new DBCException("Can't obtain attributes meta information", e);
        } catch (SQLException e) {
            throw new DBCException(e, session.getExecutionContext());
        }
    }

    @Override
    public DBSDataType getDataType()
    {
        return type;
    }

    @Override
    public JDBCCompositeDynamic cloneValue(DBRProgressMonitor monitor) throws DBCException
    {
        return new JDBCCompositeDynamic(this, monitor);
    }

}
