
package com.dc.summer.runtime.properties;

import com.dc.summer.model.DBUtils;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.preferences.DBPPropertySource;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.meta.PropertyLength;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Map-based property source
 */
public class PropertySourceMap implements DBPPropertySource {

    private List<DBPPropertyDescriptor> props = new ArrayList<>();

    private Map<?, ?> items;

    public PropertySourceMap(Map<String, ?> map)
    {
        items = new LinkedHashMap<>(map);
        for (Map.Entry<String, ?> entry : map.entrySet()) {
            props.add(new ItemPropertyDescriptor(entry.getKey(), entry.getValue()));
        }
    }

    @Override
    public Object getEditableValue()
    {
        return this;
    }

    @Override
    public DBPPropertyDescriptor[] getProperties() {
        return props.toArray(new DBPPropertyDescriptor[props.size()]);
    }

    @Override
    public Object getPropertyValue(@Nullable DBRProgressMonitor monitor, String id)
    {
        return items.get(id);
    }

    @Override
    public boolean isPropertySet(String id)
    {
        return false;
    }

    @Override
    public boolean isPropertyResettable(String id) {
        return false;
    }

    @Override
    public void resetPropertyValue(@Nullable DBRProgressMonitor monitor, String id)
    {

    }

    @Override
    public void resetPropertyValueToDefault(String id) {

    }

    @Override
    public void setPropertyValue(@Nullable DBRProgressMonitor monitor, String id, Object value)
    {
    }

    @Override
    public String toString() {
        return "<...>";
    }

    private static class ItemPropertyDescriptor implements DBPPropertyDescriptor {
        private final String name;
        private final Object value;

        ItemPropertyDescriptor(String name, Object value) {
            this.name = name;
            this.value = value;
        }

        @Override
        public String getCategory() {
            return null;
        }

        @Override
        public String getDescription() {
            return null;
        }

        @Override
        public Class<?> getDataType() {
            return Object.class;
        }

        @Override
        public boolean isRequired() {
            return false;
        }

        @Override
        public Object getDefaultValue() {
            return null;
        }

        @Override
        public boolean isEditable(Object object) {
            return false;
        }

        @NotNull
        @Override
        public PropertyLength getLength() {
            return PropertyLength.LONG;
        }

        @Nullable
        @Override
        public String[] getFeatures() {
            return null;
        }

        @Override
        public boolean hasFeature(@NotNull String feature) {
            return false;
        }

        @NotNull
        @Override
        public String getDisplayName() {
            return DBUtils.getObjectShortName(name);
        }

        @NotNull
        @Override
        public String getId() {
            return name;
        }
    }
}
