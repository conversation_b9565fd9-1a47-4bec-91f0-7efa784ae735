
package com.dc.summer.utils;

import com.dc.code.NotNull;
import com.dc.code.Nullable;

import java.util.Iterator;
import java.util.NoSuchElementException;

/**
 * Represents node of single-linked-list without any unwanted computational cost.
 * Takes 0 resources to represent empty list with NULL value.
 * @param <T>
 */
public class ListNode<T> implements Iterable<T> {
    public final ListNode<T> next;
    public final T data;

    private ListNode(@Nullable ListNode<T> next, T data) {
        this.next = next;
        this.data = data;
    }

    @NotNull
    public static <T> ListNode<T> of(@NotNull T data) {
        return new ListNode<T>(null, data);
    }

    @NotNull
    public static <T> ListNode<T> of(@NotNull T data1, @NotNull T data2) {
        return new ListNode<T>(new ListNode<T>(null, data1), data2);
    }

    @NotNull
    public static <T> ListNode<T> push(@Nullable ListNode<T> node, @NotNull T data) {
        return new ListNode<T>(node, data);
    }

    @NotNull
    public Iterator<T> iterator() {
        return new Iterator<T>() {
            private ListNode<T> node = ListNode.this;

            @Override
            public boolean hasNext() {
                return node != null;
            }

            @Override
            @NotNull
            public T next() {
                if (node != null) {
                    T result = node.data;
                    node = node.next;
                    return result;
                } else {
                    throw new NoSuchElementException();
                }
            }
        };
    }
}
