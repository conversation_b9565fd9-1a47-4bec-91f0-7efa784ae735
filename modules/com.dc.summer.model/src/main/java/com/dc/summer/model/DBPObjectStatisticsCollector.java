
package com.dc.summer.model;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * DBPObjectStatisticsCollector
 */
public interface DBPObjectStatisticsCollector {

    boolean isStatisticsCollected();

    /**
     * Loads statistics for child objects.
     */
    void collectObjectStatistics(DBRProgressMonitor monitor, boolean totalSizeOnly, boolean forceRefresh) throws DBException;

}
