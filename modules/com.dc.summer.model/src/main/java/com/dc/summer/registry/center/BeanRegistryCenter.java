package com.dc.summer.registry.center;

import com.dc.utils.verification.VerificationUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BeanRegistryCenter extends AbstractRegistryCenter<Object> {

    private static final Map<String, Object> MAP = new ConcurrentHashMap<>();

    private static final BeanRegistryCenter instance = new BeanRegistryCenter();

    private String name;

    private BeanRegistryCenter() {
    }

    public static BeanRegistryCenter getInstance() {
        return instance;
    }

    @Override
    public void register(Object o) {
        VerificationUtils.byFunction(o);
        this.name = o.getClass().getName();
        super.register(o);
    }

    @Override
    public Map<String, Object> getStorage() {
        return MAP;
    }

    @Override
    public String getName() {
        return this.name;
    }

}
