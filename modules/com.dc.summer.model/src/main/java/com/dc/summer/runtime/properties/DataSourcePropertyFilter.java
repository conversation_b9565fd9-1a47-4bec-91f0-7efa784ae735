
package com.dc.summer.runtime.properties;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.runtime.DBWorkbench;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * Datasource property filter
 */
public class DataSourcePropertyFilter implements IPropertyFilter {

    private final static Set<String> readExpPropertiesFor = new LinkedHashSet<>();

    private final boolean showExpensive;

    public DataSourcePropertyFilter()
    {
        this((DBPDataSourceContainer)null);
    }
    public DataSourcePropertyFilter(DBPDataSource dataSource)
    {
        this(dataSource == null ? null : dataSource.getContainer());
    }

    public DataSourcePropertyFilter(DBPDataSourceContainer container)
    {
        DBPPreferenceStore store = container != null ?
            container.getPreferenceStore() :
            ModelPreferences.getPreferences();
        this.showExpensive = store.getBoolean(ModelPreferences.READ_EXPENSIVE_PROPERTIES);
    }

    @Override
    public boolean select(Object object, DBPPropertyDescriptor property)
    {
        if (property instanceof ObjectPropertyDescriptor) {
            ObjectPropertyDescriptor prop = (ObjectPropertyDescriptor) property;
            if (!prop.isExpensive() || showExpensive) {
                return true;
            }
            if (object instanceof DBSObject) {
                return isExpensivePropertiesReadEnabledFor((DBSObject) object);
            }
            return false;
        }
        return false;
    }

    public static boolean isExpensivePropertiesReadEnabledFor(DBSObject object) {
        synchronized (readExpPropertiesFor) {
            String objectFullId = DBUtils.getObjectFullId(object);
            return readExpPropertiesFor.contains(objectFullId);
        }
    }

    public static void readExpensivePropertiesFor(DBSObject object, boolean read) {
        synchronized (readExpPropertiesFor) {
            String objectFullId = DBUtils.getObjectFullId(object);
            if (read) {
                readExpPropertiesFor.add(objectFullId);
            } else {
                readExpPropertiesFor.remove(objectFullId);
            }
        }
    }
}
