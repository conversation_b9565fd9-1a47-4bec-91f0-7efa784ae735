

package com.dc.summer.runtime;

import com.dc.summer.utils.ContentUtils;
import com.dc.summer.utils.GeneralUtils;
import com.dc.summer.utils.RuntimeUtils;
import org.eclipse.core.resources.IEncodedStorage;
import org.eclipse.core.resources.IStorage;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;

import java.io.*;

/**
 * LocalFileStorage
 */
public class LocalFileStorage implements IStorage, IPersistentStorage, IEncodedStorage {

    private final File file;
    private final String charset;

    public LocalFileStorage(File file, String charset) {
        this.file = file;
        this.charset = charset;
    }

    @Override
    public InputStream getContents() throws CoreException {
        try {
            return new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new CoreException(GeneralUtils.makeExceptionStatus(e));
        }
    }

    @Override
    public IPath getFullPath() {
        return new Path(file.getAbsolutePath());
    }

    @Override
    public String getName() {
        return file.getName();
    }

    @Override
    public boolean isReadOnly() {
        return !file.canWrite();
    }

    @Override
    public void setContents(IProgressMonitor monitor, InputStream stream) throws CoreException {
        try (OutputStream os = new FileOutputStream(file)) {
            ContentUtils.copyStreams(stream, 0, os, RuntimeUtils.makeMonitor(monitor));
        } catch (IOException e) {
            throw new CoreException(GeneralUtils.makeExceptionStatus(e));
        }
    }

    @Override
    public <T> T getAdapter(Class<T> adapter) {
        return null;
    }

    @Override
    public String getCharset() throws CoreException {
        return charset;
    }
}