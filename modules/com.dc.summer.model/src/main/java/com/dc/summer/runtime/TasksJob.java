
package com.dc.summer.runtime;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.DBRRunnableWithProgress;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * Job running specified tasks in queue
 */
public class TasksJob extends AbstractJob
{
    private final List<DBRRunnableWithProgress> tasks;

    private TasksJob(String name, Collection<DBRRunnableWithProgress> tasks) {
        super(name);
        setUser(true);
        this.tasks = new ArrayList<>(tasks);
    }

    private TasksJob(String name, DBRRunnableWithProgress task) {
        this(name, Collections.singletonList(task));
    }

    @Override
    protected IStatus run(DBRProgressMonitor monitor) {
        monitor.beginTask(getName(), tasks.size());
        boolean ignoreErrors = false;
        for (int i = 0; i < tasks.size(); ) {
            DBRRunnableWithProgress task = tasks.get(i);
            if (monitor.isCanceled()) {
                break;
            }
            try {
                task.run(monitor);
            } catch (InvocationTargetException e) {
                if (tasks.size() == 1) {
                    DBWorkbench.getPlatformUI().showError(getName(), null, e.getTargetException());
                } else if (!ignoreErrors) {
                    boolean keepRunning = true;
                    switch (DBWorkbench.getPlatformUI().showErrorStopRetryIgnore(getName(), e.getTargetException(), true)) {
                        case STOP:
                            keepRunning = false;
                            break;
                        case RETRY:
                            // just make it again
                            continue;
                        case IGNORE:
                            // Just do nothing
                            break;
                        case IGNORE_ALL:
                            ignoreErrors = true;
                            break;
                    }
                    if (!keepRunning) {
                        break;
                    }
                }

            } catch (InterruptedException e) {
                // Ignore
            }
            monitor.worked(1);
            i++;
        }
        monitor.done();
        return Status.OK_STATUS;
    }

    public static void runTasks(String name, Collection<DBRRunnableWithProgress> tasks) {
        new TasksJob(name, tasks).schedule();
    }

    public static void runTask(String name, DBRRunnableWithProgress task) {
        new TasksJob(name, task).schedule();
    }

}
