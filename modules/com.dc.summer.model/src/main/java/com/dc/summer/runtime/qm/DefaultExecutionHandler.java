
package com.dc.summer.runtime.qm;

import com.dc.summer.model.exec.*;
import com.dc.summer.model.qm.QMExecutionHandler;
import com.dc.summer.model.runtime.features.DBRFeature;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.DBPTransactionIsolation;

import java.util.Map;

/**
 * Default execution handler.
 * Handle methods are no-ops.
 */
public abstract class DefaultExecutionHandler implements QMExecutionHandler {

    @Override
    public void handleContextOpen(@NotNull DBCExecutionContext context, boolean transactional)
    {

    }

    @Override
    public void handleContextClose(@NotNull DBCExecutionContext context)
    {

    }

    @Override
    public void handleSessionOpen(@NotNull DBCSession session)
    {

    }

    @Override
    public void handleSessionClose(@NotNull DBCSession session)
    {

    }

    @Override
    public void handleTransactionAutocommit(@NotNull DBCExecutionContext context, boolean autoCommit)
    {

    }

    @Override
    public void handleTransactionIsolation(@NotNull DBCExecutionContext context, @NotNull DBPTransactionIsolation level)
    {

    }

    @Override
    public void handleTransactionCommit(@NotNull DBCExecutionContext context)
    {

    }

    @Override
    public void handleTransactionSavepoint(@NotNull DBCSavepoint savepoint)
    {

    }

    @Override
    public void handleTransactionRollback(@NotNull DBCExecutionContext context, DBCSavepoint savepoint)
    {

    }

    @Override
    public void handleStatementOpen(@NotNull DBCStatement statement)
    {

    }

    @Override
    public void handleStatementExecuteBegin(@NotNull DBCStatement statement)
    {

    }

    @Override
    public void handleStatementExecuteEnd(@NotNull DBCStatement statement, long rows, Throwable error)
    {
        
    }

    @Override
    public void handleStatementBind(@NotNull DBCStatement statement, Object column, Object value)
    {

    }

    @Override
    public void handleStatementClose(@NotNull DBCStatement statement, long rows)
    {

    }

    @Override
    public void handleResultSetOpen(@NotNull DBCResultSet resultSet)
    {

    }

    @Override
    public void handleResultSetClose(@NotNull DBCResultSet resultSet, long rowCount)
    {

    }

    @Override
    public void handleScriptBegin(@NotNull DBCSession session)
    {

    }

    @Override
    public void handleScriptEnd(@NotNull DBCSession session)
    {

    }

    @Override
    public void handleFeatureUsage(@NotNull DBRFeature feature, @Nullable Map<String, Object> parameters) {

    }
}
