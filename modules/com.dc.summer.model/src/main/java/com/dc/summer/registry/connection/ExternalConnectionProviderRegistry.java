package com.dc.summer.registry.connection;

import com.dc.summer.registry.center.Global;
import com.dc.summer.model.DBConstants;
import com.dc.summer.model.DBPExternalConnectionProvider;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.List;

public class ExternalConnectionProviderRegistry {

    public static final String EXTENSION_ID = "com.dc.summer.externalConnection"; //$NON-NLS-1$

    private static ExternalConnectionProviderRegistry instance = null;

    private final List<ConnectionPoolProviderDescriptor> pools = new ArrayList<>();

    public static ExternalConnectionProviderRegistry getInstance() {
        ExternalConnectionProviderRegistry registry = instance;
        if (registry == null) {
            synchronized (ExternalConnectionProviderRegistry.class) {
                registry = instance;
                if (registry == null) {
                    registry = instance = new ExternalConnectionProviderRegistry(Global.getExtensionRegistry());
                }
            }
        }
        return registry;
    }

    private ExternalConnectionProviderRegistry(IExtensionRegistry registry) {
        IConfigurationElement[] extElements = registry.getConfigurationElementsFor(EXTENSION_ID);
        for (IConfigurationElement element : extElements) {
            if (element.getName().equals("connection")) {
                this.pools.add(new ConnectionPoolProviderDescriptor(element));
            }
        }
    }

    public List<ConnectionPoolProviderDescriptor> getPools() {
        return new ArrayList<>(pools);
    }

    public ConnectionPoolProviderDescriptor getPool(String id) {
        for (ConnectionPoolProviderDescriptor poolDescriptor : pools) {
            if (poolDescriptor.getId().equalsIgnoreCase(id)) {
                return poolDescriptor;
            }
        }
        return null;
    }

    public DBPExternalConnectionProvider getExternalConnectionProvider(DBPConnectionConfiguration configuration) {
        String poolId = configuration.getProviderProperty(DBConstants.PROP_ID_POOL_ID);
        ConnectionPoolProviderDescriptor poolProviderDescriptor = getPool(poolId);
        if (poolProviderDescriptor == null && getPools().size() > 0) {
            poolProviderDescriptor = getPools().get(0);
            return poolProviderDescriptor.getInstance();
        }
        return null;
    }

}
