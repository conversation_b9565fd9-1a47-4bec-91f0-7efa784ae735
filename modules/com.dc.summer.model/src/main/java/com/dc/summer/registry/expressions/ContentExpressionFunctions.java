
package com.dc.summer.registry.expressions;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.dc.summer.Log;
import com.dc.summer.model.data.json.JSONUtils;
import com.dc.summer.model.impl.jdbc.data.JDBCContentXML;
import com.dc.utils.CommonUtils;
import com.dc.utils.xml.XMLException;
import com.dc.utils.xml.XMLUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.namespace.QName;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.StringReader;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ContentExpressionFunctions {

    private enum XMLExpressionResultType {
        STRING("string", XPathConstants.STRING),
        NUMBER("number", XPathConstants.NUMBER),
        BOOLEAN("boolean", XPathConstants.BOOLEAN),
        NODE("node", XPathConstants.NODE),
        NODESET("nodeset", XPathConstants.NODESET);

        String name;
        QName constant;

        XMLExpressionResultType(String name, QName constant) {
            this.name = name;
            this.constant = constant;
        }

        public QName getConstant() {
            return constant;
        }

        public static XMLExpressionResultType fromValue(String value) {
            for (XMLExpressionResultType type : XMLExpressionResultType.values()) {
                if (type.name.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException(value);
        }
    }

    private static final Log log = Log.getLog(ContentExpressionFunctions.class);

    private static final Gson GSON = new GsonBuilder()
        .setLenient()
        .serializeNulls()
        .setPrettyPrinting()
        .create();

    public static Object json(Object object) {
        if (object == null) {
            return null;
        }
        return JSONUtils.parseMap(GSON, new StringReader(object.toString()));
    }

    public static Object xml(Object object, String expression) {
        return xml(object, "string", expression);
    }

    public static Object xml(Object object, String returnType, String expression) {
        if (object == null || CommonUtils.isEmpty(expression) || !(object instanceof JDBCContentXML)) {
            return null;
        }
        XMLExpressionResultType resultType = XMLExpressionResultType.fromValue(returnType);
        if (resultType == null) {
            resultType = XMLExpressionResultType.STRING;
        }
        try {
            Document document = XMLUtils.parseDocument(new StringReader(((JDBCContentXML) object).getRawValue().getString()));
            XPath xPath = XPathFactory.newInstance().newXPath();
            Object result = xPath.evaluate(expression, document, resultType.getConstant());
            if (result instanceof NodeList) {
                List<String> valuesList = new ArrayList<>();
                NodeList nodeList = (NodeList) result;
                for (int i = 0; i < nodeList.getLength(); i++) {
                    valuesList.add(nodeList.item(i).getNodeValue());
                }
                return valuesList;
            }
            if (result instanceof Node) {
                return ((Node) result).getNodeValue();
            }
            return result;
        } catch (XMLException | XPathExpressionException | SQLException e) {
            log.error("Can't parse XML value", e);
        }
        return null;
    }
}
