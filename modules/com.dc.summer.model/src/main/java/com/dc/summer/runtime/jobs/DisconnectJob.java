
package com.dc.summer.runtime.jobs;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.utils.GeneralUtils;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;

/**
 * Disconnect job.
 * Always returns OK status.
 * To get real status use getConectStatus.
 */
public class DisconnectJob extends DataSourceUpdaterJob
{
    private IStatus connectStatus;
    protected final DBPDataSourceContainer container;

    public DisconnectJob(DBPDataSourceContainer container)
    {
        super("Disconnect from '" + container.getName() + "'");
        setUser(true);
        this.container = container;
    }

    public IStatus getConnectStatus() {
        return connectStatus;
    }

    @Override
    public DBPDataSource getDataSource() {
        return container.getDataSource();
    }

    @Override
    protected IStatus updateDataSource(DBRProgressMonitor monitor) {
        try {
            long startTime = System.currentTimeMillis();
            container.disconnect(monitor);

            connectStatus = Status.OK_STATUS;
        }
        catch (Throwable ex) {
            connectStatus = GeneralUtils.makeExceptionStatus(ex);
        }
        return Status.OK_STATUS;
    }

    @Override
    public boolean belongsTo(Object family)
    {
        return container == family;
    }

    @Override
    protected void canceling()
    {
        getThread().interrupt();
    }

}