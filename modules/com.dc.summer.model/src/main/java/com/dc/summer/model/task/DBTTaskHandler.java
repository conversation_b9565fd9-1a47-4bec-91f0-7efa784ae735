
package com.dc.summer.model.task;

import com.dc.summer.Log;
import com.dc.summer.model.runtime.DBRRunnableContext;
import com.dc.code.NotNull;
import com.dc.summer.DBException;

import java.io.PrintStream;
import java.util.Locale;

/**
 * Task handler
 */
public interface DBTTaskHandler {

    @NotNull
    DBTTaskRunStatus executeTask(
        @NotNull DBRRunnableContext runnableContext,
        @NotNull DBTTask task,
        @NotNull Locale locale,
        @NotNull Log log,
        @NotNull PrintStream logStream,
        @NotNull DBTTaskExecutionListener listener)
        throws DBException;


}
