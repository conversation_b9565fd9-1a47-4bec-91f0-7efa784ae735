

package com.dc.summer.model;

import com.dc.code.Nullable;

/**
 * DataSource provider editor.
 * May be editor, view or selection element
 */
public interface IDataSourceContainerProviderEx extends IDataSourceContainerProvider {

    /**
     * Change underlying datasource container
     * @return data source object.
     */
    boolean setDataSourceContainer(@Nullable DBPDataSourceContainer dataSourceContainer);

}