

package com.dc.summer.runtime.properties;

/**
 * PropertyCollector
 */
public class PropertyCollector extends PropertySourceAbstract
{
    public PropertyCollector(Object sourceObject, Object object, boolean loadLazyProps)
    {
        super(sourceObject, object, loadLazyProps);
    }

    public PropertyCollector(Object object, boolean loadLazyProps)
    {
        super(object, object, loadLazyProps);
    }

}
