package com.dc.summer.registry.center;

import java.util.Map;

public abstract class AbstractRegistryCenter<T> implements Registry<PERSON>enter<T> {

    @Override
    public void register(T t) {
        Map<String, T> map = getStorage();
        if (map == null) {
            throw new RegistryException("Storage 没有初始化！");
        }
        String name = getName();
        if (map.contains<PERSON>ey(name)) {
            return;
        }
        if (map.get(name) == null) {
            synchronized (this) {
                if (map.get(name) == null) {
                    map.putIfAbsent(name, t);
                }
            }
        }
    }

    @Override
    public T getByClassName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new RegistryException("Name 不能为空！");
        }
        return getStorage().get(name);
    }

    abstract Map<String, T> getStorage();

    abstract String getName();

}
