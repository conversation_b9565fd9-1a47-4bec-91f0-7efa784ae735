

package com.dc.summer.runtime.serialize;

import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRRunnableContext;

import java.util.Map;

/**
 * Object serializer
 */
public interface DBPObjectSerializer<CONTEXT_TYPE, OBJECT_TYPE> {

    void serializeObject(DBRRunnableContext runnableContext, CONTEXT_TYPE context, OBJECT_TYPE object, Map<String, Object> state);

    OBJECT_TYPE deserializeObject(DBRRunnableContext runnableContext, CONTEXT_TYPE objectContext, Map<String, Object> state) throws DBCException;

}
