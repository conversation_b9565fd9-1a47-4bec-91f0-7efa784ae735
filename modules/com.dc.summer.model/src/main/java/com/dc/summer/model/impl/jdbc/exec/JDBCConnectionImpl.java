
package com.dc.summer.model.impl.jdbc.exec;

import com.dc.summer.Log;
import com.dc.summer.model.statement.DBPBindStatementProvider;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.exec.jdbc.*;
import com.dc.summer.model.impl.jdbc.JDBCException;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.impl.jdbc.JDBCUtils;
import com.dc.summer.model.runtime.DBRBlockingObject;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPErrorAssistant;
import com.dc.summer.model.impl.AbstractSession;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.struct.DBSDataType;
import com.dc.summer.registry.statement.BindStatementProviderDescriptor;
import com.dc.summer.registry.statement.BindStatementProviderRegistry;

import java.sql.*;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Executor;

/**
 * Managable connection
 */
public class JDBCConnectionImpl extends AbstractSession implements JDBCSession, DBRBlockingObject {

    private static final Log log = Log.getLog(JDBCConnectionImpl.class);

    @NotNull
    protected final JDBCExecutionContext context;

    private static final BindStatementProviderRegistry BIND_STATEMENT_PROVIDER_REGISTRY = BindStatementProviderRegistry.getInstance();

    public JDBCConnectionImpl(@NotNull JDBCExecutionContext context, @NotNull DBRProgressMonitor monitor, @NotNull DBCExecutionPurpose purpose, @NotNull String taskTitle) {
        super(monitor, purpose, taskTitle);
        this.context = context;
        super.setUseNativeDateTimeFormat(false);
    }

    @Override
    public Connection getOriginal() throws SQLException {
        return context.getConnection(getProgressMonitor());
    }

    @NotNull
    @Override
    public JDBCExecutionContext getExecutionContext() {
        return context;
    }

    @NotNull
    @Override
    public JDBCDataSource getDataSource() {
        return context.getDataSource();
    }

    @Override
    public boolean isConnected() {
        try {
            synchronized (context) {
                return !isClosed();
            }
        } catch (SQLException e) {
            log.error("could not check connection state", e);
            return false;
        }
    }

    @NotNull
    @Override
    public JDBCStatement prepareStatement(
            @NotNull DBCStatementType type,
            @NotNull String sqlQuery,
            boolean scrollable,
            boolean updatable,
            boolean returnGeneratedKeys)
            throws DBCException {
        try {
            boolean hasParameters = false;
            DBPBindStatementProvider bindStatementProvider = null;
            String bindingId = this.getDataSource().getContainer().getDriver().getProviderDescriptor().getId();
            BindStatementProviderDescriptor bindStatementProviderDescriptor = BIND_STATEMENT_PROVIDER_REGISTRY.getBinding(bindingId);
            if (bindStatementProviderDescriptor != null) {
                bindStatementProvider = bindStatementProviderDescriptor.getInstance();
                hasParameters = bindStatementProvider != null && bindStatementProvider.bindingParameters(sqlQuery, this);
            }

            // 类型是执行，并且查询具有输出参数，创建CallableStatement对象，该对象将生成具有给定类型和并发性的ResultSet对象。
            if (type == DBCStatementType.EXEC && JDBCUtils.queryHasOutputParameters(getDataSource().getSQLDialect(), sqlQuery)) {
                // 仅当我们查询的参数超出范围时，才作为调用执行
                // Execute as call - only if we query has out parameters bounds
                try {
                    return prepareCall(
                            sqlQuery,
                            scrollable ? ResultSet.TYPE_SCROLL_INSENSITIVE : ResultSet.TYPE_FORWARD_ONLY,
                            updatable ? ResultSet.CONCUR_UPDATABLE : ResultSet.CONCUR_READ_ONLY);
                } catch (SQLSyntaxErrorException e) {
                    // 不支持调用语法。让我们尝试将其作为常规查询执行
                    // Call syntax not supported. Let's try execute it as a regular query
                    return prepareStatement(DBCStatementType.QUERY, sqlQuery, scrollable, updatable, returnGeneratedKeys);
                } catch (SQLFeatureNotSupportedException | UnsupportedOperationException | IncompatibleClassChangeError e) {
                    return prepareCall(sqlQuery);
                } catch (SQLException e) {
                    if (DBExecUtils.discoverErrorType(getDataSource(), e) == DBPErrorAssistant.ErrorType.FEATURE_UNSUPPORTED) {
                        return prepareCall(sqlQuery);
                    } else {
                        throw e;
                    }
                }
            }
            // 类型是脚本，创建一个Statement对象，该对象将生成具有给定类型和并发性的ResultSet对象。
            else if (type == DBCStatementType.SCRIPT && !hasParameters) {
                // 只是脚本的最简单语句
                // 有时，准备好的语句会对查询执行附加检查
                //（例如，在Oracle中，它解析输入/输出参数）
                // Just simplest statement for scripts
                // Sometimes prepared statements perform additional checks of queries
                // (e.g. in Oracle it parses IN/OUT parameters)
                JDBCStatement statement;
                try {
                    if (!scrollable && !updatable) {
                        statement = createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                    } else {
                        statement = createStatement(
                                scrollable ? ResultSet.TYPE_SCROLL_INSENSITIVE : ResultSet.TYPE_FORWARD_ONLY,
                                updatable ? ResultSet.CONCUR_UPDATABLE : ResultSet.CONCUR_READ_ONLY);
                    }
                } catch (Throwable e) {
                    try {
                        statement = createStatement();
                    } catch (Throwable e1) {
                        try {
                            statement = prepareStatement(
                                    sqlQuery,
                                    scrollable ? ResultSet.TYPE_SCROLL_INSENSITIVE : ResultSet.TYPE_FORWARD_ONLY,
                                    updatable ? ResultSet.CONCUR_UPDATABLE : ResultSet.CONCUR_READ_ONLY);
                        } catch (Throwable e2) {
                            log.debug(e);
                            statement = prepareStatement(sqlQuery);
                        }
                    }
                }
                if (statement instanceof JDBCStatementImpl) {
                    statement.setQueryString(sqlQuery);
                }
                if (context.getDataSource().getSQLDialect().isDisableScriptEscapeProcessing()) {
                    disableStatementEscapeProcessing(statement);
                }
                return statement;
            }
            // 否则生成通用预处理语句，创建一个PreparedStatement对象，该对象将生成具有给定类型和并发性的ResultSet对象。
            else {
                JDBCPreparedStatement dbStat;
                // 如果没有用?替代，就使用默认SQL
                sqlQuery = hasParameters ? bindStatementProvider.getSQLQuery() : sqlQuery;
                try {
                    // 返回生成的密钥，创建一个默认的PreparedStatement对象，该对象能够检索自动生成的密钥。
                    if (returnGeneratedKeys) {
                        dbStat = prepareStatement(
                                sqlQuery,
                                Statement.RETURN_GENERATED_KEYS);
                    } else {
                        // 通用准备语句
                        // Generic prepared statement
                        dbStat = prepareStatement(
                                sqlQuery,
                                scrollable ? ResultSet.TYPE_SCROLL_INSENSITIVE : ResultSet.TYPE_FORWARD_ONLY,
                                updatable ? ResultSet.CONCUR_UPDATABLE : ResultSet.CONCUR_READ_ONLY);
                    }
                } catch (SQLFeatureNotSupportedException | UnsupportedOperationException | IncompatibleClassChangeError e) {
                    dbStat = prepareStatement(sqlQuery);
                } catch (SQLException e) {
                    if (DBExecUtils.discoverErrorType(getDataSource(), e) == DBPErrorAssistant.ErrorType.FEATURE_UNSUPPORTED) {
                        dbStat = prepareStatement(sqlQuery);
                    } else {
                        throw e;
                    }
                }

                if (hasParameters) {
                    DBDValueHandler valueHandler = bindStatementProvider.getValueHandler();
                    Object[] parameters = bindStatementProvider.getParameters();
                    try {
                        DBSDataType dataType = bindStatementProvider.getDataType(this);
                        if (valueHandler != null) {
                            for (int i = 0; i < parameters.length; i++) {
                                valueHandler.bindValueObject(this, dbStat, dataType, i, parameters[i]);
                            }
                        }
                    } catch (DBException e) {
                        throw new SQLException(e);
                    }
                }

                return dbStat;
            }
        } catch (SQLException e) {
            throw new JDBCException(e, getExecutionContext());
        }
    }

    // Disable escaping (#3512)
    private void disableStatementEscapeProcessing(JDBCStatement statement) {
        if (statement != null) {
            try {
                statement.setEscapeProcessing(false);
            } catch (Throwable e) {
                log.debug(e);
            }
        }
    }

    @NotNull
    @Override
    public DBDValueHandler getDefaultValueHandler() {
        return context.getDataSource().getContainer().getDefaultValueHandler();
    }

    private JDBCStatement makeStatement(Statement statement)
            throws SQLException {
        if (statement instanceof CallableStatement) {
            return createCallableStatementImpl((CallableStatement) statement, null);
        } else if (statement instanceof PreparedStatement) {
            return createPreparedStatementImpl((PreparedStatement) statement, null);
        } else {
            return createStatementImpl(statement);
        }
    }

    @NotNull
    @Override
    public JDBCStatement createStatement()
            throws SQLException {
        return makeStatement(getOriginal().createStatement());
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(getOriginal().prepareStatement(sql), sql);
    }

    @NotNull
    @Override
    public JDBCCallableStatement prepareCall(String sql)
            throws SQLException {
        log.debug("==> SQL - prepareCall: " + sql);
        return createCallableStatementImpl(getOriginal().prepareCall(sql), sql);
    }

    @Override
    public String nativeSQL(String sql)
            throws SQLException {
        log.debug("==> SQL - nativeSQL: " + sql);
        return getOriginal().nativeSQL(sql);
    }

    @Override
    public void setAutoCommit(boolean autoCommit)
            throws SQLException {
        getOriginal().setAutoCommit(autoCommit);

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionAutocommit(context, autoCommit);
        }
    }

    @Override
    public boolean getAutoCommit()
            throws SQLException {
        return getOriginal().getAutoCommit();
    }

    @Override
    public void commit()
            throws SQLException {
        getOriginal().commit();

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionCommit(context);
        }
    }

    @Override
    public void rollback()
            throws SQLException {
        getOriginal().rollback();

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionRollback(context, null);
        }
    }

    @Override
    public void close() {
        DBCExecutionPurpose purpose = getPurpose();
        if (purpose.isUser()) {
            // Check for warnings
            try {
                final Connection connection = context.isConnected() ? getOriginal() : null;
                if (connection != null) {
                    JDBCUtils.reportWarnings(this, connection.getWarnings());
                    connection.clearWarnings();
                }
            } catch (Throwable e) {
                log.debug("Can't check for connection warnings", e);
            }
        }

        super.close();
    }

    @Override
    public boolean isClosed()
            throws SQLException {
        return getOriginal().isClosed();
    }

    @NotNull
    @Override
    public JDBCDatabaseMetaData getMetaData()
            throws SQLException {
        return new JDBCDatabaseMetaDataImpl(this, getOriginal().getMetaData());
    }

    @Override
    public void setReadOnly(boolean readOnly)
            throws SQLException {
        getOriginal().setReadOnly(readOnly);
    }

    @Override
    public boolean isReadOnly()
            throws SQLException {
        return getOriginal().isReadOnly();
    }

    @Override
    public void setCatalog(String catalog)
            throws SQLException {
        getOriginal().setCatalog(catalog);
    }

    @Override
    public String getCatalog()
            throws SQLException {
        return getOriginal().getCatalog();
    }

    @Override
    public void setTransactionIsolation(int level)
            throws SQLException {
        getOriginal().setTransactionIsolation(level);
    }

    @Override
    public int getTransactionIsolation()
            throws SQLException {
        return getOriginal().getTransactionIsolation();
    }

    @Override
    public SQLWarning getWarnings()
            throws SQLException {
        return getOriginal().getWarnings();
    }

    @Override
    public void clearWarnings()
            throws SQLException {
        getOriginal().clearWarnings();
    }

    @Override
    public JDBCStatement createStatement(int resultSetType, int resultSetConcurrency)
            throws SQLException {
        return createStatementImpl(getOriginal().createStatement(resultSetType, resultSetConcurrency));
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(
                getOriginal().prepareStatement(sql, resultSetType, resultSetConcurrency),
                sql);
    }

    @NotNull
    @Override
    public JDBCCallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency)
            throws SQLException {
        log.debug("==> SQL - prepareCall: " + sql);
        return createCallableStatementImpl(getOriginal().prepareCall(sql, resultSetType, resultSetConcurrency), sql);
    }

    @Override
    public Map<String, Class<?>> getTypeMap()
            throws SQLException {
        return getOriginal().getTypeMap();
    }

    @Override
    public void setTypeMap(Map<String, Class<?>> map)
            throws SQLException {
        getOriginal().setTypeMap(map);
    }

    @Override
    public void setHoldability(int holdability)
            throws SQLException {
        getOriginal().setHoldability(holdability);
    }

    @Override
    public int getHoldability()
            throws SQLException {
        return getOriginal().getHoldability();
    }

    @Override
    public Savepoint setSavepoint()
            throws SQLException {
        Savepoint savepoint = getOriginal().setSavepoint();

        JDBCSavepointImpl jdbcSavepoint = new JDBCSavepointImpl(context, savepoint);

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionSavepoint(jdbcSavepoint);
        }

        return jdbcSavepoint;
    }

    @Override
    public Savepoint setSavepoint(String name)
            throws SQLException {
        Savepoint savepoint = getOriginal().setSavepoint(name);

        JDBCSavepointImpl jdbcSavepoint = new JDBCSavepointImpl(context, savepoint);

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionSavepoint(jdbcSavepoint);
        }

        return jdbcSavepoint;
    }

    @Override
    public void rollback(Savepoint savepoint)
            throws SQLException {
        if (savepoint instanceof JDBCSavepointImpl) {
            savepoint = ((JDBCSavepointImpl) savepoint).getOriginal();
        }
        getOriginal().rollback(savepoint);

        if (isLoggingEnabled()) {
            QMUtils.getDefaultHandler().handleTransactionRollback(context, savepoint instanceof DBCSavepoint ? (DBCSavepoint) savepoint : null);
        }
    }

    @Override
    public void releaseSavepoint(Savepoint savepoint)
            throws SQLException {
        if (savepoint instanceof JDBCSavepointImpl) {
            savepoint = ((JDBCSavepointImpl) savepoint).getOriginal();
        }
        getOriginal().releaseSavepoint(savepoint);
    }

    @NotNull
    @Override
    public JDBCStatement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability)
            throws SQLException {
        return makeStatement(getOriginal().createStatement(resultSetType, resultSetConcurrency, resultSetHoldability));
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(
                getOriginal().prepareStatement(sql, resultSetType, resultSetConcurrency, resultSetHoldability),
                sql);
    }

    @NotNull
    @Override
    public JDBCCallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability)
            throws SQLException {
        log.debug("==> SQL - prepareCall: " + sql);
        return createCallableStatementImpl(
                getOriginal().prepareCall(sql, resultSetType, resultSetConcurrency, resultSetHoldability),
                sql);
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql, int autoGeneratedKeys)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(getOriginal().prepareStatement(sql, autoGeneratedKeys), sql);
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql, int[] columnIndexes)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(getOriginal().prepareStatement(sql, columnIndexes), sql);
    }

    @NotNull
    @Override
    public JDBCPreparedStatement prepareStatement(String sql, String[] columnNames)
            throws SQLException {
        log.debug("==> SQL - prepareStatement: " + sql);
        return createPreparedStatementImpl(getOriginal().prepareStatement(sql, columnNames), sql);
    }

    @Nullable
    @Override
    public String getSchema() throws SQLException {
        return getOriginal().getSchema();
    }

    @Override
    public void setSchema(String schema) throws SQLException {
        getOriginal().setSchema(schema);
    }

    @Override
    public void abort(Executor executor) throws SQLException {
        getOriginal().abort(executor);
    }

    @Override
    public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
        try {
            getOriginal().setNetworkTimeout(executor, milliseconds);
        } catch (AbstractMethodError e) {
            log.error("设置网络超时失败！", e);
        }
    }

    @Override
    public int getNetworkTimeout() throws SQLException {
        return getOriginal().getNetworkTimeout();
    }

    @Override
    public Clob createClob()
            throws SQLException {
        return getOriginal().createClob();
    }

    @Override
    public Blob createBlob()
            throws SQLException {
        return getOriginal().createBlob();
    }

    @Override
    public NClob createNClob()
            throws SQLException {
        return getOriginal().createNClob();
    }

    @Override
    public SQLXML createSQLXML()
            throws SQLException {
        return getOriginal().createSQLXML();
    }

    @Override
    public boolean isValid(int timeout)
            throws SQLException {
        return getOriginal().isValid(timeout);
    }

    @Override
    public void setClientInfo(String name, String value)
            throws SQLClientInfoException {
        try {
            getOriginal().setClientInfo(name, value);
        } catch (SQLException e) {
            if (e instanceof SQLClientInfoException) {
                throw (SQLClientInfoException) e;
            } else {
                throw new SQLClientInfoException();
            }
        }
    }

    @Override
    public void setClientInfo(Properties properties)
            throws SQLClientInfoException {
        try {
            getOriginal().setClientInfo(properties);
        } catch (SQLException e) {
            if (e instanceof SQLClientInfoException) {
                throw (SQLClientInfoException) e;
            } else {
                log.debug(e);
                throw new SQLClientInfoException();
            }
        }
    }

    @Override
    public String getClientInfo(String name)
            throws SQLException {
        return getOriginal().getClientInfo(name);
    }

    @Override
    public Properties getClientInfo()
            throws SQLException {
        return getOriginal().getClientInfo();
    }

    @Override
    public Array createArrayOf(String typeName, Object[] elements)
            throws SQLException {
        return getOriginal().createArrayOf(typeName, elements);
    }

    @Override
    public Struct createStruct(String typeName, Object[] attributes)
            throws SQLException {
        return getOriginal().createStruct(typeName, attributes);
    }

    @Override
    public <T> T unwrap(Class<T> iface)
            throws SQLException {
        return getOriginal().unwrap(iface);
    }

    @Override
    public boolean isWrapperFor(Class<?> iface)
            throws SQLException {
        return getOriginal().isWrapperFor(iface);
    }

    @Override
    public void cancelBlock(@NotNull DBRProgressMonitor monitor, @Nullable Thread blockThread)
            throws DBException {
        if (context.isConnected()) {
            try {
                // Sync execution context because async access during disconnect may cause troubles
                synchronized (getExecutionContext()) {
                    if (!getDataSource().closeConnection(getOriginal(), "Close database connection", false)) {
                        throw new DBCException("Couldn't close JDBC connection: timeout");
                    }
                }
            } catch (SQLException e) {
                throw new DBCException(e, getExecutionContext());
            }
        }
    }

    protected JDBCStatement createStatementImpl(Statement original)
            throws SQLException, IllegalArgumentException {
        if (original == null) {
            throw new IllegalArgumentException("Null statement");
        }
        return context.getDataSource().getJdbcFactory().createStatement(this, original, !isLoggingEnabled());
    }

    protected JDBCPreparedStatement createPreparedStatementImpl(PreparedStatement original, @Nullable String sql)
            throws SQLException, IllegalArgumentException {
        if (original == null) {
            throw new IllegalArgumentException("Null statement");
        }
        return context.getDataSource().getJdbcFactory().createPreparedStatement(this, original, sql, !isLoggingEnabled());
    }

    protected JDBCCallableStatement createCallableStatementImpl(CallableStatement original, @Nullable String sql)
            throws SQLException, IllegalArgumentException {
        if (original == null) {
            throw new IllegalArgumentException("Null statement");
        }
        return context.getDataSource().getJdbcFactory().createCallableStatement(this, original, sql, !isLoggingEnabled());
    }

}
