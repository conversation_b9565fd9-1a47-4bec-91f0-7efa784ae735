

package com.dc.summer.utils;

import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import org.eclipse.core.filesystem.EFS;
import org.eclipse.core.filesystem.IFileInfo;
import org.eclipse.core.filesystem.IFileStore;
import org.eclipse.core.resources.*;
import org.eclipse.core.runtime.CoreException;
import org.eclipse.core.runtime.IPath;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.data.DBDContentCached;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.ArrayUtils;
import com.dc.utils.ByteNumberFormat;
import com.dc.utils.CommonUtils;
import com.dc.utils.IOUtils;

import java.io.*;
import java.net.URI;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Content manipulation utilities
 */
public class ContentUtils {

    static final int STREAM_COPY_BUFFER_SIZE = 10000;
    private static final String LOB_DIR = ".lob"; //$NON-NLS-1$

    private static final Log log = Log.getLog(ContentUtils.class);

    public static File getLobFolder(DBRProgressMonitor monitor, DBPPlatform application)
        throws IOException
    {
        return application.getTempFolder(monitor, LOB_DIR);
    }

    public static File createTempContentFile(DBRProgressMonitor monitor, DBPPlatform application, String fileName)
        throws IOException
    {
        return makeTempFile(
            monitor,
            getLobFolder(monitor, application),
            fileName,
            "data");
/*
        try {
            String charset = application.getPreferenceStore().getString(ModelPreferences.CONTENT_HEX_ENCODING);
            file.setCharset(charset, monitor.getNestedMonitor());
        } catch (CoreException e) {
            log.error("Can't set file charset", e);
        }
        return file;
*/
    }

    public static File makeTempFile(DBRProgressMonitor monitor, File folder, String name, String extension)
        throws IOException
    {
        name = CommonUtils.escapeFileName(name);
        File tempFile = new File(folder, name + "-" + System.currentTimeMillis() + "." + extension);  //$NON-NLS-1$ //$NON-NLS-2$
        if (!tempFile.createNewFile()){
            throw new IOException(MessageFormat.format(ModelMessages.error_can_create_temp_file, tempFile.getAbsolutePath(), folder.getAbsoluteFile()));
        }
        return tempFile;
    }

    public static void deleteTempFile(DBRProgressMonitor monitor, IFile file)
    {
        try {
            file.delete(true, false, monitor.getNestedMonitor());
        }
        catch (CoreException e) {
            log.warn("Can't delete temporary file '" + file.getFullPath().toString() + "'", e);
        }
    }

    public static void saveContentToFile(InputStream contentStream, File file, DBRProgressMonitor monitor)
        throws IOException
    {
        try (OutputStream os = new FileOutputStream(file)) {
            copyStreams(contentStream, file.length(), os, monitor);
        }
        // Check for cancel
        if (monitor.isCanceled()) {
            // Delete output file
            if (!file.delete()) {
                log.warn("Can't delete incomplete file '" + file.getAbsolutePath() + "'");
            }
        }
    }

    public static void saveContentToFile(Reader contentReader, File file, String charset, DBRProgressMonitor monitor)
        throws IOException
    {
        try (Writer writer = new OutputStreamWriter(new FileOutputStream(file), charset)) {
            copyStreams(contentReader, file.length(), writer, monitor);
        }
        // Check for cancel
        if (monitor.isCanceled()) {
            // Delete output file
            if (!file.delete()) {
                log.warn("Can't delete incomplete file '" + file.getAbsolutePath() + "'");
            }
        }
    }

    public static void copyStreams(
        InputStream inputStream,
        long contentLength,
        OutputStream outputStream,
        DBRProgressMonitor monitor)
        throws IOException
    {
        monitor.beginTask("Copy binary content", contentLength < 0 ? STREAM_COPY_BUFFER_SIZE : (int) contentLength);
        try {
            byte[] buffer = new byte[STREAM_COPY_BUFFER_SIZE];
            long totalCopied = 0;
            NumberFormat nf = new ByteNumberFormat(ByteNumberFormat.BinaryPrefix.ISO);
            String subtaskSuffix = " / " + nf.format(contentLength);
            for (;;) {
                if (monitor.isCanceled()) {
                    break;
                }
                int count = inputStream.read(buffer);
                if (count <= 0) {
                    break;
                }
                totalCopied += count;
                outputStream.write(buffer, 0, count);
                monitor.worked(STREAM_COPY_BUFFER_SIZE);
                if (contentLength > 0) {
                    monitor.subTask(nf.format(totalCopied) + subtaskSuffix);
                }
            }
        }
        finally {
            monitor.done();
        }
    }

    public static void copyStreams(
        Reader reader,
        long contentLength,
        Writer writer,
        DBRProgressMonitor monitor)
        throws IOException
    {
        monitor.beginTask("Copy character content", contentLength < 0 ? STREAM_COPY_BUFFER_SIZE : (int) contentLength);
        try {
            char[] buffer = new char[STREAM_COPY_BUFFER_SIZE];
            for (;;) {
                if (monitor.isCanceled()) {
                    break;
                }
                int count = reader.read(buffer);
                if (count <= 0) {
                    break;
                }
                writer.write(buffer, 0, count);
                monitor.worked(STREAM_COPY_BUFFER_SIZE);
            }
        }
        finally {
            monitor.done();
        }
    }

    public static long calculateContentLength(
        File file,
        String charset)
        throws IOException
    {
        return calculateContentLength(
            new FileInputStream(file),
            charset);
    }

    public static long calculateContentLength(
        InputStream stream,
        String charset)
        throws IOException
    {
        return calculateContentLength(
            new InputStreamReader(
                stream,
                charset));
    }

    public static long calculateContentLength(
        Reader reader)
        throws IOException
    {
        try {
            long length = 0;
            char[] buffer = new char[STREAM_COPY_BUFFER_SIZE];
            for (;;) {
                int count = reader.read(buffer);
                if (count <= 0) {
                    break;
                }
                length += count;
            }
            return length;
        }
        finally {
            reader.close();
        }
    }

    public static void close(Closeable closeable)
    {
        try {
            closeable.close();
        }
        catch (IOException e) {
            log.warn("Error closing stream", e);
        }
    }

    public static void copyStreamToFile(DBRProgressMonitor monitor, InputStream inputStream, long contentLength, IFile localFile)
        throws IOException
    {
        //localFile.appendContents(inputStream, true, false, monitor.getNestedMonitor());
        File file = localFile.getLocation().toFile();
        try {
            try (OutputStream outputStream = new FileOutputStream(file)) {
                ContentUtils.copyStreams(inputStream, contentLength, outputStream, monitor);
            }
        }
        finally {
            inputStream.close();
        }
        syncFile(monitor, localFile);

    }

    public static void copyReaderToFile(DBRProgressMonitor monitor, Reader reader, long contentLength, String charset, IFile localFile)
        throws IOException
    {
        try {
            if (charset == null) {
                charset = localFile.getCharset();
            } else {
                localFile.setCharset(charset, monitor.getNestedMonitor());
            }
        }
        catch (CoreException e) {
            log.warn("Can't set content charset", e);
        }
        File file = localFile.getLocation().toFile();
        try {
            try (OutputStream outputStream = new FileOutputStream(file)) {
                Writer writer = new OutputStreamWriter(outputStream, charset == null ? GeneralUtils.DEFAULT_ENCODING : charset);
                ContentUtils.copyStreams(reader, contentLength, writer, monitor);
                writer.flush();
            }
        }
        finally {
            reader.close();
        }
        syncFile(monitor, localFile);
    }

    public static void syncFile(DBRProgressMonitor monitor, IResource localFile) {
        // Sync file with contents
        try {
            localFile.refreshLocal(IFile.DEPTH_ZERO, monitor.getNestedMonitor());
        }
        catch (CoreException e) {
            log.warn("Can't synchronize file '" + localFile + "' with contents", e);
        }
    }

    public static IFile getUniqueFile(IFolder folder, String fileName, String fileExt)
    {
        IFile file = folder.getFile(fileName + "." + fileExt);
        int index = 1;
        while (file.exists()) {
            file = folder.getFile(fileName + "-" + index + "." + fileExt);
            index++;
        }
        return file;
    }

    public static String readFileToString(File file) throws IOException
    {
        try (InputStream fileStream = new FileInputStream(file)) {
            UnicodeReader unicodeReader = new UnicodeReader(fileStream, StandardCharsets.UTF_8);
            StringBuilder result = new StringBuilder((int) file.length());
            char[] buffer = new char[4000];
            for (;;) {
                int count = unicodeReader.read(buffer);
                if (count <= 0) {
                    break;
                }
                result.append(buffer, 0, count);
            }
            return result.toString();
        }
    }

    public static String readToString(InputStream is, Charset charset) throws IOException
    {
        return IOUtils.readToString(new UnicodeReader(is, charset));
    }

    @Nullable
    public static IFile convertPathToWorkspaceFile(IPath path)
    {
        IWorkspaceRoot root = ResourcesPlugin.getWorkspace().getRoot();
        IFile file = root.getFileForLocation(path);
        if (file != null) {
            return file;
        }
        // Probably we have a path to some linked resource
        IPath folderPath = path.removeLastSegments(1);
        URI folderURI = folderPath.toFile().toURI();
        IContainer[] containers = root.findContainersForLocationURI(folderURI);
        if (!ArrayUtils.isEmpty(containers)) {
            IContainer container = containers[0];
            file = container.getFile(path.removeFirstSegments(path.segmentCount() - 1));
        }
        return file;
    }

    @Nullable
    public static IPath convertPathToWorkspacePath(IPath path)
    {
        IFile wFile = convertPathToWorkspaceFile(path);
        return wFile == null ? null : wFile.getFullPath();
    }

    public static boolean isTextContent(DBDContent content)
    {
        String contentType = content == null ? null : content.getContentType();
        return contentType != null && contentType.toLowerCase(Locale.ENGLISH).startsWith("text");
    }

    public static boolean isTextMime(String mimeType)
    {
        return mimeType != null && mimeType.toLowerCase(Locale.ENGLISH).startsWith("text");
    }

    public static boolean isTextValue(Object value)
    {
        if (value == null) {
            return false;
        }
        if (value instanceof CharSequence) {
            return true;
        }
        if (value instanceof byte[]) {
            for (byte b : (byte[])value) {
                if (!Character.isLetterOrDigit(b) && !Character.isSpaceChar(b) && !Character.isISOControl(b)) {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isXML(DBDContent content)
    {
        return MimeTypes.TEXT_XML.equalsIgnoreCase(content.getContentType());
    }

    public static boolean isJSON(DBDContent content)
    {
        return MimeTypes.TEXT_JSON.equalsIgnoreCase(content.getContentType());
    }

    @Nullable
    public static String getContentStringValue(@NotNull DBRProgressMonitor monitor, @NotNull DBDContent object) throws DBCException {
        if (object.isNull()) {
            return null;
        }
        DBDContentStorage data = object.getContents(monitor);
        if (data != null) {
            if (data instanceof DBDContentCached) {
                Object cachedValue = ((DBDContentCached) data).getCachedValue();
                if (cachedValue instanceof String) {
                    return (String) cachedValue;
                }
            }
            try {
                Reader contentReader = data.getContentReader();
                if (contentReader != null) {
                    try {
                        StringWriter buf = new StringWriter();
                        ContentUtils.copyStreams(contentReader, object.getContentLength(), buf, monitor);
                        return buf.toString();
                    } finally {
                        IOUtils.close(contentReader);
                    }
                }
            } catch (IOException e) {
                log.debug("Can't extract string from content", e);
            }
        }
        return object.toString();
    }

    @Nullable
    public static byte[] getContentBinaryValue(@NotNull DBRProgressMonitor monitor, @NotNull DBDContent object) throws DBCException {
        DBDContentStorage data = object.getContents(monitor);
        if (data != null) {
            if (data instanceof DBDContentCached) {
                Object cachedValue = ((DBDContentCached) data).getCachedValue();
                if (cachedValue instanceof byte[]) {
                    return (byte[]) cachedValue;
                }
            }
            try {
                InputStream contentStream = data.getContentStream();
                if (contentStream != null) {
                    try {
                        ByteArrayOutputStream buf = new ByteArrayOutputStream();
                        ContentUtils.copyStreams(contentStream, object.getContentLength(), buf, monitor);
                        return buf.toByteArray();
                    } finally {
                        IOUtils.close(contentStream);
                    }
                }
            } catch (IOException e) {
                log.debug("Can't extract string from content", e);
            }
        }
        return null;
    }

    public static byte[] blobToBytes(Blob blob) {

        BufferedInputStream is = null;
        try {
            is = new BufferedInputStream(blob.getBinaryStream());
            byte[] bytes = new byte[(int) blob.length()];
            int len = bytes.length;
            int offset = 0;
            int read = 0;
            while (offset < len && (read = is.read(bytes, offset, len)) >= 0) {
                offset += read;
            }
            return bytes;
        } catch (Exception e) {
            return null;
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                return null;
            }
        }
    }

    public static byte[] clobToBytes(Clob clob) {
        byte[] result = null;
        try (Reader reader = clob.getCharacterStream()) {
            result = clobToBytes(reader);
        } catch (Exception e) {
            log.error("clobToBytes error : ", e);
        }
        return result;
    }

    public static byte[] clobToBytes(Reader reader) {
        byte[] result = null;
        try {
            StringBuilder buffer = new StringBuilder();
            char[] charBuffer = new char[10000];
            for (;;) {
                int count = reader.read(charBuffer);
                if (count <= 0) {
                    break;
                }
                buffer.append(charBuffer, 0, count);
            }
            result = buffer.toString().getBytes();
        } catch (IOException ioException) {
            log.error("clobToBytes error : ", ioException);
        }
        return result;
    }

    public static byte[] clobToBytes(Reader reader, long contentLength) {
        byte[] result = null;
        try {
            StringBuilder buffer = new StringBuilder((int) contentLength);
            char[] charBuffer = new char[10000];
            for (;;) {
                int count = reader.read(charBuffer);
                if (count <= 0) {
                    break;
                }
                buffer.append(charBuffer, 0, count);
            }
            if (buffer.length() != contentLength) {
                log.warn("Actual content length (" + buffer.length() + ") is less than declared: " + contentLength);
            }
            result = buffer.toString().getBytes();
        } catch (IOException ioException) {
            log.error("clobToBytes error : ", ioException);
        }
        return result;
    }

    public static String clobToString(Reader reader) {
        String resultString = "";
        byte[] result = null;
        try {
            StringBuilder buffer = new StringBuilder();
            char[] charBuffer = new char[10000];
            for (;;) {
                int count = reader.read(charBuffer);
                if (count <= 0) {
                    break;
                }
                buffer.append(charBuffer, 0, count);
            }
            result = buffer.toString().getBytes();
        } catch (IOException ioException) {
            log.error("clobToString error : ", ioException);
        }
        if (result != null) {
            resultString = new String(result);
        }
        return resultString;
    }

    public static void deleteTempFile(File tempFile) {
        if (!tempFile.delete()) {
            log.warn("Can't delete temp file '" + tempFile.getAbsolutePath() + "'");
        }
    }

    public static boolean deleteFileRecursive(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File ch : files) {
                    if (!deleteFileRecursive(ch)) {
                        return false;
                    }
                }
            }
        }
        return file.delete();
    }

    public static void checkFolderExists(IFolder folder)
            throws DBException
    {
        checkFolderExists(folder, new VoidProgressMonitor());
    }

    public static void checkFolderExists(IFolder folder, DBRProgressMonitor monitor)
            throws DBException
    {
        if (!folder.exists()) {
            try {
                folder.create(true, true, monitor.getNestedMonitor());
            } catch (CoreException e) {
                throw new DBException("Can't create folder '" + folder.getFullPath() + "'", e);
            }
        }
    }

    public static void makeFileBackup(Path file) {
        if (!Files.exists(file)) {
            return;
        }
        String backupFileName = file.getFileName().toString() + ".bak";
        if (!backupFileName.startsWith(".")) {
            backupFileName = "." + backupFileName;
        }
        Path backupFile = file.getParent().resolve(backupFileName);
        if (Files.exists(backupFile)) {
            try {
                Date backupTime = new Date(Files.getLastModifiedTime(backupFile).toMillis());
                if (CommonUtils.isSameDay(backupTime, new Date())) {
                    return;
                }
            } catch (IOException e) {
                log.error("Error getting file modified time", e);
            }
        }
        try {
            Files.copy(file, backupFile, StandardCopyOption.REPLACE_EXISTING);
        } catch (Exception e) {
            log.error("Error creating backup copy of " + file.toAbsolutePath(), e);
        }
    }

    public static long getResourceLastModified(IResource resource) {
        try {
            IFileStore fileStore = EFS.getStore(resource.getLocationURI());
            IFileInfo iFileInfo = fileStore.fetchInfo();
            return iFileInfo.getLastModified();
        } catch (CoreException e) {
            log.debug(e);
            return -1;
        }
    }

    public static long getFileLength(IResource resource) {
        try {
            IFileStore fileStore = EFS.getStore(resource.getLocationURI());
            IFileInfo iFileInfo = fileStore.fetchInfo();
            return iFileInfo.getLength();
        } catch (CoreException e) {
            log.debug(e);
            return -1;
        }
    }

    public static String getTabCondition(List<String> tableNames) {
        StringBuffer tabBuf = new StringBuffer();
        for (String tabName : tableNames) {
            if (null != tabBuf && tabBuf.length() > 0) {
                tabBuf.append(",");
            }
            tabBuf.append(String.format("'%s'", tabName));
        }
        return tabBuf.toString();
    }
}
