

package com.dc.summer.utils;

import com.dc.summer.registry.center.Global;
import com.dc.summer.runtime.IVariableResolver;
import com.dc.utils.StandardConstants;

import java.io.File;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.Properties;

/**
 * SystemVariablesResolver
 */
public class SystemVariablesResolver implements IVariableResolver {

    public static final SystemVariablesResolver INSTANCE = new SystemVariablesResolver();

    public static final String VAR_APP_NAME = "application.name";
    public static final String VAR_APP_VERSION = "application.version";
    public static final String VAR_APP_PATH = "application.path";
    public static final String VAR_WORKSPACE = "workspace";
    public static final String VAR_HOME = "home";
    public static final String VAR_DBEAVER_HOME = "dbeaver_home";
    public static final String VAR_LOCAL_IP = "local.ip";

    private static Properties configuration;

    public static void setConfiguration(Properties configuration) {
        SystemVariablesResolver.configuration = configuration;
    }

    @Override
    public String get(String name) {
        //name = name.toLowerCase(Locale.ENGLISH);
        switch (name) {
            case VAR_APP_NAME:
                return GeneralUtils.getProductName();
            case VAR_APP_VERSION:
                return GeneralUtils.getProductVersion().toString();
            case VAR_HOME:
                return getUserHome();
            case VAR_WORKSPACE:
                return getWorkspacePath();
            case VAR_DBEAVER_HOME:
            case VAR_APP_PATH:
                return getInstallPath();
            case VAR_LOCAL_IP:
                try {
                    return InetAddress.getLocalHost().getHostAddress();
                } catch (UnknownHostException e) {
                    return "127.0.0.1";
                }
            default:
                if (configuration != null) {
                    final Object o = configuration.get(name);
                    if (o != null) {
                        return o.toString();
                    }
                }
                String var = System.getProperty(name);
                if (var != null) {
                    return var;
                }
                return System.getenv(name);
        }
    }

    public static String getInstallPath() {
//        return getPlainPath(Platform.getInstallLocation().getURL());
        return Global.getLOCAL();
    }

    public static String getWorkspacePath() {
        // TODO 1
//        return getPlainPath(Platform.getInstanceLocation().getURL());
        return Global.getLOCAL();
    }

    public static String getUserHome() {
        return System.getProperty(StandardConstants.ENV_USER_HOME);
    }

    private static String getPlainPath(URL url) {
        try {
            File file = RuntimeUtils.getLocalFileFromURL(url);
            return file.getAbsolutePath();
        } catch (Exception e) {
            return url.toString();
        }
    }

}
