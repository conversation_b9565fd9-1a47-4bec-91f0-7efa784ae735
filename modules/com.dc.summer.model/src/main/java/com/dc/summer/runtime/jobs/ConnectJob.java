
package com.dc.summer.runtime.jobs;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.utils.GeneralUtils;

/**
 * Connect job.
 * Always returns OK status.
 * To get real status use getConectStatus.
 */
public class ConnectJob extends AbstractJob
{
    private static final Log log = Log.getLog(ConnectJob.class);

    private volatile Thread connectThread;
    protected boolean initialize = true;
    protected boolean reflect = true;
    protected Throwable connectError;
    protected IStatus connectStatus;
    protected final DBPDataSourceContainer container;

    public ConnectJob(
        DBPDataSourceContainer container)
    {
        super("Connect to '" + container.getName() + "'");
        setUser(true);
        this.container = container;
    }

    public IStatus getConnectStatus() {
        return connectStatus;
    }

    public Throwable getConnectError() {
        return connectError;
    }

    @Override
    protected IStatus run(DBRProgressMonitor monitor)
    {
        try {
            // 把线程的名字，设置本机名称
            connectThread = getThread();
            String oldName = connectThread == null ? null : connectThread.getName();
            if (reflect && connectThread != null) {
//                connectThread.setName(getName());
            }

            try {
                // 调用容器的连接方法
                final boolean connected = container.connect(monitor, initialize, reflect);

                connectStatus = connected ? Status.OK_STATUS : Status.CANCEL_STATUS;
            } finally {
                // 线程名设置回去
                if (connectThread != null && oldName != null) {
//                    connectThread.setName(oldName);
                    connectThread = null;
                }
            }
        }
        catch (Throwable ex) {
            log.debug(ex);
            connectError = ex;
            connectStatus = GeneralUtils.makeExceptionStatus(ex);
        }

        return Status.OK_STATUS;
    }

    public IStatus runSync(DBRProgressMonitor monitor)
    {
        AbstractJob curJob = CURRENT_JOB.get();
        if (curJob != null) {
            curJob.setAttachedJob(this);
        }
        try {
            setThread(Thread.currentThread());
            reflect = false;
            return run(monitor);
        } finally {
            if (curJob != null) {
                curJob.setAttachedJob(null);
            }
        }
    }

    @Override
    public boolean belongsTo(Object family)
    {
        return container == family;
    }

    @Override
    protected void canceling()
    {
        if (connectThread != null) {
            connectThread.interrupt();
        }
    }

}