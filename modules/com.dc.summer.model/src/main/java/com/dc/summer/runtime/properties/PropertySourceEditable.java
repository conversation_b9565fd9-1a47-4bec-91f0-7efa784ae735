
package com.dc.summer.runtime.properties;

import com.dc.summer.Log;
import com.dc.summer.model.DBPNamedObject;
import com.dc.summer.model.DBPObject;
import com.dc.summer.model.DBUtils;
import com.dc.summer.model.edit.DBECommandContext;
import com.dc.summer.model.edit.DBEObjectEditor;
import com.dc.summer.model.edit.prop.DBECommandProperty;
import com.dc.summer.model.edit.prop.DBEPropertyHandler;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.Nullable;
import com.dc.summer.model.edit.DBECommandReflector;
import com.dc.summer.model.edit.DBEObjectMaker;
import com.dc.summer.model.struct.DBSObject;
import com.dc.summer.model.struct.cache.DBSObjectCache;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.utils.CommonUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * PropertySourceEditable
 */
public class PropertySourceEditable extends PropertySourceAbstract implements DBPObject, IPropertySourceEditable
{
    private static final Log log = Log.getLog(PropertySourceEditable.class);

    private DBECommandContext commandContext;
    private PropertyChangeCommand lastCommand = null;
    //private final List<IPropertySourceListener> listeners = new ArrayList<IPropertySourceListener>();
    private final CommandReflector commandReflector = new CommandReflector();

    public PropertySourceEditable(DBECommandContext commandContext, Object sourceObject, Object object)
    {
        super(sourceObject, object, true);
        this.commandContext = commandContext;
        //this.objectManager = editorInput.getObjectManager(DBEObjectEditor.class);
    }

    public PropertySourceEditable(Object sourceObject, Object object)
    {
        super(sourceObject, object, true);
    }

    @Override
    public boolean isEditable(Object object) {
        if (commandContext == null) {
            return true;
        }
        DBEObjectEditor objectEditor = getObjectEditor(DBEObjectEditor.class);
        return objectEditor != null &&
            object instanceof DBPObject && objectEditor.canEditObject((DBPObject) object);
    }

    private <T> T getObjectEditor(Class<T> managerType)
    {
        final Object editableValue = getEditableValue();
        if (editableValue == null) {
            return null;
        }
        return DBWorkbench.getPlatform().getEditorsRegistry().getObjectManager(
            editableValue.getClass(),
            managerType);
    }

    //@Override
    public DBECommandContext getCommandContext()
    {
        return commandContext;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void setPropertyValue(@Nullable DBRProgressMonitor monitor, Object editableValue, ObjectPropertyDescriptor prop, Object newValue)
        throws IllegalArgumentException
    {
        if (prop.getValueTransformer() != null) {
            newValue = prop.getValueTransformer().transform(editableValue, newValue);
        }
        final Object oldValue = getPropertyValue(monitor, editableValue, prop, true);
        if (!updatePropertyValue(monitor, editableValue, prop, newValue, false)) {
            return;
        }
        if (commandContext != null) {
            if (lastCommand == null || lastCommand.getObject() != editableValue || lastCommand.property != prop || !commandContext.isDirty()) {
                // Last command is not applicable (check for isDirty because command queue might be reverted)
                final DBEObjectEditor<DBPObject> objectEditor = getObjectEditor(DBEObjectEditor.class);
                if (objectEditor == null) {
                    log.error("Can't obtain object editor for " + getEditableValue());
                    return;
                }
                final DBEPropertyHandler<DBPObject> propertyHandler = objectEditor.makePropertyHandler(
                    (DBPObject) editableValue,
                    prop);
                PropertyChangeCommand curCommand = new PropertyChangeCommand((DBPObject) editableValue, prop, propertyHandler, oldValue, newValue);
                commandContext.addCommand(curCommand, commandReflector);
                lastCommand = curCommand;
            } else {
                lastCommand.setNewValue(newValue);
                commandContext.updateCommand(lastCommand, commandReflector);
            }
        }

        // If we perform rename then we should refresh object cache
        // To update name-based cache
        if (prop.isNameProperty() && editableValue instanceof DBSObject) {
            DBEObjectMaker objectManager = getObjectEditor(DBEObjectMaker.class);
            if (objectManager != null) {
                DBSObjectCache cache = objectManager.getObjectsCache((DBSObject) editableValue);
                if (cache != null && cache.isFullyCached()) {
                    List<? extends DBSObject> cachedObjects = CommonUtils.copyList(cache.getCachedObjects());
                    cache.setCache(cachedObjects);
                }
            }
        }

/*
        // Notify listeners
        for (IPropertySourceListener listener : listeners) {
            listener.handlePropertyChange(editableValue, prop, newValue);
        }
*/
    }

    private boolean updatePropertyValue(@Nullable DBRProgressMonitor monitor, Object editableValue, ObjectPropertyDescriptor prop, Object value, boolean force)
        throws IllegalArgumentException
    {
        // Write property value
        try {
            // Check for complex object
            // If value should be a named object then try to obtain it from list provider
            if (value != null && value.getClass() == String.class) {
                final Object[] items = prop.getPossibleValues(editableValue);
                if (items != null) {
                    boolean found = false;
                    if (items.length > 0) {
                        for (int i = 0, itemsLength = items.length; i < itemsLength; i++) {
                            if ((items[i] instanceof DBPNamedObject && value.equals(((DBPNamedObject) items[i]).getName())) ||
                                (items[i] instanceof Enum && value.equals(((Enum) items[i]).name()))
                                ) {
                                value = items[i];
                                found = true;
                                break;
                            }
                        }
                    }
                    if (!found) {
                        if (value.getClass() != prop.getDataType()){
                            value = null;
                        }
                    }
                }
            }
            final Object oldValue = getPropertyValue(monitor, editableValue, prop, true);
            if (CommonUtils.equalObjects(oldValue, value)) {
                return false;
            }

            prop.writeValue(editableValue, value);
            // Fire object update event
            if (editableValue instanceof DBSObject) {
                DBUtils.fireObjectUpdate((DBSObject) editableValue, prop);
            }
            addChangedProperties(prop, value);
            return true;
        } catch (Throwable e) {
            if (e instanceof InvocationTargetException) {
                e = ((InvocationTargetException) e).getTargetException();
            }
            if (e instanceof IllegalArgumentException) {
                throw (IllegalArgumentException) e;
            }
            throw new IllegalArgumentException("Can't write property '" + prop.getDisplayName() + "' value", e);
        }
    }

    @Override
    public boolean isPropertyResettable(Object object, ObjectPropertyDescriptor prop)
    {
        return (lastCommand != null && lastCommand.property == prop && lastCommand.getObject() == object);
    }

    @Override
    public void resetPropertyValue(@Nullable DBRProgressMonitor monitor, Object object, ObjectPropertyDescriptor prop)
    {
//        final DBECommandComposite compositeCommand = (DBECommandComposite)getCommandContext().getUserParams().get(obj);
//        if (compositeCommand != null) {
//            final Object value = compositeCommand.getProperty(prop.getId());
//        }

        if (lastCommand != null && lastCommand.property == prop) {
            setPropertyValue(monitor, object, prop, lastCommand.getOldValue());
        }
//        final ObjectProps objectProps = getObjectProps(object);
//        DBECommandProperty curCommand = objectProps.propValues.get(prop);
//        if (curCommand != null) {
//            curCommand.resetValue();
//        }
        log.warn("Property reset not implemented");
    }

    private class PropertyChangeCommand extends DBECommandProperty<DBPObject> {
        ObjectPropertyDescriptor property;
        public PropertyChangeCommand(DBPObject editableValue, ObjectPropertyDescriptor property, DBEPropertyHandler<DBPObject> propertyHandler, Object oldValue, Object newValue)
        {
            super(editableValue, propertyHandler, oldValue, newValue);
            this.property = property;
        }

        @Override
        public void updateModel()
        {
            super.updateModel();
            updatePropertyValue(null, getObject(), property, getNewValue(), true);
        }
    }

    private class CommandReflector implements DBECommandReflector<DBPObject, PropertyChangeCommand> {

        @Override
        public void redoCommand(PropertyChangeCommand command)
        {
            updatePropertyValue(null, command.getObject(), command.property, command.getNewValue(), false);
/*
            // Notify listeners
            for (IPropertySourceListener listener : listeners) {
                listener.handlePropertyChange(command.getObject(), command.property, command.getNewValue());
            }
*/
        }

        @Override
        public void undoCommand(PropertyChangeCommand command)
        {
            updatePropertyValue(null, command.getObject(), command.property, command.getOldValue(), false);
/*
            // Notify listeners
            for (IPropertySourceListener listener : listeners) {
                listener.handlePropertyChange(command.getObject(), command.property, command.getOldValue());
            }
*/
        }
    }

}