
package com.dc.summer.model.impl.jdbc.data;

import com.dc.summer.model.data.DBDContent;
import com.dc.summer.model.data.DBDContentStorage;
import com.dc.summer.model.data.DBDValueCloneable;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;

import java.io.IOException;

/**
 * JDBCContentLOB
 *
 * <AUTHOR> Rider
 */
public abstract class JDBCContentLOB extends JDBCContentAbstract implements DBDContent {

    private DBDContentStorage originalStorage;
    protected DBDContentStorage storage;

    protected JDBCContentLOB(DBCExecutionContext dataSource)
    {
        super(dataSource);
    }

    @Override
    public long getContentLength() throws DBCException {
        if (storage != null) {
            return storage.getContentLength();
        }
        return getLOBLength();
    }

    protected abstract long getLOBLength() throws DBCException;

    @Override
    public boolean updateContents(
        DBRProgressMonitor monitor,
        DBDContentStorage storage)
    {
        if (this.storage != null) {
            if (this.originalStorage != null && this.originalStorage != this.storage) {
                this.originalStorage.release();
            }
            this.originalStorage = this.storage;
        }
        this.storage = storage;
        this.modified = true;
        return true;
    }

    @Override
    public void release()
    {
        if (this.storage != null) {
            this.storage.release();
            this.storage = null;
        }
        if (this.originalStorage != null) {
            this.originalStorage.release();
            this.originalStorage = null;
        }
    }

    @Override
    public void resetContents()
    {
        if (this.originalStorage != null) {
            if (this.storage != null) {
                this.storage.release();
            }
            this.storage = this.originalStorage;
            this.modified = false;
        }
    }

    @Override
    public DBDValueCloneable cloneValue(DBRProgressMonitor monitor)
        throws DBCException
    {
        JDBCContentLOB copy = createNewContent();
        DBDContentStorage storage = getContents(monitor);
        if (storage != null) {
            try {
                copy.updateContents(monitor, storage.cloneStorage(monitor));
            } catch (IOException e) {
                throw new DBCException("IO error while clone content", e);
            }
        }
        return copy;
    }

    protected abstract JDBCContentLOB createNewContent();

}
