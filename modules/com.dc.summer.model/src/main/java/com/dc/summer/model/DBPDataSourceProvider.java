
package com.dc.summer.model;

import com.dc.summer.DBException;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.model.connection.DBPAuthModelDescriptor;
import com.dc.summer.model.connection.DBPDriver;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.summer.model.connection.DBPConnectionConfiguration;

/**
 * Data source provider
 */
public interface DBPDataSourceProvider extends DBPDataSourceURLProvider, DBPObject
{
    long FEATURE_NONE        = 0;
    long FEATURE_CATALOGS    = 1;
    long FEATURE_SCHEMAS     = 2;

    /**
     * Initializes data source provider
     * @param platform application
     */
    void init(@NotNull DBPPlatform platform);

    /**
     * Supported features
     * @return features
     */
    long getFeatures();
    
    /**
     * Supported connection properties.
     *
     * @param monitor progress monitor
     * @param driver driver
     * @param connectionInfo connection information   @return property group which contains all supported properties
     * @throws DBException on any error
     */
    DBPPropertyDescriptor[] getConnectionProperties(
        DBRProgressMonitor monitor,
        DBPDriver driver,
        DBPConnectionConfiguration connectionInfo)
        throws DBException;

    /**
     * Opens new data source
     * @param monitor progress monitor
     * @param container data source container
     * @return new data source object
     * @throws DBException on any error
     */
    @NotNull
    DBPDataSource openDataSource(
        @NotNull DBRProgressMonitor monitor,
        @NotNull DBPDataSourceContainer container)
        throws DBException;

    default DBPAuthModelDescriptor detectConnectionAuthModel(DBPDriver driver, DBPConnectionConfiguration connectionInfo) {
        return connectionInfo.getAuthModelDescriptor();
    }

    default boolean providesDriverClasses() {
        return true;
    }

}
