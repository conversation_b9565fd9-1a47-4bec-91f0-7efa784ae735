

package com.dc.summer.model.navigator;

import com.dc.code.NotNull;

/**
 * Navigator model event
 */
public class DBNEvent {

    public static final Object FORCE_REFRESH = new Object();
    public static final Object UPDATE_ON_SAVE = new Object();

    public enum Action
    {
        ADD,
        REMOVE,
        UPDATE,
    }

    public enum NodeChange {
        LOAD,
        UNLOAD,
        REFRESH,
        SELECT,
        STRUCT_REFRESH,
        LOCK,
        UNLOCK,
    }

    private Object source;
    private Action action;
    private NodeChange nodeChange;
    @NotNull
    private DBNNode node;

    public DBNEvent(Object source, Action action, @NotNull DBNNode node)
    {
        this(source, action, NodeChange.REFRESH, node);
        this.action = action;
        this.node = node;
    }

    public DBNEvent(Object source, Action action, NodeChange nodeChange, @NotNull DBNNode node)
    {
        this.source = source;
        this.action = action;
        this.nodeChange = nodeChange;
        this.node = node;
    }

    public Object getSource()
    {
        return source;
    }

    public Action getAction()
    {
        return action;
    }

    public NodeChange getNodeChange()
    {
        return nodeChange;
    }

    @NotNull
    public DBNNode getNode()
    {
        return node;
    }

    @Override
    public String toString() {
        return action + ":" + nodeChange + ":" + node;
    }
}