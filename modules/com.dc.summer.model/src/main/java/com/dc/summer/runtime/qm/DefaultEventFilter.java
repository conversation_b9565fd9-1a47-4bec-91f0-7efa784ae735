

package com.dc.summer.runtime.qm;

import com.dc.summer.model.qm.QMEvent;
import com.dc.summer.model.qm.QMObjectType;
import com.dc.summer.model.qm.meta.*;
import com.dc.summer.ModelPreferences;
import com.dc.summer.model.qm.QMEventFilter;
import com.dc.summer.model.qm.QMUtils;
import com.dc.summer.model.qm.filters.QMEventCriteria;
import com.dc.summer.model.qm.meta.*;

/**
 * Default event filter based on preference settings.
 */
public class DefaultEventFilter implements QMEventFilter {

    private QMEventCriteria eventCriteria = new QMEventCriteria();

    public DefaultEventFilter()
    {
        reloadPreferences();
    }

    public void reloadPreferences()
    {
        eventCriteria = QMUtils.createDefaultCriteria(ModelPreferences.getPreferences());
    }

    @Override
    public boolean accept(QMEvent event) {
        QMMObject object = event.getObject();
        if (object instanceof QMMStatementExecuteInfo) {
            return eventCriteria.hasObjectType(QMObjectType.query) &&
                eventCriteria.hasQueryType(((QMMStatementExecuteInfo) object).getStatement().getPurpose());
        } else if (object instanceof QMMTransactionInfo || object instanceof QMMTransactionSavepointInfo) {
            return eventCriteria.hasObjectType(QMObjectType.txn);
        } else if (object instanceof QMMConnectionInfo) {
            return eventCriteria.hasObjectType(QMObjectType.session);
        }
        return true;
    }

}
