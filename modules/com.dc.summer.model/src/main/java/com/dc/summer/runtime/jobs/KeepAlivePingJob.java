
package com.dc.summer.runtime.jobs;

import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.exec.DBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import com.dc.summer.model.runtime.AbstractJob;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.runtime.DBWorkbench;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * KeepAlivePingJob
 */
class KeepAlivePingJob extends AbstractJob {
    private static final Log log = Log.getLog(KeepAlivePingJob.class);

    private static final Map<String, Integer> failedAttempts = new HashMap<>();

    private final DBPDataSource dataSource;
    private final boolean disconnectOnError;

    KeepAlivePingJob(DBPDataSource dataSource, boolean disconnectOnError) {
        super("Connection ping (" + dataSource.getContainer().getName() + ")");
        setUser(false);
        setSystem(true);
        this.dataSource = dataSource;
        this.disconnectOnError = disconnectOnError;
    }

    @Override
    protected IStatus run(DBRProgressMonitor monitor) {
        boolean hasDeadContexts = false;
        for (final DBSInstance instance : dataSource.getAvailableInstances()) {
            for (final DBCExecutionContext context : instance.getAllContexts()) {
                try {
                    context.checkContextAlive(monitor);
                } catch (Exception e) {
                    log.debug("Context [" + dataSource.getName() + "::" + context.getContextName() + "] check failed: " + e.getMessage());
                    hasDeadContexts = true;
                    break;
                }
            }
        }
        if (hasDeadContexts) {
            // Invalidate whole datasource. Do not log errors (as it can spam tons of logs)
            final List<InvalidateJob.ContextInvalidateResult> results = InvalidateJob.invalidateDataSource(
                monitor,
                dataSource,
                disconnectOnError,
                false,
                null,
                    null);
            synchronized (failedAttempts) {
                String dsId = dataSource.getContainer().getId();
                if (isSuccess(results) || disconnectOnError) {
                    log.debug("Datasource " + dataSource.getName() + " invalidated: " + results);
                    failedAttempts.remove(dsId);
                } else {
                    log.debug("Datasource " + dataSource.getName() + " invalidate failed: " + results);
                    Integer curAttempts = failedAttempts.get(dsId);
                    if (curAttempts == null) {
                        curAttempts = 1;
                    } else {
                        curAttempts++;
                    }
                    failedAttempts.put(dsId, curAttempts);
                }
            }
        }
        return Status.OK_STATUS;
    }

    private boolean isSuccess(List<InvalidateJob.ContextInvalidateResult> results) {
        for (InvalidateJob.ContextInvalidateResult result : results) {
            switch (result.result) {
                case ALIVE:
                case RECONNECTED:
                case CONNECTED:
                    return true;
            }
        }
        return false;
    }

    public static int getFailedAttemptCount(DBPDataSource dataSource) {
        synchronized (failedAttempts) {
            Integer attempts = failedAttempts.get(dataSource.getContainer().getId());
            return attempts == null ? 0 : attempts;
        }
    }

}