
package com.dc.summer.model.impl.data.transformers;

import com.dc.summer.DBException;
import com.dc.summer.Log;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.DBValueFormatting;
import com.dc.summer.model.data.*;
import com.dc.summer.model.impl.data.ProxyValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;
import com.dc.summer.utils.GeneralUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.*;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.impl.data.formatters.BinaryFormatterString;
import com.dc.utils.CommonUtils;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

/**
 * Transforms string value into binary
 */
public class BinaryAttributeTransformer implements DBDAttributeTransformer {

    private static final Log log = Log.getLog(BinaryAttributeTransformer.class);

    private static final String PROP_FORMAT = "format";
    private static final String PROP_ENCODING = "encoding";

    private static final String FORMAT_NATIVE = "native";
    private static final String FORMAT_HEX = "hex";

    @Override
    public void transformAttribute(@NotNull DBCSession session, @NotNull DBDAttributeBinding attribute, @NotNull List<Object[]> rows, @NotNull Map<String, Object> options) throws DBException {
        DBPDataSource dataSource = session.getDataSource();
        String formatterId = CommonUtils.toString(options.get(PROP_FORMAT), FORMAT_HEX);

        DBDBinaryFormatter formatter;
        if (FORMAT_NATIVE.equals(formatterId)) {
            formatter = dataSource.getSQLDialect().getNativeBinaryFormatter();
        } else {
            formatter = DBValueFormatting.getBinaryPresentation(formatterId);
        }
        if (formatter == null) {
            formatter = new BinaryFormatterString();
        }

        String encodingName = CommonUtils.toString(options.get(PROP_ENCODING), GeneralUtils.UTF8_ENCODING);
        Charset charset;
        try {
            charset = Charset.forName(encodingName);
        } catch (Exception e) {
            log.warn(e);
            charset = Charset.defaultCharset();
        }

        attribute.setTransformHandler(new BinaryValueHandler(attribute.getValueHandler(), charset, formatter));
    }

    private class BinaryValueHandler extends ProxyValueHandler {
        private final Charset charset;
        private final DBDBinaryFormatter formatter;
        public BinaryValueHandler(DBDValueHandler target, Charset charset, DBDBinaryFormatter formatter) {
            super(target);
            this.charset = charset;
            this.formatter = formatter;
        }

        @NotNull
        @Override
        public String getValueDisplayString(@NotNull DBSTypedObject column, @Nullable Object value, @NotNull DBDDisplayFormat format) {
            if (value == null) {
                return super.getValueDisplayString(column, null, format);
            }
            ByteBuffer bb = charset.encode(CommonUtils.toString(value));
            byte[] bytes = bb.array();
            return formatter.toString(bytes, 0, bytes.length);
        }
    }

}
