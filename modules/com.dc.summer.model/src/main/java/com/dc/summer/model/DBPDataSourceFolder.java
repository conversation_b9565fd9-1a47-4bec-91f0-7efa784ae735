
package com.dc.summer.model;

import com.dc.summer.model.app.DBPDataSourceRegistry;

/**
 * Datasource folder
 */
public interface DBPDataSourceFolder extends DBPNamedObject2 {

    String getFolderPath();

    String getDescription();

    DBPDataSourceFolder getParent();

    void setParent(DBPDataSourceFolder parent);

    DBPDataSourceFolder[] getChildren();

    DBPDataSourceRegistry getDataSourceRegistry();

    boolean canMoveTo(DBPDataSourceFolder folder);
}
