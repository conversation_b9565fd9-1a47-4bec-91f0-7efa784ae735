

package com.dc.summer.model;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.struct.DBSObject;

/**
 * Refreshable object.
 * allows refresh of object's content
 */
public interface DBPRefreshableObject extends DBSObject
{
    /**
     * Refreshes object
     * @return self or new object of the same class (with refreshed state).
     * Null result means that object was deleted (moved, renamed)
     */
    @Nullable
    DBSObject refreshObject(@NotNull DBRProgressMonitor monitor)
        throws DBException;

}
