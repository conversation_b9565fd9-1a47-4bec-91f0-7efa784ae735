
package com.dc.summer.model;

/**
 * Provided data source configuration storage
 */
public interface DBPDataSourceConfigurationStorage {

    String getStorageId();

    default String getStorageName() {
        return getStorageId();
    }

    boolean isValid();

    boolean isDefault();

    String getStatus();

    // Used for secure credentials save/load (it is a prt of credentials file name)
    String getStorageSubId();

}
