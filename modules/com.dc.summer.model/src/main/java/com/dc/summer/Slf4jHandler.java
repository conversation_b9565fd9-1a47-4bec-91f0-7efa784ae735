package com.dc.summer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Slf4j<PERSON><PERSON><PERSON> implements LogHandler{

    @Override
    public String getName(String name) {
        return null;
    }

    @Override
    public boolean isDebugEnabled(String name) {
        return false;
    }

    @Override
    public boolean isErrorEnabled(String name) {
        return false;
    }

    @Override
    public boolean isFatalEnabled(String name) {
        return false;
    }

    @Override
    public boolean isInfoEnabled(String name) {
        return false;
    }

    @Override
    public boolean isTraceEnabled(String name) {
        return false;
    }

    @Override
    public boolean isWarnEnabled(String name) {
        return false;
    }

    @Override
    public void trace(String name, Object message) {
        Logger log = LoggerFactory.getLogger(name);
        if (message instanceof Throwable) {
            log.trace(String.valueOf(message), (Throwable) message);
        } else {
            log.trace(String.valueOf(message));
        }
    }

    @Override
    public void trace(String name, Object message, Throwable t) {
        Logger log = LoggerFactory.getLogger(name);
        log.trace(String.valueOf(message), t);
    }

    @Override
    public void debug(String name, Object message) {
        Logger log = LoggerFactory.getLogger(name);
        if (message instanceof Throwable) {
            log.debug(String.valueOf(message), (Throwable) message);
        } else {
            log.debug(String.valueOf(message));
        }
    }

    @Override
    public void debug(String name, Object message, Throwable t) {
        Logger log = LoggerFactory.getLogger(name);
        log.debug(String.valueOf(message), t);
    }

    @Override
    public void info(String name, Object message) {
        Logger log = LoggerFactory.getLogger(name);
        if (message instanceof Throwable) {
            log.info(String.valueOf(message), (Throwable) message);
        } else {
            log.info(String.valueOf(message));
        }
    }

    @Override
    public void info(String name, Object message, Throwable t) {
        Logger log = LoggerFactory.getLogger(name);
        log.info(String.valueOf(message), t);
    }

    @Override
    public void warn(String name, Object message) {
        Logger log = LoggerFactory.getLogger(name);
        if (message instanceof Throwable) {
            log.warn(String.valueOf(message), (Throwable) message);
        } else {
            log.warn(String.valueOf(message));
        }
    }

    @Override
    public void warn(String name, Object message, Throwable t) {
        Logger log = LoggerFactory.getLogger(name);
        log.warn(String.valueOf(message), t);
    }

    @Override
    public void error(String name, Object message) {
        Logger log = LoggerFactory.getLogger(name);
        if (message instanceof Throwable) {
            log.error(String.valueOf(message), (Throwable) message);
        } else {
            log.error(String.valueOf(message));
        }
    }

    @Override
    public void error(String name, Object message, Throwable t) {
        Logger log = LoggerFactory.getLogger(name);
        log.error(String.valueOf(message), t);
    }

}
