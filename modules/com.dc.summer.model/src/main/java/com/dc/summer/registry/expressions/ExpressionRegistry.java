
package com.dc.summer.registry.expressions;

import com.dc.summer.registry.center.Global;
import org.eclipse.core.runtime.IConfigurationElement;
import org.eclipse.core.runtime.IExtensionRegistry;

import java.util.ArrayList;
import java.util.List;

public class ExpressionRegistry {

    static final String TAG_FUNCTION = "function"; //$NON-NLS-1$
    static final String TAG_NAMESPACE = "namespace"; //$NON-NLS-1$

    private static ExpressionRegistry instance = null;

    public synchronized static ExpressionRegistry getInstance() {
        if (instance == null) {
            instance = new ExpressionRegistry();
            instance.loadExtensions(Global.getExtensionRegistry());
        }
        return instance;
    }

    private final List<ExpressionNamespaceDescriptor> expressionNamespaces = new ArrayList<>();

    private ExpressionRegistry() {
    }

    private void loadExtensions(IExtensionRegistry registry) {
        {
            IConfigurationElement[] extConfigs = registry.getConfigurationElementsFor(ExpressionNamespaceDescriptor.EXP_EXTENSION_ID);
            for (IConfigurationElement ext : extConfigs) {
                // Load expression functions
                if (TAG_NAMESPACE.equals(ext.getName())) {
                    this.expressionNamespaces.add(new ExpressionNamespaceDescriptor(ext));
                }
            }
        }
    }

    public void dispose() {
        expressionNamespaces.clear();
    }

    public List<ExpressionNamespaceDescriptor> getExpressionNamespaces() {
        return expressionNamespaces;
    }

}
