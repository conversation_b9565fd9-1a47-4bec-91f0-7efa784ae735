package com.dc.summer.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.sql.*;

@Slf4j
@SuppressWarnings("unchecked")
public class NewJdbcUtil {

    public static String formatSql(String sql) {
        return sql.replaceAll(";\\s*$", "");
    }


    public static void close(Statement stmt) {
        try {
            if (stmt != null) {
                stmt.close();
            }
        } catch (Exception e) {
            log.error("close error : ", e);
        }
    }

    public static void close(PreparedStatement ps) {
        try {
            if (ps != null) {
                ps.close();
            }
        } catch (Exception e) {
            log.error("close error : ", e);
        }
    }

    public static void close(ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
        } catch (SQLException e) {
            log.error("close error : ", e);
        }
    }

    public static void close(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (Exception e) {
                log.error("close error : ", e);
            }
        }
    }

    public static String valueToDateFormat(String value, String pattern) {
        return String.format("TO_DATE('%s', '%s')", value, pattern);
    }

    public static String valueToTimeStampFormat(String value, String pattern) {
        return String.format("TO_TIMESTAMP('%s', '%s')", value, pattern);
    }

    public static String valueToTimeStampTZFormat(String value, String pattern) {
        return String.format("TO_TIMESTAMP_TZ('%s', '%s')", value, pattern);
    }

    public static String valueToDSINTERVAL(String value) {
        return String.format("TO_DSINTERVAL('%s')", value);
    }

    public static String valueToYMINTERVAL(String value) {
        return String.format("TO_YMINTERVAL('%s')", value);
    }

    public static String str2HexStr(String str) {

        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;

        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
            sb.append(' ');
        }
        return sb.toString().trim();
    }

    public static String bytes2HexString(byte[] b) {
        String r = "";

        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            r += hex.toUpperCase();
        }

        return r;
    }


    public static byte[] string2Bytes(String s){
        byte[] r = s.getBytes();
        return r;
    }

    public static byte[] toByteArray (Object obj) {
        byte[] bytes = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.flush();
            bytes = bos.toByteArray ();
            oos.close();
            bos.close();
        } catch (IOException ex) {
            log.error("toByteArray error : ", ex);
        }
        return bytes;
    }
}
