
package com.dc.summer.model.impl;

import com.dc.summer.model.struct.rdb.DBSCatalog;
import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCExecutionContextDefaults;
import com.dc.summer.model.exec.DBCFeatureNotSupportedException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.rdb.DBSSchema;

/**
 * VoidExecutionContextDefaults
 */
public class VoidExecutionContextDefaults implements DBCExecutionContextDefaults {

    @Override
    public DBSCatalog getDefaultCatalog() {
        return null;
    }

    @Override
    public DBSSchema getDefaultSchema() {
        return null;
    }

    @Override
    public boolean supportsCatalogChange() {
        return false;
    }

    @Override
    public boolean supportsSchemaChange() {
        return false;
    }

    @Override
    public void setDefaultCatalog(DBRProgressMonitor monitor, DBSCatalog catalog, DBSSchema schema, boolean force) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public void setDefaultSchema(DBRProgressMonitor monitor, DBSSchema schema, boolean force) throws DBCException {
        throw new DBCFeatureNotSupportedException();
    }

    @Override
    public boolean refreshDefaults(DBRProgressMonitor monitor, boolean useBootstrapSettings) throws DBException {
        throw new DBCFeatureNotSupportedException();
    }
}
