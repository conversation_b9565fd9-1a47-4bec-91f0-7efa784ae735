

package com.dc.summer.model.qm;

import com.dc.summer.model.qm.meta.*;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.qm.meta.*;
import com.dc.utils.CommonUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * QM meta event
 */
public class QMMetaEventEntity implements QMEvent {
    private final long id;
    private final QMSessionInfo sessionInfo;
    private final QMMObject object;
    private final QMEventAction action;

    public QMMetaEventEntity(QMMObject object, QMEventAction action, long id, String sessionId, @Nullable QMSessionInfo sessionInfo) {
        this.id = id;
        this.sessionInfo = sessionInfo;
        this.object = object;
        this.action = action;
    }

    public long getId() {
        return id;
    }

    @Nullable
    public QMSessionInfo getSessionInfo() {
        return sessionInfo;
    }

    public QMMObject getObject() {
        return object;
    }

    public QMEventAction getAction() {
        return action;
    }

    public static Map<String, Object> toMap(QMMetaEventEntity event) throws DBException {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("objectClassName", event.getObject().getClass().getName());
        result.put("object", event.getObject().toMap());
        result.put("action", event.getAction().getId());
        result.put("id", event.getId());
        if (event.getSessionInfo() != null) {
            result.put("sessionName", event.getSessionInfo().getUserName());
            result.put("sessionId", event.getSessionInfo().getUserDomain());
        }
        return result;
    }

    public static QMMetaEventEntity fromMap(Map<String, Object> map) {
        String className = CommonUtils.toString(map.get("objectClassName"));
        Map<String, Object> object = (Map<String, Object>) map.get("object");
        QMMObject eventObject;
        if (className.equals(QMMConnectionInfo.class.getName())) {
            eventObject = QMMConnectionInfo.fromMap(object);
        } else if (className.equals(QMMStatementExecuteInfo.class.getName())) {
            eventObject = QMMStatementExecuteInfo.fromMap(object);
        } else if (className.equals(QMMStatementInfo.class.getName())) {
            eventObject = QMMStatementInfo.fromMap(object);
        } else if (className.equals(QMMTransactionInfo.class.getName())) {
            eventObject = QMMTransactionInfo.fromMap(object);
        } else {
            eventObject = null;
        }
        QMEventAction action = QMEventAction.getById(CommonUtils.toInt(map.get("action")));
        long id = CommonUtils.toLong(map.get("id"));
        QMSessionInfo sessionInfo = null;
        String sessionUserName = CommonUtils.toString(map.get("sessionName"));
        String sessionUserDomain = CommonUtils.toString(map.get("sessionId"));
        if (!sessionUserName.isEmpty()) {
            sessionInfo = new QMSessionInfo(sessionUserName, sessionUserDomain);
        }
        return new QMMetaEventEntity(eventObject, action, id, "", sessionInfo);
    }
}
