

package com.dc.summer.model.access;

import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.runtime.DBRProgressMonitor;

/**
 * Auth credentials provider.
 */
public interface DBACredentialsProvider {

    /**
     * Fill credential parameters in the specified container and configuration.
     * Returns false on auth cancel. True otherwise.
     */
    boolean provideAuthParameters(@NotNull DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer dataSourceContainer, @NotNull DBPConnectionConfiguration configuration)
        throws DBException;

}
