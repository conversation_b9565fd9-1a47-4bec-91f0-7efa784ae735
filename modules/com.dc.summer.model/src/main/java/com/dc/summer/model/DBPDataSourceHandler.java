

package com.dc.summer.model;

import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.code.NotNull;

/**
 * DBPDataSourceHandler
 */
public interface DBPDataSourceHandler
{
    void beforeConnect(DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer dataSourceContainer)
        throws DBException;

    void beforeDisconnect(DBRProgressMonitor monitor, @NotNull DBPDataSourceContainer dataSourceContainer)
        throws DBException;
}