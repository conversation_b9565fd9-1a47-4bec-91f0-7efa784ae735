package com.dc.summer.registry.center;


import com.dc.summer.model.impl.auth.AuthModelKerberosCredentials;
import com.dc.utils.io.FindFileUtils;
import com.dc.utils.io.ReaderUtils;
import com.dc.utils.xml.XMLException;
import com.dc.utils.xml.XMLUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.core.runtime.*;
import org.osgi.framework.*;
import org.w3c.dom.Document;

import java.io.*;
import java.lang.reflect.Field;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class Global {

    private static Global instance;

    private static final String USER_DIR = System.getProperty("user.dir");

    private static final String DRIVERS = "/drivers/";

    private static final String LOCAL = "/local/";

    private Registry registry;

    private Bundle bundle;

    private static Config config;

    private static final String PLUGIN_NAME = "plugin.xml";

    private static final String POM_NAME = "pom.properties";

    private static final String TAG_NAME = "plugin";

    private static final String EXTENSION_NAME = "extension";

    private static final String POINT_NAME = "point";

    private static final String VERSION_NAME = "version";

    public static final String PACKAGE = "com.dc.summer";

    private static final List<String> CLASS_NAMES = new ArrayList<>() {};

    static {
        CLASS_NAMES.add("class");
        CLASS_NAMES.add("sampleClass");
    }

    @Data
    public static class Config {
        private String root;

        private String krb5;

        public void init() {
            if (Global.config == null) {
                setConfig(this);
            }
        }
    }

    private List<Document> documents;

    private Set<String> files;

    private Global() {
    }

    public static String getROOT() {
        return config != null && config.root != null ? config.root : USER_DIR;
    }

    /**
     * @deprecated Use {@link AuthModelKerberosCredentials#getKrb5Conf()}
     */
    @Deprecated
    public static String getKRB5() {
        return config != null ? config.krb5 : null;
    }

    public static String getEXPORT() {
        String path = getROOT() + "/export";
        File file = new File(path);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        return getROOT() + "/export/";
    }

    public static String getDOWNLOAD() {
        return getROOT() + "/download/";
    }

    public static String getREPORT() {
        return getROOT() + "/report/";
    }

    public static void setConfig(Config config) {
        Global.config = config;
    }

    public static String getUserDir() {
        return USER_DIR;
    }

    public static String getDRIVERS() {
        return getROOT() + DRIVERS;
    }

    public static String getLOCAL() {
        return "file:" + getROOT() + LOCAL;
    }

    public static Registry getExtensionRegistry() {
        init();
        return instance.registry;
    }

    public static Bundle getBundle() {
        init();
        return instance.bundle;
    }

    public static void initializeMessages(final String baseName, final Class<?> clazz) {

        init();

        String dir = baseName.substring(0, baseName.lastIndexOf('.'));
        String name = baseName.substring(baseName.lastIndexOf('.') + 1);

        String language = Locale.getDefault().getLanguage();
        if (language.equals(Locale.ENGLISH.getLanguage())) {
            language = "";
        } else {
            language = "_" + language;
        }
        final String file = name + language + ".properties";
        for (String f : FindFileUtils.getFileNamesByDir(PACKAGE, dir, s -> s.equals(file))) {
            try {

                Properties properties = ReaderUtils.load(f, Properties.class, reader -> {
                    Properties p = new Properties();
                    p.load(reader);
                    return p;
                });

                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    String property = properties.getProperty(field.getName());
                    if (property != null) {
                        field.set(null, property);
                    }
                }

            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static synchronized void init() {
        if (instance == null) {
            log.debug("Init Global - start");
            Global global = new Global();

            global.files = FindFileUtils.getFileNamesByName(PACKAGE, PLUGIN_NAME);

            global.documents = new ArrayList<>(global.files.size());
            for (String f : global.files) {
                try {
                    global.documents.add(XMLUtils.parseDocument(f));
                } catch (XMLException e) {
                    throw new RuntimeException(e);
                }
            }

            instance = global;
            instance.registry = new Registry();
            instance.bundle = new Bundle();
            log.debug("Init Global - finish");
        }
    }

    public static class Element implements IConfigurationElement {

        private final org.w3c.dom.Element element;

        private final Contributor contributor;

        public Element(org.w3c.dom.Element element, String contributorId) {
            this.element = element;
            this.contributor = new Contributor(contributorId);
        }

        private static Element[] getElementArray(Collection<org.w3c.dom.Element> elements, String contributorId) {
            return getElementList(elements, contributorId).toArray(new Element[0]);
        }

        private static List<Element> getElementList(Collection<org.w3c.dom.Element> elements, String contributorId) {
            return elements.stream().map(e -> new Element(e, contributorId)).collect(Collectors.toList());
        }

        @Override
        public Object createExecutableExtension(String propertyName) throws CoreException {
            return null;
        }

        @Override
        public String getAttribute(String name) throws InvalidRegistryObjectException {
            String attribute = this.element.getAttribute(name);
            return attribute.trim().isEmpty() ? null : attribute;
        }

        @Override
        public String getAttribute(String attrName, String locale) throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getAttributeAsIs(String name) throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String[] getAttributeNames() throws InvalidRegistryObjectException {
            return new String[0];
        }

        @Override
        public IConfigurationElement[] getChildren() throws InvalidRegistryObjectException {
            return Element.getElementArray(XMLUtils.getChildElementList(this.element), contributor.name);
        }

        @Override
        public IConfigurationElement[] getChildren(String name) throws InvalidRegistryObjectException {
            return Element.getElementArray(XMLUtils.getChildElementList(this.element, name), contributor.name);
        }

        @Override
        public IExtension getDeclaringExtension() throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getName() throws InvalidRegistryObjectException {
            return this.element.getTagName();
        }

        @Override
        public Object getParent() throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getValue() throws InvalidRegistryObjectException {
            return this.element.getNodeValue();
        }

        @Override
        public String getValue(String locale) throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getValueAsIs() throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getNamespace() throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public String getNamespaceIdentifier() throws InvalidRegistryObjectException {
            return null;
        }

        @Override
        public IContributor getContributor() throws InvalidRegistryObjectException {
            return contributor;
        }

        @Override
        public boolean isValid() {
            return false;
        }

        @Override
        public int getHandleId() {
            return 0;
        }
    }

    public static class Contributor implements IContributor {

        private final String name;

        public Contributor(String name) {
            this.name = name;
        }

        @Override
        public String getName() {
            return this.name;
        }
    }

    public static class Registry implements IExtensionRegistry {


        private final Map<String, IConfigurationElement[]> map = new HashMap<>();

        private Registry() {
        }

        @Override
        public void addRegistryChangeListener(IRegistryChangeListener listener, String namespace) {

        }

        @Override
        public void addRegistryChangeListener(IRegistryChangeListener listener) {

        }

        @Override
        public IConfigurationElement[] getConfigurationElementsFor(String extensionPointId) {
            int lastdot = extensionPointId.lastIndexOf('.');
            if (lastdot == -1)
                return new IConfigurationElement[0];
            return getConfigurationElementsFor(extensionPointId.substring(0, lastdot), extensionPointId.substring(lastdot + 1));
        }

        @Override
        public IConfigurationElement[] getConfigurationElementsFor(String namespace, String extensionPointName) {

            String key = namespace + "." + extensionPointName;

            if (map.containsKey(key)) {
                return map.get(key);
            }

            List<Element> elements = getElements(key);

            IConfigurationElement[] iConfigurationElements = elements.toArray(new IConfigurationElement[0]);
            map.put(key, iConfigurationElements);
            return iConfigurationElements;
        }

        private List<Element> getElements(String key) {
            List<Element> elements = new LinkedList<>();

            for (Document document : instance.documents) {
                org.w3c.dom.Element element = document.getDocumentElement();
                if (!element.getTagName().equals(TAG_NAME)) {
                    continue;
                }

                List<org.w3c.dom.Element> childElementList = XMLUtils.getChildElementList(element, EXTENSION_NAME);
                for (org.w3c.dom.Element childElement : childElementList) {
                    if (childElement.getAttribute(POINT_NAME).equals(key)) {
                        elements.addAll(Element.getElementList(XMLUtils.getChildElementList(childElement), key));
                    }
                }
            }
            return elements;
        }

        @Override
        public IConfigurationElement[] getConfigurationElementsFor(String namespace, String extensionPointName, String extensionId) {
            return new IConfigurationElement[0];
        }

        @Override
        public IExtension getExtension(String extensionId) {
            return null;
        }

        @Override
        public IExtension getExtension(String extensionPointId, String extensionId) {
            return null;
        }

        @Override
        public IExtension getExtension(String namespace, String extensionPointName, String extensionId) {
            return null;
        }

        @Override
        public IExtensionPoint getExtensionPoint(String extensionPointId) {
            return null;
        }

        @Override
        public IExtensionPoint getExtensionPoint(String namespace, String extensionPointName) {
            return null;
        }

        @Override
        public IExtensionPoint[] getExtensionPoints() {
            return new IExtensionPoint[0];
        }

        @Override
        public IExtensionPoint[] getExtensionPoints(String namespace) {
            return new IExtensionPoint[0];
        }

        @Override
        public IExtensionPoint[] getExtensionPoints(IContributor contributor) {
            return new IExtensionPoint[0];
        }

        @Override
        public IExtension[] getExtensions(String namespace) {
            return new IExtension[0];
        }

        @Override
        public IExtension[] getExtensions(IContributor contributor) {
            return new IExtension[0];
        }

        @Override
        public String[] getNamespaces() {
            return new String[0];
        }

        @Override
        public void removeRegistryChangeListener(IRegistryChangeListener listener) {

        }

        @Override
        public boolean addContribution(InputStream is, IContributor contributor, boolean persist, String name, ResourceBundle translationBundle, Object token) throws IllegalArgumentException {
            return false;
        }

        @Override
        public boolean removeExtension(IExtension extension, Object token) throws IllegalArgumentException {
            return false;
        }

        @Override
        public boolean removeExtensionPoint(IExtensionPoint extensionPoint, Object token) throws IllegalArgumentException {
            return false;
        }

        @Override
        public void stop(Object token) throws IllegalArgumentException {

        }

        @Override
        public void addListener(IRegistryEventListener listener) {

        }

        @Override
        public void addListener(IRegistryEventListener listener, String extensionPointId) {

        }

        @Override
        public void removeListener(IRegistryEventListener listener) {

        }

        @Override
        public boolean isMultiLanguage() {
            return false;
        }
    }

    public static class Bundle implements org.osgi.framework.Bundle {

        private String version;

        private final Set<String> names = new HashSet<>();

        public Bundle() {
            loadVersion();
            for (Document document : instance.documents) {
                List<org.w3c.dom.Element> elements = XMLUtils.getChildElementList(document.getDocumentElement(), EXTENSION_NAME);
                loadNames(elements);
            }
        }

        private void loadVersion() {
            try {
                String path = Objects.requireNonNull(this.getClass().getClassLoader().getResource("")).getPath();
                path = path.replace("classes", "maven-archiver") + POM_NAME;
                Properties properties = ReaderUtils.load(path, Properties.class, reader -> {
                    Properties p = new Properties();
                    p.load(reader);
                    return p;
                });
                this.version = properties.getProperty(VERSION_NAME).replaceAll("[^\\d.]", "");
            } catch (Exception e) {
                this.version = "0";
            }
        }

        private void loadNames(Collection<org.w3c.dom.Element> elements) {
            for (org.w3c.dom.Element element : elements) {
                for (String className : CLASS_NAMES) {
                    String name = element.getAttribute(className);
                    if (!name.isEmpty()) {
                        names.add(name);
                    }
                    if (element.hasChildNodes()) {
                        loadNames(XMLUtils.getChildElementList(element));
                    }
                }
            }
        }


        @Override
        public int getState() {
            return org.osgi.framework.Bundle.ACTIVE;
        }

        @Override
        public void start(int options) throws BundleException {

        }

        @Override
        public void start() throws BundleException {

        }

        @Override
        public void stop(int options) throws BundleException {

        }

        @Override
        public void stop() throws BundleException {

        }

        @Override
        public void update(InputStream input) throws BundleException {

        }

        @Override
        public void update() throws BundleException {

        }

        @Override
        public void uninstall() throws BundleException {

        }

        @Override
        public Dictionary<String, String> getHeaders() {
            return null;
        }

        @Override
        public long getBundleId() {
            return 0;
        }

        @Override
        public String getLocation() {
            return null;
        }

        @Override
        public ServiceReference<?>[] getRegisteredServices() {
            return new ServiceReference[0];
        }

        @Override
        public ServiceReference<?>[] getServicesInUse() {
            return new ServiceReference[0];
        }

        @Override
        public boolean hasPermission(Object permission) {
            return false;
        }

        @Override
        public URL getResource(String name) {
            return null;
        }

        @Override
        public Dictionary<String, String> getHeaders(String locale) {
            return null;
        }

        @Override
        public String getSymbolicName() {
            return this.getClass().getName();
        }

        @Override
        public Class<?> loadClass(String name) throws ClassNotFoundException {
            ClassRegistryCenter center = ClassRegistryCenter.getInstance();
            Class<?> clazz = center.getByClassName(name);
            if (clazz == null && this.names.contains(name)) {
                clazz = Class.forName(name);
                center.register(clazz);
            }
            return clazz;
        }

        @Override
        public Enumeration<URL> getResources(String name) throws IOException {
            return null;
        }

        @Override
        public Enumeration<String> getEntryPaths(String path) {
            return null;
        }

        @Override
        public URL getEntry(String path) {
            try {
                if (!path.contains(":")) {
                    path = "file:" + path;
                }
                return new URL(path);
            } catch (MalformedURLException e) {
                log.error("getEntry error : ", e);
                return null;
            }
        }

        @Override
        public long getLastModified() {
            return 0;
        }

        @Override
        public Enumeration<URL> findEntries(String path, String filePattern, boolean recurse) {
            return null;
        }

        @Override
        public BundleContext getBundleContext() {
            return null;
        }

        @Override
        public Map<X509Certificate, List<X509Certificate>> getSignerCertificates(int signersType) {
            return null;
        }

        @Override
        public Version getVersion() {
            return new Version(version);
        }

        @Override
        public <A> A adapt(Class<A> type) {
            return null;
        }

        @Override
        public File getDataFile(String filename) {
            return null;
        }

        @Override
        public int compareTo(org.osgi.framework.Bundle o) {
            return 0;
        }
    }

}
