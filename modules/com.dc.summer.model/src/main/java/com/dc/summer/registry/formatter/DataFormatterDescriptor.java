

package com.dc.summer.registry.formatter;

import com.dc.summer.Log;
import com.dc.summer.model.data.DBDDataFormatterSample;
import com.dc.summer.model.impl.AbstractDescriptor;
import com.dc.summer.model.impl.PropertyDescriptor;
import com.dc.summer.model.preferences.DBPPropertyDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.summer.model.data.DBDDataFormatter;

/**
 * DataFormatterDescriptor
 */
public class DataFormatterDescriptor extends AbstractDescriptor
{
    private static final Log log = Log.getLog(DataFormatterDescriptor.class);

    public static final String EXTENSION_ID = "com.dc.summer.dataFormatter"; //$NON-NLS-1$

    private String id;
    private String name;
    private String description;
    private DBPPropertyDescriptor[] properties;
    private DBDDataFormatterSample sample;
    private ObjectType formatterType;

    public DataFormatterDescriptor(IConfigurationElement config)
    {
        super(config);

        this.id = config.getAttribute("id");
        this.formatterType = new ObjectType(config.getAttribute("class"));
        this.name = config.getAttribute("label");
        this.description = config.getAttribute("description");
        this.properties = PropertyDescriptor.extractPropertyGroups(config);

        Class<?> objectClass = getObjectClass(config.getAttribute("sampleClass"));
        try {
            sample = (DBDDataFormatterSample)objectClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("Can't instantiate data formatter '" + getId() + "' sample");
        }
    }

    public String getId()
    {
        return id;
    }

    public String getName()
    {
        return name;
    }

    public String getDescription()
    {
        return description;
    }

    public DBDDataFormatterSample getSample()
    {
        return sample;
    }

    public DBPPropertyDescriptor[] getProperties() {
        return properties;
    }

    public DBDDataFormatter createFormatter() throws ReflectiveOperationException
    {
        Class<? extends DBDDataFormatter> clazz = formatterType.getObjectClass(DBDDataFormatter.class);
        if (clazz == null) {
            return null;
        }
        return clazz.getConstructor().newInstance();
    }

}
