
package com.dc.summer.runtime.encode;

import com.dc.utils.IOUtils;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;

/**
 * Content encryption/description
 */
public class ContentEncrypter {

    public static final String CIPHER_NAME = "AES/CBC/PKCS5Padding";
    public static final String KEY_ALGORITHM = "AES";

    private SecretKey secretKey;
    private Cipher cipher;

    public ContentEncrypter(<PERSON><PERSON><PERSON> secretKey) {
        this.secretKey = secretKey;
        try {
            this.cipher = Cipher.getInstance(CIPHER_NAME);
        } catch (Exception e) {
            throw new IllegalStateException("Internal error during encrypted init", e);
        }
    }

    public byte[] encrypt(String content) throws InvalidKeyException, IOException {
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] iv = cipher.getIV();

        ByteArrayOutputStream resultBuffer = new ByteArrayOutputStream();
        try (CipherOutputStream cipherOut = new CipherOutputStream(resultBuffer, cipher)) {
            resultBuffer.write(iv);
            cipherOut.write(content.getBytes(StandardCharsets.UTF_8));
        }
        return resultBuffer.toByteArray();
    }

    public String decrypt(byte[] contents) throws InvalidAlgorithmParameterException, InvalidKeyException, IOException {
        try (InputStream byteStream = new ByteArrayInputStream(contents)) {
            byte[] fileIv = new byte[16];
            byteStream.read(fileIv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(fileIv));

            try (CipherInputStream cipherIn = new CipherInputStream(byteStream, cipher)) {
                ByteArrayOutputStream resultBuffer = new ByteArrayOutputStream();
                IOUtils.copyStream(cipherIn, resultBuffer);
                return new String(resultBuffer.toByteArray(), StandardCharsets.UTF_8);
            }

        }
    }

}