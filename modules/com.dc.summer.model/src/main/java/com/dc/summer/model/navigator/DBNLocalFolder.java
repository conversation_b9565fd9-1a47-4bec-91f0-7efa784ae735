
package com.dc.summer.model.navigator;

import com.dc.summer.model.*;
import com.dc.code.NotNull;
import com.dc.summer.DBException;
import com.dc.summer.model.app.DBPDataSourceRegistry;
import com.dc.summer.model.messages.ModelMessages;
import com.dc.summer.model.meta.Property;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.VoidProgressMonitor;
import com.dc.summer.model.struct.DBSObject;
import com.dc.utils.ArrayUtils;
import com.dc.utils.CommonUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * DBNLocalFolder
 */
public class DBNLocalFolder extends DBNNode implements DBNContainer
{
    private DBPDataSourceFolder folder;

    public DBNLocalFolder(DBNProjectDatabases parentNode, DBPDataSourceFolder folder)
    {
        super(parentNode);
        this.folder = folder;
    }

    @Override
    protected void dispose(boolean reflect)
    {
        super.dispose(reflect);
    }

    public DBPDataSourceFolder getFolder() {
        return folder;
    }

    public void setFolder(DBPDataSourceFolder folder) {
        this.folder = folder;
    }

    public DBPDataSourceRegistry getDataSourceRegistry() {
        return ((DBNProjectDatabases)parentNode).getDataSourceRegistry();
    }

    @NotNull
    @Property(viewable = true, order = 1)
    public String getName()
    {
        return getNodeName();
    }

    @Override
    public Object getValueObject()
    {
        return folder;
    }

    @Override
    public String getChildrenType()
    {
        return ModelMessages.model_navigator_Connection;
    }

    @Override
    public String getNodeType()
    {
        return "folder";
    }

    @Override
    public String getNodeName()
    {
        return folder.getName();
    }

    @Override
    public String getNodeDescription()
    {
        return folder.getDescription();
    }

    @Override
    public String getNodeItemPath() {
        return NodePathType.folder.getPrefix() + getOwnerProject().getId() + "/" + folder.getFolderPath();
    }

    @Override
    public DBNProjectDatabases getParentNode() {
        return (DBNProjectDatabases)super.getParentNode();
    }

    @Override
    public boolean allowsChildren()
    {
        return true;
    }

    @Override
    public boolean hasChildren(boolean navigableOnly) {
        if (!ArrayUtils.isEmpty(folder.getChildren())) {
            return true;
        }
        for (DBNDataSource dataSource : getParentNode().getDataSources()) {
            if (folder == dataSource.getDataSourceContainer().getFolder()) {
                return true;
            }
        }
        return false;
    }

    public DBNNode getLogicalParent() {
        if (folder.getParent() == null) {
            return getParentNode();
        } else {
            return getParentNode().getFolderNode(folder.getParent());
        }
    }

    @Override
    public DBNNode[] getChildren(DBRProgressMonitor monitor)
    {
        if (ArrayUtils.isEmpty(folder.getChildren())) {
            return ArrayUtils.toArray(DBNDataSource.class, getDataSources());
        }
        final List<DBNNode> nodes = new ArrayList<>();
        for (DBPDataSourceFolder childFolder : folder.getChildren()) {
            nodes.add(getParentNode().getFolderNode(childFolder));
        }
        nodes.addAll(getDataSources());
        sortNodes(nodes);
        return ArrayUtils.toArray(DBNNode.class, nodes);
    }

    public List<DBNDataSource> getDataSources()
    {
        List<DBNDataSource> children = new ArrayList<>();
        DBNProjectDatabases parent = getParentNode();
        for (DBNDataSource dataSource : parent.getDataSources()) {
            if (folder == dataSource.getDataSourceContainer().getFolder()) {
                children.add(dataSource);
            }
        }
        sortNodes(children);
        return children;
    }

    @Override
    public Class<? extends DBSObject> getChildrenClass()
    {
        return DBPDataSourceContainer.class;
    }

    @Override
    public boolean supportsDrop(DBNNode otherNode)
    {
        return otherNode == null || otherNode instanceof DBNDataSource ||
            (otherNode instanceof DBNLocalFolder && ((DBNLocalFolder) otherNode).getFolder().canMoveTo(getFolder()));
    }

    @Override
    public void dropNodes(Collection<DBNNode> nodes) throws DBException {
        for (DBNNode node : nodes) {
            if (node.getOwnerProject() == this.getOwnerProject()) {
                if (node instanceof DBNDataSource) {
                    ((DBNDataSource) node).moveToFolder(getOwnerProject(), folder);
                } else if (node instanceof DBNLocalFolder) {
                    ((DBNLocalFolder) node).getFolder().setParent(this.getFolder());
                }
            } else {
                if (node instanceof DBNDataSource) {
                    getParentNode().moveNodesToFolder(Collections.singletonList(node), this.getFolder());
                } else if (node instanceof DBNLocalFolder) {
                    log.error("Can't move nodes between projects");
                }
            }
        }
        DBNModel.updateConfigAndRefreshDatabases(this);
    }

    @Override
    public boolean supportsRename()
    {
        return true;
    }

    @Override
    public void rename(DBRProgressMonitor monitor, String newName) throws DBException
    {
        folder.setName(newName);
        DBNModel.updateConfigAndRefreshDatabases(this);
    }

    public boolean hasConnected() {
        for (DBPDataSourceFolder childFolder : folder.getChildren()) {
            if (getParentNode().getFolderNode(childFolder).hasConnected()) {
                return true;
            }
        }
        for (DBNDataSource ds : getDataSources()) {
            if (ds.getDataSource() != null) {
                return true;
            }
        }
        return false;
    }

    public List<DBNDataSource> getNestedDataSources() {
        List<DBNDataSource> result = new ArrayList<>();
        fillNestedDataSources(result);
        return result;
    }

    private void fillNestedDataSources(List<DBNDataSource> dataSources) {
        for (DBNNode childFolder : getChildren(new VoidProgressMonitor())) {
            if (childFolder instanceof DBNLocalFolder) {
                ((DBNLocalFolder) childFolder).fillNestedDataSources(dataSources);
            }
        }
        dataSources.addAll(getDataSources());
    }

    @Override
    public String toString() {
        return folder.getFolderPath();
    }

}
