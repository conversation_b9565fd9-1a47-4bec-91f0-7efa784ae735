
package com.dc.summer.runtime.net;

import com.dc.summer.ModelPreferences;
import com.dc.summer.bundle.ModelActivator;
import com.dc.summer.model.DBPDataSourceContainer;
import com.dc.summer.model.connection.DBPAuthInfo;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.impl.net.SocksConstants;
import com.dc.summer.model.net.DBWHandlerConfiguration;
import com.dc.summer.model.net.DBWHandlerType;
import com.dc.summer.model.preferences.DBPPreferenceStore;
import com.dc.summer.runtime.DBWorkbench;
import com.dc.summer.runtime.encode.EncryptionException;
import com.dc.summer.runtime.encode.SecuredPasswordEncrypter;
import org.eclipse.core.net.proxy.IProxyData;
import org.eclipse.core.net.proxy.IProxyService;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceReference;

import java.net.Authenticator;
import java.net.PasswordAuthentication;

/**
 * Global authenticator
 */
public class GlobalProxyAuthenticator extends Authenticator {

    private SecuredPasswordEncrypter encrypter;

    private final IProxyService proxyService;

    public GlobalProxyAuthenticator() {
        BundleContext bundleContext = ModelActivator.getInstance().getBundle().getBundleContext();
        ServiceReference<IProxyService> proxyServiceRef = bundleContext.getServiceReference(IProxyService.class);
        if (proxyServiceRef != null) {
            proxyService = bundleContext.getService(proxyServiceRef);
        } else {
            proxyService = null;
        }
    }

    @Nullable
    @Override
    protected PasswordAuthentication getPasswordAuthentication() {
        {
            DBPPreferenceStore store = ModelPreferences.getPreferences();

            // 1. Check for drivers download proxy
            final String proxyHost = store.getString(ModelPreferences.UI_PROXY_HOST);
            if (!CommonUtils.isEmpty(proxyHost) && proxyHost.equalsIgnoreCase(getRequestingHost()) &&
                store.getInt(ModelPreferences.UI_PROXY_PORT) == getRequestingPort()) {
                String userName = store.getString(ModelPreferences.UI_PROXY_USER);
                String userPassword = decryptPassword(store.getString(ModelPreferences.UI_PROXY_PASSWORD));
                if (CommonUtils.isEmpty(userName) || CommonUtils.isEmpty(userPassword)) {
                    DBPAuthInfo authInfo = readCredentialsInUI("Auth proxy '" + proxyHost + "'", userName, userPassword);
                    if (authInfo != null) {
                        userName = authInfo.getUserName();
                        userPassword = authInfo.getUserPassword();
                        if (authInfo.isSavePassword()) {
                            // Save in preferences
                            store.setValue(ModelPreferences.UI_PROXY_USER, userName);
                            store.setValue(ModelPreferences.UI_PROXY_PASSWORD, encryptPassword(userPassword));
                        }
                    }
                }
                if (!CommonUtils.isEmpty(userName) && !CommonUtils.isEmpty(userPassword)) {
                    return new PasswordAuthentication(userName, userPassword.toCharArray());
                }
            }
        }

        {
            // 2. Check for connections' proxies
            String requestingProtocol = getRequestingProtocol();
            if (SocksConstants.PROTOCOL_SOCKS5.equals(requestingProtocol) || SocksConstants.PROTOCOL_SOCKS4.equals(requestingProtocol)) {
                DBPDataSourceContainer activeContext = DBExecUtils.findConnectionContext(getRequestingHost(), getRequestingPort(), getRequestingScheme());
                if (activeContext != null) {
                    for (DBWHandlerConfiguration networkHandler : activeContext.getConnectionConfiguration().getHandlers()) {
                        if (networkHandler.isEnabled() && networkHandler.getType() == DBWHandlerType.PROXY) {
                            String userName = networkHandler.getUserName();
                            String userPassword = networkHandler.getPassword();
                            if (CommonUtils.isEmpty(userName) || CommonUtils.isEmpty(userPassword)) {
                                DBPAuthInfo authInfo = readCredentialsInUI(getRequestingPrompt(), userName, userPassword);
                                if (authInfo != null) {
                                    userName = authInfo.getUserName();
                                    userPassword = authInfo.getUserPassword();
                                    if (authInfo.isSavePassword()) {
                                        // Save DS config
                                        networkHandler.setUserName(userName);
                                        networkHandler.setPassword(userPassword);
                                        networkHandler.setSavePassword(true);
                                        activeContext.getRegistry().flushConfig();
                                    }
                                }
                            }
                            if (!CommonUtils.isEmpty(userName) && !CommonUtils.isEmpty(userPassword)) {
                                return new PasswordAuthentication(userName, userPassword.toCharArray());
                            }
                        }
                    }
                }
            }
        }

        if (proxyService != null) {
            // Try to use Eclispe proxy config for global proxies
            IProxyData[] proxyData = proxyService.getProxyData();
            if (proxyData != null) {
                for (IProxyData pd : proxyData) {
                    if (getRequestingProtocol().startsWith(pd.getType()) && pd.getUserId() != null && pd.getHost() != null && pd.getPort() == this.getRequestingPort() && pd.getHost().equalsIgnoreCase(getRequestingHost())) {
                        return new PasswordAuthentication(pd.getUserId(), pd.getPassword().toCharArray());
                    }
                }

                return null;
            }
        }
        return null;
    }

    private String encryptPassword(String password) {
        try {
            if (encrypter == null) {
                encrypter = new SecuredPasswordEncrypter();
            }
            return encrypter.encrypt(password);
        } catch (EncryptionException e) {
            return password;
        }
    }

    private String decryptPassword(String password) {
        if (CommonUtils.isEmpty(password)) {
            return password;
        }
        try {
            if (encrypter == null) {
                encrypter = new SecuredPasswordEncrypter();
            }
            return encrypter.decrypt(password);
        } catch (EncryptionException e) {
            return password;
        }
    }

    private DBPAuthInfo readCredentialsInUI(String prompt, String userName, String userPassword) {
        return DBWorkbench.getPlatformUI().promptUserCredentials(prompt, userName, userPassword, false, true);
    }

}
