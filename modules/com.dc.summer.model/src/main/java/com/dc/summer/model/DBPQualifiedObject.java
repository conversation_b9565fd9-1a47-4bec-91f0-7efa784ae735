
package com.dc.summer.model;

import com.dc.code.NotNull;

/**
 * Named object extension
 */
public interface DBPQualifiedObject extends DBPObject
{

    /**
     * Entity full qualified name.
     * Should include all parent objects' names and thus uniquely identify this entity within database.
     * @return full qualified name, never returns null.
     * @param context evaluation context
     */
    @NotNull
    String getFullyQualifiedName(DBPEvaluationContext context);

}
