
package com.dc.summer.runtime.jobs;

import com.dc.summer.Log;
import com.dc.summer.model.*;
import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.exec.*;
import com.dc.summer.model.net.DBWNetworkHandler;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSInstance;
import com.dc.summer.runtime.DBWorkbench;
import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Invalidate datasource job.
 * Invalidates all datasource contexts (not just the one passed in constructor).
 */
public class InvalidateJob extends DataSourceJob {
    private static final Log log = Log.getLog(InvalidateJob.class);

    private static final String TASK_INVALIDATE = "dsInvalidate";

    private static final ReentrantLock LOCK = new ReentrantLock();

    public static class ContextInvalidateResult {
        public final DBCExecutionContext.InvalidateResult result;
        public final Exception error;

        ContextInvalidateResult(DBCExecutionContext.InvalidateResult result, Exception error) {
            this.result = result;
            this.error = error;
        }

        @Override
        public String toString() {
            return result.name();
        }
    }

    private long timeSpent;
    private List<ContextInvalidateResult> invalidateResults = new ArrayList<>();

    public InvalidateJob(
            DBPDataSource dataSource) {
        super("Invalidate " + dataSource.getContainer().getName(), DBUtils.getDefaultContext(dataSource.getDefaultInstance(), false));
    }

    public List<ContextInvalidateResult> getInvalidateResults() {
        return invalidateResults;
    }

    public long getTimeSpent() {
        return timeSpent;
    }

    @Override
    protected IStatus run(DBRProgressMonitor monitor) {
        DBPDataSource dataSource = getExecutionContext().getDataSource();

        // Disable disconnect on failure. It is the worst case anyway.
        // Not sure that we should force disconnect even here.
        this.invalidateResults = invalidateDataSource(monitor, dataSource, false, true, null, null);

        return Status.OK_STATUS;
    }

    public static List<ContextInvalidateResult> invalidateDataSource(DBRProgressMonitor monitor,
                                                                     DBPDataSource dataSource,
                                                                     boolean disconnectOnFailure,
                                                                     boolean showErrors,
                                                                     DBCExecutionContext executionContext,
                                                                     DBPConnectionConfiguration configuration) {
        // Do not recover if connection was canceled
        log.debug("Invalidate datasource '" + dataSource.getContainer().getName() + "' connections...");

        List<ContextInvalidateResult> invalidateResults = new ArrayList<>();

        DBPDataSourceContainer container = dataSource.getContainer();

        boolean networkOK;
        int goodContextsNumber, aliveContextsNumber;

        monitor.beginTask("Invalidate datasource '" + dataSource.getContainer().getName() + "'", 1);

        monitor.subTask("Obtain exclusive datasource lock");
        Object dsLock = container.getExclusiveLock().acquireTaskLock(TASK_INVALIDATE, true);
        if (dsLock == DBPExclusiveResource.TASK_PROCESED) {
            // Already invalidated
            monitor.done();
            log.debug("Datasource '" + dataSource.getContainer().getName() + "' was already invalidated");
            return invalidateResults;
        }
        log.debug("Invalidate datasource '" + container.getName() + "' (" + container.getId() + ")");
        try {

            monitor.subTask("Invalidate network connection");
            DBWNetworkHandler[] activeHandlers = container.getActiveNetworkHandlers();
            networkOK = true;
            aliveContextsNumber = 0;
            goodContextsNumber = 0;
            if (activeHandlers != null && activeHandlers.length > 0) {
                for (DBWNetworkHandler nh : activeHandlers) {
                    log.debug("\tInvalidate network handler '" + nh.getClass().getSimpleName() + "' for " + container.getId());
                    monitor.subTask("Invalidate handler [" + nh.getClass().getSimpleName() + "]");
                    try {
                        nh.invalidateHandler(monitor, dataSource);
                    } catch (Exception e) {
                        invalidateResults.add(new ContextInvalidateResult(DBCExecutionContext.InvalidateResult.ERROR, e));
                        networkOK = false;
                        break;
                    }
                }
            }

            // Invalidate datasource
            if (executionContext != null) {
                monitor.subTask("Invalidate connections of [" + container.getName() + "] - " + executionContext.getContextId());
            } else {
                monitor.subTask("Invalidate connections of [" + container.getName() + "]");
            }

            List<DBCExecutionContext> contexts = new ArrayList<>();

            if (executionContext != null) {
                contexts.add(executionContext);
            } else {
                dataSource.getAvailableInstances().forEach(instance -> contexts.addAll(List.of(instance.getAllContexts())));
            }

            for (DBCExecutionContext context : contexts) {
                log.debug("\tInvalidate context '" + context.getContextName() + "' for " + container.getId());
                if (networkOK) {
                    LOCK.lock();
                    try {
                        final DBCExecutionContext.InvalidateResult result = context.invalidateContext(monitor, disconnectOnFailure, configuration);
                        if (result != DBCExecutionContext.InvalidateResult.ERROR) {
                            goodContextsNumber++;
                        }
                        if (result == DBCExecutionContext.InvalidateResult.ALIVE) {
                            aliveContextsNumber++;
                        }
                        invalidateResults.add(new ContextInvalidateResult(result, null));
                    } catch (Exception e) {
                        log.debug("\tFailed: " + e.getMessage());
                        invalidateResults.add(new ContextInvalidateResult(DBCExecutionContext.InvalidateResult.ERROR, e));
                    } finally {
                        LOCK.unlock();
                    }
                }
            }

            if (goodContextsNumber > 0 && goodContextsNumber == aliveContextsNumber) {
                // Nothing to reinit, all contexts are alive. Why we are here??
                return invalidateResults;
            }
            if (goodContextsNumber == 0 && disconnectOnFailure) {
                // Close whole datasource. Target host seems to be unavailable
                try {
                    container.disconnect(monitor);
                } catch (Exception e) {
                    log.error("Error closing inaccessible datasource", e);
                }
                StringBuilder msg = new StringBuilder();
                for (ContextInvalidateResult result : invalidateResults) {
                    if (result.error != null) {
                        if (msg.length() > 0) msg.append("\n");
                        msg.append(result.error.getMessage());
                    }
                }
                DBWorkbench.getPlatformUI().showError("Forced disconnect", "Datasource '" + container.getName() + "' was disconnected: destination database unreachable.\n" + msg);
            }

        } finally {
            container.getExclusiveLock().releaseTaskLock(TASK_INVALIDATE, dsLock);
            monitor.done();
        }

        return invalidateResults;
    }

    public static void invalidateTransaction(DBRProgressMonitor monitor, DBPDataSource dataSource, DBCExecutionContext executionContext) {
        // Transaction aborted
        if (executionContext != null) {
            log.debug("Invalidate context [" + executionContext.getDataSource().getContainer().getName() + "/" + executionContext.getContextName() + "] transactions");
        } else {
            log.debug("Invalidate datasource [" + dataSource.getContainer().getName() + "] transactions");
        }

        // Invalidate transactions
        if (executionContext != null) {
            monitor.subTask("Invalidate context [" + executionContext.getDataSource().getContainer().getName() + "/" + executionContext.getContextName() + "] transactions");
            invalidateTransaction(monitor, executionContext);
        } else {
            monitor.subTask("Invalidate datasource [" + dataSource.getContainer().getName() + "] transactions");
            for (DBSInstance instance : dataSource.getAvailableInstances()) {
                for (DBCExecutionContext context : instance.getAllContexts()) {
                    invalidateTransaction(monitor, context);
                }
            }
        }
    }

    public static void invalidateTransaction(DBRProgressMonitor monitor, DBCExecutionContext context) {
        DBCTransactionManager txnManager = DBUtils.getTransactionManager(context);
        if (txnManager != null) {
            try {
                if (!txnManager.isAutoCommit()) {
                    try (DBCSession session = context.openSession(monitor, DBCExecutionPurpose.UTIL, "Rollback failed transaction")) {
                        // Disable logging to avoid QM handlers notifications.
                        // These notifications may trigger smart commit mode during txn recover. See #9066
                        session.enableLogging(false);
                        txnManager.rollback();
                    }
                }
            } catch (DBCException e) {
                log.error("Error invalidating aborted transaction", e);
            }
        }
    }


    @Override
    protected void canceling() {
        getThread().interrupt();
    }

}