

package com.dc.summer.runtime.serialize;

import com.dc.summer.Log;
import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.summer.DBException;

/**
 * SerializerDescriptor
 */
public class SerializerDescriptor extends AbstractDescriptor
{
    private static final Log log = Log.getLog(SerializerDescriptor.class);

    public static final String EXTENSION_ID = "com.dc.summer.serialize"; //$NON-NLS-1$

    private String id;
    private ObjectType serializerType;

    SerializerDescriptor(IConfigurationElement config)
    {
        super(config);

        this.id = config.getAttribute("id");
        this.serializerType = new ObjectType(config.getAttribute("class"));
    }

    public String getId()
    {
        return id;
    }

    public DBPObjectSerializer createSerializer() throws DBException {
        return serializerType.createInstance(DBPObjectSerializer.class);
    }

}
