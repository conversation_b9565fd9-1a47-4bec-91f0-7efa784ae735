

package com.dc.summer.runtime;

import com.dc.summer.Log;
import com.dc.summer.runtime.ui.console.ConsoleUserInterface;
import com.dc.summer.utils.GeneralUtils;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.app.DBPPlatform;
import com.dc.summer.runtime.ui.DBPPlatformUI;

/**
 * Workbench
 */
public class DBWorkbench {

    private static final Log log = Log.getLog(DBWorkbench.class);

    private static final DBWorkbench instance = new DBWorkbench();
    private static final ConsoleUserInterface CONSOLE_USER_INTERFACE = new ConsoleUserInterface();

    private static volatile DBPPlatform platformInstance = null;
    private static volatile DBPPlatformUI platformUIInstance = null;

    public static DBPPlatform getPlatform() {
        if (platformInstance == null) {
            synchronized (DBWorkbench.class) {
                if (platformInstance == null) {
                    platformInstance = GeneralUtils.adapt(instance, DBPPlatform.class);
                    if (platformInstance == null) {
                        throw new IllegalStateException("Internal configuration error. Platform not instantiated.");
                    }
                }
            }
        }
        return platformInstance;
    }

    public static <T extends DBPPlatform> T getPlatform(Class<T> pc) {
        return pc.cast(getPlatform());
    }

    public static DBPPlatformUI getPlatformUI() {
        if (platformUIInstance == null) {
            synchronized (DBWorkbench.class) {
                if (platformUIInstance == null) {
                    if (getPlatform().getApplication().isHeadlessMode()) {
                        return CONSOLE_USER_INTERFACE;
                    }
                    platformUIInstance = GeneralUtils.adapt(instance, DBPPlatformUI.class);
                    if (platformUIInstance == null) {
                        // Use console UI
                        log.debug("No platform UI installed. Use console interface.");
                        platformUIInstance = CONSOLE_USER_INTERFACE;
                    }
                }
            }
        }
        return platformUIInstance;
    }

    /**
     * Service management
     */
    @Nullable
    public static <T> T getService(@NotNull Class<T> serviceType) {
        T service = ServiceRegistry.getInstance().getService(serviceType);
        if (service == null) {
            log.debug("Service '" + serviceType.getName() + "' not found");
        }
        return service;
    }

}
