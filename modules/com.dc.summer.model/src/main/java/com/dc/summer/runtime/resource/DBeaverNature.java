
package com.dc.summer.runtime.resource;

import org.eclipse.core.resources.IProject;
import org.eclipse.core.resources.IProjectNature;
import org.eclipse.core.runtime.CoreException;

/**
 * DBeaver project nature
 */
public class DBeaverNature implements IProjectNature {

    public static final String NATURE_ID = "com.dc.summer.DBeaverNature";

    private IProject project;

    public void configure() throws CoreException {

    }

    public void deconfigure() throws CoreException {

    }

    public IProject getProject() {
        return project;
    }

    public void setProject(IProject value) {
        project = value;
    }
}