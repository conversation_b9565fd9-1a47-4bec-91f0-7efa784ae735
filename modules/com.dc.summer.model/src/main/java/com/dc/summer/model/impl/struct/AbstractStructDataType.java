
package com.dc.summer.model.impl.struct;

import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.struct.DBSEntityConstraint;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.DBException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSEntity;
import com.dc.summer.model.struct.DBSEntityAssociation;
import com.dc.summer.model.struct.DBSEntityAttribute;

import java.util.Collection;

/**
 * AbstractStructDataType
 */
public abstract class AbstractStructDataType<DS extends DBPDataSource> extends AbstractDataType<DS> implements DBSEntity
{
    public AbstractStructDataType(DS dataSource) {
        super(dataSource);
    }

    @Nullable
    @Override
    public DBSEntityAttribute getAttribute(@NotNull DBRProgressMonitor monitor, @NotNull String attributeName) throws DBException {
        Collection<? extends DBSEntityAttribute> attributes = getAttributes(monitor);
        if (attributes != null && !attributes.isEmpty()) {
            for (DBSEntityAttribute attr : attributes) {
                if (attr.getName().equals(attributeName)) {
                    return attr;
                }
            }
        }
        return null;
    }

    /**
     * Doesn't make sense here
     */
    @Nullable
    @Override
    public Collection<? extends DBSEntityAssociation> getReferences(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    /**
     * Doesn't make sense here
     */
    @Nullable
    @Override
    public Collection<? extends DBSEntityAssociation> getAssociations(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

    /**
     * Doesn't make sense here
     */
    @Nullable
    @Override
    public Collection<? extends DBSEntityConstraint> getConstraints(@NotNull DBRProgressMonitor monitor) throws DBException {
        return null;
    }

}
