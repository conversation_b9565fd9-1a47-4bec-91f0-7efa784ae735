
package com.dc.summer.model;

import com.dc.summer.model.connection.DBPConnectionConfiguration;
import com.dc.summer.model.connection.DBPDriver;

/**
 * Data source URL provider
 */
public interface DBPDataSourceURLProvider {
    /**
     * Constructs connection URL
     * @param driver driver descriptor
     * @param connectionInfo connection info
     * @return valid connection URL or null (if URLs not supported by driver)
     */
    String getConnectionURL(DBPDriver driver, DBPConnectionConfiguration connectionInfo);

}
