

package com.dc.summer.registry.connection;

import com.dc.code.NotNull;
import com.dc.summer.model.DBPDataSourceProvider;
import com.dc.summer.model.DBPExternalConnection;
import com.dc.summer.model.DBPExternalConnectionProvider;
import com.dc.summer.model.impl.AbstractDescriptor;
import org.eclipse.core.runtime.IConfigurationElement;

/**
 * ConnectionPoolDescriptor
 */
public class ConnectionPoolProviderDescriptor extends AbstractDescriptor {
    private final String id;
    private final String implClass;
    private final String description;
    private final ObjectType implType;

    private volatile DBPExternalConnectionProvider instance;

    public ConnectionPoolProviderDescriptor(IConfigurationElement config) {
        super(config.getContributor().getName());
        this.id = config.getAttribute("id");
        this.implClass = config.getAttribute("class");
        this.description = config.getAttribute("description");
        this.implType = new ObjectType(implClass);
    }

    public String getId() {
        return id;
    }

    public String getImplClass() {
        return implClass;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return id;
    }


    @NotNull
    public DBPExternalConnectionProvider getInstance() {
        if (instance == null) {
            synchronized (this) {
                if (instance == null) {
                    try {
                        // locate class
                        this.instance = implType.createInstance(DBPExternalConnectionProvider.class);
                    } catch (Throwable ex) {
                        this.instance = null;
                        throw new IllegalStateException("Can't initialize external connection provider '" + implType.getImplName() + "'", ex);
                    }
                }
            }

        }
        return instance;
    }
}
