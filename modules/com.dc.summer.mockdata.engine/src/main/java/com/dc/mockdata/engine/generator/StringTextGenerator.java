package com.dc.mockdata.engine.generator;

import com.dc.summer.DBException;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.struct.DBSAttributeBase;
import com.dc.summer.model.struct.DBSDataManipulator;
import com.dc.utils.CommonUtils;

import java.io.IOException;
import java.util.Map;

/**
 * Simple string generator (lorem ipsum)
 */
public class StringTextGenerator extends AbstractStringValueGenerator {

    private String templateString;
    private int minLength = 1;
    private int maxLength = 100;

    public StringTextGenerator() {
    }

    @Override
    public void init(DBSDataManipulator container, DBSAttributeBase attribute, Map<String, Object> properties) throws DBException {
        super.init(container, attribute, properties);

        this.templateString = CommonUtils.toString(properties.get("template"));
        if (this.templateString == null) {
            throw new DBCException("Empty template string for simple string generator");
        }

        Integer min = (Integer) properties.get("minLength");
        if (min != null) {
            this.minLength = min;
        }

        if (this.minLength > this.templateString.length()) {
            this.minLength = this.templateString.length();
        }

        Integer max = (Integer) properties.get("maxLength");
        if (max != null) {
            this.maxLength = max;
        }

        if (this.maxLength == 0 || (attribute!= null && attribute.getMaxLength() > 0L) && (long) this.maxLength > attribute.getMaxLength()) {
            this.maxLength = (int) attribute.getMaxLength();
        }

        if (this.maxLength > this.templateString.length()) {
            this.maxLength = this.templateString.length();
        }

        if (this.minLength > this.maxLength) {
            this.maxLength = this.minLength;
        }
    }

    @Override
    public void nextRow() {
    }

    @Override
    public Object generateOneValue(DBRProgressMonitor monitor) throws DBException, IOException {
        if (this.isGenerateNULL()) {
            return null;
        } else {
            int length = this.minLength + this.random.nextInt(this.maxLength - this.minLength + 1);
            int tplLength = this.templateString.length();
            int start = this.random.nextInt(tplLength);
            if (start > 0) {
                // Find word begin
                int wordStart = start;

                while (wordStart < tplLength && !Character.isWhitespace(this.templateString.charAt(wordStart - 1))) {
                    wordStart++;
                }

                if (wordStart < tplLength) {
                    start = wordStart;
                }
            }

            if (start + length < tplLength) {
                return this.tune(this.templateString.substring(start, start + length));
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append(this.templateString.substring(start));
                int newlength = length - (tplLength - start);

                for (int i = 0; i < newlength / tplLength; i++) {
                    sb.append(this.templateString);
                }

                sb.append(this.templateString.substring(0, newlength % tplLength));
                return this.tune(sb.toString().trim());
            }
        }
    }
}
