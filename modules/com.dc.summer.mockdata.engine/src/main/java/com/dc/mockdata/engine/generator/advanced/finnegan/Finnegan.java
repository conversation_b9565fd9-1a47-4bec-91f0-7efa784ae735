package com.dc.mockdata.engine.generator.advanced.finnegan;

import com.dc.mockdata.engine.util.FNV1a;

import java.io.Serializable;
import java.text.Normalizer;
import java.text.Normalizer.Form;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Finnegan implements Serializable {

    private static final long serialVersionUID = -2578460257281186353L;
    public final String[] openingVowels;
    public final String[] midVowels;
    public final String[] openingConsonants;
    public final String[] midConsonants;
    public final String[] closingConsonants;
    public final String[] vowelSplitters;
    public final String[] closingSyllables;
    public boolean clean;
    public final LinkedHashMap<Integer, Double> syllableFrequencies;
    protected double totalSyllableFrequency = 0.0;
    public final double vowelStartFrequency;
    public final double vowelEndFrequency;
    public final double vowelSplitFrequency;
    public final double syllableEndFrequency;
    protected final Pattern[] sanityChecks;
    public ArrayList<Modifier> modifiers;
    protected static final Pattern repeats = Pattern.compile("(.)\\1+");
    protected static final Pattern diacritics = Pattern.compile("[\\u0300-\\u036F\\u1DC0-\\u1DFF]+");
    public static final Pattern[] vulgarChecks = new Pattern[]{
            Pattern.compile("[SsξCcсςС][hнН].*[dtтτТΤf]"),
            Pattern.compile("([PpрρРΡ][hнН])|[KkкκКΚFfDdCcсςС].{1,4}[KkкκКΚCcсςСxхжχХЖΧ]"),
            Pattern.compile("[BbъыбвβЪЫБВΒ]..?.?[cсςС][hнН]"),
            Pattern.compile("[WwшщψШЩHhнН]..?[rяЯ]"),
            Pattern.compile("[TtтτТΤ]..?[tтτТΤ]"),
            Pattern.compile("([PpрρРΡ][hнН])|[Ff]..?[rяЯ][tтτТΤ]"),
            Pattern.compile("([Ssξ][hнН])|[j][iτιΙ].?[sξzΖ]"),
            Pattern.compile("[AaаαАΑΛ][NnийИЙΝ]..?[SsξlιζzΖ]"),
            Pattern.compile("[AaаαАΑΛ][sξ][sξ]"),
            Pattern.compile(".[uμυν][hнН]?[nийИЙΝ]+[tтτТΤ]"),
            Pattern.compile("[NnFf]..?g"),
            Pattern.compile("[PpрρРΡ][eеёзξεЕЁЗΞΕΣioоюσοОЮΟuμυν][eеёзξεЕЁЗΞΕΣoоюσοОЮΟs]"),
            Pattern.compile("[MmмМΜ]..?[rяЯ].?d"),
            Pattern.compile("[Gg][hнН]?[aаαАΑΛeеёзξεЕЁЗΞΕΣ][yуλγУΥeеёзξεЕЁЗΞΕΣ]")
    };
    public static final Pattern[] englishSanityChecks = new Pattern[]{
            Pattern.compile("[AEIOUaeiou]{3}"),
            Pattern.compile("(\\w)\\1\\1"),
            Pattern.compile("(.)\\1(.)\\2"),
            Pattern.compile("[Aa][ae]"),
            Pattern.compile("[Uu][umlkj]"),
            Pattern.compile("[Ii][iyqkhrl]"),
            Pattern.compile("[Oo][c]"),
            Pattern.compile("[Yy][aeiou]{2}"),
            Pattern.compile("[Rr][aeiouy]+[xrhp]"),
            Pattern.compile("[Qq]u[yu]"),
            Pattern.compile("[^oai]uch"),
            Pattern.compile("[^tcsz]hh"),
            Pattern.compile("[Hh][tcszi]h"),
            Pattern.compile("[Tt]t[^aeiouy]{2}"),
            Pattern.compile("[IYiy]h[^aeiouy ]"),
            Pattern.compile("[szSZrlRL][^aeiou][rlsz]"),
            Pattern.compile("[UIuiYy][wy]"),
            Pattern.compile("^[UIui][ae]"),
            Pattern.compile("q$")
    };
    public static final Pattern[] japaneseSanityChecks = new Pattern[]{
            Pattern.compile("[AEIOUaeiou]{3}"),
            Pattern.compile("(\\w)\\1\\1"),
            Pattern.compile("[Tt]s[^u]"),
            Pattern.compile("[Ff][^u]"),
            Pattern.compile("[Yy][^auo]"),
            Pattern.compile("[Tt][ui]"),
            Pattern.compile("[SsZzDd]i"),
            Pattern.compile("[Hh]u")
    };
    public RNG rng;
    public static final char[][] accentedVowels = new char[][]{
            {'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ā', 'ă', 'ą', 'ǻ', 'ǽ'},
            {'è', 'é', 'ê', 'ë', 'ē', 'ĕ', 'ė', 'ę', 'ě'},
            {'ì', 'í', 'î', 'ï', 'ĩ', 'ī', 'ĭ', 'į', 'ı'},
            {'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ō', 'ŏ', 'ő', 'œ', 'ǿ'},
            {'ù', 'ú', 'û', 'ü', 'ũ', 'ū', 'ŭ', 'ů', 'ű', 'ų'}
    };
    public static final char[][] accentedConsonants = new char[][]{
            {'b'},
            {'c', 'ç', 'ć', 'ĉ', 'ċ', 'č'},
            {'d', 'þ', 'ð', 'ď', 'đ'},
            {'f'},
            {'g', 'ĝ', 'ğ', 'ġ', 'ģ'},
            {'h', 'ĥ', 'ħ'},
            {'j', 'ĵ', 'ȷ'},
            {'k', 'ķ'},
            {'l', 'ĺ', 'ļ', 'ľ', 'ŀ', 'ł'},
            {'m'},
            {'n', 'ñ', 'ń', 'ņ', 'ň', 'ŋ'},
            {'p'},
            {'q'},
            {'r', 'ŕ', 'ŗ', 'ř'},
            {'s', 'ś', 'ŝ', 'ş', 'š', 'ș'},
            {'t', 'ţ', 'ť', 'ț'},
            {'v'},
            {'w', 'ŵ', 'ẁ', 'ẃ', 'ẅ'},
            {'x'},
            {'y', 'ý', 'ÿ', 'ŷ', 'ỳ'},
            {'z', 'ź', 'ż', 'ž'}
    };
    public static final Finnegan LOVECRAFT = new Finnegan(
            new String[]{"a", "i", "o", "e", "u", "a", "i", "o", "e", "u", "ia", "ai", "aa", "ei"},
            new String[0],
            new String[]{"s", "t", "k", "n", "y", "p", "k", "l", "g", "gl", "th", "sh", "ny", "ft", "hm", "zvr", "cth"},
            new String[]{"h", "gl", "gr", "nd", "mr", "vr", "kr"},
            new String[]{"l", "p", "s", "t", "n", "k", "g", "x", "rl", "th", "gg", "gh", "ts", "lt", "rk", "kh", "sh", "ng", "shk"},
            new String[]{"aghn", "ulhu", "urath", "oigor", "alos", "'yeh", "achtal", "urath", "ikhet", "adzek"},
            new String[]{"'", "-"},
            new int[]{1, 2, 3},
            new double[]{6.0, 7.0, 2.0},
            0.4,
            0.31,
            0.07,
            0.04,
            null,
            true
    );
    public static final Finnegan ENGLISH = new Finnegan(
            new String[]{
                    "a",
                    "a",
                    "a",
                    "a",
                    "o",
                    "o",
                    "o",
                    "e",
                    "e",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "i",
                    "i",
                    "u",
                    "a",
                    "a",
                    "a",
                    "a",
                    "o",
                    "o",
                    "o",
                    "e",
                    "e",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "i",
                    "i",
                    "u",
                    "a",
                    "a",
                    "a",
                    "o",
                    "o",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "i",
                    "u",
                    "a",
                    "a",
                    "a",
                    "o",
                    "o",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "i",
                    "u",
                    "au",
                    "ai",
                    "ai",
                    "ou",
                    "ea",
                    "ie",
                    "io",
                    "ei"
            },
            new String[]{"u", "u", "oa", "oo", "oo", "oo", "ee", "ee", "ee", "ee"},
            new String[]{
                    "b",
                    "bl",
                    "br",
                    "c",
                    "cl",
                    "cr",
                    "ch",
                    "d",
                    "dr",
                    "f",
                    "fl",
                    "fr",
                    "g",
                    "gl",
                    "gr",
                    "h",
                    "j",
                    "k",
                    "l",
                    "m",
                    "n",
                    "p",
                    "pl",
                    "pr",
                    "qu",
                    "r",
                    "s",
                    "sh",
                    "sk",
                    "st",
                    "sp",
                    "sl",
                    "sm",
                    "sn",
                    "t",
                    "tr",
                    "th",
                    "thr",
                    "v",
                    "w",
                    "y",
                    "z",
                    "b",
                    "bl",
                    "br",
                    "c",
                    "cl",
                    "cr",
                    "ch",
                    "d",
                    "dr",
                    "f",
                    "fl",
                    "fr",
                    "g",
                    "gr",
                    "h",
                    "j",
                    "k",
                    "l",
                    "m",
                    "n",
                    "p",
                    "pl",
                    "pr",
                    "r",
                    "s",
                    "sh",
                    "st",
                    "sp",
                    "sl",
                    "t",
                    "tr",
                    "th",
                    "w",
                    "y",
                    "b",
                    "br",
                    "c",
                    "ch",
                    "d",
                    "dr",
                    "f",
                    "g",
                    "h",
                    "j",
                    "l",
                    "m",
                    "n",
                    "p",
                    "r",
                    "s",
                    "sh",
                    "st",
                    "sl",
                    "t",
                    "tr",
                    "th",
                    "b",
                    "d",
                    "f",
                    "g",
                    "h",
                    "l",
                    "m",
                    "n",
                    "p",
                    "r",
                    "s",
                    "sh",
                    "t",
                    "th",
                    "b",
                    "d",
                    "f",
                    "g",
                    "h",
                    "l",
                    "m",
                    "n",
                    "p",
                    "r",
                    "s",
                    "sh",
                    "t",
                    "th",
                    "r",
                    "s",
                    "t",
                    "l",
                    "n",
                    "str",
                    "spr",
                    "spl",
                    "wr",
                    "kn",
                    "kn",
                    "gn"
            },
            new String[]{
                    "x",
                    "cst",
                    "bs",
                    "ff",
                    "lg",
                    "g",
                    "gs",
                    "ll",
                    "ltr",
                    "mb",
                    "mn",
                    "mm",
                    "ng",
                    "ng",
                    "ngl",
                    "nt",
                    "ns",
                    "nn",
                    "ps",
                    "mbl",
                    "mpr",
                    "pp",
                    "ppl",
                    "ppr",
                    "rr",
                    "rr",
                    "rr",
                    "rl",
                    "rtn",
                    "ngr",
                    "ss",
                    "sc",
                    "rst",
                    "tt",
                    "tt",
                    "ts",
                    "ltr",
                    "zz"
            },
            new String[]{
                    "b",
                    "rb",
                    "bb",
                    "c",
                    "rc",
                    "ld",
                    "d",
                    "ds",
                    "dd",
                    "f",
                    "ff",
                    "lf",
                    "rf",
                    "rg",
                    "gs",
                    "ch",
                    "lch",
                    "rch",
                    "tch",
                    "ck",
                    "ck",
                    "lk",
                    "rk",
                    "l",
                    "ll",
                    "lm",
                    "m",
                    "rm",
                    "mp",
                    "n",
                    "nk",
                    "nch",
                    "nd",
                    "ng",
                    "ng",
                    "nt",
                    "ns",
                    "lp",
                    "rp",
                    "p",
                    "r",
                    "rn",
                    "rts",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ss",
                    "ss",
                    "st",
                    "ls",
                    "t",
                    "t",
                    "ts",
                    "w",
                    "wn",
                    "x",
                    "ly",
                    "lly",
                    "z",
                    "b",
                    "c",
                    "d",
                    "f",
                    "g",
                    "k",
                    "l",
                    "m",
                    "n",
                    "p",
                    "r",
                    "s",
                    "t",
                    "w"
            },
            new String[]{
                    "ate",
                    "ite",
                    "ism",
                    "ist",
                    "er",
                    "er",
                    "er",
                    "ed",
                    "ed",
                    "ed",
                    "es",
                    "es",
                    "ied",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ate",
                    "ite",
                    "ism",
                    "ist",
                    "er",
                    "er",
                    "er",
                    "ed",
                    "ed",
                    "ed",
                    "es",
                    "es",
                    "ied",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ate",
                    "ite",
                    "ism",
                    "ist",
                    "er",
                    "er",
                    "er",
                    "ed",
                    "ed",
                    "ed",
                    "es",
                    "es",
                    "ied",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ay",
                    "ay",
                    "ey",
                    "oy",
                    "ay",
                    "ay",
                    "ey",
                    "oy",
                    "ough",
                    "aught",
                    "ant",
                    "ont",
                    "oe",
                    "ance",
                    "ell",
                    "eal",
                    "oa",
                    "urt",
                    "ut",
                    "iom",
                    "ion",
                    "ion",
                    "ision",
                    "ation",
                    "ation",
                    "ition",
                    "ough",
                    "aught",
                    "ant",
                    "ont",
                    "oe",
                    "ance",
                    "ell",
                    "eal",
                    "oa",
                    "urt",
                    "ut",
                    "iom",
                    "ion",
                    "ion",
                    "ision",
                    "ation",
                    "ation",
                    "ition",
                    "ily",
                    "ily",
                    "ily",
                    "adly",
                    "owly",
                    "oorly",
                    "ardly",
                    "iedly"
            },
            new String[0],
            new int[]{1, 2, 3, 4},
            new double[]{7.0, 8.0, 4.0, 1.0},
            0.22,
            0.1,
            0.0,
            0.25,
            englishSanityChecks,
            true
    );
    public static final Finnegan GREEK_ROMANIZED = new Finnegan(
            new String[]{"a", "a", "a", "o", "o", "o", "e", "e", "i", "i", "i", "au", "ai", "ai", "oi", "oi", "ia", "io", "ou", "ou", "eo", "ei"},
            new String[]{"ui", "ei"},
            new String[]{"rh", "s", "z", "t", "t", "k", "ch", "n", "th", "kth", "m", "p", "ps", "b", "l", "kr", "g", "phth"},
            new String[]{"lph", "pl", "l", "l", "kr", "nch", "nx", "ps"},
            new String[]{"s", "p", "t", "ch", "n", "m", "s", "p", "t", "ch", "n", "m", "b", "g", "st", "rst", "rt", "sp", "rk", "ph", "x", "z", "nk", "ng", "th"},
            new String[]{"os", "os", "is", "us", "um", "eum", "ium", "iam", "us", "um", "es", "anes", "eros", "or", "ophon", "on", "otron"},
            new String[0],
            new int[]{1, 2, 3},
            new double[]{5.0, 7.0, 4.0},
            0.45,
            0.45,
            0.0,
            0.3,
            null,
            true
    );
    public static final Finnegan GREEK_AUTHENTIC = new Finnegan(
            new String[]{"α", "α", "α", "ο", "ο", "ο", "ε", "ε", "ι", "ι", "ι", "αυ", "αι", "αι", "οι", "οι", "ια", "ιο", "ου", "ου", "εο", "ει"},
            new String[]{"υι", "ει"},
            new String[]{"ρ", "σ", "ζ", "τ", "τ", "κ", "χ", "ν", "θ", "κθ", "μ", "π", "ψ", "β", "λ", "κρ", "γ", "φθ"},
            new String[]{"λφ", "πλ", "λ", "λ", "κρ", "γχ", "γξ", "ψ"},
            new String[]{"σ", "π", "τ", "χ", "ν", "μ", "σ", "π", "τ", "χ", "ν", "μ", "β", "γ", "στ", "ρστ", "ρτ", "σπ", "ρκ", "φ", "ξ", "ζ", "γκ", "γγ", "θ"},
            new String[]{"ος", "ος", "ις", "υς", "υμ", "ευμ", "ιυμ", "ιαμ", "υς", "υμ", "ες", "ανες", "ερος", "ορ", "οφον", "ον", "οτρον"},
            new String[0],
            new int[]{1, 2, 3},
            new double[]{5.0, 7.0, 4.0},
            0.45,
            0.45,
            0.0,
            0.3,
            null,
            true
    );
    public static final Finnegan FRENCH = new Finnegan(
            new String[]{
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "u",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "u",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "a",
                    "a",
                    "e",
                    "e",
                    "i",
                    "o",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "ai",
                    "oi",
                    "oui",
                    "au",
                    "œu",
                    "ou"
            },
            new String[]{
                    "ai",
                    "aie",
                    "aou",
                    "eau",
                    "oi",
                    "oui",
                    "oie",
                    "eu",
                    "eu",
                    "à",
                    "â",
                    "ai",
                    "aî",
                    "aï",
                    "aie",
                    "aou",
                    "aoû",
                    "au",
                    "ay",
                    "e",
                    "é",
                    "ée",
                    "è",
                    "ê",
                    "eau",
                    "ei",
                    "eî",
                    "eu",
                    "eû",
                    "i",
                    "î",
                    "ï",
                    "o",
                    "ô",
                    "oe",
                    "oê",
                    "oë",
                    "œu",
                    "oi",
                    "oie",
                    "oï",
                    "ou",
                    "oû",
                    "oy",
                    "u",
                    "û",
                    "ue",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "u",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o",
                    "a",
                    "a",
                    "e",
                    "e",
                    "i",
                    "o",
                    "a",
                    "a",
                    "a",
                    "e",
                    "e",
                    "e",
                    "i",
                    "i",
                    "o"
            },
            new String[]{
                    "tr",
                    "ch",
                    "m",
                    "b",
                    "b",
                    "br",
                    "j",
                    "j",
                    "j",
                    "j",
                    "g",
                    "t",
                    "t",
                    "t",
                    "c",
                    "d",
                    "f",
                    "f",
                    "h",
                    "n",
                    "l",
                    "l",
                    "s",
                    "s",
                    "s",
                    "r",
                    "r",
                    "r",
                    "v",
                    "v",
                    "p",
                    "pl",
                    "pr",
                    "bl",
                    "br",
                    "dr",
                    "gl",
                    "gr"
            },
            new String[]{
                    "cqu",
                    "gu",
                    "qu",
                    "rqu",
                    "nt",
                    "ng",
                    "ngu",
                    "mb",
                    "ll",
                    "nd",
                    "ndr",
                    "nct",
                    "st",
                    "xt",
                    "mbr",
                    "pl",
                    "g",
                    "gg",
                    "ggr",
                    "gl",
                    "m",
                    "m",
                    "mm",
                    "v",
                    "v",
                    "f",
                    "f",
                    "f",
                    "ff",
                    "b",
                    "b",
                    "bb",
                    "d",
                    "d",
                    "dd",
                    "s",
                    "s",
                    "s",
                    "ss",
                    "ss",
                    "ss",
                    "cl",
                    "cr",
                    "ng",
                    "ç",
                    "ç",
                    "rç"
            },
            new String[0],
            new String[]{
                    "e",
                    "e",
                    "e",
                    "e",
                    "e",
                    "é",
                    "é",
                    "er",
                    "er",
                    "er",
                    "er",
                    "er",
                    "es",
                    "es",
                    "es",
                    "es",
                    "es",
                    "es",
                    "e",
                    "e",
                    "e",
                    "e",
                    "e",
                    "é",
                    "é",
                    "er",
                    "er",
                    "er",
                    "er",
                    "er",
                    "er",
                    "es",
                    "es",
                    "es",
                    "es",
                    "es",
                    "e",
                    "e",
                    "e",
                    "e",
                    "e",
                    "é",
                    "é",
                    "é",
                    "er",
                    "er",
                    "er",
                    "er",
                    "er",
                    "es",
                    "es",
                    "es",
                    "es",
                    "es",
                    "ent",
                    "em",
                    "en",
                    "en",
                    "aim",
                    "ain",
                    "an",
                    "oin",
                    "ien",
                    "iere",
                    "ors",
                    "anse",
                    "ombs",
                    "ommes",
                    "ancs",
                    "ends",
                    "œufs",
                    "erfs",
                    "ongs",
                    "aps",
                    "ats",
                    "ives",
                    "ui",
                    "illes",
                    "aen",
                    "aon",
                    "am",
                    "an",
                    "eun",
                    "ein",
                    "age",
                    "age",
                    "uile",
                    "uin",
                    "um",
                    "un",
                    "un",
                    "un",
                    "aille",
                    "ouille",
                    "eille",
                    "ille",
                    "eur",
                    "it",
                    "ot",
                    "oi",
                    "oi",
                    "oi",
                    "aire",
                    "om",
                    "on",
                    "on",
                    "im",
                    "in",
                    "in",
                    "ien",
                    "ien",
                    "ion",
                    "il",
                    "eil",
                    "oin",
                    "oint",
                    "iguïté",
                    "ience",
                    "incte",
                    "ang",
                    "ong",
                    "acré",
                    "eau",
                    "ouche",
                    "oux",
                    "oux",
                    "ect",
                    "ecri",
                    "agne",
                    "uer",
                    "aix",
                    "eth",
                    "ut",
                    "ant",
                    "anc",
                    "anc",
                    "anche",
                    "ioche",
                    "eaux",
                    "ive",
                    "eur",
                    "ancois",
                    "ecois"
            },
            new String[0],
            new int[]{1, 2, 3},
            new double[]{18.0, 7.0, 2.0},
            0.35,
            1.0,
            0.0,
            0.55,
            null,
            true
    );
    public static final Finnegan RUSSIAN_ROMANIZED = new Finnegan(
            new String[]{"a", "e", "e", "i", "i", "o", "u", "ie", "y", "e", "iu", "ia", "y", "a", "a", "o", "u"},
            new String[0],
            new String[]{
                    "b",
                    "v",
                    "g",
                    "d",
                    "k",
                    "l",
                    "p",
                    "r",
                    "s",
                    "t",
                    "f",
                    "kh",
                    "ts",
                    "b",
                    "v",
                    "g",
                    "d",
                    "k",
                    "l",
                    "p",
                    "r",
                    "s",
                    "t",
                    "f",
                    "kh",
                    "ts",
                    "b",
                    "v",
                    "g",
                    "d",
                    "k",
                    "l",
                    "p",
                    "r",
                    "s",
                    "t",
                    "f",
                    "zh",
                    "m",
                    "n",
                    "z",
                    "ch",
                    "sh",
                    "shch",
                    "br",
                    "sk",
                    "tr",
                    "bl",
                    "gl",
                    "kr",
                    "gr"
            },
            new String[]{"bl", "br", "pl", "dzh", "tr", "gl", "gr", "kr"},
            new String[]{
                    "b",
                    "v",
                    "g",
                    "d",
                    "zh",
                    "z",
                    "k",
                    "l",
                    "m",
                    "n",
                    "p",
                    "r",
                    "s",
                    "t",
                    "f",
                    "kh",
                    "ts",
                    "ch",
                    "sh",
                    "v",
                    "f",
                    "sk",
                    "sk",
                    "sk",
                    "s",
                    "b",
                    "d",
                    "d",
                    "n",
                    "r",
                    "r"
            },
            new String[]{"odka", "odna", "usk", "ask", "usky", "ad", "ar", "ovich", "ev", "ov", "of", "agda", "etsky", "ich", "on", "akh", "iev", "ian"},
            new String[0],
            new int[]{1, 2, 3, 4, 5, 6},
            new double[]{4.0, 5.0, 6.0, 5.0, 3.0, 1.0},
            0.1,
            0.2,
            0.0,
            0.12,
            englishSanityChecks,
            true
    );
    public static final Finnegan RUSSIAN_AUTHENTIC = new Finnegan(
            new String[]{"а", "е", "ё", "и", "й", "о", "у", "ъ", "ы", "э", "ю", "я", "ы", "а", "а", "о", "у"},
            new String[0],
            new String[]{
                    "б",
                    "в",
                    "г",
                    "д",
                    "к",
                    "л",
                    "п",
                    "р",
                    "с",
                    "т",
                    "ф",
                    "х",
                    "ц",
                    "б",
                    "в",
                    "г",
                    "д",
                    "к",
                    "л",
                    "п",
                    "р",
                    "с",
                    "т",
                    "ф",
                    "х",
                    "ц",
                    "б",
                    "в",
                    "г",
                    "д",
                    "к",
                    "л",
                    "п",
                    "р",
                    "с",
                    "т",
                    "ф",
                    "ж",
                    "м",
                    "н",
                    "з",
                    "ч",
                    "ш",
                    "щ",
                    "бр",
                    "ск",
                    "тр",
                    "бл",
                    "гл",
                    "кр",
                    "гр"
            },
            new String[]{"бл", "бр", "пл", "дж", "тр", "гл", "гр", "кр"},
            new String[]{
                    "б",
                    "в",
                    "г",
                    "д",
                    "ж",
                    "з",
                    "к",
                    "л",
                    "м",
                    "н",
                    "п",
                    "р",
                    "с",
                    "т",
                    "ф",
                    "х",
                    "ц",
                    "ч",
                    "ш",
                    "в",
                    "ф",
                    "ск",
                    "ск",
                    "ск",
                    "с",
                    "б",
                    "д",
                    "д",
                    "н",
                    "р",
                    "р"
            },
            new String[]{"одка", "одна", "уск", "аск", "ускы", "ад", "ар", "овйч", "ев", "ов", "оф", "агда", "ёцкы", "йч", "он", "ах", "ъв", "ян"},
            new String[0],
            new int[]{1, 2, 3, 4, 5, 6},
            new double[]{4.0, 5.0, 6.0, 5.0, 3.0, 1.0},
            0.1,
            0.2,
            0.0,
            0.12,
            null,
            true
    );
    public static final Finnegan JAPANESE_ROMANIZED = new Finnegan(
            new String[]{"a", "a", "a", "a", "e", "e", "i", "i", "i", "i", "o", "o", "o", "u", "ou", "u", "ai", "ai"},
            new String[0],
            new String[]{
                    "k",
                    "ky",
                    "s",
                    "sh",
                    "t",
                    "ts",
                    "ch",
                    "n",
                    "ny",
                    "h",
                    "f",
                    "hy",
                    "m",
                    "my",
                    "y",
                    "r",
                    "ry",
                    "g",
                    "gy",
                    "z",
                    "j",
                    "d",
                    "b",
                    "by",
                    "p",
                    "py",
                    "k",
                    "t",
                    "n",
                    "s",
                    "k",
                    "t",
                    "d",
                    "s",
                    "sh",
                    "sh",
                    "g",
                    "r",
                    "b",
                    "k",
                    "t",
                    "n",
                    "s",
                    "k",
                    "t",
                    "b",
                    "s",
                    "sh",
                    "sh",
                    "g",
                    "r",
                    "b",
                    "k",
                    "t",
                    "n",
                    "s",
                    "k",
                    "t",
                    "z",
                    "s",
                    "sh",
                    "sh",
                    "ch",
                    "ry",
                    "ts"
            },
            new String[]{
                    "k",
                    "ky",
                    "s",
                    "sh",
                    "t",
                    "ts",
                    "ch",
                    "n",
                    "ny",
                    "h",
                    "f",
                    "hy",
                    "m",
                    "my",
                    "y",
                    "r",
                    "ry",
                    "g",
                    "gy",
                    "z",
                    "j",
                    "d",
                    "b",
                    "by",
                    "p",
                    "py",
                    "k",
                    "t",
                    "d",
                    "s",
                    "k",
                    "t",
                    "d",
                    "s",
                    "sh",
                    "sh",
                    "y",
                    "j",
                    "p",
                    "r",
                    "d",
                    "k",
                    "t",
                    "b",
                    "s",
                    "k",
                    "t",
                    "b",
                    "s",
                    "sh",
                    "sh",
                    "y",
                    "j",
                    "p",
                    "r",
                    "d",
                    "k",
                    "t",
                    "z",
                    "s",
                    "f",
                    "g",
                    "z",
                    "b",
                    "d",
                    "ts",
                    "nn",
                    "nn",
                    "nn",
                    "nd",
                    "nz",
                    "mm",
                    "kk",
                    "kk",
                    "tt",
                    "ss",
                    "ssh",
                    "tch"
            },
            new String[]{"n"},
            new String[0],
            new String[0],
            new int[]{1, 2, 3, 4, 5},
            new double[]{5.0, 4.0, 5.0, 4.0, 3.0},
            0.3,
            0.9,
            0.0,
            0.0,
            japaneseSanityChecks,
            true
    );
    public static final Finnegan SWAHILI = new Finnegan(
            new String[]{
                    "a",
                    "i",
                    "o",
                    "e",
                    "u",
                    "a",
                    "a",
                    "i",
                    "o",
                    "o",
                    "e",
                    "u",
                    "a",
                    "a",
                    "i",
                    "o",
                    "o",
                    "u",
                    "a",
                    "a",
                    "i",
                    "i",
                    "o",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "i",
                    "o",
                    "e",
                    "u",
                    "a",
                    "a",
                    "i",
                    "o",
                    "o",
                    "e",
                    "u",
                    "a",
                    "a",
                    "i",
                    "o",
                    "o",
                    "u",
                    "a",
                    "a",
                    "i",
                    "i",
                    "o",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "aa",
                    "aa",
                    "ue",
                    "uo",
                    "ii",
                    "ea"
            },
            new String[0],
            new String[]{
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "nb",
                    "nj",
                    "ns",
                    "nz",
                    "nb",
                    "nch",
                    "nj",
                    "ns",
                    "ny",
                    "nz",
                    "nb",
                    "nch",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nz",
                    "nb",
                    "nch",
                    "nd",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nt",
                    "nz",
                    "nb",
                    "nch",
                    "nd",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nt",
                    "nv",
                    "nw",
                    "nz",
                    "mb",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "mk",
                    "mp",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "md",
                    "mk",
                    "mp",
                    "ms",
                    "mt",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "md",
                    "mf",
                    "mg",
                    "mj",
                    "mk",
                    "mp",
                    "ms",
                    "mt",
                    "mv",
                    "mw",
                    "my",
                    "mz",
                    "sh",
                    "sh",
                    "sh",
                    "ny",
                    "kw",
                    "dh",
                    "th",
                    "sh",
                    "ny",
                    "dh",
                    "th",
                    "sh",
                    "gh",
                    "r",
                    "ny",
                    "dh",
                    "th",
                    "sh",
                    "gh",
                    "r",
                    "ny"
            },
            new String[]{
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "h",
                    "j",
                    "l",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "y",
                    "z",
                    "m",
                    "n",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "b",
                    "ch",
                    "d",
                    "f",
                    "g",
                    "h",
                    "j",
                    "k",
                    "l",
                    "p",
                    "s",
                    "t",
                    "v",
                    "w",
                    "y",
                    "z",
                    "m",
                    "n",
                    "kw",
                    "nb",
                    "nj",
                    "ns",
                    "nz",
                    "nb",
                    "nch",
                    "nj",
                    "ns",
                    "ny",
                    "nz",
                    "nb",
                    "nch",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nz",
                    "nb",
                    "nch",
                    "nd",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nt",
                    "nz",
                    "nb",
                    "nch",
                    "nd",
                    "nf",
                    "ng",
                    "nj",
                    "nk",
                    "np",
                    "ns",
                    "nt",
                    "nw",
                    "nz",
                    "mb",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "mk",
                    "mp",
                    "ms",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "md",
                    "mk",
                    "mp",
                    "ms",
                    "mt",
                    "my",
                    "mz",
                    "mb",
                    "mch",
                    "md",
                    "mf",
                    "mg",
                    "mj",
                    "mk",
                    "mp",
                    "ms",
                    "mt",
                    "mw",
                    "my",
                    "mz",
                    "sh",
                    "sh",
                    "sh",
                    "ny",
                    "kw",
                    "dh",
                    "th",
                    "sh",
                    "ny",
                    "dh",
                    "th",
                    "sh",
                    "gh",
                    "r",
                    "ny",
                    "dh",
                    "th",
                    "sh",
                    "gh",
                    "r",
                    "ny",
                    "ng",
                    "ng",
                    "ng",
                    "ng",
                    "ng"
            },
            new String[]{""},
            new String[]{"a-@2a", "a-@2a", "a-@3a", "a-@2a", "a-@2a", "a-@3a", "i-@2i", "i-@2i", "i-@3i", "e-@2e", "e-@2e", "e-@3e", "u-@2u", "u-@2u", "u-@3u"},
            new String[0],
            new int[]{1, 2, 3, 4, 5},
            new double[]{1.0, 7.0, 6.0, 4.0, 2.0},
            0.2,
            1.0,
            0.0,
            0.25,
            null,
            true
    );
    public static final Finnegan SOMALI = new Finnegan(
            new String[]{
                    "a", "a", "a", "a", "a", "a", "a", "aa", "aa", "aa", "e", "e", "ee", "i", "i", "i", "i", "ii", "o", "o", "o", "oo", "u", "u", "u", "uu", "uu"
            },
            new String[0],
            new String[]{
                    "b",
                    "t",
                    "j",
                    "x",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "dh",
                    "c",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "w",
                    "h",
                    "y",
                    "x",
                    "g",
                    "b",
                    "d",
                    "s",
                    "m",
                    "dh",
                    "n",
                    "r",
                    "g",
                    "b",
                    "s",
                    "dh"
            },
            new String[]{
                    "bb",
                    "gg",
                    "dd",
                    "bb",
                    "dd",
                    "rr",
                    "ddh",
                    "cc",
                    "gg",
                    "ff",
                    "ll",
                    "mm",
                    "nn",
                    "bb",
                    "gg",
                    "dd",
                    "bb",
                    "dd",
                    "gg",
                    "bb",
                    "gg",
                    "dd",
                    "bb",
                    "dd",
                    "gg",
                    "cy",
                    "fk",
                    "ft",
                    "nt",
                    "rt",
                    "lt",
                    "qm",
                    "rdh",
                    "rsh",
                    "lq",
                    "my",
                    "gy",
                    "by",
                    "lkh",
                    "rx",
                    "md",
                    "bd",
                    "dg",
                    "fd",
                    "mf",
                    "dh",
                    "dh",
                    "dh",
                    "dh"
            },
            new String[]{
                    "b",
                    "t",
                    "j",
                    "x",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "c",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "h",
                    "x",
                    "g",
                    "b",
                    "d",
                    "s",
                    "m",
                    "q",
                    "n",
                    "r",
                    "b",
                    "t",
                    "j",
                    "x",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "c",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "h",
                    "x",
                    "g",
                    "b",
                    "d",
                    "s",
                    "m",
                    "q",
                    "n",
                    "r",
                    "b",
                    "t",
                    "j",
                    "x",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "c",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "g",
                    "b",
                    "d",
                    "s",
                    "q",
                    "n",
                    "r",
                    "b",
                    "t",
                    "x",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "g",
                    "b",
                    "d",
                    "s",
                    "r",
                    "n",
                    "b",
                    "t",
                    "kh",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "g",
                    "b",
                    "d",
                    "s",
                    "r",
                    "n",
                    "b",
                    "t",
                    "d",
                    "r",
                    "s",
                    "sh",
                    "g",
                    "f",
                    "q",
                    "k",
                    "l",
                    "m",
                    "n",
                    "g",
                    "b",
                    "d",
                    "s",
                    "r",
                    "n"
            },
            new String[]{"aw", "ow", "ay", "ey", "oy", "ay", "ay"},
            new String[0],
            new int[]{1, 2, 3, 4, 5},
            new double[]{5.0, 4.0, 5.0, 4.0, 1.0},
            0.25,
            0.3,
            0.0,
            0.08,
            null,
            true
    );
    public static final Finnegan HINDI_ROMANIZED = new Finnegan(
            new String[]{
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "a",
                    "ā",
                    "ā",
                    "i",
                    "i",
                    "i",
                    "i",
                    "ī",
                    "i",
                    "i",
                    "ī",
                    "ī",
                    "u",
                    "u",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "u",
                    "ū",
                    "e",
                    "ai",
                    "ai",
                    "o",
                    "o",
                    "o",
                    "au",
                    "aṃ",
                    "aṃ",
                    "aṃ",
                    "aṃ",
                    "aṃ",
                    "āṃ",
                    "āṃ",
                    "iṃ",
                    "iṃ",
                    "iṃ",
                    "īṃ",
                    "īṃ",
                    "uṃ",
                    "uṃ",
                    "ūṃ",
                    "aiṃ",
                    "aiṃ",
                    "oṃ",
                    "oṃ",
                    "oṃ",
                    "auṃ"
            },
            new String[]{"a'", "i'", "u'", "o'", "a'", "i'", "u'", "o'"},
            new String[]{
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "kṛ",
                    "kṝ",
                    "kḷ",
                    "c",
                    "c",
                    "c",
                    "c",
                    "c",
                    "c",
                    "cṛ",
                    "cṝ",
                    "cḷ",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "tṛ",
                    "tṝ",
                    "tṛ",
                    "tṝ",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "pṛ",
                    "pṝ",
                    "pḷ",
                    "pḹ",
                    "pṛ",
                    "pṝ",
                    "p",
                    "p",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "khṛ",
                    "khṝ",
                    "khḷ",
                    "khḹ",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "chṛ",
                    "chṝ",
                    "chḷ",
                    "chḹ",
                    "ṭh",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "thṛ",
                    "thṝ",
                    "thḷ",
                    "thḹ",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "phṛ",
                    "phṝ",
                    "phḷ",
                    "phḹ",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "jh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ś",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ṣ",
                    "s",
                    "g",
                    "j",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "ḍh",
                    "dh",
                    "bh",
                    "ṅ",
                    "ṇ",
                    "n",
                    "m",
                    "h",
                    "y",
                    "r",
                    "l",
                    "v",
                    "ṣ",
                    "s",
                    "g",
                    "ḍ",
                    "d",
                    "b",
                    "gh",
                    "ḍh",
                    "dh",
                    "bh",
                    "n",
                    "m",
                    "v",
                    "s",
                    "g",
                    "ḍ",
                    "d",
                    "b",
                    "g",
                    "d",
                    "b",
                    "dh",
                    "bh",
                    "n",
                    "m",
                    "v",
                    "g",
                    "ḍ",
                    "d",
                    "b",
                    "g",
                    "d",
                    "b",
                    "dh",
                    "bh",
                    "n",
                    "m",
                    "v"
            },
            new String[]{
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "k",
                    "k",
                    "k",
                    "k",
                    "k",
                    "nk",
                    "rk",
                    "kṛ",
                    "kṛ",
                    "kṛ",
                    "kṛ",
                    "kṛ",
                    "nkṛ",
                    "rkṛ",
                    "kṝ",
                    "kṝ",
                    "kṝ",
                    "kṝ",
                    "kṝ",
                    "nkṝ",
                    "rkṝ",
                    "kḷ",
                    "kḷ",
                    "kḷ",
                    "kḷ",
                    "kḷ",
                    "nkḷ",
                    "rkḷ",
                    "c",
                    "c",
                    "c",
                    "c",
                    "c",
                    "c",
                    "cṛ",
                    "cṝ",
                    "cḷ",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "ṭ",
                    "t",
                    "t",
                    "t",
                    "t",
                    "nt",
                    "rt",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "ntṛ",
                    "rtṛ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "ntṝ",
                    "rtṝ",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "tṛ",
                    "ntṛ",
                    "rtṛ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "tṝ",
                    "ntṝ",
                    "rtṝ",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "npṛ",
                    "rpṛ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "npṝ",
                    "rpṝ",
                    "pḷ",
                    "pḷ",
                    "pḷ",
                    "pḷ",
                    "pḷ",
                    "npḷ",
                    "rpḷ",
                    "pḹ",
                    "pḹ",
                    "pḹ",
                    "pḹ",
                    "pḹ",
                    "npḹ",
                    "rpḹ",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "pṛ",
                    "npṛ",
                    "rpṛ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "pṝ",
                    "npṝ",
                    "rpṝ",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "p",
                    "p",
                    "p",
                    "p",
                    "p",
                    "np",
                    "rp",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "kh",
                    "nkh",
                    "rkh",
                    "khṛ",
                    "khṛ",
                    "khṛ",
                    "khṛ",
                    "khṛ",
                    "nkhṛ",
                    "rkhṛ",
                    "khṝ",
                    "khṝ",
                    "khṝ",
                    "khṝ",
                    "khṝ",
                    "nkhṝ",
                    "rkhṝ",
                    "khḷ",
                    "khḷ",
                    "khḷ",
                    "khḷ",
                    "khḷ",
                    "nkhḷ",
                    "rkhḷ",
                    "khḹ",
                    "khḹ",
                    "khḹ",
                    "khḹ",
                    "khḹ",
                    "nkhḹ",
                    "rkhḹ",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "ch",
                    "chṛ",
                    "chṝ",
                    "chḷ",
                    "chḹ",
                    "ṭh",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "th",
                    "th",
                    "th",
                    "th",
                    "th",
                    "nth",
                    "rth",
                    "thṛ",
                    "thṛ",
                    "thṛ",
                    "thṛ",
                    "thṛ",
                    "nthṛ",
                    "rthṛ",
                    "thṝ",
                    "thṝ",
                    "thṝ",
                    "thṝ",
                    "thṝ",
                    "nthṝ",
                    "rthṝ",
                    "thḷ",
                    "thḷ",
                    "thḷ",
                    "thḷ",
                    "thḷ",
                    "nthḷ",
                    "rthḷ",
                    "thḹ",
                    "thḹ",
                    "thḹ",
                    "thḹ",
                    "thḹ",
                    "nthḹ",
                    "rthḹ",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "ph",
                    "nph",
                    "rph",
                    "phṛ",
                    "phṛ",
                    "phṛ",
                    "phṛ",
                    "phṛ",
                    "nphṛ",
                    "rphṛ",
                    "phṝ",
                    "phṝ",
                    "phṝ",
                    "phṝ",
                    "phṝ",
                    "nphṝ",
                    "rphṝ",
                    "phḷ",
                    "phḷ",
                    "phḷ",
                    "phḷ",
                    "phḷ",
                    "nphḷ",
                    "rphḷ",
                    "phḹ",
                    "phḹ",
                    "phḹ",
                    "phḹ",
                    "phḹ",
                    "nphḹ",
                    "rphḹ",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "jh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ñ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "ś",
                    "nś",
                    "rś",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "j",
                    "j",
                    "j",
                    "j",
                    "j",
                    "nj",
                    "rj",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "ṅ",
                    "ṇ",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "h",
                    "y",
                    "y",
                    "y",
                    "y",
                    "y",
                    "ny",
                    "ry",
                    "r",
                    "l",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "ṣ",
                    "nṣ",
                    "rṣ",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "gh",
                    "ngh",
                    "rgh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "ḍh",
                    "nḍh",
                    "rḍh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "s",
                    "s",
                    "s",
                    "s",
                    "s",
                    "ns",
                    "rs",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "ḍ",
                    "nḍ",
                    "rḍ",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "g",
                    "g",
                    "g",
                    "g",
                    "g",
                    "ng",
                    "rg",
                    "d",
                    "d",
                    "d",
                    "d",
                    "d",
                    "nd",
                    "rd",
                    "b",
                    "b",
                    "b",
                    "b",
                    "b",
                    "nb",
                    "rb",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "dh",
                    "ndh",
                    "rdh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "bh",
                    "nbh",
                    "rbh",
                    "n",
                    "m",
                    "m",
                    "m",
                    "m",
                    "m",
                    "nm",
                    "rm",
                    "v",
                    "v",
                    "v",
                    "v",
                    "v",
                    "nv",
                    "rv"
            },
            new String[]{"t", "d", "m", "r", "dh", "b", "t", "d", "m", "r", "dh", "bh", "nt", "nt", "nk", "ṣ"},
            new String[]{"it", "it", "ati", "adva", "aṣ", "arma", "ardha", "abi", "ab", "aya"},
            new String[0],
            new int[]{1, 2, 3, 4, 5},
            new double[]{1.0, 2.0, 3.0, 3.0, 1.0},
            0.15,
            0.75,
            0.0,
            0.12,
            null,
            true
    )
            .addModifiers(Modifier.replacementTable("ṛṝḷḹḍṭṅṇṣṃḥ", "ŗŕļĺđţńņşĕĭ"));
    public static final Finnegan FANTASY_NAME = GREEK_ROMANIZED.mix(RUSSIAN_ROMANIZED.mix(FRENCH.removeAccents().mix(JAPANESE_ROMANIZED, 0.5), 0.85), 0.925);
    public static final Finnegan FANCY_FANTASY_NAME = FANTASY_NAME.addAccents(0.47, 0.07);

    public String removeAccents(CharSequence str) {
        String alteredString = Normalizer.normalize(str, Form.NFD);
        return diacritics.matcher(alteredString)
                .replaceAll("")
                .replace('æ', 'a')
                .replace('œ', 'o')
                .replace('ø', 'o')
                .replace('Æ', 'A')
                .replace('Œ', 'O')
                .replace('Ø', 'O');
    }

    public Finnegan() {
        this(
                new String[]{
                        "a",
                        "a",
                        "a",
                        "a",
                        "o",
                        "o",
                        "o",
                        "e",
                        "e",
                        "e",
                        "e",
                        "e",
                        "i",
                        "i",
                        "i",
                        "i",
                        "u",
                        "a",
                        "a",
                        "a",
                        "a",
                        "o",
                        "o",
                        "o",
                        "e",
                        "e",
                        "e",
                        "e",
                        "e",
                        "i",
                        "i",
                        "i",
                        "i",
                        "u",
                        "a",
                        "a",
                        "a",
                        "o",
                        "o",
                        "e",
                        "e",
                        "e",
                        "i",
                        "i",
                        "i",
                        "u",
                        "a",
                        "a",
                        "a",
                        "o",
                        "o",
                        "e",
                        "e",
                        "e",
                        "i",
                        "i",
                        "i",
                        "u",
                        "au",
                        "ai",
                        "ai",
                        "ou",
                        "ea",
                        "ie",
                        "io",
                        "ei"
                },
                new String[]{"u", "u", "oa", "oo", "oo", "oo", "ee", "ee", "ee", "ee"},
                new String[]{
                        "b",
                        "bl",
                        "br",
                        "c",
                        "cl",
                        "cr",
                        "ch",
                        "d",
                        "dr",
                        "f",
                        "fl",
                        "fr",
                        "g",
                        "gl",
                        "gr",
                        "h",
                        "j",
                        "k",
                        "l",
                        "m",
                        "n",
                        "p",
                        "pl",
                        "pr",
                        "qu",
                        "r",
                        "s",
                        "sh",
                        "sk",
                        "st",
                        "sp",
                        "sl",
                        "sm",
                        "sn",
                        "t",
                        "tr",
                        "th",
                        "thr",
                        "v",
                        "w",
                        "y",
                        "z",
                        "b",
                        "bl",
                        "br",
                        "c",
                        "cl",
                        "cr",
                        "ch",
                        "d",
                        "dr",
                        "f",
                        "fl",
                        "fr",
                        "g",
                        "gr",
                        "h",
                        "j",
                        "k",
                        "l",
                        "m",
                        "n",
                        "p",
                        "pl",
                        "pr",
                        "r",
                        "s",
                        "sh",
                        "st",
                        "sp",
                        "sl",
                        "t",
                        "tr",
                        "th",
                        "w",
                        "y",
                        "b",
                        "br",
                        "c",
                        "ch",
                        "d",
                        "dr",
                        "f",
                        "g",
                        "h",
                        "j",
                        "l",
                        "m",
                        "n",
                        "p",
                        "r",
                        "s",
                        "sh",
                        "st",
                        "sl",
                        "t",
                        "tr",
                        "th",
                        "b",
                        "d",
                        "f",
                        "g",
                        "h",
                        "l",
                        "m",
                        "n",
                        "p",
                        "r",
                        "s",
                        "sh",
                        "t",
                        "th",
                        "b",
                        "d",
                        "f",
                        "g",
                        "h",
                        "l",
                        "m",
                        "n",
                        "p",
                        "r",
                        "s",
                        "sh",
                        "t",
                        "th",
                        "r",
                        "s",
                        "t",
                        "l",
                        "n",
                        "str",
                        "spr",
                        "spl",
                        "wr",
                        "kn",
                        "kn",
                        "gn"
                },
                new String[]{
                        "x",
                        "cst",
                        "bs",
                        "ff",
                        "lg",
                        "g",
                        "gs",
                        "ll",
                        "ltr",
                        "mb",
                        "mn",
                        "mm",
                        "ng",
                        "ng",
                        "ngl",
                        "nt",
                        "ns",
                        "nn",
                        "ps",
                        "mbl",
                        "mpr",
                        "pp",
                        "ppl",
                        "ppr",
                        "rr",
                        "rr",
                        "rr",
                        "rl",
                        "rtn",
                        "ngr",
                        "ss",
                        "sc",
                        "rst",
                        "tt",
                        "tt",
                        "ts",
                        "ltr",
                        "zz"
                },
                new String[]{
                        "b",
                        "rb",
                        "bb",
                        "c",
                        "rc",
                        "ld",
                        "d",
                        "ds",
                        "dd",
                        "f",
                        "ff",
                        "lf",
                        "rf",
                        "rg",
                        "gs",
                        "ch",
                        "lch",
                        "rch",
                        "tch",
                        "ck",
                        "ck",
                        "lk",
                        "rk",
                        "l",
                        "ll",
                        "lm",
                        "m",
                        "rm",
                        "mp",
                        "n",
                        "nk",
                        "nch",
                        "nd",
                        "ng",
                        "ng",
                        "nt",
                        "ns",
                        "lp",
                        "rp",
                        "p",
                        "r",
                        "rn",
                        "rts",
                        "s",
                        "s",
                        "s",
                        "s",
                        "ss",
                        "ss",
                        "st",
                        "ls",
                        "t",
                        "t",
                        "ts",
                        "w",
                        "wn",
                        "x",
                        "ly",
                        "lly",
                        "z",
                        "b",
                        "c",
                        "d",
                        "f",
                        "g",
                        "k",
                        "l",
                        "m",
                        "n",
                        "p",
                        "r",
                        "s",
                        "t",
                        "w"
                },
                new String[]{
                        "ate",
                        "ite",
                        "ism",
                        "ist",
                        "er",
                        "er",
                        "er",
                        "ed",
                        "ed",
                        "ed",
                        "es",
                        "es",
                        "ied",
                        "y",
                        "y",
                        "y",
                        "y",
                        "ate",
                        "ite",
                        "ism",
                        "ist",
                        "er",
                        "er",
                        "er",
                        "ed",
                        "ed",
                        "ed",
                        "es",
                        "es",
                        "ied",
                        "y",
                        "y",
                        "y",
                        "y",
                        "ate",
                        "ite",
                        "ism",
                        "ist",
                        "er",
                        "er",
                        "er",
                        "ed",
                        "ed",
                        "ed",
                        "es",
                        "es",
                        "ied",
                        "y",
                        "y",
                        "y",
                        "y",
                        "ay",
                        "ay",
                        "ey",
                        "oy",
                        "ay",
                        "ay",
                        "ey",
                        "oy",
                        "ough",
                        "aught",
                        "ant",
                        "ont",
                        "oe",
                        "ance",
                        "ell",
                        "eal",
                        "oa",
                        "urt",
                        "ut",
                        "iom",
                        "ion",
                        "ion",
                        "ision",
                        "ation",
                        "ation",
                        "ition",
                        "ough",
                        "aught",
                        "ant",
                        "ont",
                        "oe",
                        "ance",
                        "ell",
                        "eal",
                        "oa",
                        "urt",
                        "ut",
                        "iom",
                        "ion",
                        "ion",
                        "ision",
                        "ation",
                        "ation",
                        "ition",
                        "ily",
                        "ily",
                        "ily",
                        "adly",
                        "owly",
                        "oorly",
                        "ardly",
                        "iedly"
                },
                new String[0],
                new int[]{1, 2, 3, 4},
                new double[]{7.0, 8.0, 4.0, 1.0},
                0.22,
                0.1,
                0.0,
                0.25,
                englishSanityChecks,
                true
        );
    }

    public Finnegan(
            String[] openingVowels,
            String[] midVowels,
            String[] openingConsonants,
            String[] midConsonants,
            String[] closingConsonants,
            String[] closingSyllables,
            String[] vowelSplitters,
            int[] syllableLengths,
            double[] syllableFrequencies,
            double vowelStartFrequency,
            double vowelEndFrequency,
            double vowelSplitFrequency,
            double syllableEndFrequency
    ) {
        this(
                openingVowels,
                midVowels,
                openingConsonants,
                midConsonants,
                closingConsonants,
                closingSyllables,
                vowelSplitters,
                syllableLengths,
                syllableFrequencies,
                vowelStartFrequency,
                vowelEndFrequency,
                vowelSplitFrequency,
                syllableEndFrequency,
                englishSanityChecks,
                true
        );
    }

    public Finnegan(
            String[] openingVowels,
            String[] midVowels,
            String[] openingConsonants,
            String[] midConsonants,
            String[] closingConsonants,
            String[] closingSyllables,
            String[] vowelSplitters,
            int[] syllableLengths,
            double[] syllableFrequencies,
            double vowelStartFrequency,
            double vowelEndFrequency,
            double vowelSplitFrequency,
            double syllableEndFrequency,
            Pattern[] sane,
            boolean clean
    ) {
        this.rng = new RNG(
                FNV1a.hash64(new String[][]{openingVowels, midVowels, openingConsonants, midConsonants, closingConsonants, closingSyllables, vowelSplitters})
                        ^ FNV1a.hash64(syllableLengths)
                        ^ FNV1a.hash64(syllableFrequencies) << 31
                        ^ Double.doubleToLongBits(vowelStartFrequency + 19.0 * (vowelEndFrequency + 19.0 * (vowelSplitFrequency + 19.0 * syllableEndFrequency)))
        );
        this.openingVowels = openingVowels;
        this.midVowels = new String[openingVowels.length + midVowels.length];
        System.arraycopy(midVowels, 0, this.midVowels, 0, midVowels.length);
        System.arraycopy(openingVowels, 0, this.midVowels, midVowels.length, openingVowels.length);
        this.openingConsonants = openingConsonants;
        this.midConsonants = new String[midConsonants.length + closingConsonants.length];
        System.arraycopy(midConsonants, 0, this.midConsonants, 0, midConsonants.length);
        System.arraycopy(closingConsonants, 0, this.midConsonants, midConsonants.length, closingConsonants.length);
        this.closingConsonants = closingConsonants;
        this.vowelSplitters = vowelSplitters;
        this.closingSyllables = closingSyllables;
        this.syllableFrequencies = new LinkedHashMap<>(syllableLengths.length);

        for (int i = 0; i < syllableLengths.length && i < syllableFrequencies.length; i++) {
            this.syllableFrequencies.put(Integer.valueOf(syllableLengths[i]), Double.valueOf(syllableFrequencies[i]));
        }

        for (Double freq : this.syllableFrequencies.values()) {
            this.totalSyllableFrequency = this.totalSyllableFrequency + freq;
        }

        if (vowelStartFrequency > 1.0) {
            this.vowelStartFrequency = 1.0 / vowelStartFrequency;
        } else {
            this.vowelStartFrequency = vowelStartFrequency;
        }

        if (vowelEndFrequency > 1.0) {
            this.vowelEndFrequency = 1.0 / vowelEndFrequency;
        } else {
            this.vowelEndFrequency = vowelEndFrequency;
        }

        if (vowelSplitters.length == 0) {
            this.vowelSplitFrequency = 0.0;
        } else if (vowelSplitFrequency > 1.0) {
            this.vowelSplitFrequency = 1.0 / vowelSplitFrequency;
        } else {
            this.vowelSplitFrequency = vowelSplitFrequency;
        }

        if (closingSyllables.length == 0) {
            this.syllableEndFrequency = 0.0;
        } else if (syllableEndFrequency > 1.0) {
            this.syllableEndFrequency = 1.0 / syllableEndFrequency;
        } else {
            this.syllableEndFrequency = syllableEndFrequency;
        }

        this.clean = clean;
        this.sanityChecks = sane;
        this.modifiers = new ArrayList<>(16);
    }

    private Finnegan(
            String[] openingVowels,
            String[] midVowels,
            String[] openingConsonants,
            String[] midConsonants,
            String[] closingConsonants,
            String[] closingSyllables,
            String[] vowelSplitters,
            LinkedHashMap<Integer, Double> syllableFrequencies,
            double vowelStartFrequency,
            double vowelEndFrequency,
            double vowelSplitFrequency,
            double syllableEndFrequency,
            Pattern[] sanityChecks,
            boolean clean,
            RNG rng,
            Collection<Modifier> modifiers
    ) {
        this.openingVowels = copyStrings(openingVowels);
        this.midVowels = copyStrings(midVowels);
        this.openingConsonants = copyStrings(openingConsonants);
        this.midConsonants = copyStrings(midConsonants);
        this.closingConsonants = copyStrings(closingConsonants);
        this.closingSyllables = copyStrings(closingSyllables);
        this.vowelSplitters = copyStrings(vowelSplitters);
        this.syllableFrequencies = new LinkedHashMap<>(syllableFrequencies);
        this.vowelStartFrequency = vowelStartFrequency;
        this.vowelEndFrequency = vowelEndFrequency;
        this.vowelSplitFrequency = vowelSplitFrequency;
        this.syllableEndFrequency = syllableEndFrequency;

        for (Double freq : this.syllableFrequencies.values()) {
            this.totalSyllableFrequency = this.totalSyllableFrequency + freq;
        }

        if (sanityChecks == null) {
            this.sanityChecks = null;
        } else {
            this.sanityChecks = new Pattern[sanityChecks.length];
            System.arraycopy(sanityChecks, 0, this.sanityChecks, 0, sanityChecks.length);
        }

        this.clean = clean;
        this.rng = new RNG(rng.state);
        this.modifiers = new ArrayList<>(modifiers);
    }

    protected boolean checkAll(CharSequence testing, Pattern[] checks) {
        String fixed = this.removeAccents(testing);

        for (int i = 0; i < checks.length; i++) {
            if (checks[i].matcher(fixed).find()) {
                return false;
            }
        }

        return true;
    }

    public String word(boolean capitalize) {
        return this.word(this.rng.state, capitalize);
    }

    public String word(long seed, boolean capitalize) {
        this.rng.state = seed;

        while (true) {
            StringBuffer sb = new StringBuffer(20);
            double syllableChance = this.rng.nextDouble(this.totalSyllableFrequency);
            int syllables = 1;
            int i = 0;

            for (Entry<Integer, Double> kv : this.syllableFrequencies.entrySet()) {
                if (syllableChance < kv.getValue()) {
                    syllables = kv.getKey();
                    break;
                }

                syllableChance -= kv.getValue();
            }

            if (this.rng.nextDouble() < this.vowelStartFrequency) {
                sb.append(this.rng.getRandomElement(this.openingVowels));
                sb.append(this.rng.getRandomElement(this.midConsonants));
                i++;
            } else {
                sb.append(this.rng.getRandomElement(this.openingConsonants));
            }

            while (i < syllables - 1) {
                sb.append(this.rng.getRandomElement(this.midVowels));
                if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                    sb.append(this.rng.getRandomElement(this.vowelSplitters));
                    sb.append(this.rng.getRandomElement(this.midVowels));
                }

                sb.append(this.rng.getRandomElement(this.midConsonants));
                i++;
            }

            if (this.rng.nextDouble() < this.syllableEndFrequency) {
                String close = this.rng.getRandomElement(this.closingSyllables);
                if ((!close.contains("@1") || syllables != 1) && (!close.contains("@2") || syllables != 2) && (!close.contains("@3") || syllables != 3)) {
                    if (!close.contains("@")) {
                        sb.append(close);
                    } else if (this.rng.nextDouble() < this.vowelEndFrequency) {
                        sb.append(this.rng.getRandomElement(this.midVowels));
                        if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                            sb.append(this.rng.getRandomElement(this.vowelSplitters));
                            sb.append(this.rng.getRandomElement(this.midVowels));
                        }
                    }
                } else {
                    sb.append(close.replaceAll("@\\d", sb.toString()));
                }
            } else {
                sb.append(this.rng.getRandomElement(this.midVowels));
                if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                    sb.append(this.rng.getRandomElement(this.vowelSplitters));
                    sb.append(this.rng.getRandomElement(this.midVowels));
                }

                if (this.rng.nextDouble() >= this.vowelEndFrequency) {
                    sb.append(this.rng.getRandomElement(this.closingConsonants));
                    if (this.rng.nextDouble() < this.syllableEndFrequency) {
                        String close = this.rng.getRandomElement(this.closingSyllables);
                        if ((!close.contains("@1") || syllables != 1) && (!close.contains("@2") || syllables != 2) && (!close.contains("@3") || syllables != 3)
                        ) {
                            if (!close.contains("@")) {
                                sb.append(close);
                            }
                        } else {
                            sb.append(close.replaceAll("@\\d", sb.toString()));
                        }
                    }
                }
            }

            if (this.sanityChecks == null || this.checkAll(sb, this.sanityChecks)) {
                for (Modifier mod : this.modifiers) {
                    sb = mod.modify(this.rng, sb);
                }

                if (capitalize) {
                    sb.setCharAt(0, Character.toUpperCase(sb.charAt(0)));
                }

                if (!this.clean || this.checkAll(sb, vulgarChecks)) {
                    return sb.toString();
                }
            }
        }
    }

    public String word(boolean capitalize, int approxSyllables) {
        return this.word(this.rng.state, capitalize, approxSyllables);
    }

    public String word(long seed, boolean capitalize, int approxSyllables) {
        this.rng.setState(seed);
        if (approxSyllables <= 0) {
            String finished = this.rng.getRandomElement(this.openingVowels);
            return capitalize ? finished.substring(0, 1).toUpperCase() : finished.substring(0, 1);
        } else {
            while (true) {
                StringBuffer sb = new StringBuffer(20);
                int i = 0;
                if (this.rng.nextDouble() < this.vowelStartFrequency) {
                    sb.append(this.rng.getRandomElement(this.openingVowels));
                    sb.append(this.rng.getRandomElement(this.midConsonants));
                    i++;
                } else {
                    sb.append(this.rng.getRandomElement(this.openingConsonants));
                }

                while (i < approxSyllables - 1) {
                    sb.append(this.rng.getRandomElement(this.midVowels));
                    if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                        sb.append(this.rng.getRandomElement(this.vowelSplitters));
                        sb.append(this.rng.getRandomElement(this.midVowels));
                    }

                    sb.append(this.rng.getRandomElement(this.midConsonants));
                    i++;
                }

                if (this.rng.nextDouble() < this.syllableEndFrequency) {
                    String close = this.rng.getRandomElement(this.closingSyllables);
                    if ((!close.contains("@1") || approxSyllables != 1)
                            && (!close.contains("@2") || approxSyllables != 2)
                            && (!close.contains("@3") || approxSyllables != 3)) {
                        if (!close.contains("@")) {
                            sb.append(close);
                        } else if (this.rng.nextDouble() < this.vowelEndFrequency) {
                            sb.append(this.rng.getRandomElement(this.midVowels));
                            if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                                sb.append(this.rng.getRandomElement(this.vowelSplitters));
                                sb.append(this.rng.getRandomElement(this.midVowels));
                            }
                        }
                    } else {
                        sb.append(close.replaceAll("@\\d", sb.toString()));
                    }
                } else {
                    sb.append(this.rng.getRandomElement(this.midVowels));
                    if (this.rng.nextDouble() < this.vowelSplitFrequency) {
                        sb.append(this.rng.getRandomElement(this.vowelSplitters));
                        sb.append(this.rng.getRandomElement(this.midVowels));
                    }

                    if (this.rng.nextDouble() >= this.vowelEndFrequency) {
                        sb.append(this.rng.getRandomElement(this.closingConsonants));
                        if (this.rng.nextDouble() < this.syllableEndFrequency) {
                            String close = this.rng.getRandomElement(this.closingSyllables);
                            if ((!close.contains("@1") || approxSyllables != 1)
                                    && (!close.contains("@2") || approxSyllables != 2)
                                    && (!close.contains("@3") || approxSyllables != 3)) {
                                if (!close.contains("@")) {
                                    sb.append(close);
                                }
                            } else {
                                close = close.replaceAll("@\\d", sb.toString());
                                sb.append(close);
                            }
                        }
                    }
                }

                if (this.sanityChecks == null || this.checkAll(sb, this.sanityChecks)) {
                    for (Modifier mod : this.modifiers) {
                        sb = mod.modify(this.rng, sb);
                    }

                    if (capitalize) {
                        sb.setCharAt(0, Character.toUpperCase(sb.charAt(0)));
                    }

                    if (!this.clean || this.checkAll(sb, vulgarChecks)) {
                        return sb.toString();
                    }
                }
            }
        }
    }

    public String sentence(int minWords, int maxWords) {
        return this.sentence(this.rng.state, minWords, maxWords, new String[]{",", ",", ",", ";"}, new String[]{".", ".", ".", "!", "?", "..."}, 0.2);
    }

    public String sentence(int minWords, int maxWords, String[] midPunctuation, String[] endPunctuation, double midPunctuationFrequency) {
        return this.sentence(this.rng.state, minWords, maxWords, midPunctuation, endPunctuation, midPunctuationFrequency);
    }

    public String sentence(long seed, int minWords, int maxWords, String[] midPunctuation, String[] endPunctuation, double midPunctuationFrequency) {
        this.rng.state = seed;
        if (minWords < 1) {
            minWords = 1;
        }

        if (minWords > maxWords) {
            maxWords = minWords;
        }

        if (midPunctuationFrequency > 1.0) {
            midPunctuationFrequency = 1.0 / midPunctuationFrequency;
        }

        StringBuilder sb = new StringBuilder(12 * maxWords);
        sb.append(this.word(true));

        for (int i = 1; i < minWords; i++) {
            if (this.rng.nextDouble() < midPunctuationFrequency) {
                sb.append(this.rng.getRandomElement(midPunctuation));
            }

            sb.append(' ');
            sb.append(this.word(false));
        }

        for (int i = minWords; i < maxWords && this.rng.nextInt(2 * maxWords) > i; i++) {
            if (this.rng.nextDouble() < midPunctuationFrequency) {
                sb.append(this.rng.getRandomElement(midPunctuation));
            }

            sb.append(' ');
            sb.append(this.word(false));
        }

        sb.append(this.rng.getRandomElement(endPunctuation));
        return sb.toString();
    }

    public String sentence(int minWords, int maxWords, String[] midPunctuation, String[] endPunctuation, double midPunctuationFrequency, int maxChars) {
        return this.sentence(this.rng.state, minWords, maxWords, midPunctuation, endPunctuation, midPunctuationFrequency, maxChars);
    }

    public String sentence(
            long seed, int minWords, int maxWords, String[] midPunctuation, String[] endPunctuation, double midPunctuationFrequency, int maxChars
    ) {
        this.rng.state = seed;
        if (minWords < 1) {
            minWords = 1;
        }

        if (minWords > maxWords) {
            maxWords = minWords;
        }

        if (midPunctuationFrequency > 1.0) {
            midPunctuationFrequency = 1.0 / midPunctuationFrequency;
        }

        if (maxChars < 4) {
            return "!";
        } else {
            if (maxChars <= 5 * minWords) {
                minWords = 1;
                maxWords = 1;
            }

            int frustration = 0;
            StringBuilder sb = new StringBuilder(maxChars);

            String next;
            for (next = this.word(true); next.length() >= maxChars - 1 && frustration < 50; frustration++) {
                next = this.word(true);
            }

            if (frustration >= 50) {
                return "!";
            } else {
                sb.append(next);

                for (int i = 1; i < minWords && frustration < 50 && sb.length() < maxChars - 7; i++) {
                    if (this.rng.nextDouble() < midPunctuationFrequency && sb.length() < maxChars - 3) {
                        sb.append(this.rng.getRandomElement(midPunctuation));
                    }

                    for (next = this.word(false); sb.length() + next.length() >= maxChars - 2 && frustration < 50; frustration++) {
                        next = this.word(false);
                    }

                    if (frustration >= 50) {
                        break;
                    }

                    sb.append(' ');
                    sb.append(next);
                }

                for (int i = minWords; i < maxWords && sb.length() < maxChars - 7 && this.rng.nextInt(2 * maxWords) > i && frustration < 50; i++) {
                    if (this.rng.nextDouble() < midPunctuationFrequency && sb.length() < maxChars - 3) {
                        sb.append(this.rng.getRandomElement(midPunctuation));
                    }

                    for (next = this.word(false); sb.length() + next.length() >= maxChars - 2 && frustration < 50; frustration++) {
                        next = this.word(false);
                    }

                    if (frustration >= 50) {
                        break;
                    }

                    sb.append(' ');
                    sb.append(next);
                }

                next = this.rng.getRandomElement(endPunctuation);
                if (sb.length() + next.length() >= maxChars) {
                    next = ".";
                }

                sb.append(next);
                return sb.length() > maxChars ? "!" : sb.toString();
            }
        }
    }

    protected String[] merge1000(String[] me, String[] other, double otherInfluence) {
        if (other.length <= 0 && me.length <= 0) {
            return new String[0];
        } else {
            String[] ret = new String[1000];
            int otherCount = (int) (1000.0 * otherInfluence);
            int idx = 0;
            if (other.length > 0) {
                String[] tmp = this.rng.shuffle(other);

                for (idx = 0; idx < otherCount; idx++) {
                    ret[idx] = tmp[idx % tmp.length];
                }
            }

            if (me.length > 0) {
                for (String[] tmp = this.rng.shuffle(me); idx < 1000; idx++) {
                    ret[idx] = tmp[idx % tmp.length];
                }
            } else {
                while (idx < 1000) {
                    ret[idx] = other[idx % other.length];
                    idx++;
                }
            }

            return ret;
        }
    }

    protected String[] accentVowels(String[] me, double influence) {
        String[] ret = new String[1000];
        int otherCount = (int) (1000.0 * influence);
        int idx = 0;
        if (me.length <= 0) {
            return new String[0];
        } else {
            String[] tmp = this.rng.shuffle(me);

            for (idx = 0; idx < otherCount; idx++) {
                ret[idx] = tmp[idx % tmp.length]
                        .replace('a', accentedVowels[0][this.rng.nextInt(accentedVowels[0].length)])
                        .replace('e', accentedVowels[1][this.rng.nextInt(accentedVowels[1].length)])
                        .replace('i', accentedVowels[2][this.rng.nextInt(accentedVowels[2].length)])
                        .replace('o', accentedVowels[3][this.rng.nextInt(accentedVowels[3].length)])
                        .replace('u', accentedVowels[4][this.rng.nextInt(accentedVowels[4].length)]);
                Matcher matcher = repeats.matcher(ret[idx]);
                if (matcher.find()) {
                    ret[idx] = matcher.replaceAll(this.rng.getRandomElement(me));
                }
            }

            while (idx < 1000) {
                ret[idx] = tmp[idx % tmp.length];
                idx++;
            }

            return ret;
        }
    }

    protected String[] accentConsonants(String[] me, double influence) {
        String[] ret = new String[1000];
        int otherCount = (int) (1000.0 * influence);
        int idx = 0;
        if (me.length <= 0) {
            return new String[0];
        } else {
            String[] tmp = this.rng.shuffle(me);

            for (idx = 0; idx < otherCount; idx++) {
                ret[idx] = tmp[idx % tmp.length]
                        .replace('c', accentedConsonants[1][this.rng.nextInt(accentedConsonants[1].length)])
                        .replace('d', accentedConsonants[2][this.rng.nextInt(accentedConsonants[2].length)])
                        .replace('f', accentedConsonants[3][this.rng.nextInt(accentedConsonants[3].length)])
                        .replace('g', accentedConsonants[4][this.rng.nextInt(accentedConsonants[4].length)])
                        .replace('h', accentedConsonants[5][this.rng.nextInt(accentedConsonants[5].length)])
                        .replace('j', accentedConsonants[6][this.rng.nextInt(accentedConsonants[6].length)])
                        .replace('k', accentedConsonants[7][this.rng.nextInt(accentedConsonants[7].length)])
                        .replace('l', accentedConsonants[8][this.rng.nextInt(accentedConsonants[8].length)])
                        .replace('n', accentedConsonants[10][this.rng.nextInt(accentedConsonants[10].length)])
                        .replace('r', accentedConsonants[13][this.rng.nextInt(accentedConsonants[13].length)])
                        .replace('s', accentedConsonants[14][this.rng.nextInt(accentedConsonants[14].length)])
                        .replace('t', accentedConsonants[15][this.rng.nextInt(accentedConsonants[15].length)])
                        .replace('w', accentedConsonants[17][this.rng.nextInt(accentedConsonants[17].length)])
                        .replace('y', accentedConsonants[19][this.rng.nextInt(accentedConsonants[19].length)])
                        .replace('z', accentedConsonants[20][this.rng.nextInt(accentedConsonants[20].length)]);
                Matcher matcher = repeats.matcher(ret[idx]);
                if (matcher.find()) {
                    ret[idx] = matcher.replaceAll(this.rng.getRandomElement(me));
                }
            }

            while (idx < 1000) {
                ret[idx] = tmp[idx % tmp.length];
                idx++;
            }

            return ret;
        }
    }

    protected String[] accentBoth(String[] me, double vowelInfluence, double consonantInfluence) {
        String[] ret = new String[1000];
        int idx = 0;
        if (me.length <= 0) {
            return new String[0];
        } else {
            String[] tmp = this.rng.shuffle(me);

            for (int var12 = 0; var12 < 1000; var12++) {
                boolean subVowel = this.rng.nextDouble() < vowelInfluence;
                boolean subCon = this.rng.nextDouble() < consonantInfluence;
                if (subVowel && subCon) {
                    ret[var12] = tmp[var12 % tmp.length]
                            .replace('a', accentedVowels[0][this.rng.nextInt(accentedVowels[0].length)])
                            .replace('e', accentedVowels[1][this.rng.nextInt(accentedVowels[1].length)])
                            .replace('i', accentedVowels[2][this.rng.nextInt(accentedVowels[2].length)])
                            .replace('o', accentedVowels[3][this.rng.nextInt(accentedVowels[3].length)])
                            .replace('u', accentedVowels[4][this.rng.nextInt(accentedVowels[4].length)])
                            .replace('c', accentedConsonants[1][this.rng.nextInt(accentedConsonants[1].length)])
                            .replace('d', accentedConsonants[2][this.rng.nextInt(accentedConsonants[2].length)])
                            .replace('f', accentedConsonants[3][this.rng.nextInt(accentedConsonants[3].length)])
                            .replace('g', accentedConsonants[4][this.rng.nextInt(accentedConsonants[4].length)])
                            .replace('h', accentedConsonants[5][this.rng.nextInt(accentedConsonants[5].length)])
                            .replace('j', accentedConsonants[6][this.rng.nextInt(accentedConsonants[6].length)])
                            .replace('k', accentedConsonants[7][this.rng.nextInt(accentedConsonants[7].length)])
                            .replace('l', accentedConsonants[8][this.rng.nextInt(accentedConsonants[8].length)])
                            .replace('n', accentedConsonants[10][this.rng.nextInt(accentedConsonants[10].length)])
                            .replace('r', accentedConsonants[13][this.rng.nextInt(accentedConsonants[13].length)])
                            .replace('s', accentedConsonants[14][this.rng.nextInt(accentedConsonants[14].length)])
                            .replace('t', accentedConsonants[15][this.rng.nextInt(accentedConsonants[15].length)])
                            .replace('w', accentedConsonants[17][this.rng.nextInt(accentedConsonants[17].length)])
                            .replace('y', accentedConsonants[19][this.rng.nextInt(accentedConsonants[19].length)])
                            .replace('z', accentedConsonants[20][this.rng.nextInt(accentedConsonants[20].length)]);
                    Matcher matcher = repeats.matcher(ret[var12]);
                    if (matcher.find()) {
                        ret[var12] = matcher.replaceAll(this.rng.getRandomElement(me));
                    }
                } else if (subVowel) {
                    ret[var12] = tmp[var12 % tmp.length]
                            .replace('a', accentedVowels[0][this.rng.nextInt(accentedVowels[0].length)])
                            .replace('e', accentedVowels[1][this.rng.nextInt(accentedVowels[1].length)])
                            .replace('i', accentedVowels[2][this.rng.nextInt(accentedVowels[2].length)])
                            .replace('o', accentedVowels[3][this.rng.nextInt(accentedVowels[3].length)])
                            .replace('u', accentedVowels[4][this.rng.nextInt(accentedVowels[4].length)]);
                    Matcher matcher = repeats.matcher(ret[var12]);
                    if (matcher.find()) {
                        ret[var12] = matcher.replaceAll(this.rng.getRandomElement(me));
                    }
                } else if (subCon) {
                    ret[var12] = tmp[var12 % tmp.length]
                            .replace('c', accentedConsonants[1][this.rng.nextInt(accentedConsonants[1].length)])
                            .replace('d', accentedConsonants[2][this.rng.nextInt(accentedConsonants[2].length)])
                            .replace('f', accentedConsonants[3][this.rng.nextInt(accentedConsonants[3].length)])
                            .replace('g', accentedConsonants[4][this.rng.nextInt(accentedConsonants[4].length)])
                            .replace('h', accentedConsonants[5][this.rng.nextInt(accentedConsonants[5].length)])
                            .replace('j', accentedConsonants[6][this.rng.nextInt(accentedConsonants[6].length)])
                            .replace('k', accentedConsonants[7][this.rng.nextInt(accentedConsonants[7].length)])
                            .replace('l', accentedConsonants[8][this.rng.nextInt(accentedConsonants[8].length)])
                            .replace('n', accentedConsonants[10][this.rng.nextInt(accentedConsonants[10].length)])
                            .replace('r', accentedConsonants[13][this.rng.nextInt(accentedConsonants[13].length)])
                            .replace('s', accentedConsonants[14][this.rng.nextInt(accentedConsonants[14].length)])
                            .replace('t', accentedConsonants[15][this.rng.nextInt(accentedConsonants[15].length)])
                            .replace('w', accentedConsonants[17][this.rng.nextInt(accentedConsonants[17].length)])
                            .replace('y', accentedConsonants[19][this.rng.nextInt(accentedConsonants[19].length)])
                            .replace('z', accentedConsonants[20][this.rng.nextInt(accentedConsonants[20].length)]);
                    Matcher matcher = repeats.matcher(ret[var12]);
                    if (matcher.find()) {
                        ret[var12] = matcher.replaceAll(this.rng.getRandomElement(me));
                    }
                } else {
                    ret[var12] = tmp[var12 % tmp.length];
                }
            }

            return ret;
        }
    }

    public Finnegan mix(Finnegan other, double otherInfluence) {
        otherInfluence = Math.max(0.0, Math.min(otherInfluence, 1.0));
        double myInfluence = 1.0 - otherInfluence;
        long oldState = this.rng.state;
        this.rng.state = (long) this.hashCode() & 4294967295L | ((long) other.hashCode() & 4294967295L) << 32 ^ Double.doubleToLongBits(otherInfluence);
        String[] ov = this.merge1000(this.openingVowels, other.openingVowels, otherInfluence);
        String[] mv = this.merge1000(this.midVowels, other.midVowels, otherInfluence);
        String[] oc = this.merge1000(this.openingConsonants, other.openingConsonants, otherInfluence);
        String[] mc = this.merge1000(this.midConsonants, other.midConsonants, otherInfluence);
        String[] cc = this.merge1000(this.closingConsonants, other.closingConsonants, otherInfluence);
        String[] cs = this.merge1000(this.closingSyllables, other.closingSyllables, otherInfluence);
        String[] splitters = this.merge1000(this.vowelSplitters, other.vowelSplitters, otherInfluence);
        LinkedHashMap<Integer, Double> freqs = new LinkedHashMap<>(this.syllableFrequencies);

        for (Entry<Integer, Double> kv : other.syllableFrequencies.entrySet()) {
            if (freqs.containsKey(kv.getKey())) {
                freqs.put(kv.getKey(), Double.valueOf(kv.getValue() + freqs.get(kv.getKey())));
            } else {
                freqs.put(kv.getKey(), kv.getValue());
            }
        }

        List<Modifier> mods = new ArrayList<>(
                (int) (Math.ceil((double) this.modifiers.size() * myInfluence) + Math.ceil((double) other.modifiers.size() * otherInfluence))
        );
        mods.addAll(this.rng.randomPortion(this.modifiers, (int) Math.ceil((double) this.modifiers.size() * myInfluence)));
        mods.addAll(this.rng.randomPortion(other.modifiers, (int) Math.ceil((double) other.modifiers.size() * otherInfluence)));
        Finnegan finished = new Finnegan(
                ov,
                mv,
                oc,
                mc,
                cc,
                cs,
                splitters,
                freqs,
                this.vowelStartFrequency * myInfluence + other.vowelStartFrequency * otherInfluence,
                this.vowelEndFrequency * myInfluence + other.vowelEndFrequency * otherInfluence,
                this.vowelSplitFrequency * myInfluence + other.vowelSplitFrequency * otherInfluence,
                this.syllableEndFrequency * myInfluence + other.syllableEndFrequency * otherInfluence,
                this.sanityChecks == null ? other.sanityChecks : this.sanityChecks,
                true,
                new RNG(this.rng.state),
                mods
        );
        this.rng.state = oldState;
        return finished;
    }

    public Finnegan addAccents(double vowelInfluence, double consonantInfluence) {
        vowelInfluence = Math.max(0.0, Math.min(vowelInfluence, 1.0));
        consonantInfluence = Math.max(0.0, Math.min(consonantInfluence, 1.0));
        long oldState = this.rng.state;
        this.rng.state = (long) this.hashCode() & 4294967295L
                ^ (Double.doubleToLongBits(vowelInfluence) & 4294967295L | Double.doubleToLongBits(consonantInfluence) << 32);
        String[] ov = this.accentVowels(this.openingVowels, vowelInfluence);
        String[] mv = this.accentVowels(this.midVowels, vowelInfluence);
        String[] oc = this.accentConsonants(this.openingConsonants, consonantInfluence);
        String[] mc = this.accentConsonants(this.midConsonants, consonantInfluence);
        String[] cc = this.accentConsonants(this.closingConsonants, consonantInfluence);
        String[] cs = this.accentBoth(this.closingSyllables, vowelInfluence, consonantInfluence);
        int[] lens = new int[this.syllableFrequencies.size()];
        double[] odds = new double[this.syllableFrequencies.size()];
        int i = 0;

        for (Entry<Integer, Double> kv : this.syllableFrequencies.entrySet()) {
            lens[i] = kv.getKey();
            odds[i++] = kv.getValue();
        }

        Finnegan finished = new Finnegan(
                ov,
                mv,
                oc,
                mc,
                cc,
                cs,
                this.vowelSplitters,
                lens,
                odds,
                this.vowelStartFrequency,
                this.vowelEndFrequency,
                this.vowelSplitFrequency,
                this.syllableEndFrequency,
                this.sanityChecks,
                this.clean
        );
        finished.rng.state = this.rng.state;
        this.rng.state = oldState;
        return finished;
    }

    static String[] copyStrings(String[] start) {
        String[] next = new String[start.length];
        System.arraycopy(start, 0, next, 0, start.length);
        return next;
    }

    public Finnegan removeAccents() {
        String[] ov = (String[]) this.openingVowels.clone();
        String[] mv = (String[]) this.midVowels.clone();
        String[] oc = (String[]) this.openingConsonants.clone();
        String[] mc = (String[]) this.midConsonants.clone();
        String[] cc = (String[]) this.closingConsonants.clone();
        String[] cs = (String[]) this.closingSyllables.clone();

        for (int i = 0; i < ov.length; i++) {
            ov[i] = this.removeAccents(this.openingVowels[i]);
        }

        for (int i = 0; i < mv.length; i++) {
            mv[i] = this.removeAccents(this.midVowels[i]);
        }

        for (int i = 0; i < oc.length; i++) {
            oc[i] = this.removeAccents(this.openingConsonants[i]);
        }

        for (int i = 0; i < mc.length; i++) {
            mc[i] = this.removeAccents(this.midConsonants[i]);
        }

        for (int i = 0; i < cc.length; i++) {
            cc[i] = this.removeAccents(this.closingConsonants[i]);
        }

        for (int i = 0; i < cs.length; i++) {
            cs[i] = this.removeAccents(this.closingSyllables[i]);
        }

        int[] lens = new int[this.syllableFrequencies.size()];
        double[] odds = new double[this.syllableFrequencies.size()];
        int i = 0;

        for (Entry<Integer, Double> kv : this.syllableFrequencies.entrySet()) {
            lens[i] = kv.getKey();
            odds[i++] = kv.getValue();
        }

        Finnegan finished = new Finnegan(
                ov,
                mv,
                oc,
                mc,
                cc,
                cs,
                this.vowelSplitters,
                lens,
                odds,
                this.vowelStartFrequency,
                this.vowelEndFrequency,
                this.vowelSplitFrequency,
                this.syllableEndFrequency,
                this.sanityChecks,
                this.clean
        );
        finished.rng.state = this.rng.state;
        return finished;
    }

    public Finnegan addModifiers(Collection<Modifier> mods) {
        Finnegan next = this.copy();
        next.modifiers.addAll(mods);
        return next;
    }

    public Finnegan addModifiers(Modifier... mods) {
        Finnegan next = this.copy();
        Collections.addAll(next.modifiers, mods);
        return next;
    }

    public Finnegan removeModifiers() {
        Finnegan next = this.copy();
        next.modifiers.clear();
        return next;
    }

    public static Modifier modifier(String pattern, String replacement) {
        return new Modifier(pattern, replacement);
    }

    public static Modifier modifier(String pattern, String replacement, double chance) {
        return new Modifier(pattern, replacement, chance);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            Finnegan finnegan = (Finnegan) o;
            if (this.clean != finnegan.clean) {
                return false;
            } else if (Double.compare(finnegan.totalSyllableFrequency, this.totalSyllableFrequency) != 0) {
                return false;
            } else if (Double.compare(finnegan.vowelStartFrequency, this.vowelStartFrequency) != 0) {
                return false;
            } else if (Double.compare(finnegan.vowelEndFrequency, this.vowelEndFrequency) != 0) {
                return false;
            } else if (Double.compare(finnegan.vowelSplitFrequency, this.vowelSplitFrequency) != 0) {
                return false;
            } else if (Double.compare(finnegan.syllableEndFrequency, this.syllableEndFrequency) != 0) {
                return false;
            } else if (!Arrays.equals((Object[]) this.openingVowels, (Object[]) finnegan.openingVowels)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.midVowels, (Object[]) finnegan.midVowels)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.openingConsonants, (Object[]) finnegan.openingConsonants)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.midConsonants, (Object[]) finnegan.midConsonants)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.closingConsonants, (Object[]) finnegan.closingConsonants)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.vowelSplitters, (Object[]) finnegan.vowelSplitters)) {
                return false;
            } else if (!Arrays.equals((Object[]) this.closingSyllables, (Object[]) finnegan.closingSyllables)) {
                return false;
            } else if (this.syllableFrequencies != null ? this.syllableFrequencies.equals(finnegan.syllableFrequencies) : finnegan.syllableFrequencies == null) {
                if (!Arrays.equals((Object[]) this.sanityChecks, (Object[]) finnegan.sanityChecks)) {
                    return false;
                } else if (this.rng != null ? this.rng.equals(finnegan.rng) : finnegan.rng == null) {
                    return this.modifiers != null ? this.modifiers.equals(finnegan.modifiers) : finnegan.modifiers == null;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        long result = FNV1a.hash64(this.openingVowels);
        result = 31L * result + FNV1a.hash64(this.midVowels);
        result = 31L * result + FNV1a.hash64(this.openingConsonants);
        result = 31L * result + FNV1a.hash64(this.midConsonants);
        result = 31L * result + FNV1a.hash64(this.closingConsonants);
        result = 31L * result + FNV1a.hash64(this.vowelSplitters);
        result = 31L * result + FNV1a.hash64(this.closingSyllables);
        result = 31L * result + (long) (this.clean ? 1 : 0);
        result = 31L * result + (long) (this.syllableFrequencies != null ? this.syllableFrequencies.hashCode() : 0);
        long temp = Double.doubleToLongBits(this.totalSyllableFrequency);
        result = 31L * result + (long) ((int) (temp ^ temp >>> 32));
        temp = Double.doubleToLongBits(this.vowelStartFrequency);
        result = 31L * result + (long) ((int) (temp ^ temp >>> 32));
        temp = Double.doubleToLongBits(this.vowelEndFrequency);
        result = 31L * result + (long) ((int) (temp ^ temp >>> 32));
        temp = Double.doubleToLongBits(this.vowelSplitFrequency);
        result = 31L * result + (long) ((int) (temp ^ temp >>> 32));
        temp = Double.doubleToLongBits(this.syllableEndFrequency);
        result = 31L * result + (long) ((int) (temp ^ temp >>> 32));
        result = 31L * result + (long) (this.sanityChecks != null ? this.sanityChecks.length + 1 : 0);
        result = 31L * result + (long) (this.modifiers != null ? this.modifiers.hashCode() : 0);
        result = 31L * result + (long) (this.rng != null ? this.rng.hashCode() : 0);
        return (int) result;
    }

    @Override
    public String toString() {
        return "Finnegan{openingVowels="
                + Arrays.toString((Object[]) this.openingVowels)
                + ", midVowels="
                + Arrays.toString((Object[]) this.midVowels)
                + ", openingConsonants="
                + Arrays.toString((Object[]) this.openingConsonants)
                + ", midConsonants="
                + Arrays.toString((Object[]) this.midConsonants)
                + ", closingConsonants="
                + Arrays.toString((Object[]) this.closingConsonants)
                + ", vowelSplitters="
                + Arrays.toString((Object[]) this.vowelSplitters)
                + ", closingSyllables="
                + Arrays.toString((Object[]) this.closingSyllables)
                + ", syllableFrequencies="
                + this.syllableFrequencies
                + ", totalSyllableFrequency="
                + this.totalSyllableFrequency
                + ", vowelStartFrequency="
                + this.vowelStartFrequency
                + ", vowelEndFrequency="
                + this.vowelEndFrequency
                + ", vowelSplitFrequency="
                + this.vowelSplitFrequency
                + ", syllableEndFrequency="
                + this.syllableEndFrequency
                + ", sanityChecks="
                + Arrays.toString((Object[]) this.sanityChecks)
                + ", clean="
                + this.clean
                + ", modifiers="
                + this.modifiers
                + ", RNG="
                + this.rng
                + "}";
    }

    public long getSeed() {
        return this.rng.state;
    }

    public void setSeed(long seed) {
        this.rng.state = seed;
    }

    public Finnegan copy() {
        return new Finnegan(
                this.openingVowels,
                this.midVowels,
                this.openingConsonants,
                this.midConsonants,
                this.closingConsonants,
                this.closingSyllables,
                this.vowelSplitters,
                this.syllableFrequencies,
                this.vowelStartFrequency,
                this.vowelEndFrequency,
                this.vowelSplitFrequency,
                this.syllableEndFrequency,
                this.sanityChecks,
                this.clean,
                this.rng,
                this.modifiers
        );
    }

    public static class Alteration implements Serializable {
        private static final long serialVersionUID = -2138854697837563188L;
        public Pattern pattern;
        public String replacer;
        public double chance;

        public Alteration() {
            this("[sśŝşšș]+h?", "th");
        }

        public Alteration(String pattern, String replacement) {
            this.pattern = Pattern.compile(pattern);
            this.replacer = replacement;
            this.chance = 1.0;
        }

        public Alteration(String pattern, String replacement, double chance) {
            this.pattern = Pattern.compile(pattern);
            this.replacer = replacement;
            this.chance = chance;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            } else if (o != null && this.getClass() == o.getClass()) {
                Alteration that = (Alteration) o;
                return Double.compare(that.chance, this.chance) != 0 ? false : this.replacer.equals(that.replacer);
            } else {
                return false;
            }
        }

        @Override
        public int hashCode() {
            long result = FNV1a.hash64(this.replacer);
            result = 31L * result + (long) this.pattern.hashCode();
            long temp = Double.doubleToLongBits(this.chance);
            result = 31L * result + (temp ^ temp >>> 32);
            return (int) result;
        }

        @Override
        public String toString() {
            return "Alteration{pattern=" + this.pattern + "replacer=" + this.replacer + ", chance=" + this.chance + "}";
        }
    }

    public static class Modifier implements Serializable {
        private static final long serialVersionUID = 1734863678490422371L;
        public final Alteration[] alterations;
        public static final Modifier LISP = new Modifier("[sśŝşšș]+h?", "th");
        public static final Modifier HISS = new Modifier("(.)([sśŝşšșzźżž])+", "$1$2$2$2");
        public static final Modifier STUTTER = new Modifier(
                new Alteration("^([^aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųyýÿŷỳαοειυаеёийъыэюяоу]+)", "$1-$1", 0.2),
                new Alteration("^([aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųαοειυаеёийъыэюяоу]+)", "$1-$1", 0.2)
        );
        public static final Modifier DOUBLE_VOWELS = new Modifier(
                "([aàáâãäåæāăąǻǽeèéêëēĕėęěòóôõöøōŏőœǿ])([^aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųyýÿŷỳ]|$)", "$1$1$2", 0.4
        );
        public static final Modifier DOUBLE_CONSONANTS = new Modifier(
                "([aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųyýÿŷỳαοειυаеёийъыэюяоу]|^)([^aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųyýÿŷỳαοειυаеёийъыэюяоуqwhjx])([aàáâãäåæāăąǻǽeèéêëēĕėęěiìíîïĩīĭįıoòóôõöøōŏőœǿuùúûüũūŭůűųyýÿŷỳαοειυаеёийъыэюяоу]|$)",
                "$1$2$2$3",
                0.5
        );
        public static final Modifier NO_DOUBLES = new Modifier("(.)\\1", "$1");

        public Modifier() {
            this("sh?", "th");
        }

        public Modifier(String pattern, String replacement) {
            this.alterations = new Alteration[]{new Alteration(pattern, replacement)};
        }

        public Modifier(String pattern, String replacement, double chance) {
            this.alterations = new Alteration[]{new Alteration(pattern, replacement, chance)};
        }

        public Modifier(Alteration... alts) {
            this.alterations = alts == null ? new Alteration[0] : alts;
        }

        public StringBuffer modify(RNG rng, StringBuffer sb) {
            for (Alteration alt : this.alterations) {
                Matcher m = alt.pattern.matcher(sb);
                StringBuffer sb2 = new StringBuffer();

                while (m.find()) {
                    if (rng.nextDouble() < alt.chance) {
                        m.appendReplacement(sb2, alt.replacer);
                    } else {
                        m.appendReplacement(sb2, m.group());
                    }
                }

                m.appendTail(sb2);
                sb = sb2;
            }

            return sb;
        }

        public static Modifier replacementTable(String initial, String change) {
            Alteration[] alts = new Alteration[Math.min(initial.length(), change.length())];

            for (int i = 0; i < alts.length; i++) {
                alts[i] = new Alteration("\\Q" + initial.charAt(i) + "\\E", change.substring(i, i + 1));
            }

            return new Modifier(alts);
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            } else if (o != null && this.getClass() == o.getClass()) {
                Modifier modifier = (Modifier) o;
                return Arrays.equals((Object[]) this.alterations, (Object[]) modifier.alterations);
            } else {
                return false;
            }
        }

        @Override
        public int hashCode() {
            return Arrays.hashCode((Object[]) this.alterations);
        }

        @Override
        public String toString() {
            return "Modifier{alterations=" + Arrays.toString((Object[]) this.alterations) + "}";
        }
    }

    public class RNG implements Serializable {
        private static final long serialVersionUID = 4378460257281186371L;
        private static final long DOUBLE_MASK = 9007199254740991L;
        private static final double NORM_53 = 1.110223E-16F;
        public long state;

        public RNG() {
            this.state = Double.doubleToLongBits(Math.random());
        }

        public RNG(long seed) {
            this.state = seed;
        }

        public long nextLong() {
            long z = this.state += -7046029254386353131L;
            z = (z ^ z >>> 30) * -4658895280553007687L;
            z = (z ^ z >>> 27) * -7723592293110705685L;
            return z ^ z >>> 31;
        }

        public int nextInt() {
            return (int) this.nextLong();
        }

        public int nextInt(int n) {
            if (n <= 0) {
                throw new IllegalArgumentException();
            } else {
                int bits = this.nextInt() >>> 1;
                return bits % n;
            }
        }

        public int nextInt(int lower, int upper) {
            if (upper - lower <= 0) {
                throw new IllegalArgumentException();
            } else {
                return lower + this.nextInt(upper - lower);
            }
        }

        public long nextLong(long n) {
            if (n <= 0L) {
                throw new IllegalArgumentException();
            } else {
                long bits = this.nextLong() >>> 1;
                return bits % n;
            }
        }

        public long nextLong(long lower, long upper) {
            if (upper - lower <= 0L) {
                throw new IllegalArgumentException();
            } else {
                return lower + this.nextLong(upper - lower);
            }
        }

        public double nextDouble() {
            return (double) (this.nextLong() & 9007199254740991L) * 1.110223E-16F;
        }

        public double nextDouble(double outer) {
            return this.nextDouble() * outer;
        }

        public <T> T getRandomElement(T[] array) {
            return (T) (array.length < 1 ? null : array[this.nextInt(array.length)]);
        }

        public <T> T[] shuffle(T[] elements) {
            Object[] array = (Object[]) elements.clone();
            int n = array.length;

            for (int i = 0; i < n; i++) {
                int r = i + this.nextInt(n - i);
                T t = (T) array[r];
                array[r] = array[i];
                array[i] = t;
            }

            return (T[]) array;
        }

        public <T> ArrayList<T> shuffle(List<T> elements) {
            ArrayList<T> al = new ArrayList<>(elements);
            int n = al.size();

            for (int i = 0; i < n; i++) {
                Collections.swap(al, i + this.nextInt(n - i), i);
            }

            return al;
        }

        public <T> List<T> randomPortion(List<T> data, int count) {
            return this.<T>shuffle(data).subList(0, Math.min(count, data.size()));
        }

        public long getState() {
            return this.state;
        }

        public void setState(long state) {
            this.state = state;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            } else if (o != null && this.getClass() == o.getClass()) {
                RNG rng = (RNG) o;
                return this.state == rng.state;
            } else {
                return false;
            }
        }

        @Override
        public int hashCode() {
            return (int) (this.state ^ this.state >>> 32);
        }

        @Override
        public String toString() {
            return "RNG{state=" + this.state + "}";
        }
    }
}
