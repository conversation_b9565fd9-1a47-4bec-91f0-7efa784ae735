
package com.dc.summer.registry.driver;

import com.dc.summer.registry.center.Global;
import com.dc.summer.Log;
import org.eclipse.core.runtime.IConfigurationElement;
import com.dc.code.Nullable;
import com.dc.summer.model.connection.DBPDriverLibrary;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.utils.ArrayUtils;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * DriverLibraryRemote
 */
public class DriverLibraryRemote extends DriverLibraryLocal
{
    private static final Log log = Log.getLog(DriverLibraryRemote.class);
    public static final String[] SUPPORTED_PROTOCOLS = {
        "http",
        "https",
        "ftp",
    };

    public DriverLibraryRemote(DriverDescriptor driver, FileType type, String url) {
        super(driver, type, url);
    }

    public DriverLibraryRemote(DriverDescriptor driver, IConfigurationElement config) {
        super(driver, config);
    }

    private DriverLibraryRemote(DriverDescriptor driver, DriverLibraryRemote copyFrom) {
        super(driver, copyFrom);
    }

    @Override
    public DBPDriverLibrary copyLibrary(DriverDescriptor driverDescriptor) {
        return new DriverLibraryRemote(driverDescriptor, this);
    }

    @Override
    public boolean isDownloadable()
    {
        return true;
    }

    @Override
    protected String getLocalFilePath() {
        try {
            String path = new URL(getPath()).getPath();
            if (path.startsWith("/")) {
                path = path.replaceFirst("/", "");
            }
            return Global.getDRIVERS() + path;

        } catch (MalformedURLException e) {
            log.error(e);
            return getPath();
        }
    }

    @Nullable
    @Override
    public String getExternalURL(DBRProgressMonitor monitor) {
        return getPath();
    }


    public static boolean supportsURL(String url) {
        int pos = url.indexOf(":/");
        if (pos <= 0) {
            return false;
        }
        return ArrayUtils.contains(SUPPORTED_PROTOCOLS, url.substring(0, pos));
    }
}
