
package com.dc.parser.ext.postgresql.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.statement.ddl.AlterSchemaStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.ext.postgresql.statement.PostgreSQLStatement;

import java.util.Optional;

/**
 * PostgreSQL alter schema statement.
 */
@Getter
@Setter
public final class PostgreSQLAlterSchemaStatement extends AlterSchemaStatement implements PostgreSQLStatement {
    
    private IdentifierValue renameSchema;
    
    /**
     * Get rename schema.
     *
     * @return rename schema
     */
    public Optional<IdentifierValue> getRenameSchema() {
        return Optional.ofNullable(renameSchema);
    }
}
