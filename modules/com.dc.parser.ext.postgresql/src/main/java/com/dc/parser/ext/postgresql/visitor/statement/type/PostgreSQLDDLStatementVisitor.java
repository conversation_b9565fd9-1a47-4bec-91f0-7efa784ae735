package com.dc.parser.ext.postgresql.visitor.statement.type;

import com.dc.parser.ext.postgresql.parser.autogen.PostgreSQLStatementParser.*;
import com.dc.parser.ext.postgresql.statement.ddl.*;
import com.dc.parser.ext.postgresql.visitor.statement.PostgreSQLStatementVisitor;
import com.dc.parser.model.api.ASTNode;
import com.dc.parser.model.api.visitor.statement.type.DDLStatementVisitor;
import com.dc.parser.model.enums.DirectionType;
import com.dc.parser.model.segment.SQLSegment;
import com.dc.parser.model.segment.ddl.AlterDefinitionSegment;
import com.dc.parser.model.segment.ddl.CreateDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.AddColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.DropColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.ModifyColumnDefinitionSegment;
import com.dc.parser.model.segment.ddl.column.alter.RenameColumnSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.ConstraintSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.AddConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.DropConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ModifyConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.constraint.alter.ValidateConstraintDefinitionSegment;
import com.dc.parser.model.segment.ddl.cursor.CursorNameSegment;
import com.dc.parser.model.segment.ddl.cursor.DirectionSegment;
import com.dc.parser.model.segment.ddl.index.IndexNameSegment;
import com.dc.parser.model.segment.ddl.index.IndexSegment;
import com.dc.parser.model.segment.ddl.table.RelationalTableSegment;
import com.dc.parser.model.segment.ddl.table.RenameTableDefinitionSegment;
import com.dc.parser.model.segment.dml.column.ColumnSegment;
import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.dml.expr.FunctionSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.segment.generic.DatabaseSegment;
import com.dc.parser.model.segment.generic.NameSegment;
import com.dc.parser.model.segment.generic.OwnerSegment;
import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.segment.generic.table.TableNameSegment;
import com.dc.parser.model.statement.dml.DeleteStatement;
import com.dc.parser.model.statement.dml.InsertStatement;
import com.dc.parser.model.statement.dml.SelectStatement;
import com.dc.parser.model.statement.dml.UpdateStatement;
import com.dc.parser.model.value.collection.CollectionValue;
import com.dc.parser.model.value.identifier.IdentifierValue;
import com.dc.parser.model.value.literal.impl.NumberLiteralValue;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.misc.Interval;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DDL statement visitor for PostgreSQL.
 */
public final class PostgreSQLDDLStatementVisitor extends PostgreSQLStatementVisitor implements DDLStatementVisitor {
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitCreateTable(final CreateTableContext ctx) {
        PostgreSQLCreateTableStatement result = new PostgreSQLCreateTableStatement(null != ctx.ifNotExists());
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        if (null != ctx.createDefinitionClause()) {
            CollectionValue<CreateDefinitionSegment> createDefinitions = (CollectionValue<CreateDefinitionSegment>) visit(ctx.createDefinitionClause());

            int start = createDefinitions.getValue().stream().findFirst().map(SQLSegment::getStartIndex).orElse(0);
            int stop = createDefinitions.getValue().stream().skip(createDefinitions.getValue().size() - 1).findFirst().map(SQLSegment::getStopIndex).orElse(0);
            RelationalTableSegment relationalTableSegment = new RelationalTableSegment(start, stop);
            result.setRelationalTable(relationalTableSegment);

            for (CreateDefinitionSegment each : createDefinitions.getValue()) {
                if (each instanceof ColumnDefinitionSegment) {
                    relationalTableSegment.getColumnDefinitions().add((ColumnDefinitionSegment) each);
                } else if (each instanceof ConstraintDefinitionSegment) {
                    relationalTableSegment.getConstraintDefinitions().add((ConstraintDefinitionSegment) each);
                }
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitCreateDefinitionClause(final CreateDefinitionClauseContext ctx) {
        CollectionValue<CreateDefinitionSegment> result = new CollectionValue<>();
        for (CreateDefinitionContext each : ctx.createDefinition()) {
            if (null != each.columnDefinition()) {
                result.getValue().add((ColumnDefinitionSegment) visit(each.columnDefinition()));
            }
            if (null != each.tableConstraint()) {
                result.getValue().add((ConstraintDefinitionSegment) visit(each.tableConstraint()));
            }
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitAlterTable(final AlterTableContext ctx) {
        PostgreSQLAlterTableStatement result = new PostgreSQLAlterTableStatement();
        result.setTable((SimpleTableSegment) visit(ctx.tableNameClause().tableName()));
        if (null != ctx.alterDefinitionClause()) {
            for (AlterDefinitionSegment each : ((CollectionValue<AlterDefinitionSegment>) visit(ctx.alterDefinitionClause())).getValue()) {
                if (each instanceof AddColumnDefinitionSegment) {
                    result.getAddColumnDefinitions().add((AddColumnDefinitionSegment) each);
                } else if (each instanceof ModifyColumnDefinitionSegment) {
                    result.getModifyColumnDefinitions().add((ModifyColumnDefinitionSegment) each);
                } else if (each instanceof DropColumnDefinitionSegment) {
                    result.getDropColumnDefinitions().add((DropColumnDefinitionSegment) each);
                } else if (each instanceof AddConstraintDefinitionSegment) {
                    result.getAddConstraintDefinitions().add((AddConstraintDefinitionSegment) each);
                } else if (each instanceof ValidateConstraintDefinitionSegment) {
                    result.getValidateConstraintDefinitions().add((ValidateConstraintDefinitionSegment) each);
                } else if (each instanceof ModifyConstraintDefinitionSegment) {
                    result.getModifyConstraintDefinitions().add((ModifyConstraintDefinitionSegment) each);
                } else if (each instanceof DropConstraintDefinitionSegment) {
                    result.getDropConstraintDefinitions().add((DropConstraintDefinitionSegment) each);
                } else if (each instanceof RenameTableDefinitionSegment) {
                    result.setRenameTable(((RenameTableDefinitionSegment) each).getRenameTable());
                } else if (each instanceof RenameColumnSegment) {
                    result.getRenameColumnDefinitions().add((RenameColumnSegment) each);
                }
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterAggregate(final AlterAggregateContext ctx) {
        return new PostgreSQLAlterAggregateStatement();
    }
    
    @Override
    public ASTNode visitAlterCollation(final AlterCollationContext ctx) {
        return new PostgreSQLAlterCollationStatement();
    }
    
    @Override
    public ASTNode visitAlterDefaultPrivileges(final AlterDefaultPrivilegesContext ctx) {
        return new PostgreSQLAlterDefaultPrivilegesStatement();
    }
    
    @Override
    public ASTNode visitAlterForeignDataWrapper(final AlterForeignDataWrapperContext ctx) {
        return new PostgreSQLAlterForeignDataWrapperStatement();
    }
    
    @Override
    public ASTNode visitAlterDefinitionClause(final AlterDefinitionClauseContext ctx) {
        CollectionValue<AlterDefinitionSegment> result = new CollectionValue<>();
        if (null != ctx.alterTableActions()) {
            result.getValue().addAll(ctx.alterTableActions().alterTableAction().stream().flatMap(each -> getAlterDefinitionSegments(each).stream()).collect(Collectors.toList()));
        }
        if (null != ctx.renameColumnSpecification()) {
            result.getValue().add((RenameColumnSegment) visit(ctx.renameColumnSpecification()));
        }
        if (null != ctx.renameTableSpecification()) {
            result.getValue().add((RenameTableDefinitionSegment) visit(ctx.renameTableSpecification()));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    private Collection<AlterDefinitionSegment> getAlterDefinitionSegments(final AlterTableActionContext ctx) {
        Collection<AlterDefinitionSegment> result = new LinkedList<>();
        if (null != ctx.addColumnSpecification()) {
            result.addAll(((CollectionValue<AddColumnDefinitionSegment>) visit(ctx.addColumnSpecification())).getValue());
        }
        if (null != ctx.addConstraintSpecification() && null != ctx.addConstraintSpecification().tableConstraint()) {
            result.add((AddConstraintDefinitionSegment) visit(ctx.addConstraintSpecification()));
        }
        if (null != ctx.validateConstraintSpecification()) {
            result.add((ValidateConstraintDefinitionSegment) visit(ctx.validateConstraintSpecification()));
        }
        if (null != ctx.modifyColumnSpecification()) {
            result.add((ModifyColumnDefinitionSegment) visit(ctx.modifyColumnSpecification()));
        }
        if (null != ctx.modifyConstraintSpecification()) {
            result.add((ModifyConstraintDefinitionSegment) visit(ctx.modifyConstraintSpecification()));
        }
        if (null != ctx.dropColumnSpecification()) {
            result.add((DropColumnDefinitionSegment) visit(ctx.dropColumnSpecification()));
        }
        if (null != ctx.dropConstraintSpecification()) {
            result.add((DropConstraintDefinitionSegment) visit(ctx.dropConstraintSpecification()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterForeignTable(final AlterForeignTableContext ctx) {
        return new PostgreSQLAlterForeignTableStatement();
    }
    
    @Override
    public ASTNode visitDropForeignTable(final DropForeignTableContext ctx) {
        return new PostgreSQLDropForeignTableStatement();
    }
    
    @Override
    public ASTNode visitAlterGroup(final AlterGroupContext ctx) {
        return new PostgreSQLAlterGroupStatement();
    }
    
    @Override
    public ASTNode visitAlterMaterializedView(final AlterMaterializedViewContext ctx) {
        return new PostgreSQLAlterMaterializedViewStatement();
    }
    
    @Override
    public ASTNode visitAlterOperator(final AlterOperatorContext ctx) {
        return new PostgreSQLAlterOperatorStatement();
    }
    
    @Override
    public ASTNode visitAddConstraintSpecification(final AddConstraintSpecificationContext ctx) {
        return new AddConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintDefinitionSegment) visit(ctx.tableConstraint()));
    }
    
    @Override
    public ASTNode visitValidateConstraintSpecification(final ValidateConstraintSpecificationContext ctx) {
        return new ValidateConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitModifyConstraintSpecification(final ModifyConstraintSpecificationContext ctx) {
        return new ModifyConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitDropConstraintSpecification(final DropConstraintSpecificationContext ctx) {
        return new DropConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ConstraintSegment) visit(ctx.constraintName()));
    }
    
    @Override
    public ASTNode visitAlterDomain(final AlterDomainContext ctx) {
        return new PostgreSQLAlterDomainStatement();
    }
    
    @Override
    public ASTNode visitAlterPolicy(final AlterPolicyContext ctx) {
        return new PostgreSQLAlterPolicyStatement();
    }
    
    @Override
    public ASTNode visitAlterPublication(final AlterPublicationContext ctx) {
        return new PostgreSQLAlterPublicationStatement();
    }
    
    @Override
    public ASTNode visitAlterSubscription(final AlterSubscriptionContext ctx) {
        return new PostgreSQLAlterSubscriptionStatement();
    }
    
    @Override
    public ASTNode visitAlterTrigger(final AlterTriggerContext ctx) {
        return new PostgreSQLAlterTriggerStatement();
    }
    
    @Override
    public ASTNode visitAlterType(final AlterTypeContext ctx) {
        return new PostgreSQLAlterTypeStatement();
    }
    
    @Override
    public ASTNode visitRenameTableSpecification(final RenameTableSpecificationContext ctx) {
        RenameTableDefinitionSegment result = new RenameTableDefinitionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex());
        TableNameSegment tableName = new TableNameSegment(ctx.identifier().start.getStartIndex(), ctx.identifier().stop.getStopIndex(), (IdentifierValue) visit(ctx.identifier()));
        result.setRenameTable(new SimpleTableSegment(tableName));
        return result;
    }
    
    @Override
    public ASTNode visitAddColumnSpecification(final AddColumnSpecificationContext ctx) {
        CollectionValue<AddColumnDefinitionSegment> result = new CollectionValue<>();
        ColumnDefinitionContext columnDefinition = ctx.columnDefinition();
        if (null != columnDefinition) {
            AddColumnDefinitionSegment addColumnDefinition = new AddColumnDefinitionSegment(
                    ctx.columnDefinition().getStart().getStartIndex(), columnDefinition.getStop().getStopIndex(), Collections.singleton((ColumnDefinitionSegment) visit(columnDefinition)));
            result.getValue().add(addColumnDefinition);
        }
        return result;
    }
    
    @Override
    public ASTNode visitColumnDefinition(final ColumnDefinitionContext ctx) {
        ColumnSegment column = (ColumnSegment) visit(ctx.columnName());
        DataTypeSegment dataType = (DataTypeSegment) visit(ctx.dataType());
        boolean isPrimaryKey = ctx.columnConstraint().stream().anyMatch(each -> null != each.columnConstraintOption() && null != each.columnConstraintOption().primaryKey());
        ColumnDefinitionSegment result = new ColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        result.setColumnName(column);
        result.setPrimaryKey(isPrimaryKey);
        result.setDataType(dataType);
        for (ColumnConstraintContext each : ctx.columnConstraint()) {
            if (null != each.columnConstraintOption().tableName()) {
                result.getReferencedTables().add((SimpleTableSegment) visit(each.columnConstraintOption().tableName()));
            }
        }
        return result;
    }

    private String getText(final ParserRuleContext ctx) {
        return ctx.start.getInputStream().getText(new Interval(ctx.start.getStartIndex(), ctx.stop.getStopIndex()));
    }
    
    @Override
    public ASTNode visitTableConstraintUsingIndex(final TableConstraintUsingIndexContext ctx) {
        ConstraintDefinitionSegment result = new ConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (null != ctx.constraintName()) {
            result.setConstraintName((ConstraintSegment) visit(ctx.constraintName()));
        }
        if (null != ctx.indexName()) {
            result.setIndexName((IndexSegment) visit(ctx.indexName()));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitTableConstraint(final TableConstraintContext ctx) {
        ConstraintDefinitionSegment result = new ConstraintDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex());
        if (null != ctx.constraintClause()) {
            result.setConstraintName((ConstraintSegment) visit(ctx.constraintClause().constraintName()));
        }
        if (null != ctx.tableConstraintOption().primaryKey()) {
            result.getPrimaryKeyColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.tableConstraintOption().columnNames(0))).getValue());
        }
        if (null != ctx.tableConstraintOption().FOREIGN()) {
            result.setReferencedTable((SimpleTableSegment) visit(ctx.tableConstraintOption().tableName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitModifyColumnSpecification(final ModifyColumnSpecificationContext ctx) {
        // TODO visit pk and table ref
        ColumnSegment column = (ColumnSegment) visit(ctx.modifyColumn().columnName());
        DataTypeSegment dataType = null == ctx.dataType() ? null : (DataTypeSegment) visit(ctx.dataType());
        ColumnDefinitionSegment columnDefinition = new ColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), getText(ctx));
        columnDefinition.setColumnName(column);
        columnDefinition.setDataType(dataType);
        return new ModifyColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), columnDefinition);
    }
    
    @Override
    public ASTNode visitDropColumnSpecification(final DropColumnSpecificationContext ctx) {
        return new DropColumnDefinitionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), Collections.singleton((ColumnSegment) visit(ctx.columnName())));
    }
    
    @Override
    public ASTNode visitRenameColumnSpecification(final RenameColumnSpecificationContext ctx) {
        return new RenameColumnSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), (ColumnSegment) visit(ctx.columnName(0)), (ColumnSegment) visit(ctx.columnName(1)));
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropTable(final DropTableContext ctx) {
        boolean containsCascade = ctx.dropTableOpt() != null && null != ctx.dropTableOpt().CASCADE();
        PostgreSQLDropTableStatement result = new PostgreSQLDropTableStatement();
        result.setIfExists(null != ctx.ifExists());
        result.setContainsCascade(containsCascade);
        result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.tableNames())).getValue());
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitTruncateTable(final TruncateTableContext ctx) {
        PostgreSQLTruncateStatement result = new PostgreSQLTruncateStatement();
        result.getTables().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.tableNamesClause())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitDropPolicy(final DropPolicyContext ctx) {
        return new PostgreSQLDropPolicyStatement();
    }
    
    @Override
    public ASTNode visitDropRule(final DropRuleContext ctx) {
        return new PostgreSQLDropRuleStatement();
    }
    
    @Override
    public ASTNode visitDropStatistics(final DropStatisticsContext ctx) {
        return new PostgreSQLDropStatisticsStatement();
    }
    
    @Override
    public ASTNode visitDropPublication(final DropPublicationContext ctx) {
        return new PostgreSQLDropPublicationStatement();
    }
    
    @Override
    public ASTNode visitDropSubscription(final DropSubscriptionContext ctx) {
        return new PostgreSQLDropSubscriptionStatement();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitCreateIndex(final CreateIndexContext ctx) {
        PostgreSQLCreateIndexStatement result = new PostgreSQLCreateIndexStatement();
        result.setIfNotExists(null != ctx.ifNotExists());
        result.setTable((SimpleTableSegment) visit(ctx.tableName()));
        result.getColumns().addAll(((CollectionValue<ColumnSegment>) visit(ctx.indexParams())).getValue());
        if (null != ctx.indexName()) {
            result.setIndex((IndexSegment) visit(ctx.indexName()));
        } else {
            result.setGeneratedIndexStartIndex(ctx.ON().getSymbol().getStartIndex() - 1);
        }
        return result;
    }
    
    @Override
    public ASTNode visitIndexParams(final IndexParamsContext ctx) {
        CollectionValue<ColumnSegment> result = new CollectionValue<>();
        for (IndexElemContext each : ctx.indexElem()) {
            if (null != each.colId()) {
                result.getValue().add(new ColumnSegment(each.colId().start.getStartIndex(), each.colId().stop.getStopIndex(), new IdentifierValue(each.colId().getText())));
            }
            if (null != each.functionExprWindowless()) {
                FunctionSegment functionSegment = (FunctionSegment) visit(each.functionExprWindowless());
                functionSegment.getParameters().forEach(param -> {
                    if (param instanceof ColumnSegment) {
                        result.getValue().add((ColumnSegment) param);
                    }
                });
            }
        }
        return result;
    }
    
    @Override
    public ASTNode visitFunctionExprWindowless(final FunctionExprWindowlessContext ctx) {
        FunctionSegment result = new FunctionSegment(ctx.getStart().getStartIndex(), ctx.getStop().getStopIndex(), ctx.funcApplication().funcName().getText(), getOriginalText(ctx));
        result.getParameters().addAll(getExpressions(ctx.funcApplication().funcArgList().funcArgExpr()));
        return result;
    }
    
    private Collection<ExpressionSegment> getExpressions(final Collection<FuncArgExprContext> aExprContexts) {
        if (null == aExprContexts) {
            return Collections.emptyList();
        }
        Collection<ExpressionSegment> result = new ArrayList<>(aExprContexts.size());
        for (FuncArgExprContext each : aExprContexts) {
            result.add((ExpressionSegment) visit(each.aExpr()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterIndex(final AlterIndexContext ctx) {
        PostgreSQLAlterIndexStatement result = new PostgreSQLAlterIndexStatement();
        result.setIndex(createIndexSegment((SimpleTableSegment) visit(ctx.qualifiedName())));
        if (null != ctx.alterIndexDefinitionClause().renameIndexSpecification()) {
            result.setRenameIndex((IndexSegment) visit(ctx.alterIndexDefinitionClause().renameIndexSpecification().indexName()));
        }
        return result;
    }
    
    private IndexSegment createIndexSegment(final SimpleTableSegment tableSegment) {
        IndexNameSegment indexName = new IndexNameSegment(tableSegment.getTableName().getStartIndex(), tableSegment.getTableName().getStopIndex(), tableSegment.getTableName().getIdentifier());
        IndexSegment result = new IndexSegment(tableSegment.getStartIndex(), tableSegment.getStopIndex(), indexName);
        tableSegment.getOwner().ifPresent(result::setOwner);
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropIndex(final DropIndexContext ctx) {
        PostgreSQLDropIndexStatement result = new PostgreSQLDropIndexStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getIndexes().addAll(createIndexSegments(((CollectionValue<SimpleTableSegment>) visit(ctx.qualifiedNameList())).getValue()));
        return result;
    }
    
    private Collection<IndexSegment> createIndexSegments(final Collection<SimpleTableSegment> tableSegments) {
        Collection<IndexSegment> result = new LinkedList<>();
        for (SimpleTableSegment each : tableSegments) {
            result.add(createIndexSegment(each));
        }
        return result;
    }
    

    @Override
    public ASTNode visitTableNameClause(final TableNameClauseContext ctx) {
        return visit(ctx.tableName());
    }
    
    @Override
    public ASTNode visitTableNamesClause(final TableNamesClauseContext ctx) {
        Collection<SimpleTableSegment> tableSegments = new LinkedList<>();
        for (int i = 0; i < ctx.tableNameClause().size(); i++) {
            tableSegments.add((SimpleTableSegment) visit(ctx.tableNameClause(i)));
        }
        CollectionValue<SimpleTableSegment> result = new CollectionValue<>();
        result.getValue().addAll(tableSegments);
        return result;
    }
    
    @Override
    public ASTNode visitAlterFunction(final AlterFunctionContext ctx) {
        return new PostgreSQLAlterFunctionStatement();
    }
    
    @Override
    public ASTNode visitAlterProcedure(final AlterProcedureContext ctx) {
        return new PostgreSQLAlterProcedureStatement();
    }
    
    @Override
    public ASTNode visitCreateFunction(final CreateFunctionContext ctx) {
        return new PostgreSQLCreateFunctionStatement();
    }
    
    @Override
    public ASTNode visitCreateProcedure(final CreateProcedureContext ctx) {
        return new PostgreSQLCreateProcedureStatement();
    }
    
    @Override
    public ASTNode visitDropFunction(final DropFunctionContext ctx) {
        return new PostgreSQLDropFunctionStatement();
    }
    
    @Override
    public ASTNode visitDropGroup(final DropGroupContext ctx) {
        return new PostgreSQLDropGroupStatement();
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropView(final DropViewContext ctx) {
        PostgreSQLDropViewStatement result = new PostgreSQLDropViewStatement();
        result.setIfExists(null != ctx.ifExists());
        result.getViews().addAll(((CollectionValue<SimpleTableSegment>) visit(ctx.qualifiedNameList())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitCreateView(final CreateViewContext ctx) {
        PostgreSQLCreateViewStatement result = new PostgreSQLCreateViewStatement();
        result.setReplaceView(null != ctx.REPLACE());
        result.setView((SimpleTableSegment) visit(ctx.qualifiedName()));
        result.setViewDefinition(getOriginalText(ctx.select()));
        result.setSelect((SelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitAlterView(final AlterViewContext ctx) {
        PostgreSQLAlterViewStatement result = new PostgreSQLAlterViewStatement();
        result.setView((SimpleTableSegment) visit(ctx.qualifiedName()));
        if (ctx.alterViewClauses() instanceof AlterRenameViewContext) {
            NameContext nameContext = ((AlterRenameViewContext) ctx.alterViewClauses()).name();
            result.setRenameView(new SimpleTableSegment(new TableNameSegment(nameContext.getStart().getStartIndex(),
                    nameContext.getStop().getStopIndex(), (IdentifierValue) visit(nameContext.identifier()))));
        }
        return result;
    }
    
    @Override
    public ASTNode visitDropDatabase(final DropDatabaseContext ctx) {
        PostgreSQLDropDatabaseStatement result = new PostgreSQLDropDatabaseStatement();
        result.setDatabaseName(new DatabaseSegment(ctx.name().getStart().getStartIndex(), ctx.name().getStop().getStopIndex(), (IdentifierValue) visit(ctx.name())));
        result.setIfExists(null != ctx.ifExists());
        return result;
    }
    
    @Override
    public ASTNode visitAlterRoutine(final AlterRoutineContext ctx) {
        return new PostgreSQLAlterRoutineStatement();
    }
    
    @Override
    public ASTNode visitAlterRule(final AlterRuleContext ctx) {
        return new PostgreSQLAlterRuleStatement();
    }
    
    @Override
    public ASTNode visitDropProcedure(final DropProcedureContext ctx) {
        return new PostgreSQLDropProcedureStatement();
    }
    
    @Override
    public ASTNode visitDropRoutine(final DropRoutineContext ctx) {
        return new PostgreSQLDropRoutineStatement();
    }
    
    @Override
    public ASTNode visitCreateDatabase(final CreateDatabaseContext ctx) {
        PostgreSQLCreateDatabaseStatement result = new PostgreSQLCreateDatabaseStatement();
        IdentifierValue value = (IdentifierValue) visit(ctx.name());
        result.setDatabaseName(new DatabaseSegment(ctx.name().getStart().getStartIndex(), ctx.name().getStop().getStopIndex(), value));
        return result;
    }
    
    @Override
    public ASTNode visitCreateSequence(final CreateSequenceContext ctx) {
        PostgreSQLCreateSequenceStatement result = new PostgreSQLCreateSequenceStatement();
        result.setSequenceName(((SimpleTableSegment) visit(ctx.qualifiedName())).getTableName().getIdentifier().getValue());
        return result;
    }
    
    @Override
    public ASTNode visitAlterSequence(final AlterSequenceContext ctx) {
        PostgreSQLAlterSequenceStatement result = new PostgreSQLAlterSequenceStatement();
        result.setSequenceName(((SimpleTableSegment) visit(ctx.qualifiedName())).getTableName().getIdentifier().getValue());
        return result;
    }
    
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public ASTNode visitDropSequence(final DropSequenceContext ctx) {
        PostgreSQLDropSequenceStatement result = new PostgreSQLDropSequenceStatement();
        result.setSequenceNames(((CollectionValue) visit(ctx.qualifiedNameList())).getValue());
        return result;
    }
    
    @Override
    public ASTNode visitPrepare(final PrepareContext ctx) {
        PostgreSQLPrepareStatement result = new PostgreSQLPrepareStatement();
        if (null != ctx.preparableStmt().select()) {
            result.setSelect((SelectStatement) visit(ctx.preparableStmt().select()));
        }
        if (null != ctx.preparableStmt().insert()) {
            result.setInsert((InsertStatement) visit(ctx.preparableStmt().insert()));
        }
        if (null != ctx.preparableStmt().update()) {
            result.setUpdate((UpdateStatement) visit(ctx.preparableStmt().update()));
        }
        if (null != ctx.preparableStmt().delete()) {
            result.setDelete((DeleteStatement) visit(ctx.preparableStmt().delete()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitDeallocate(final DeallocateContext ctx) {
        return new PostgreSQLDeallocateStatement();
    }
    
    @Override
    public ASTNode visitDropCast(final DropCastContext ctx) {
        return new PostgreSQLDropCastStatement();
    }
    
    @Override
    public ASTNode visitCreateTablespace(final CreateTablespaceContext ctx) {
        return new PostgreSQLCreateTablespaceStatement();
    }
    
    @Override
    public ASTNode visitAlterTablespace(final AlterTablespaceContext ctx) {
        return new PostgreSQLAlterTablespaceStatement();
    }
    
    @Override
    public ASTNode visitDropTablespace(final DropTablespaceContext ctx) {
        return new PostgreSQLDropTablespaceStatement();
    }
    
    @Override
    public ASTNode visitDropTextSearch(final DropTextSearchContext ctx) {
        return new PostgreSQLDropTextSearchStatement();
    }
    
    @Override
    public ASTNode visitDropDomain(final DropDomainContext ctx) {
        return new PostgreSQLDropDomainStatement();
    }
    
    @Override
    public ASTNode visitCreateDomain(final CreateDomainContext ctx) {
        return new PostgreSQLCreateDomainStatement();
    }
    
    @Override
    public ASTNode visitCreateRule(final CreateRuleContext ctx) {
        return new PostgreSQLCreateRuleStatement();
    }
    
    @Override
    public ASTNode visitCreateLanguage(final CreateLanguageContext ctx) {
        return new PostgreSQLCreateLanguageStatement();
    }
    
    @Override
    public ASTNode visitCreateSchema(final CreateSchemaContext ctx) {
        PostgreSQLCreateSchemaStatement result = new PostgreSQLCreateSchemaStatement();
        if (null != ctx.createSchemaClauses().colId()) {
            result.setSchemaName(new IdentifierValue(ctx.createSchemaClauses().colId().getText()));
        }
        if (null != ctx.createSchemaClauses().roleSpec() && null != ctx.createSchemaClauses().roleSpec().identifier()) {
            result.setUsername((IdentifierValue) visit(ctx.createSchemaClauses().roleSpec().identifier()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterSchema(final AlterSchemaContext ctx) {
        PostgreSQLAlterSchemaStatement result = new PostgreSQLAlterSchemaStatement();
        result.setSchemaName((IdentifierValue) visit(ctx.name().get(0)));
        if (ctx.name().size() > 1) {
            result.setRenameSchema((IdentifierValue) visit(ctx.name().get(1)));
        }
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitDropSchema(final DropSchemaContext ctx) {
        PostgreSQLDropSchemaStatement result = new PostgreSQLDropSchemaStatement();
        result.getSchemaNames().addAll(((CollectionValue<IdentifierValue>) visit(ctx.nameList())).getValue());
        result.setContainsCascade(null != ctx.dropBehavior() && null != ctx.dropBehavior().CASCADE());
        return result;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public ASTNode visitNameList(final NameListContext ctx) {
        CollectionValue<IdentifierValue> result = new CollectionValue<>();
        if (null != ctx.nameList()) {
            result.combine((CollectionValue<IdentifierValue>) visit(ctx.nameList()));
        }
        if (null != ctx.name()) {
            result.getValue().add((IdentifierValue) visit(ctx.name()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitAlterLanguage(final AlterLanguageContext ctx) {
        return new PostgreSQLAlterLanguageStatement();
    }
    
    @Override
    public ASTNode visitAlterServer(final AlterServerContext ctx) {
        return new PostgreSQLAlterServerStatement();
    }
    
    @Override
    public ASTNode visitAlterStatistics(final AlterStatisticsContext ctx) {
        return new PostgreSQLAlterStatisticsStatement();
    }
    
    @Override
    public ASTNode visitDropLanguage(final DropLanguageContext ctx) {
        return new PostgreSQLDropLanguageStatement();
    }
    
    @Override
    public ASTNode visitCreateConversion(final CreateConversionContext ctx) {
        return new PostgreSQLCreateConversionStatement();
    }
    
    @Override
    public ASTNode visitCreateType(final CreateTypeContext ctx) {
        return new PostgreSQLCreateTypeStatement();
    }
    
    @Override
    public ASTNode visitDropConversion(final DropConversionContext ctx) {
        return new PostgreSQLDropConversionStatement();
    }
    
    @Override
    public ASTNode visitAlterConversion(final AlterConversionContext ctx) {
        return new PostgreSQLAlterConversionStatement();
    }
    
    @Override
    public ASTNode visitCreateTextSearch(final CreateTextSearchContext ctx) {
        return new PostgreSQLCreateTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchConfiguration(final AlterTextSearchConfigurationContext ctx) {
        return new PostgreSQLAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchDictionary(final AlterTextSearchDictionaryContext ctx) {
        return new PostgreSQLAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchTemplate(final AlterTextSearchTemplateContext ctx) {
        return new PostgreSQLAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitAlterTextSearchParser(final AlterTextSearchParserContext ctx) {
        return new PostgreSQLAlterTextSearchStatement();
    }
    
    @Override
    public ASTNode visitCreateExtension(final CreateExtensionContext ctx) {
        return new PostgreSQLCreateExtensionStatement();
    }
    
    @Override
    public ASTNode visitAlterExtension(final AlterExtensionContext ctx) {
        return new PostgreSQLAlterExtensionStatement();
    }
    
    @Override
    public ASTNode visitDropExtension(final DropExtensionContext ctx) {
        return new PostgreSQLDropExtensionStatement();
    }
    
    @Override
    public ASTNode visitDiscard(final DiscardContext ctx) {
        return new PostgreSQLDiscardStatement();
    }
    
    @Override
    public ASTNode visitDropOwned(final DropOwnedContext ctx) {
        return new PostgreSQLDropOwnedStatement();
    }
    
    @Override
    public ASTNode visitDropOperator(final DropOperatorContext ctx) {
        return new PostgreSQLDropOperatorStatement();
    }
    
    @Override
    public ASTNode visitDropMaterializedView(final DropMaterializedViewContext ctx) {
        return new PostgreSQLDropMaterializedViewStatement();
    }
    
    @Override
    public ASTNode visitDropEventTrigger(final DropEventTriggerContext ctx) {
        return new PostgreSQLDropEventTriggerStatement();
    }
    
    @Override
    public ASTNode visitDropAggregate(final DropAggregateContext ctx) {
        return new PostgreSQLDropAggregateStatement();
    }
    
    @Override
    public ASTNode visitDropCollation(final DropCollationContext ctx) {
        return new PostgreSQLDropCollationStatement();
    }
    
    @Override
    public ASTNode visitDropForeignDataWrapper(final DropForeignDataWrapperContext ctx) {
        return new PostgreSQLDropForeignDataWrapperStatement();
    }
    
    @Override
    public ASTNode visitDropTrigger(final DropTriggerContext ctx) {
        return new PostgreSQLDropTriggerStatement();
    }
    
    @Override
    public ASTNode visitDropType(final DropTypeContext ctx) {
        return new PostgreSQLDropTypeStatement();
    }
    
    @Override
    public ASTNode visitComment(final CommentContext ctx) {
        if (null != ctx.commentClauses().objectTypeAnyName() && null != ctx.commentClauses().objectTypeAnyName().TABLE()) {
            return commentOnTable(ctx);
        }
        if (null != ctx.commentClauses().COLUMN()) {
            return commentOnColumn(ctx);
        }
        if (null != ctx.commentClauses().objectTypeNameOnAnyName()) {
            return getTableFromComment(ctx);
        }
        return new PostgreSQLCommentStatement();
    }
    
    @SuppressWarnings("unchecked")
    private PostgreSQLCommentStatement commentOnColumn(final CommentContext ctx) {
        PostgreSQLCommentStatement result = new PostgreSQLCommentStatement();
        Iterator<NameSegment> nameSegmentIterator = ((CollectionValue<NameSegment>) visit(ctx.commentClauses().anyName())).getValue().iterator();
        Optional<NameSegment> columnName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        columnName.ifPresent(optional -> result.setColumn(new ColumnSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier())));
        result.setComment(new IdentifierValue(ctx.commentClauses().commentText().getText()));
        setTableSegment(result, nameSegmentIterator);
        return result;
    }
    
    @SuppressWarnings("unchecked")
    private PostgreSQLCommentStatement commentOnTable(final CommentContext ctx) {
        PostgreSQLCommentStatement result = new PostgreSQLCommentStatement();
        Iterator<NameSegment> nameSegmentIterator = ((CollectionValue<NameSegment>) visit(ctx.commentClauses().anyName())).getValue().iterator();
        result.setComment(new IdentifierValue(ctx.commentClauses().commentText().getText()));
        setTableSegment(result, nameSegmentIterator);
        return result;
    }
    
    private void setTableSegment(final PostgreSQLCommentStatement statement, final Iterator<NameSegment> nameSegmentIterator) {
        Optional<NameSegment> tableName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        tableName.ifPresent(optional -> statement.setTable(new SimpleTableSegment(new TableNameSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier()))));
        Optional<NameSegment> schemaName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        schemaName.ifPresent(optional -> statement.getTable().setOwner(new OwnerSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier())));
        Optional<NameSegment> databaseName = nameSegmentIterator.hasNext() ? Optional.of(nameSegmentIterator.next()) : Optional.empty();
        databaseName.ifPresent(optional -> statement.getTable().getOwner()
                .ifPresent(owner -> owner.setOwner(new OwnerSegment(optional.getStartIndex(), optional.getStopIndex(), optional.getIdentifier()))));
    }
    
    private PostgreSQLCommentStatement getTableFromComment(final CommentContext ctx) {
        PostgreSQLCommentStatement result = new PostgreSQLCommentStatement();
        result.setTable((SimpleTableSegment) visit(ctx.commentClauses().tableName()));
        return result;
    }
    
    @Override
    public ASTNode visitDropOperatorClass(final DropOperatorClassContext ctx) {
        return new PostgreSQLDropOperatorClassStatement();
    }
    
    @Override
    public ASTNode visitDropOperatorFamily(final DropOperatorFamilyContext ctx) {
        return new PostgreSQLDropOperatorFamilyStatement();
    }
    
    @Override
    public ASTNode visitDropAccessMethod(final DropAccessMethodContext ctx) {
        return new PostgreSQLDropAccessMethodStatement();
    }
    
    @Override
    public ASTNode visitDropServer(final DropServerContext ctx) {
        return new PostgreSQLDropServerStatement();
    }
    
    @Override
    public ASTNode visitDeclare(final DeclareContext ctx) {
        PostgreSQLDeclareStatement result = new PostgreSQLDeclareStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        result.setSelect((SelectStatement) visit(ctx.select()));
        return result;
    }
    
    @Override
    public ASTNode visitFetch(final FetchContext ctx) {
        PostgreSQLFetchStatement result = new PostgreSQLFetchStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        if (null != ctx.direction()) {
            result.setDirection((DirectionSegment) visit(ctx.direction()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitMove(final MoveContext ctx) {
        PostgreSQLMoveStatement result = new PostgreSQLMoveStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        if (null != ctx.direction()) {
            result.setDirection((DirectionSegment) visit(ctx.direction()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitClose(final CloseContext ctx) {
        PostgreSQLCloseStatement result = new PostgreSQLCloseStatement();
        if (null != ctx.cursorName()) {
            result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        }
        result.setCloseAll(null != ctx.ALL());
        return result;
    }
    
    @Override
    public ASTNode visitCursorName(final CursorNameContext ctx) {
        return null != ctx.name() ? new CursorNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (IdentifierValue) visit(ctx.name()))
                : new CursorNameSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), (IdentifierValue) visit(ctx.hostVariable()));
    }
    
    @Override
    public ASTNode visitCluster(final ClusterContext ctx) {
        PostgreSQLClusterStatement result = new PostgreSQLClusterStatement();
        if (null != ctx.tableName()) {
            result.setSimpleTable((SimpleTableSegment) visit(ctx.tableName()));
        }
        if (null != ctx.clusterIndexSpecification()) {
            result.setIndex((IndexSegment) visit(ctx.clusterIndexSpecification().indexName()));
        }
        return result;
    }
    
    @Override
    public ASTNode visitCreateAccessMethod(final CreateAccessMethodContext ctx) {
        return new PostgreSQLCreateAccessMethodStatement();
    }
    
    @Override
    public ASTNode visitCreateAggregate(final CreateAggregateContext ctx) {
        return new PostgreSQLCreateAggregateStatement();
    }
    
    @Override
    public ASTNode visitNext(final NextContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.NEXT);
    }
    
    @Override
    public ASTNode visitPrior(final PriorContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.PRIOR);
    }
    
    @Override
    public ASTNode visitFirst(final FirstContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FIRST);
    }
    
    @Override
    public ASTNode visitLast(final LastContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.LAST);
    }
    
    @Override
    public ASTNode visitAbsoluteCount(final AbsoluteCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.ABSOLUTE_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitRelativeCount(final RelativeCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.RELATIVE_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitCount(final CountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitAll(final AllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.ALL);
    }
    
    @Override
    public ASTNode visitForward(final ForwardContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD);
    }
    
    @Override
    public ASTNode visitForwardCount(final ForwardCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitForwardAll(final ForwardAllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.FORWARD_ALL);
    }
    
    @Override
    public ASTNode visitBackward(final BackwardContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD);
    }
    
    @Override
    public ASTNode visitBackwardCount(final BackwardCountContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD_COUNT, ((NumberLiteralValue) visit(ctx.signedIconst())).getValue().longValue());
    }
    
    @Override
    public ASTNode visitBackwardAll(final BackwardAllContext ctx) {
        return new DirectionSegment(ctx.start.getStartIndex(), ctx.stop.getStopIndex(), DirectionType.BACKWARD_ALL);
    }
    
    @Override
    public ASTNode visitCreateCast(final CreateCastContext ctx) {
        return new PostgreSQLCreateCastStatement();
    }
    
    @Override
    public ASTNode visitListen(final ListenContext ctx) {
        String channelName = ctx.channelName().getText();
        PostgreSQLListenStatement result = new PostgreSQLListenStatement();
        result.setChannelName(channelName);
        return result;
    }
    
    @Override
    public ASTNode visitUnlisten(final UnlistenContext ctx) {
        return new PostgreSQLUnlistenStatement();
    }
    
    @Override
    public ASTNode visitNotifyStmt(final NotifyStmtContext ctx) {
        return new PostgreSQLNotifyStmtStatement();
    }
    
    @Override
    public ASTNode visitCreateCollation(final CreateCollationContext ctx) {
        return new PostgreSQLCreateCollationStatement();
    }
    
    @Override
    public ASTNode visitRefreshMatViewStmt(final RefreshMatViewStmtContext ctx) {
        return new PostgreSQLRefreshMatViewStmtStatement();
    }
    
    @Override
    public ASTNode visitReindex(final ReindexContext ctx) {
        return new PostgreSQLReindexStatement();
    }
    
    @Override
    public ASTNode visitSecurityLabelStmt(final SecurityLabelStmtContext ctx) {
        return new PostgreSQLSecurityLabelStmtStatement();
    }
    
    @Override
    public ASTNode visitCreateEventTrigger(final CreateEventTriggerContext ctx) {
        return new PostgreSQLCreateEventTriggerStatement();
    }
    
    @Override
    public ASTNode visitCreateForeignDataWrapper(final CreateForeignDataWrapperContext ctx) {
        return new PostgreSQLCreateForeignDataWrapperStatement();
    }
    
    @Override
    public ASTNode visitCreateForeignTable(final CreateForeignTableContext ctx) {
        return new PostgreSQLCreateForeignTableStatement();
    }
    
    @Override
    public ASTNode visitCreateMaterializedView(final CreateMaterializedViewContext ctx) {
        return new PostgreSQLCreateMaterializedViewStatement();
    }
    
    @Override
    public ASTNode visitCreateOperator(final CreateOperatorContext ctx) {
        return new PostgreSQLCreateOperatorStatement();
    }
    
    @Override
    public ASTNode visitCreatePolicy(final CreatePolicyContext ctx) {
        return new PostgreSQLCreatePolicyStatement();
    }
    
    @Override
    public ASTNode visitCreatePublication(final CreatePublicationContext ctx) {
        return new PostgreSQLCreatePublicationStatement();
    }

    @Override
    public ASTNode visitAlterEventTrigger(AlterEventTriggerContext ctx) {
        return new PostgreSQLAlterEventTriggerStatement();
    }

    @Override
    public ASTNode visitAlterLargeObject(AlterLargeObjectContext ctx) {
        return new PostgreSQLAlterLargeObjectStatement();
    }

    @Override
    public ASTNode visitAlterSystem(AlterSystemContext ctx) {
        return new PostgreSQLAlterSystemStatement();
    }

    @Override
    public ASTNode visitAlterUserMapping(AlterUserMappingContext ctx) {
        return new PostgreSQLAlterUserMappingStatement();
    }

    @Override
    public ASTNode visitCreateStatistics(CreateStatisticsContext ctx) {
        return new PostgreSQLCreateStatisticsStatement();
    }

    @Override
    public ASTNode visitCreateSubscription(CreateSubscriptionContext ctx) {
        return new PostgreSQLCreateSubscriptionStatement();
    }

    @Override
    public ASTNode visitCreateTransform(CreateTransformContext ctx) {
        return new PostgreSQLCreateTransformStatement();
    }

    @Override
    public ASTNode visitCreateUserMapping(CreateUserMappingContext ctx) {
        return new PostgreSQLCreateUserMappingStatement();
    }

    @Override
    public ASTNode visitDropUserMapping(DropUserMappingContext ctx) {
        return new PostgreSQLDropUserMappingStatement();
    }

    @Override
    public ASTNode visitDropTransform(DropTransformContext ctx) {
        return new PostgreSQLDropTransformStatement();
    }

    @Override
    public ASTNode visitCreateOperatorClass(CreateOperatorClassContext ctx) {
        return new PostgreSQLCreateOperatorClassStatement();
    }

    @Override
    public ASTNode visitCreateOperatorFamily(CreateOperatorFamilyContext ctx) {
        return new PostgreSQLCreateOperatorFamilyStatement();
    }

    @Override
    public ASTNode visitAlterOperatorClass(AlterOperatorClassContext ctx) {
        return new PostgreSQLAlterOperatorClassStatement();
    }

    @Override
    public ASTNode visitAlterOperatorFamily(AlterOperatorFamilyContext ctx) {
        return new PostgreSQLAlterOperatorFamilyStatement();
    }

    @Override
    public ASTNode visitImportForeignSchema(ImportForeignSchemaContext ctx) {
        return new PostgreSQLImportForeignSchemaStatement();
    }

    @Override
    public ASTNode visitRefreshMaterializedView(RefreshMaterializedViewContext ctx) {
        return new PostgreSQLRefreshMaterializedViewStatement();
    }

    @Override
    public ASTNode visitOpen(final OpenContext ctx) {
        PostgreSQLOpenStatement result = new PostgreSQLOpenStatement();
        result.setCursorName((CursorNameSegment) visit(ctx.cursorName()));
        return result;
    }
}
