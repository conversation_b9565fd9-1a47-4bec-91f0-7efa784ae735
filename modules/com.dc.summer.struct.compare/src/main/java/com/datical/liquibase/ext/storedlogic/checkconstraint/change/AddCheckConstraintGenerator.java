//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.datical.liquibase.ext.storedlogic.checkconstraint.change;

import com.datical.liquibase.ext.storedlogic.checkconstraint.CheckConstraint;
import java.util.ArrayList;
import java.util.Arrays;
import liquibase.database.Database;
import liquibase.database.core.AbstractDb2Database;
import liquibase.database.core.MSSQLDatabase;
import liquibase.database.core.MySQLDatabase;
import liquibase.database.core.OracleDatabase;
import liquibase.database.core.PostgresDatabase;
import liquibase.exception.ValidationErrors;
import liquibase.sql.Sql;
import liquibase.sql.UnparsedSql;
import liquibase.sqlgenerator.SqlGeneratorChain;
import liquibase.sqlgenerator.SqlGeneratorFactory;
import liquibase.sqlgenerator.core.AbstractSqlGenerator;
import liquibase.structure.DatabaseObject;
import liquibase.structure.core.Table;

public class AddCheckConstraintGenerator extends AbstractSqlGenerator<AddCheckConstraintStatement> {
    public AddCheckConstraintGenerator() {
    }

    public boolean supports(AddCheckConstraintStatement var1, Database var2) {
        return var2 instanceof OracleDatabase || var2 instanceof MSSQLDatabase || var2 instanceof MySQLDatabase && ((MySQLDatabase)var2).isMinimumMySQLVersion("8.0.16") || var2 instanceof AbstractDb2Database || var2 instanceof PostgresDatabase;
    }

    public ValidationErrors validate(AddCheckConstraintStatement var1, Database var2, SqlGeneratorChain var3) {
        ValidationErrors var4;
        (var4 = new ValidationErrors()).checkRequiredField("constraintBody", var1.getConstraintBody());
        var4.checkRequiredField("constraintName", var1.getConstraintName());
        var4.checkRequiredField("tableName", var1.getTableName());
        return var4;
    }

    public Sql[] generateSql(AddCheckConstraintStatement var1, Database var2, SqlGeneratorChain var3) {
        ArrayList var6 = new ArrayList();
        // FIXME 移除小括号
        String var4 = String.format("ALTER TABLE %s ADD CONSTRAINT %s CHECK %s", var2.escapeTableName(var1.getCatalogName(), var1.getSchemaName(), var1.getTableName()), var2.escapeConstraintName(var1.getConstraintName()), var1.getConstraintBody());
        if (var2 instanceof OracleDatabase) {
            var4 = var4 + (var1.isDisabled() ? " DISABLE " : "");
            var4 = var4 + (!var1.isValidate() && !var1.isDisabled() ? " ENABLE NOVALIDATE " : "");
        }

        var6.add(new UnparsedSql(var4, new DatabaseObject[]{this.getAffectedCheckConstraint(var1)}));
        if (var1.isDisabled() && !(var2 instanceof OracleDatabase)) {
            DisableCheckConstraintStatement var5 = new DisableCheckConstraintStatement(var1.getCatalogName(), var1.getSchemaName(), var1.getTableName(), var1.getConstraintName());
            var6.addAll(Arrays.asList(SqlGeneratorFactory.getInstance().generateSql(var5, var2)));
        }

        return (Sql[])var6.toArray(new Sql[var6.size()]);
    }

    protected CheckConstraint getAffectedCheckConstraint(AddCheckConstraintStatement var1) {
        return (new CheckConstraint()).setName(var1.getConstraintName()).setTable((Table)(new Table()).setName(var1.getTableName()).setSchema(var1.getCatalogName(), var1.getSchemaName()));
    }
}
