# execute-export接口直接执行模式使用指南

## 概述

为了解决`execute-export`接口中Redis缓存不存在导致重复执行SQL查询的问题，我们引入了**直接执行模式**。该模式参考`execute-batch`接口的设计，允许直接执行SQL并导出结果，避免依赖Redis缓存，从而解决以下问题：

1. **Redis缓存不存在时的重复查询**：当缓存过期或不存在时，系统会重新执行SQL查询
2. **CLOB/时间类型导致的重复查询**：某些数据类型在缓存中存储有问题，导致需要重新查询
3. **资源浪费**：一次导出可能执行两次相同的SQL查询

## 新增功能

### 1. 导出模式枚举 (ExportMode)

```java
public enum ExportMode {
    CACHE("从缓存获取结果集"),    // 兼容旧版本
    DIRECT("直接执行SQL并导出")   // 新版本，推荐使用
}
```

### 2. SqlExportMessage增强

新增以下字段：

```java
public class SqlExportMessage {
    // 原有字段...
    
    // 新增字段
    private List<ValidExecuteModel> batchExecuteModels;  // 批处理执行模型
    private boolean isErrorContinue = false;             // 遇到错误是否继续
    private ResultFormat resultFormat = new ResultFormat(); // 结果集格式
    private ExportMode exportMode = ExportMode.CACHE;    // 导出模式
    
    // 辅助方法
    public boolean isDirectMode();    // 判断是否为直接模式
    public boolean isCacheMode();     // 判断是否为缓存模式
    public void validate();           // 验证参数有效性
}
```

## 使用方式

### 1. 直接执行模式（推荐）

```json
{
  "token": "your-session-token",
  "exportType": "XLSX",
  "exportMode": "DIRECT",
  "batchExecuteModels": [
    {
      "sql": "SELECT * FROM users WHERE status = 'active'",
      "operation": "SELECT",
      "sqlRecord": {
        "sql": "SELECT * FROM users WHERE status = 'active'",
        "sqlType": "SELECT"
      },
      "sqlHistory": {
        "connectionPattern": "READ_ONLY",
        "origin": "BROWSER"
      },
      "permissionModel": {
        "needPermission": true
      }
    }
  ],
  "isErrorContinue": false,
  "resultFormat": {
    "maxRows": 10000,
    "fetchSize": 1000
  },
  "fileCharset": "UTF-8",
  "exportDesensitize": false,
  "userId": "user123"
}
```

### 2. 缓存模式（兼容旧版本）

```json
{
  "token": "your-session-token",
  "exportType": "CSV",
  "exportMode": "CACHE",
  "sqlExportModels": [
    {
      "taskId": "task-123",
      "resultsIndexModels": [
        {
          "sqlIndex": 0,
          "resultModels": [
            {
              "resultIndex": 0,
              "resultName": "查询结果"
            }
          ]
        }
      ]
    }
  ],
  "fileCharset": "UTF-8",
  "userId": "user123"
}
```

## 核心优势

### 1. 避免重复查询
- **问题**：缓存模式下，当Redis缓存不存在时会重新执行SQL
- **解决**：直接模式一次性执行SQL并导出，避免重复查询

### 2. 支持复杂数据类型
- **问题**：CLOB、BLOB、时间类型等在缓存中可能存储异常
- **解决**：直接处理查询结果，避免缓存序列化问题

### 3. 支持多结果集
- **问题**：存储过程可能返回多个结果集
- **解决**：通过sqlIndex和resultIndex精确定位要导出的结果集

### 4. 更好的性能
- **减少内存使用**：不需要在Redis中存储大量结果集数据
- **减少网络开销**：避免Redis读写操作
- **提升响应速度**：直接执行，无需等待缓存操作

## 技术实现

### 1. 核心流程

```java
// 直接执行模式的核心逻辑
if (message.isDirectMode()) {
    // 直接执行SQL并导出
    transferResult = contextInfo.getTransfer().exportDataByContext(
        monitor,
        processor,
        () -> getDirectWebSQLQueryResultSets(message, contextInfo, monitor),
        // ... 其他参数
    );
} else {
    // 缓存模式（兼容旧版本）
    transferResult = contextInfo.getTransfer().exportDataByContext(
        monitor,
        processor,
        () -> getAsyncWebSQLQueryResultSets(message, contextInfo),
        // ... 其他参数
    );
}
```

### 2. 结果集处理

```java
// 将查询结果转换为导出结果集
for (int sqlIndex = 0; sqlIndex < queryResults.size(); sqlIndex++) {
    WebSQLQueryResult queryResult = queryResults.get(sqlIndex);
    
    if (queryResult.getResultSet() != null && !queryResult.getResultSet().isEmpty()) {
        for (int resultIndex = 0; resultIndex < queryResult.getResultSet().size(); resultIndex++) {
            WebSQLQueryResultSet resultSet = queryResult.getResultSet().get(resultIndex);
            
            // 设置结果集名称和索引
            String resultName = String.format("SQL[%d]_结果集[%d]", sqlIndex, resultIndex);
            resultSet.setResultName(resultName);
            resultSet.setSqlIndex(sqlIndex);
            resultSet.setIndex(resultIndex);
            
            resultSets.add(resultSet);
        }
    }
}
```

## 最佳实践

### 1. 选择合适的导出模式

- **直接模式**：推荐用于新的导出需求，特别是：
  - 包含CLOB、BLOB、时间类型的查询
  - 大数据量导出
  - 存储过程调用
  - 对性能要求较高的场景

- **缓存模式**：适用于：
  - 已有的导出功能（保持兼容性）
  - 需要多次导出相同结果集的场景
  - 结果集较小且查询耗时较长的场景

### 2. 参数配置建议

```json
{
  "resultFormat": {
    "maxRows": 50000,        // 根据实际需求调整最大行数
    "fetchSize": 2000        // 适当的批次大小，平衡内存和性能
  },
  "isErrorContinue": false,  // 建议设为false，遇到错误立即停止
  "exportDesensitize": true, // 根据数据敏感性决定是否脱敏
  "splitFileSize": 100       // 大文件自动拆分，单位MB
}
```

### 3. 错误处理

```java
try {
    message.validate(); // 验证参数
    WebAsyncTaskInfo taskInfo = executeService.asyncSqlExport(message);
    return Result.success(taskInfo);
} catch (IllegalArgumentException e) {
    // 参数验证失败
    return Result.error("参数错误: " + e.getMessage());
} catch (ServiceException e) {
    // 业务逻辑错误
    return Result.error("执行失败: " + e.getMessage());
}
```

## 兼容性说明

### 1. 向后兼容
- 原有的缓存模式完全保持不变
- 现有的API调用无需修改
- 默认使用缓存模式，确保兼容性

### 2. 迁移建议
- 新功能建议使用直接模式
- 现有功能可逐步迁移到直接模式
- 可以通过配置开关控制默认模式

### 3. 性能对比

| 场景 | 缓存模式 | 直接模式 | 建议 |
|------|----------|----------|------|
| 小结果集（<1000行） | 较快 | 快 | 两者皆可 |
| 大结果集（>10000行） | 慢 | 快 | 直接模式 |
| 包含CLOB/BLOB | 可能失败 | 正常 | 直接模式 |
| 多结果集 | 复杂 | 简单 | 直接模式 |
| 重复导出 | 快 | 中等 | 缓存模式 |

## 监控和日志

### 1. 关键日志
- SQL执行时间
- 结果集大小
- 导出文件大小
- 错误信息

### 2. 性能指标
- 平均执行时间
- 内存使用峰值
- 并发导出数量
- 成功率统计

## 总结

直接执行模式的引入有效解决了execute-export接口中的重复查询问题，提升了系统性能和稳定性。建议在新的导出需求中优先使用直接模式，同时保持对现有功能的兼容性支持。
