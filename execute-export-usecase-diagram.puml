@startuml execute-export用例图
!theme plain
title Summer数据库管理工具 - execute-export接口用例图
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle
skinparam usecase {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
    FontSize 12
}
skinparam actor {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    FontSize 12
}
skinparam package {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
    FontSize 14
}

' 定义参与者
actor "数据分析师" as DataAnalyst
actor "系统管理员" as Admin
actor "审核员" as Reviewer
actor "外部系统" as ExternalSystem

' 定义系统边界
package "Summer数据库管理系统" {
    
    ' 导出管理用例组
    package "导出管理" as ExportManagement {
        usecase "执行SQL导出" as UC_ExecuteExport
        usecase "选择导出格式" as UC_SelectFormat
        usecase "配置导出参数" as UC_ConfigParams
        usecase "设置文件编码" as UC_SetEncoding
        usecase "配置分隔符" as UC_SetDelimiter
        usecase "添加水印" as UC_AddWatermark
        usecase "文件加密" as UC_EncryptFile
        usecase "脱敏导出" as UC_DesensitizeExport
        usecase "分页导出" as UC_PagedExport
        usecase "大文件拆分" as UC_SplitLargeFile
        usecase "自定义文件名" as UC_CustomFileName
    }
    
    ' 任务管理用例组
    package "任务管理" as TaskManagement {
        usecase "创建异步任务" as UC_CreateAsyncTask
        usecase "监控任务进度" as UC_MonitorProgress
        usecase "查询任务状态" as UC_QueryTaskStatus
        usecase "获取任务结果" as UC_GetTaskResult
        usecase "取消任务执行" as UC_CancelTask
        usecase "任务错误处理" as UC_HandleTaskError
        usecase "任务调度管理" as UC_TaskScheduling
    }
    
    ' 数据处理用例组
    package "数据处理" as DataProcessing {
        usecase "获取SQL结果集" as UC_GetResultSet
        usecase "数据格式转换" as UC_DataTransform
        usecase "数据验证" as UC_ValidateData
        usecase "数据脱敏处理" as UC_DataMasking
        usecase "结果集分页" as UC_ResultPaging
        usecase "数据压缩" as UC_CompressData
    }
    
    ' 文件管理用例组
    package "文件管理" as FileManagement {
        usecase "生成导出文件" as UC_GenerateFile
        usecase "文件上传存储" as UC_UploadFile
        usecase "文件下载" as UC_DownloadFile
        usecase "文件删除" as UC_DeleteFile
        usecase "文件预览" as UC_PreviewFile
        usecase "文件压缩打包" as UC_ZipFiles
    }
    
    ' 会话管理用例组
    package "会话管理" as SessionManagement {
        usecase "打开数据库连接" as UC_OpenConnection
        usecase "管理会话状态" as UC_ManageSession
        usecase "连接心跳检测" as UC_Heartbeat
        usecase "关闭会话连接" as UC_CloseSession
        usecase "连接池管理" as UC_ConnectionPool
    }
    
    ' 安全管理用例组
    package "安全管理" as SecurityManagement {
        usecase "权限验证" as UC_AuthorizeUser
        usecase "访问控制" as UC_AccessControl
        usecase "操作审计" as UC_AuditOperation
        usecase "敏感数据保护" as UC_ProtectSensitiveData
        usecase "安全日志记录" as UC_SecurityLogging
    }
    
    ' 通知管理用例组
    package "通知管理" as NotificationManagement {
        usecase "发送完成通知" as UC_SendNotification
        usecase "邮件通知" as UC_EmailNotification
        usecase "系统消息推送" as UC_SystemMessage
        usecase "告警处理" as UC_AlertHandling
    }
    
    ' 系统管理用例组
    package "系统管理" as SystemManagement {
        usecase "配置管理" as UC_ConfigManagement
        usecase "性能监控" as UC_PerformanceMonitor
        usecase "错误日志记录" as UC_ErrorLogging
        usecase "系统健康检查" as UC_HealthCheck
        usecase "资源管理" as UC_ResourceManagement
    }
}

' 参与者与用例的关系
DataAnalyst --> UC_ExecuteExport : 发起导出请求
DataAnalyst --> UC_SelectFormat : 选择导出格式
DataAnalyst --> UC_ConfigParams : 配置导出参数
DataAnalyst --> UC_MonitorProgress : 监控导出进度
DataAnalyst --> UC_QueryTaskStatus : 查询任务状态
DataAnalyst --> UC_GetTaskResult : 获取导出结果
DataAnalyst --> UC_DownloadFile : 下载导出文件
DataAnalyst --> UC_CancelTask : 取消导出任务
DataAnalyst --> UC_PreviewFile : 预览导出文件

Admin --> UC_ConfigManagement : 系统配置管理
Admin --> UC_PerformanceMonitor : 性能监控
Admin --> UC_ConnectionPool : 连接池管理
Admin --> UC_ResourceManagement : 资源管理
Admin --> UC_HealthCheck : 系统健康检查
Admin --> UC_TaskScheduling : 任务调度管理

Reviewer --> UC_AuditOperation : 审计操作记录
Reviewer --> UC_SecurityLogging : 查看安全日志
Reviewer --> UC_AccessControl : 访问控制管理

ExternalSystem --> UC_EmailNotification : 邮件服务
ExternalSystem --> UC_SystemMessage : 消息推送服务
ExternalSystem --> UC_AlertHandling : 告警处理

' 用例之间的包含关系 (include)
UC_ExecuteExport ..> UC_AuthorizeUser : <<include>>
UC_ExecuteExport ..> UC_CreateAsyncTask : <<include>>
UC_ExecuteExport ..> UC_GetResultSet : <<include>>
UC_ExecuteExport ..> UC_DataTransform : <<include>>
UC_ExecuteExport ..> UC_GenerateFile : <<include>>
UC_ExecuteExport ..> UC_UploadFile : <<include>>
UC_ExecuteExport ..> UC_SendNotification : <<include>>

UC_CreateAsyncTask ..> UC_ManageSession : <<include>>
UC_CreateAsyncTask ..> UC_TaskScheduling : <<include>>

UC_GetResultSet ..> UC_OpenConnection : <<include>>
UC_GetResultSet ..> UC_ValidateData : <<include>>

UC_DataTransform ..> UC_DataMasking : <<include>>
UC_DataTransform ..> UC_ResultPaging : <<include>>

UC_GenerateFile ..> UC_CompressData : <<include>>
UC_GenerateFile ..> UC_ZipFiles : <<include>>

UC_MonitorProgress ..> UC_QueryTaskStatus : <<include>>

' 用例之间的扩展关系 (extend)
UC_DesensitizeExport ..> UC_ExecuteExport : <<extend>>
UC_PagedExport ..> UC_ExecuteExport : <<extend>>
UC_SplitLargeFile ..> UC_ExecuteExport : <<extend>>
UC_AddWatermark ..> UC_GenerateFile : <<extend>>
UC_EncryptFile ..> UC_GenerateFile : <<extend>>
UC_CustomFileName ..> UC_GenerateFile : <<extend>>

UC_SetEncoding ..> UC_ConfigParams : <<extend>>
UC_SetDelimiter ..> UC_ConfigParams : <<extend>>

UC_EmailNotification ..> UC_SendNotification : <<extend>>
UC_SystemMessage ..> UC_SendNotification : <<extend>>

UC_HandleTaskError ..> UC_MonitorProgress : <<extend>>
UC_CancelTask ..> UC_MonitorProgress : <<extend>>

UC_ProtectSensitiveData ..> UC_DataMasking : <<extend>>
UC_AuditOperation ..> UC_AuthorizeUser : <<extend>>

UC_Heartbeat ..> UC_ManageSession : <<extend>>
UC_CloseSession ..> UC_ManageSession : <<extend>>

UC_ErrorLogging ..> UC_HandleTaskError : <<extend>>
UC_AlertHandling ..> UC_HandleTaskError : <<extend>>

UC_DeleteFile ..> UC_FileManagement : <<extend>>
UC_PreviewFile ..> UC_FileManagement : <<extend>>

' 注释说明
note right of UC_ExecuteExport
  核心用例：处理SQL结果集导出请求
  支持多种格式：XLSX、CSV、JSON、XML等
  支持异步执行和进度监控
end note

note right of UC_DesensitizeExport
  扩展功能：对敏感数据进行脱敏处理
  根据配置规则自动识别和处理敏感字段
end note

note right of UC_SplitLargeFile
  扩展功能：大文件自动拆分
  当文件超过指定大小时自动拆分为多个文件
end note

note bottom of TaskManagement
  任务管理支持异步执行
  提供完整的任务生命周期管理
  包括创建、监控、取消、错误处理
end note

@enduml
