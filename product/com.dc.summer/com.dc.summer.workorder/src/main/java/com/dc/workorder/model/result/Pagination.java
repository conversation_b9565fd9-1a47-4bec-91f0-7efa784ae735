package com.dc.workorder.model.result;

import lombok.Data;

@Data
public class Pagination {

    private Integer page = 0;
    private Integer pages;
    private Integer per_page;
    private Integer total;


    public static Pagination make(Integer offset, Integer limit, Integer total) {
        Pagination pagination = new Pagination();
        pagination.setTotal(total);
        if (null == limit) {
            pagination.setPer_page(20);
        } else {
            pagination.setPer_page(limit);
        }
        Double pages = Math.ceil(total / pagination.getPer_page());
        pagination.setPages(pages.intValue());
        //
        if (null == offset) {
            offset = 0;
        }
        Double page = Math.ceil(offset / pagination.getPer_page());
        pagination.setPage(page.intValue() == 0 ? 1 : page.intValue());
        return pagination;
    }
}
