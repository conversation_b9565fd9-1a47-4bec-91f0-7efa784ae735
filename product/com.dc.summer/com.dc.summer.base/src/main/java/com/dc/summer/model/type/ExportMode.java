package com.dc.summer.model.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导出模式枚举
 * 
 * <AUTHOR> Team
 */
@Getter
@AllArgsConstructor
public enum ExportMode {

    /**
     * 缓存模式 - 从Redis缓存获取已执行的SQL结果集
     * 兼容旧版本的导出方式
     */
    CACHE("从缓存获取结果集"),

    /**
     * 直接模式 - 直接执行SQL并导出结果
     * 新版本的导出方式，避免重复执行SQL
     */
    DIRECT("直接执行SQL并导出");

    private final String description;

    /**
     * 根据名称获取导出模式
     * 
     * @param name 模式名称
     * @return 导出模式
     */
    public static ExportMode of(String name) {
        if (name == null) {
            return CACHE; // 默认使用缓存模式保持兼容性
        }
        
        for (ExportMode mode : values()) {
            if (mode.name().equalsIgnoreCase(name)) {
                return mode;
            }
        }
        
        return CACHE; // 未知模式默认使用缓存模式
    }

    /**
     * 是否为缓存模式
     * 
     * @return true if cache mode
     */
    public boolean isCache() {
        return this == CACHE;
    }

    /**
     * 是否为直接模式
     * 
     * @return true if direct mode
     */
    public boolean isDirect() {
        return this == DIRECT;
    }
}
