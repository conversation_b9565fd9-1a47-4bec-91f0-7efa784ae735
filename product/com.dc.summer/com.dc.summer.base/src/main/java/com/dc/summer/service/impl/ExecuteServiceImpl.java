package com.dc.summer.service.impl;

import com.dc.config.ApiConfig;
import com.dc.repository.mysql.mapper.RcSqlMapper;
import com.dc.repository.mysql.utils.MyBatisUtils;
import com.dc.springboot.core.model.chain.Chain;
import com.dc.springboot.core.model.chain.ChainBuilder;
import com.dc.springboot.core.model.chain.CheckChainRunner;
import com.dc.springboot.core.model.chain.StreamChainRunner;
import com.dc.springboot.core.model.data.ResultFormat;
import com.dc.springboot.core.model.exception.ResultException;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.execution.*;
import com.dc.springboot.core.model.result.*;
import com.dc.springboot.core.model.script.ParseScriptMessage;
import com.dc.springboot.core.model.type.ConnectionPatternType;
import com.dc.springboot.core.model.type.OriginType;
import com.dc.springboot.core.model.type.ReviewType;
import com.dc.springboot.core.model.type.SqlExecuteStatus;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.data.transfer.registry.DataTransferProcessorDescriptor;
import com.dc.summer.data.transfer.registry.DataTransferRegistry;
import com.dc.summer.exec.handler.StatementHandler;
import com.dc.summer.model.DBPDataSource;
import com.dc.summer.model.chain.impl.*;
import com.dc.summer.model.data.DynamicSqlInfo;
import com.dc.summer.model.data.SqlCheckParam;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.data.generator.SQLGeneratorResultSetController;
import com.dc.summer.model.data.message.*;
import com.dc.summer.model.data.model.RecycleModel;
import com.dc.summer.model.data.model.ResultModel;
import com.dc.summer.model.data.model.ResultsIndexModel;
import com.dc.summer.model.data.model.SqlExportModel;
import com.dc.summer.model.data.result.DynamicSqlResult;
import com.dc.summer.model.exec.DBCExecutionPurpose;
import com.dc.summer.model.impl.jdbc.JDBCDataSource;
import com.dc.summer.model.impl.jdbc.JDBCExecutionContext;
import com.dc.summer.model.runtime.DBRProgressMonitor;
import com.dc.summer.model.runtime.LoggingProgressMonitor;
import com.dc.summer.model.sql.SQLQueryType;
import com.dc.summer.model.sql.generator.SQLGenerator;
import com.dc.summer.model.sql.registry.SQLGeneratorConfigurationRegistry;
import com.dc.summer.model.sql.registry.SQLGeneratorDescriptor;
import com.dc.summer.model.type.WebAsyncTaskType;
import com.dc.summer.model.type.WebDataFormat;
import com.dc.summer.service.ExecuteService;
import com.dc.summer.service.MessageService;
import com.dc.springboot.core.service.ParserService;
import com.dc.summer.service.ResultService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.springboot.core.model.script.WebSQLQueryInfo;
import com.dc.springboot.core.model.script.WebSQLScriptInfo;
import com.dc.summer.service.transfer.WebDataTransferName;
import com.dc.type.DatabaseType;
import com.dc.utils.verification.VerificationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class ExecuteServiceImpl implements ExecuteService {

    @Resource
    private SummerConfig config;

    @Resource
    private MessageService messageService;

    @Resource
    private RcSqlMapper rcSqlMapper;

    @Resource
    private SqlCheckServiceImpl sqlCheckService;

    @Resource
    private SqlSessionTemplate sqlSessionTemplate;

    @Resource
    private ResultService resultService;

    @Resource
    private List<ParserService> parserServiceList;

    @Resource
    private ParserService parserServiceImpl;

    @Override
    public WebSQLExecuteInfo executeBindingStatement(BindingExecuteMessage message) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());

        WebSQLExecuteInfo webSQLExecuteInfo = new WebSQLExecuteInfo();

        try {

            WebSQLResultsInfo previousResults = contextInfo.getPreviousResults(message.getSingleExecuteModel().getSql());

            LoggingProgressMonitor monitor = new LoggingProgressMonitor();

            List<WebSQLQueryResult> queryResults = null;
            if (previousResults == null) {
                queryResults = contextInfo.getProcessor().processQuery(
                        monitor,
                        Collections.singletonList(message.getSingleExecuteModel().getBatchExecuteModel()),
                        WebDataFormat.resultset,
                        new ResultFormat(),
                        DBCExecutionPurpose.USER,
                        false,
                        validExecuteModel -> ChainBuilder.none(),
                        validExecuteModel -> ChainBuilder.none(),
                        validExecuteModel -> ChainBuilder.none(),
                        false,
                        true,
                        null);
                WebSQLQueryResult webSQLQueryResult = queryResults.get(0);
                String resultId = webSQLQueryResult.getResultSet().get(0).getResultId();
                previousResults = contextInfo.getResults(resultId);
            }

            List<WebSQLQueryResult> updateResults = contextInfo.getProcessor().updateResultsDataBatch(
                    monitor,
                    previousResults,
                    Collections.singletonList(message.getUpdatedRows()),
                    false,
                    validExecuteModel -> ChainBuilder.none(),
                    validExecuteModel -> ChainBuilder.none(),
                    validExecuteModel -> ChainBuilder.none(),
                    null,
                    false);
            webSQLExecuteInfo.addQueryResults(updateResults);
            webSQLExecuteInfo.addQueryResults(queryResults);

        } finally {
            if (StringUtils.isBlank(message.getToken())) {
                contextInfo.close();
            }
            if (contextInfo.isClosed()) {
                contextInfo.close();
                WebSQLContextInfo.getSimpleContext(message.getToken());
            }
        }

        return webSQLExecuteInfo;

    }

    @Override
    public WebSQLExecuteInfo syncExecuteSingleQuery(SingleSyncExecuteMessage message, boolean autoOpenSession) {

        WebSQLContextInfo contextInfo = autoOpenSession ?
                WebSQLContextInfo.getOrOpenContext(message.getConnectionMessage()) :
                WebSQLContextInfo.getSimpleContext(message.getToken());

        try {

            List<WebSQLQueryResult> queryResults = contextInfo.getProcessor().processQuery(
                    new LoggingProgressMonitor(),
                    Collections.singletonList(message.getSingleExecuteModel().getBatchExecuteModel()),
                    WebDataFormat.resultset,
                    new ResultFormat(),
                    DBCExecutionPurpose.USER,
                    false,
                    validExecuteModel -> ChainBuilder.none(),
                    validExecuteModel -> ChainBuilder.none(),
                    validExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new PrivilegesNoteChain(contextInfo, message.getSingleExecuteModel().getPrivilegeModel(), null)),
                    false,
                    false,
                    message.getTokenConfig());
            return new WebSQLExecuteInfo().addQueryResults(queryResults);

        } finally {
            if (StringUtils.isBlank(message.getToken())) {
                contextInfo.close();
            }
            if (contextInfo.isClosed()) {
                contextInfo.close();
                WebSQLContextInfo.getSimpleContext(message.getToken());
            }
        }

    }

    @Override
    public WebSQLExecuteInfo syncExecuteBatchQuery(BatchSyncExecuteMessage message) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.openExecuteContext(
                message.generateConnectionTokenMessage(message.getTokenConfig(), true, true, "Sync Execute"));

        final LoggingProgressMonitor monitor = new LoggingProgressMonitor();

        List<WebSQLQueryResult> queryResults = contextInfo.getProcessor().processQuery(
                monitor,
                message.getBatchExecuteModels(),
                WebDataFormat.resultset,
                new ResultFormat(),
                DBCExecutionPurpose.USER,
                message.isErrorContinue(),
                validExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                        .addChain(new TransmitSyncBatchChain(validExecuteModel)),
                validExecuteModel -> ChainBuilder.build(new CheckChainRunner<WebSQLQueryResult>())
                        .addChain(new PermissionChain(validExecuteModel.getPermissionModel()))
                        .addChain(new FrequencyChain(
                                message.getVisitFrequencyDataModel(),
                                contextInfo.getDataSourceContainer().getConnectionId(),
                                validExecuteModel.getOperation(),
                                validExecuteModel.getUserId(),
                                validExecuteModel.getOrigin(),
                                validExecuteModel.isNotRecordFrequency()))
                        .addChain(new BackupModifyChain(
                                monitor,
                                contextInfo,
                                validExecuteModel.getOperation(),
                                validExecuteModel.getBackupModel(),
                                validExecuteModel.getUserId(),
                                sqlSessionTemplate,
                                validExecuteModel.getSql())),
                validExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                        .addChainIfConditionPass(validExecuteModel.isNeedRecordInPrivateTable(),
                                () -> new PrivateTableChain(validExecuteModel, contextInfo, validExecuteModel.getUserId()))
                        .addChain(new BackupInsertChain(
                                monitor,
                                contextInfo,
                                validExecuteModel.getOperation(),
                                validExecuteModel.getBackupModel(),
                                validExecuteModel.getUserId(),
                                sqlSessionTemplate,
                                validExecuteModel.getSql()))
                        .addChain(new BackupOracleDropChain(
                                monitor,
                                contextInfo,
                                validExecuteModel.getBackupModel(),
                                validExecuteModel.getSql(),
                                validExecuteModel.getUserId(),
                                validExecuteModel.getOperation()))
                        .addChain(new BackupTransactionBatchChain(
                                monitor,
                                contextInfo,
                                validExecuteModel.getBackupModel(),
                                validExecuteModel.getSqlHistory().getOrigin(),
                                sqlSessionTemplate,
                                validExecuteModel.getOperation(),
                                validExecuteModel.getUserId(),
                                contextInfo.getAutoCommit()))
                        .addChain(new PrivilegesNoteChain(contextInfo, validExecuteModel.getPrivilegeModel(), validExecuteModel.getPrivilegeExpire())),
                false,
                false,
                null);

        WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResults(queryResults);
        messageService.printLogMessage(contextInfo, queryResults, contextInfo.getToken(), null);

        contextInfo.close();

        return executeInfo;
    }

    @Override
    public WebAsyncTaskInfo asyncExecuteSingleQuery(SingleAsyncExecuteMessage message) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(message.getToken(), message.getTokenConfig());
        AtomicBoolean firstExecute = new AtomicBoolean(true);

        return contextInfo.createAsyncTask("Execute Single Query", (taskId, monitor) -> {
            List<WebSQLQueryResult> queryResults = contextInfo.getProcessor().processQuery(
                    monitor,
                    message.getValidExecuteModels(),
                    WebDataFormat.resultset,
                    message.getResultFormat(),
                    DBCExecutionPurpose.getById(contextInfo.getPurpose()),
                    true,
                    validExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new TransmitAsyncSingleChain(message, contextInfo.getDataSource())),
                    validExecuteModel -> buildAsyncExecuteCheckChain(
                            validExecuteModel,
                            message.getVisitFrequencyDataModel(),
                            contextInfo,
                            monitor,
                            firstExecute.get(),
                            message.getUserId()),
                    validExecuteModel -> buildAsyncExecuteStreamChain(
                            validExecuteModel,
                            message.isNeedReview(),
                            contextInfo,
                            monitor,
                            message.getUserId(),
                            message.getReviewType() == null ? ReviewType.OTHER.getValue() : message.getReviewType(),
                            message.getDataSize()),
                    true,
                    true,
                    message.getTokenConfig());
            try {
                WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResults(queryResults);
                resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
                if (firstExecute.get()) {
                    if (ConnectionPatternType.of(message.getSqlHistory().getConnectionPattern()).existsSecurityCollaboration()) {
                        int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                        messageService.sendAlertMessage(message, executeInfo, securityRuleSetId);
                    }
                    messageService.printLogMessage(contextInfo, queryResults, message.getToken(), taskId);
                }
            } finally {
                firstExecute.set(false);
            }
        });
    }

    @Override
    public WebAsyncTaskInfo asyncExecuteBatchQuery(BatchAsyncExecuteMessage message) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(message.getToken(), message.getTokenConfig());

        AtomicBoolean firstExecute = new AtomicBoolean(true);
        AtomicBoolean isCheckSerialNumber = new AtomicBoolean(true);

        return contextInfo.createAsyncTask("Execute Batch Query", (taskId, monitor) -> {

            StaticEnvChain.Builder staticEnvChainBuilder = new StaticEnvChain.Builder(
                    message.getVisitFrequencyDataModel(),
                    message.getExecuteEvents(),
                    contextInfo);

            StaticBatchResultChain staticBatchResultChain = new StaticBatchResultChain(
                    monitor.getAsyncTask(),
                    contextInfo,
                    monitor);

            if (monitor.getTaskName() != null) {
                IntStream.range(0, message.getBatchExecuteModels().size())
                                .forEach(value -> message.getBatchExecuteModels().get(value).setConfirm(value == Integer.parseInt(monitor.getTaskName())));
            }

            List<WebSQLQueryResult> queryResults = contextInfo.getProcessor().processQuery(
                    monitor,
                    message.getBatchExecuteModels(),
                    WebDataFormat.resultset,
                    message.getResultFormat(),
                    DBCExecutionPurpose.getById(contextInfo.getPurpose()),
                    message.isErrorContinue(),
                    validExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(staticEnvChainBuilder.buildChain(
                                    validExecuteModel,
                                    message.getOrderRelevance()))
                            .addChain(new TransmitAsyncBatchChain(
                                    validExecuteModel,
                                    contextInfo.getDataSource()))
                            .addChain(new CrossDatabaseQueryOpenChain(
                                    validExecuteModel.getCrossDatabaseQueryModels(),
                                    contextInfo,
                                    monitor,
                                    message.getExecuteEvents())),
                    validExecuteModel -> buildAsyncExecuteCheckChain(
                            validExecuteModel,
                            message.getVisitFrequencyDataModel(),
                            contextInfo,
                            monitor,
                            isCheckSerialNumber.getAndSet(false),
                            contextInfo.getUser().getUserId()),
                    validExecuteModel -> buildAsyncExecuteStreamChain(
                            validExecuteModel,
                            validExecuteModel.isNeedReview(),
                            contextInfo,
                            monitor,
                            contextInfo.getUser().getUserId(),
                            staticEnvChainBuilder.getReviewType(),
                            staticEnvChainBuilder.getDataSize())
                            .addChain(new CrossDatabaseQueryCloseChain(
                                    validExecuteModel.getCrossDatabaseQueryModels(),
                                    contextInfo,
                                    monitor))
                            .addChain(new ContinueExecutionChain(
                                    message.isErrorContinue(),
                                    monitor.getAsyncTask(),
                                    contextInfo))
                            .addChain(staticBatchResultChain),
                    true,
                    true,
                    message.getTokenConfig());

            try {
                WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResults(queryResults);
                if (firstExecute.get()) {
                    if (ConnectionPatternType.of(message.getBatchExecuteModels().get(0).getSqlHistory().getConnectionPattern()).existsSecurityCollaboration()) {
                        int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                        messageService.sendAlertMessageList(message, executeInfo, securityRuleSetId);
                    }
                    messageService.printLogMessage(contextInfo, queryResults, message.getToken(), taskId);
                }
            } finally {
                firstExecute.set(false);
            }
        });
    }

    @Override
    public WebAsyncTaskInfo asyncExecuteUpdate(SqlUpdateMessage message) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(message.getToken(), message.getTokenConfig());

        WebSQLResultsInfo resultsInfo = contextInfo.getResults(message.getResultId());

        return contextInfo.createAsyncTask("Execute Update", (taskId, monitor) -> {

            List<WebSQLQueryResult> queryResults = contextInfo.getProcessor().updateResultsDataBatch(
                    monitor,
                    resultsInfo,
                    collectRows(message),
                    message.isErrorContinue(),
                    row -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new TransmitUpdateChain(row)),
                    row -> ChainBuilder.build(new CheckChainRunner<WebSQLQueryResult>())
                            .addChain(new PermissionChain(row.getPermissionModel()))
                            .addChain(new FrequencyChain(
                                    message.getVisitFrequencyDataModel(),
                                    contextInfo.getDataSourceContainer().getConnectionId(),
                                    row.getOperation(),
                                    contextInfo.getUser().getUserId(),
                                    OriginType.BROWSER.getValue(),
                                    false))
                            .addChain(new BackupModifyChain(
                                    monitor,
                                    contextInfo,
                                    row.getOperation(),
                                    row.getBackupModel(),
                                    contextInfo.getUser().getUserId(),
                                    sqlSessionTemplate,
                                    row.getSqlRecord().getSql())),
                    row -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new BackupInsertChain(
                                    monitor,
                                    contextInfo,
                                    row.getOperation(),
                                    row.getBackupModel(),
                                    contextInfo.getUser().getUserId(),
                                    sqlSessionTemplate,
                                    row.getSqlRecord().getSql()))
                            .addChain(new BackupTransactionBatchChain(
                                    monitor,
                                    contextInfo,
                                    row.getBackupModel(),
                                    OriginType.BROWSER.getValue(),
                                    sqlSessionTemplate,
                                    row.getOperation(),
                                    contextInfo.getUser().getUserId(),
                                    contextInfo.getAutoCommit()))
                            .addChain(new ReviewChain(
                                    row.getOperation(),
                                    message.getToken(),
                                    row.isNeedReview(),
                                    contextInfo.getAutoCommit(),
                                    row.getSqlRecord().getSql(),
                                    contextInfo.getReviewConfig() != null && StringUtils.isNotBlank(contextInfo.getReviewConfig().getTrigger()) ? ReviewType.ofByName(contextInfo.getReviewConfig().getTrigger()).getValue() : ReviewType.OTHER.getValue())),
                    message.getSqlHistory(),
                    true);

            WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo().addQueryResults(queryResults);
            resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
            messageService.printLogMessage(contextInfo, queryResults, message.getToken(), taskId);
        });
    }

    @Override
    public WebAsyncTaskInfo asyncJobExport(JobExportMessage message) {

        if (message.isExport()) {
            VerificationUtils.byFunction(message)
                    .face(JobExportMessage::getExportType).notNull()
                    .face(JobExportMessage::getLogId).notNull();
            message.getBatchExecuteModels()
                    .forEach(batchExecuteModel -> batchExecuteModel.setLimit(1));
        } else {
            message.getBatchExecuteModels().stream()
                    .filter(batchExecuteModel -> SQLQueryType.of(batchExecuteModel.getOperation()).isSelect())
                    .forEach(batchExecuteModel -> batchExecuteModel.setConfirm(false));
        }

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(message.getToken(), message.getTokenConfig());

        return contextInfo.createAsyncTask("Job Export", (taskId, monitor) -> {

            StaticBatchResultChain staticBatchResultChain = new StaticBatchResultChain(
                    monitor.getAsyncTask(),
                    contextInfo,
                    monitor);

            SqlExportMessage sqlExportMessage = SqlExportMessage.buildInstance(message);

            contextInfo.getProcessor().processQuery(
                    monitor,
                    message.getBatchExecuteModels(),
                    WebDataFormat.resultset,
                    message.getResultFormat(),
                    DBCExecutionPurpose.getById(contextInfo.getPurpose()),
                    message.isErrorContinue(),
                    batchExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new TransmitJobExportChain(batchExecuteModel, message.getUserId())),
                    batchExecuteModel -> ChainBuilder.build(new CheckChainRunner<WebSQLQueryResult>())
                            .addChain(new BackupModifyChain(monitor,
                                    contextInfo,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getUserId(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getSql())),
                    batchExecuteModel -> ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                            .addChain(new BackupInsertChain(
                                    monitor,
                                    contextInfo,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getUserId(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getSql()))
                            .addChain(new BackupOracleDropChain(
                                    monitor,
                                    contextInfo,
                                    batchExecuteModel.getBackupModel(),
                                    batchExecuteModel.getSql(),
                                    batchExecuteModel.getUserId(),
                                    batchExecuteModel.getOperation()))
                            .addChain(new BackupTransactionBatchChain(
                                    monitor,
                                    contextInfo,
                                    batchExecuteModel.getBackupModel(),
                                    OriginType.JOB_EXECUTE.getValue(),
                                    sqlSessionTemplate,
                                    batchExecuteModel.getOperation(),
                                    batchExecuteModel.getUserId(),
                                    contextInfo.getAutoCommit()))
                            .addChain(staticBatchResultChain)
                            .addChain(new JobExportChain(
                                    message,
                                    sqlExportMessage,
                                    contextInfo,
                                    monitor))
                            .addChain(new PrivilegesNoteChain(contextInfo, batchExecuteModel.getPrivilegeModel(), batchExecuteModel.getPrivilegeExpire())),
                    false,
                    true,
                    message.getTokenConfig());

        });
    }

    @Override
    public WebAsyncTaskInfo asyncSqlExport(SqlExportMessage message) {
        // 验证消息参数
        message.validate();

        WebSQLContextInfo contextInfo = WebSQLContextInfo.getAndSetContext(message.getToken(), message.getTokenConfig());
        AtomicBoolean firstExecute = new AtomicBoolean(true);

        return contextInfo.createAsyncTask("Sql Export", (taskId, monitor) -> {
            DataTransferProcessorDescriptor processor = DataTransferRegistry.getInstance()
                    .getProcessor(message.getExportType().getProcessorFullId());

            WebSQLTransferResult transferResult;

            if (message.isDirectMode()) {
                // 直接执行模式：直接执行SQL并导出，避免重复查询
                transferResult = contextInfo.getTransfer().exportDataByContext(
                        monitor,
                        processor,
                        () -> getDirectWebSQLQueryResultSets(message, contextInfo, monitor),
                        message,
                        message.getUserId(),
                        resultSets -> ChainBuilder.build(new StreamChainRunner<WebSQLTransferResult>())
                                .addChain(new ToastChain(resultSets)),
                        new WebDataTransferName.ResultSetName(),
                        StringUtils.isNotBlank(message.getExportFileName()) ? new WebDataTransferName.CustomResultSetName(message.getExportFileName()) : new WebDataTransferName.ResultSetName(),
                        true,
                        true,
                        false,
                        message.getExportFileName(),
                        message.getTokenConfig(),
                        OriginType.BROWSER);
            } else {
                // 缓存模式：从Redis缓存获取结果集（兼容旧版本）
                transferResult = contextInfo.getTransfer().exportDataByContext(
                        monitor,
                        processor,
                        () -> getAsyncWebSQLQueryResultSets(message, contextInfo),
                        message,
                        message.getUserId(),
                        resultSets -> ChainBuilder.build(new StreamChainRunner<WebSQLTransferResult>())
                                .addChain(new ToastChain(resultSets)),
                        new WebDataTransferName.ResultSetName(),
                        StringUtils.isNotBlank(message.getExportFileName()) ? new WebDataTransferName.CustomResultSetName(message.getExportFileName()) : new WebDataTransferName.ResultSetName(),
                        true,
                        true,
                        false,
                        message.getExportFileName(),
                        message.getTokenConfig(),
                        OriginType.BROWSER);
            }

            try {
                WebSQLExecuteInfo executeInfo = new WebSQLExecuteInfo();
                executeInfo.setTransferResult(transferResult);
                resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
                if (firstExecute.get()) {
                    if (ConnectionPatternType.of(message.getExecuteEvent().getConnectionPattern()).existsSecurityCollaboration()) {
                        int securityRuleSetId = contextInfo.getConnection().getSecurityRuleSetId();
                        //本地导出，告警，调用php接口
                        messageService.sendAlertMessage(message, executeInfo, securityRuleSetId);
                    }
                }
            } finally {
                firstExecute.set(false);
            }
        });
    }

    @Override
    public WebAsyncTaskInfo asyncPreviewBlob(PreviewBlobMessage message) {
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());
        String url = config.getPath().getDcBackend() + ApiConfig.LOBS.getPath();
        return contextInfo.createAsyncTask("Preview Blob", (taskId, monitor) -> {
            WebSQLExecuteInfo executeInfo = contextInfo.getProcessor().blobDataPreview(
                    monitor,
                    message.getPreviewBlobModel(),
                    message.getDataFormat(),
                    url);
            resultService.saveStringValue(taskId, executeInfo, contextInfo.getExpirationTime());
        });
    }

    @Override
    public WebAsyncTaskInfo asyncCheckRecycle(CheckRecycleMessage message) {
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());
        return contextInfo.createAsyncTask("Check Recycle", (taskId, monitor) -> {
                    final RecycleModel recycleModel = message.getRecycleModel();
                    try {
                        List<Long> sqlIdList;
                        if (StringUtils.isNotBlank(recycleModel.getSqlIdList())) {
                            sqlIdList = Arrays.stream(recycleModel.getSqlIdList().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                        } else {
                            sqlIdList = rcSqlMapper.getRcSqlIdByBatchId(recycleModel.getBatchId());
                        }
                        SqlCheckParam sqlCheckParam = new SqlCheckParam();
                        sqlCheckParam.setHas_super_manager(recycleModel.isHas_super_manager());
                        sqlCheckParam.setUser_id(recycleModel.getUserId());
                        sqlCheckParam.setRedis_key(recycleModel.getRequestId());
                        sqlCheckParam.setEnable_desensite_type(recycleModel.getEnableDesensiteType());
                        sqlCheckParam.setSqlList(sqlIdList);
                        sqlCheckParam.setBatchId(recycleModel.getBatchId());
                        sqlCheckParam.setContextInfo(contextInfo);
                        sqlCheckParam.setMonitor(monitor);
                        sqlCheckParam.setWrongSchemaIdList(recycleModel.getWrongSchemaIdList());
                        sqlCheckService.checkTableRecords(sqlCheckParam);
                    } catch (Exception e) {
                        log.error("检查恢复失败！", e);
                    } finally {
                        contextInfo.close();
                    }
                }
        );
    }

    @Override
    public void killConnection(WebSQLContextInfo contextInfo) {
        killConnection(contextInfo, WebAsyncTaskType.CANCELED);
    }

    @Override
    public void interruptConnection(WebSQLContextInfo contextInfo) {
        killConnection(contextInfo, WebAsyncTaskType.INTERRUPTED);
    }

    private void killConnection(WebSQLContextInfo contextInfo, WebAsyncTaskType taskType) {
        try {
            for (WebAsyncTaskInfo asyncTask : contextInfo.getAsyncTasks()) {
                if (!asyncTask.isOver()) {
                    asyncTask.setStatus(taskType);
                }
            }
            DBPDataSource dataSource = contextInfo.getDataSourceContainer().getDataSource();
            if (dataSource instanceof JDBCDataSource) {
                ((JDBCDataSource) dataSource).killConnection(new LoggingProgressMonitor(), (JDBCExecutionContext) contextInfo.getExecutionContext());
            }
        } catch (Exception e) {
            log.error("killConnection 失败，使用 cancel。", e);
            StatementHandler.handle(contextInfo.getToken()).cancel();
        }
    }

    @Override
    public String sqlGenerate(SqlGenerateMessage message) {

        SQLGeneratorResultSetController objects = new SQLGeneratorResultSetController();

        // 内存中取结果信息
        WebSQLContextInfo contextInfo = WebSQLContextInfo.getSimpleContext(message.getToken());
        objects.setDataSource(contextInfo.getDataSource());

        WebSQLResultsInfo resultsInfo = contextInfo.getResults(message.getResultId());
        objects.setResultsInfo(resultsInfo);

        // 缓存中取结果集
        try {
            WebSQLExecuteInfo executeInfo = resultService.getStringValue(message);
            WebSQLQueryResultSet resultSet = executeInfo.getQueryResults().get(message.getSqlIndex()).getResultSet().get(message.getResultIndex());
            objects.setResultSet(resultSet);
        } catch (ResultException e) {
            throw new ServiceException("生成SQL失败，当前执行结果失效，请重新查询", e);
        }

        objects.setRowNumbers(message.getRowNumbers());

        SQLGeneratorConfigurationRegistry registry = SQLGeneratorConfigurationRegistry.getInstance();
        SQLGeneratorDescriptor descriptor = registry.getGenerator(message.getGeneratorId());

        Exception exception;

        try {
            // 构建生成器
            SQLGenerator<Object> generator = descriptor.createGenerator(Collections.singletonList(objects));
            generator.setFullyQualifiedNames(message.isFullyQualifiedNames());
            generator.setCompactSQL(message.isCompactSQL());
            generator.setExcludeAutoGeneratedColumn(message.isExcludeAutoGeneratedColumn());
            generator.setUseCustomDataFormat(message.isUseCustomDataFormat());
            generator.run(new LoggingProgressMonitor());
            return generator.getResult();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            exception = e;
        } catch (Exception e) {
            exception = e;
        }

        throw new ServiceException("Run generator error.", exception);
    }

    @Override
    public DynamicSqlInfo dynamicSql(DynamicSqlMessage message) {

        DynamicSqlInfo dynamicSqlInfo = new DynamicSqlInfo();

        String sqlTemplate = message.getSqlTemplate();
        Map<String, Object> params = message.getParams();

        BoundSql showSql = MyBatisUtils.getShowSql(sqlTemplate, params);

        dynamicSqlInfo.setPreviewSqlList(
                this.parseScript(new ParseScriptMessage(showSql.getSql(), message.getDatabaseType()))
                        .getQueries()
                        .stream()
                        .map(WebSQLQueryInfo::getText)
                        .collect(Collectors.toList()));

        BoundSql bindSql = MyBatisUtils.getBindSql(sqlTemplate, params);
        List<WebSQLQueryInfo> bindQueries = this.parseScript(new ParseScriptMessage(bindSql.getSql(), message.getDatabaseType()))
                .getQueries();

        BoundSql parseSql = MyBatisUtils.getParseSql(sqlTemplate, null);
        List<WebSQLQueryInfo> parseQueries = this.parseScript(new ParseScriptMessage(parseSql.getSql(), message.getDatabaseType()))
                .getQueries();

        int size;

        if ((size = bindQueries.size()) != parseQueries.size()) {
            throw new ServiceException("动态 SQL 解析错误。");
        }

        List<ParameterMapping> parameterMappings = bindSql.getParameterMappings();

        List<Object> totalData = parameterMappings.stream()
                .map(parameterMapping -> params.get(parameterMapping.getProperty()))
                .collect(Collectors.toCollection(LinkedList::new));

        dynamicSqlInfo.setDynamicSqlResults(
                IntStream.range(0, size)
                        .mapToObj(i -> {
                            DynamicSqlResult dynamicSqlResult = new DynamicSqlResult();
                            String bindQuery = bindQueries.get(i).getText();
                            String parseQuery = parseQueries.get(i).getText();

                            dynamicSqlResult.setSql(bindQuery);

                            List<Object> data = new ArrayList<>(totalData.size());

                            int cursor = StringUtils.countMatches(bindQuery, '?') - StringUtils.countMatches(parseQuery, '?');
                            for (int j = 0; j < cursor; j++) {
                                data.add(totalData.remove(0));
                            }

                            dynamicSqlResult.setData(data);

                            return dynamicSqlResult;
                        })
                        .collect(Collectors.toList()));

        return dynamicSqlInfo;
    }

    @Override
    public WebSQLScriptInfo parseScript(ParseScriptMessage message) {

        final String token = message.getToken();
        if (StringUtils.isNotBlank(token)) {
            WebSQLContextInfo.getSimpleContext(token);
        }

        for (ParserService parserService : parserServiceList) {
            if (parserService.supportsThisType(DatabaseType.of(message.getDatabaseType()))) {
                return parserService.parseSqlScript(message);
            }
        }
        return parserServiceImpl.parseSqlScript(message);
    }

    private List<WebSQLResultsBaseRow> collectRows(SqlUpdateMessage message) {
        List<WebSQLResultsUpdateRow> updatedRows = message.getUpdatedRows().stream()
                .map(webSQLResultsUpdateRow -> webSQLResultsUpdateRow.setOperation(SQLQueryType.UPDATE.name()))
                .collect(Collectors.toList());
        List<WebSQLResultsBaseRow> deletedRows = message.getDeletedRows().stream()
                .map(webSQLResultsUpdateRow -> webSQLResultsUpdateRow.setOperation(SQLQueryType.DELETE.name()))
                .collect(Collectors.toList());
        List<WebSQLResultsBaseRow> addedRows = message.getAddedRows().stream()
                .map(webSQLResultsUpdateRow -> webSQLResultsUpdateRow.setOperation(SQLQueryType.INSERT.name()))
                .collect(Collectors.toList());
        List<WebSQLResultsBaseRow> createdRows = message.getCreatedRows().stream()
                .map(webSQLResultsUpdateRow -> webSQLResultsUpdateRow.setOperation(SQLQueryType.CREATE.name()))
                .collect(Collectors.toList());
        List<WebSQLResultsBaseRow> rows = new ArrayList<>(updatedRows.size() + deletedRows.size() + addedRows.size());
        rows.addAll(updatedRows);
        rows.addAll(deletedRows);
        rows.addAll(addedRows);
        rows.addAll(createdRows);
        return rows;
    }

    private List<WebSQLQueryResultSet> getAsyncWebSQLQueryResultSets(SqlExportMessage message, WebSQLContextInfo contextInfo) throws ServiceException {

        List<SqlExportModel> sqlExportModels = message.getSqlExportModels();

        List<WebSQLQueryResultSet> resultSets = new LinkedList<>();

        for (SqlExportModel sqlExportModel : sqlExportModels) {
            String taskId = sqlExportModel.getTaskId();
            TaskResultMessage taskResultMessage = new TaskResultMessage();
            taskResultMessage.setTaskId(taskId);
            taskResultMessage.setToken(message.getToken());

            WebSQLExecuteInfo executeInfo;
            for (ResultsIndexModel resultIndex : sqlExportModel.getResultsIndexModels()) {
                taskResultMessage.setStage(resultIndex.getSqlIndex() + 1);
                try {
                    executeInfo = resultService.getStringValue(taskResultMessage);
                    throwExecuteNotSuccess(executeInfo, 0);
                } catch (Exception e) {
                    try {
                        log.info("获取结果集失败，重新查询: {}", e.getMessage());
                        WebAsyncTaskInfo webAsyncTaskInfo = contextInfo.asyncTaskStatus(taskId, false);
                        webAsyncTaskInfo.exec(String.valueOf(resultIndex.getSqlIndex()));
                        executeInfo = resultService.getStringValue(taskResultMessage);
                    } catch (Exception ex) {
                        log.error("重新查询失败！", ex);
                        throw new ServiceException("请重新查询。");
                    }
                    throwExecuteNotSuccess(executeInfo, 0);
                }
                addResultSet(resultIndex, executeInfo, resultSets, 0);
            }

        }

        return resultSets;
    }

    private static void addResultSet(ResultsIndexModel resultIndex, WebSQLExecuteInfo executeInfo, List<WebSQLQueryResultSet> resultSets, Integer sqlIndex) {
        for (ResultModel resultModel : resultIndex.getResultModels()) {
            WebSQLQueryResultSet resultSet = executeInfo.getQueryResults().get(sqlIndex).getResultSet().get(resultModel.getResultIndex());
            resultSet.setResultName(resultModel.getResultName());
            resultSets.add(resultSet);
        }
    }

    private static void throwExecuteNotSuccess(WebSQLExecuteInfo executeInfo, Integer resultIndex) {
        WebSQLQueryResult webSQLQueryResult = executeInfo.getQueryResults().get(resultIndex);
        if (webSQLQueryResult.getStatus() != SqlExecuteStatus.SUCCESS.getValue()) {
            throw new ServiceException(webSQLQueryResult.getMessage());
        } else if (webSQLQueryResult.getResultSet() == null) {
            throw new ServiceException("没有找到结果集。");
        }
    }

    @SafeVarargs
    private ChainBuilder<WebSQLQueryResult> buildAsyncExecuteCheckChain(
            ValidExecuteModel validExecuteModel,
            VisitFrequencyDataModel visitFrequencyDataModel,
            WebSQLContextInfo contextInfo,
            DBRProgressMonitor monitor,
            boolean isCheckSerialNumber,
            String userId,
            Chain<WebSQLQueryResult>... chains) {

        return ChainBuilder.build(new CheckChainRunner<WebSQLQueryResult>())
                .addChains(chains)
                .addChain(new PermissionChain(validExecuteModel.getPermissionModel()))
                .addChain(new FrequencyChain(
                        visitFrequencyDataModel,
                        contextInfo.getDataSourceContainer().getConnectionId(),
                        validExecuteModel.getOperation(),
                        userId,
                        validExecuteModel.getOrigin(),
                        validExecuteModel.isNotRecordFrequency()))
                .addChain(new BackupModifyChain(
                        monitor,
                        contextInfo,
                        validExecuteModel.getOperation(),
                        validExecuteModel.getBackupModel(),
                        userId,
                        sqlSessionTemplate,
                        validExecuteModel.getSqlRecord().getSql()))
                .addChain(new SerialNumberChain(
                        contextInfo,
                        validExecuteModel.getSerialNumber(),
                        isCheckSerialNumber));
    }

    @SafeVarargs
    private ChainBuilder<WebSQLQueryResult> buildAsyncExecuteStreamChain(
            ValidExecuteModel validExecuteModel,
            boolean needReview,
            WebSQLContextInfo contextInfo,
            DBRProgressMonitor monitor,
            String userId,
            int reviewType,
            String dataSize,
            Chain<WebSQLQueryResult>... chains) {

        return ChainBuilder.build(new StreamChainRunner<WebSQLQueryResult>())
                .addChains(chains)
                .addChain(new MaxDataSizeChain(dataSize))
                .addChain(new SyncSchemaChain(
                        contextInfo,
                        validExecuteModel.isNeedSyncSchema(),
                        validExecuteModel.getSqlHistory().getConnectionPattern(),
                        messageService))
                .addChainIfConditionPass(validExecuteModel.isNeedRecordInPrivateTable(),
                        () -> new PrivateTableChain(validExecuteModel, contextInfo, userId))
                .addChain(new BackupInsertChain(
                        monitor,
                        contextInfo,
                        validExecuteModel.getOperation(),
                        validExecuteModel.getBackupModel(),
                        userId,
                        sqlSessionTemplate,
                        validExecuteModel.getSqlRecord().getSql()))
                .addChain(new BackupOracleDropChain(
                        monitor,
                        contextInfo,
                        validExecuteModel.getBackupModel(),
                        validExecuteModel.getSqlRecord().getSql(),
                        userId,
                        validExecuteModel.getOperation()))
                .addChain(new BackupTransactionBatchChain(
                        monitor,
                        contextInfo,
                        validExecuteModel.getBackupModel(),
                        validExecuteModel.getSqlHistory().getOrigin(),
                        sqlSessionTemplate,
                        validExecuteModel.getOperation(),
                        userId,
                        contextInfo.getAutoCommit()))
                .addChain(new ReviewChain(
                        validExecuteModel.getOperation(),
                        contextInfo.getToken(),
                        needReview,
                        contextInfo.getAutoCommit(),
                        validExecuteModel.getSqlRecord().getSql(),
                        reviewType))
                .addChain(new PrivilegesNoteChain(contextInfo, validExecuteModel.getPrivilegeModel(), validExecuteModel.getPrivilegeExpire()));
    }

}
