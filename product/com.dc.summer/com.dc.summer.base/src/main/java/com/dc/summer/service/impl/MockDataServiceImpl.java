package com.dc.summer.service.impl;

import com.dc.mockdata.engine.model.MockGeneratorDescriptor;
import com.dc.mockdata.engine.model.MockGeneratorRegistry;
import com.dc.mockdata.engine.model.MockValueGenerator;
import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.component.Cloner;
import com.dc.springboot.core.model.database.DcJobColumnDto;
import com.dc.springboot.core.model.database.MockDataMessage;
import com.dc.springboot.core.model.exception.ServiceException;
import com.dc.springboot.core.model.mockdata.MockDataPreviewMessage;
import com.dc.springboot.core.utils.SqlFieldDataUtils;
import com.dc.summer.DBException;
import com.dc.summer.exec.model.counter.HandlerCounter;
import com.dc.summer.model.data.MockDataPreviewInfo;
import com.dc.summer.model.data.WebAsyncTaskInfo;
import com.dc.summer.model.exec.DBExecUtils;
import com.dc.summer.model.sql.SqlFieldData;
import com.dc.summer.service.MockDataService;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MarkerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class MockDataServiceImpl implements MockDataService {

    private final MockGeneratorRegistry mockGeneratorRegistry = MockGeneratorRegistry.getInstance();

    @Resource
    private RedisService redisService;

    @Resource
    private Cloner cloner;

    @Override
    public MockDataPreviewInfo mockDataPreview(MockDataPreviewMessage mockDataPreviewMessage) {

        //过滤跳过生成器
        List<Map<String, Object>> params = mockDataPreviewMessage.getParams()
                .stream()
                .filter(property -> {
                    Map<String, Object> properties = (Map<String, Object>) property.get("algorithm");
                    String algorithmId = properties.get("id").toString();
                    return !"<skip>".equals(algorithmId);
                }).collect(Collectors.toList());

        //通过算法标识构造生成器
        List<MockValueGenerator> generators = params
                .stream()
                .map(property -> {
                    try {
                        Map<String, Object> properties = (Map<String, Object>) property.get("algorithm");
                        String algorithmId = properties.get("id").toString();
                        MockGeneratorDescriptor generator = mockGeneratorRegistry.getGenerator(algorithmId);
                        if (generator == null) {
                            throw new ServiceException("算法标识: " + algorithmId + " 未匹配到生成器");
                        }
                        MockValueGenerator generatorInstance = generator.createGenerator();
                        generatorInstance.init(null, null, properties);
                        return generatorInstance;
                    } catch (DBException e) {
                        log.error(e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toList());

        List<String> fields = params
                .stream()
                .map(property -> property.get("column_name").toString())
                .collect(Collectors.toList());

        MockDataPreviewInfo mockDataPreviewInfo = new MockDataPreviewInfo();
        mockDataPreviewInfo.setFields(fields);

        //生成10条测试数据
        List<List<Object>> values = IntStream.range(0, 10)
                .mapToObj(i -> generators.stream()
                        .map(generator -> {
                            try {
                                return generator.generateValue(null);
                            } catch (DBException | IOException e) {
                                throw new RuntimeException(e);
                            }
                        })
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        mockDataPreviewInfo.setValues(values);

        return mockDataPreviewInfo;
    }

    @Override
    public WebAsyncTaskInfo asyncMockDataGenerate(MockDataMessage mockDataMessage) {

        WebSQLContextInfo contextInfo = WebSQLContextInfo.openExecuteContext(
                mockDataMessage.generateConnectionTokenMessage(true, true, "Mock_Data"));

        return contextInfo.createTempTask("Mock-Data", (taskId, monitor) -> {

            HandlerCounter.setMarker(MarkerFactory.getMarker("Mock-Data"));

            boolean isSuccess = true;
            try {
                //Schema schema = schemaMapper.getSchemaByUniqueKey(mockDataMessage.getSchemaId()).orElseThrow(() -> new ServiceException("未找到schema"));
                long startTime = System.currentTimeMillis();

                log(mockDataMessage, "测试数据构建任务开始执行");

                long totalSize = mockDataMessage.getRows();
                int batchSize = mockDataMessage.getBatchLimitRows();

                List<DcJobColumnDto> dcJobColumnDtos = mockDataMessage.getDcJobColumnDtos();
                //处理跳过生成器
                Set<String> columnSet = dcJobColumnDtos.stream()
                        .filter(dcJobColumnDto -> {
                            Map<String, Object> algorithm = dcJobColumnDto.getAlgorithm();
                            String algorithmId = algorithm.get("id").toString();
                            return !"<skip>".equals(algorithmId);
                        }).map(DcJobColumnDto::getColumnName)
                        .collect(Collectors.toSet());

                List<SqlFieldData> sqlFieldDatas = SqlFieldDataUtils.getExecuteSqlFieldData(contextInfo.getExecutionContext(), monitor, null, mockDataMessage.getSchema(), mockDataMessage.getTableName());
                if (CollectionUtils.isEmpty(sqlFieldDatas)) {
                    String logMessage = "列信息为空";
                    log(mockDataMessage, logMessage);
                    throw new ServiceException(logMessage);
                }

                log(mockDataMessage, "获取列信息成功");

                sqlFieldDatas = sqlFieldDatas.stream()
                        .filter(sqlFieldData -> columnSet.contains(sqlFieldData.getFieldName()))
                        .collect(Collectors.toList());

                //通过算法标识构造生成器
                List<MockValueGenerator> generators =  dcJobColumnDtos
                        .stream()
                        .filter(dcJobColumnDto -> columnSet.contains(dcJobColumnDto.getColumnName()))
                        .map(DcJobColumnDto::getAlgorithm)
                        .map(properties -> {
                            try {
                                String algorithmId = properties.get("id").toString();
                                MockGeneratorDescriptor generator = mockGeneratorRegistry.getGenerator(algorithmId);
                                if (generator == null) {
                                    throw new ServiceException("算法标识: " + algorithmId + " 未匹配到生成器");
                                }
                                MockValueGenerator generatorInstance = generator.createGenerator();
                                generatorInstance.init(null, null, properties);
                                return generatorInstance;
                            } catch (DBException e) {
                                log.error(e.getMessage(), e);
                                throw new RuntimeException(e);
                            }
                        }).collect(Collectors.toList());

                log(mockDataMessage, "开始准备测试数据,总行数:" + totalSize + ",批量执行行数:" + batchSize);

                // 处理所有批次
                for (int i = 0; i < totalSize; i += batchSize) {

                    // 计算当前批次的结束索引
                    long end = Math.min(i + batchSize, totalSize);
                    int batch = (int) end - i;

                    //生成测试数据
                    List<List<SqlFieldData>> list = Lists.newArrayListWithCapacity(batch);
                    for (int j = 0; j < batch; j++) {
                        List<SqlFieldData> sqlFieldDataList = Lists.newArrayListWithCapacity(sqlFieldDatas.size());
                        for (int i1 = 0; i1 < sqlFieldDatas.size(); i1++) {
                            Object value = generators.get(i1).generateValue(monitor);
                            SqlFieldData data = cloner.clone(sqlFieldDatas.get(i1));
                            data.setFieldValue(value);
                            sqlFieldDataList.add(data);
                        }
                        list.add(sqlFieldDataList);
                    }

                    String schemaName = mockDataMessage.getSchema();
                    String tableName = mockDataMessage.getTableName();

                    String insertSql;

                    String conflictManagement = mockDataMessage.getConflictManagement();
                    if ("1".equals(conflictManagement)) {
                        insertSql = contextInfo.getDataSource().getInfo().generateInsertIgnoreSql(schemaName, tableName, list, null);
                    } else if ("2".equals(conflictManagement)) {
                        insertSql = contextInfo.getDataSource().getInfo().generateReplaceSql(schemaName, tableName, list, null);
                    } else {
                        insertSql = contextInfo.getDataSource().getInfo().generateInsertSqlBatch(schemaName, tableName, list, null);
                    }

                    try {
                        long updateNum = DBExecUtils.executeUpdate(
                                monitor,
                                contextInfo.getExecutionContext(),
                                "SET identity_insert",
                                insertSql,
                                dbcStatement -> {
                                }
                        );

                        String logMessage = "已经构建" + (i + batch) + "行数据,耗时:" + (System.currentTimeMillis() - startTime) + "(ms)";
                        log.info(logMessage);
                        log(mockDataMessage, logMessage);
                    } catch (DBException e) {
                        isSuccess = false;
                        String logMessage = "写入测试数据失败:" + e.getMessage();
                        log(mockDataMessage, logMessage);
                    }
                }

                contextInfo.close();
            } catch (Exception e) {
                isSuccess = false;
                log.error(e.getMessage(), e);
                log(mockDataMessage, e.getMessage());
            } finally {
                String logMessage = isSuccess ? "测试数据构建成功" : "测试数据构建失败";
                log.info(logMessage);
                finishLog(mockDataMessage, logMessage);
            }
        });
    }

    private void log(MockDataMessage message, String logMessage) {
        if (message.isRedisLog()) {
            String key = message.getMockDataLogId();
            long time = TimeUnit.MINUTES.toSeconds(30);
            redisService.lLPush(key, logMessage, time);
        } else {
            log.info(logMessage);
        }
    }

    private void finishLog(MockDataMessage message, String logMessage) {
        if (message.isRedisLog()) {
            String key = message.getMockDataLogId();
            long time = TimeUnit.MINUTES.toSeconds(30);
            redisService.lLPush(key, logMessage, time);
            redisService.set(message.getMockDataStatus(), "completed", time);
        } else {
            log.info(logMessage);
        }
    }
}
