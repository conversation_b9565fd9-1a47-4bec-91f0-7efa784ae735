package com.dc.summer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dc.config.ApiConfig;
import com.dc.repository.mysql.mapper.*;
import com.dc.repository.mysql.model.*;
import com.dc.springboot.core.component.JSON;
import com.dc.springboot.core.config.PathConfig;
import com.dc.springboot.core.model.database.ConnectionConfig;
import com.dc.springboot.core.model.database.TestConnectionMessage;
import com.dc.springboot.core.model.parser.dto.DatabaseConnectionDto;
import com.dc.summer.component.SummerMapper;
import com.dc.summer.config.SummerConfig;
import com.dc.summer.constants.UCmdbConstants;
import com.dc.summer.exec.model.data.TestConnectionConfiguration;
import com.dc.summer.model.ConnectExtendMap;
import com.dc.summer.model.EntityMessage;
import com.dc.summer.registry.center.Global;
import com.dc.summer.service.*;
import com.dc.summer.service.sql.WebSQLContextInfo;
import com.dc.summer.util.PaModelConvertUtil;
import com.dc.summer.util.XlsxUtil;
import com.dc.type.DatabaseType;
import com.dc.utils.http.FileUtil;
import com.dc.utils.http.HttpClientUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pingan.cdsf.driver.bridger.dto.*;
import com.pingan.cdsf.driver.bridger.service.UcmdbService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Transactional
@Service
@Slf4j
public class DbConnectionServiceImpl extends BaseServiceImpl<DatabaseConnectionMapper, DatabaseConnection> implements DbConnectionService {

    public static final String DEFAULT_PASSWORD = "dc@123456";

    public static final String COMPLEX_DEFAULT_PASSWORD = "1qa@#$s0_se?&,.";

    @Resource
    PaDatabaseEntityService paDatabaseEntityService;

    @Resource
    PaInstanceService paInstanceService;

    @Resource
    PaSubInstanceService paSubInstanceService;
    @Resource
    public UserMapper userMapper;


    @Resource
    public DbEntityService dbEntityService;

    @Resource
    public SysOrgMapper sysOrgMapper;

    @Resource
    public SysOrgUserMapper sysOrgUserMapper;

    @Resource
    public PaDatabaseEntityMapper paDatabaseEntityMapper;

    @Resource
    public PaInstanceMapper paInstanceMapper;

    @Resource
    public PaSubInstanceMapper paSubInstanceMapper;

    Gson gson = new Gson();

    private static final int PAGE_SIZE = 1000;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private UserService userService;


    @Resource
    public CategoryMapper categoryMapper;

    @Resource
    public DatabaseCategoryUserMapper categoryUserMapper;

    @Resource
    public DatabaseConnectionMapper connectionMapper;

    @Resource
    public DatabaseCollectionUserMapper collectionUserMapper;

    @Resource
    public CiGroupMapper groupMapper;

    @Resource
    public GroupUserMapper groupUserMapper;

    @Resource
    public CiGroupRuleMapper groupRuleMapper;

    @Resource
    public CiGroupPermissionMapper groupPermissionMapper;

    @Resource
    public DatabaseConnectionMapper databaseConnectionMapper;
    @Resource
    public SecurityRuleSetService securityRuleSetService;
    @Resource
    public ResourceCatalogMapper resourceCatalogMapper;

    @Resource
    public CatalogRelationMapper catalogRelationMapper;

    @Resource
    public DbEntityMapper entityMapper;

    @Resource
    public PaUserMapper paUserMapper;

    @Resource
    public CustomizationConfigMapper customizationConfigMapper;

    @Resource
    private SummerMapper summerMapper;

    @Value("${pingan.ucmdb.timeout:2}")
    private Integer instanceTimeout;


    @Override
    public SseEmitter syncInstance(SseEmitter emitter) throws Exception {
        UcmdbService ucmdbService = com.dc.springboot.core.component.Resource.getBean(UcmdbService.class);
        ucmdbService.login();
        List<CustomizationConfig> customizationConfigList = customizationConfigMapper.selectList(new QueryWrapper<>());
        List<List<String>> customerList = new ArrayList<>();
        List<Map<String, String>> userList = new ArrayList<>();
        String[] redisDealList = null;
        List<String> businessUserIds = new ArrayList<>();

        for (CustomizationConfig customizationConfig : customizationConfigList) {
            String nameKey = customizationConfig.getNameKey();
            if ("sync_range".equals(nameKey)) {
                String syncRange = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(syncRange)) {
                    Map<String, String> syncMap = gson.fromJson(syncRange, Map.class);
                    String filePath = syncMap.get("file_path");
                    String fileName = syncMap.get("file_name");
                    if (StringUtils.isEmpty(filePath) && StringUtils.isEmpty(fileName)) {
                        continue;
                    }
                    customerList = this.getCustomerList(filePath, fileName);
                }
            }

            if ("leader_with_org".equals(nameKey)) {
                String orgId = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(orgId)) {
                    List<SysOrgUser> sysOrgUserList = sysOrgUserMapper.selectList(new QueryWrapper<SysOrgUser>().eq("org_id", orgId));
                    sysOrgUserList.forEach(sysOrgUser -> {
                        businessUserIds.add(sysOrgUser.getUid());
                    });
                }
            }

            if ("connect_account".equals(nameKey)) {
                String connectAccount = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(connectAccount)) {
                    Map<String, String> syncMap = gson.fromJson(connectAccount, Map.class);
                    String readonly = syncMap.get("readonly");
                    String wr = syncMap.get("wr");
                    if (StringUtils.isNotEmpty(readonly)) {
                        String[] readUserList = readonly.split(",");
                        for (String ru : readUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "2");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                    if (StringUtils.isNotEmpty(wr)) {
                        String[] wrUserList = wr.split(",");
                        for (String ru : wrUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "1");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                }
            }
            if ("redis_suffix".equals(nameKey)) {
                String redisDeal = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(redisDeal)) {
                    redisDealList = redisDeal.split(",");
                }
            }
        }
        if (customerList == null || customerList.size() == 0) {
            throw new Exception("sync range is null");
        }

        if (businessUserIds == null || businessUserIds.size() == 0) {
            throw new Exception("sync instance leader is null");
        }

        if (userList == null || userList.size() == 0) {
            throw new Exception("sync connect account is null");
        }


        emitter.send("sync instance start");
        String[] finalRedisDealList = redisDealList;
        List<List<String>> finalCustomerList = customerList;
        new Thread(() -> {
            try {
                User sysUser = userService.getSystemUser();
                List<SecurityRuleSet> securityRuleSets = securityRuleSetService.getAllDefaultSecurityRuleSet();
                List<Category> dbCategoryList = categoryMapper.selectList(new QueryWrapper<>());

                for (List<String> customer : finalCustomerList) {
                    if (customer != null && customer.size() > 1) {
                        String customerName = customer.get(0);
                        if ("主受益人".equals(customerName)) {
                            continue;
                        }
                        UcmdbParam ucmdbParam = new UcmdbParam();
                        ucmdbParam.setRows(PAGE_SIZE);
                        ucmdbParam.setCustomerNumber(customerName);
                        String databaseTypeString = customer.get(1);
                        String[] databaseArray = databaseTypeString.split(",");
                        for (String database : databaseArray) {
                            ucmdbParam.setPage(1);
                            String databaseType = UCmdbConstants.dbMap.get(database);
                            if (databaseType == null) {
                                continue;
                            }
                            ucmdbParam.setDatabaseType(databaseType);
                            emitter.send("sync " + customerName + " " + database  +" start");
                            UcmdbResult userInfo = ucmdbService.getData(ucmdbParam);
                            Integer totalCount = userInfo.getTotal();
                            emitter.send("sync " + customerName + " " + " count:" +totalCount);
                            List<PaDatabaseEntityDto> entityList = userInfo.getEntityDtoList();
                            if (entityList == null || entityList.size() == 0) {
                                continue;
                            }
                            List<Integer> statisticsList = Arrays.asList(0,0);
                            if (databaseType.equals("DBT:O")) {
                                statisticsList = this.dealOracleData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                            } else if (databaseType.equals("DBT:M")) {
                                statisticsList = this.dealMysqlData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                            } else if (databaseType.equals("DBT:RA")) {
                                statisticsList = this.dealPostgresqlData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                            } else if (databaseType.equals("DBT:TI")) {
                                statisticsList = this.dealTidbData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                            } else if (databaseType.equals("DBT:MG")) {
                                statisticsList = this.dealMongodbData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                            } else if (databaseType.equals("DBT:R")) {
                                statisticsList = this.dealRedisData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, finalRedisDealList);
                            }

                            int count = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);
                            if (count > 1) {
                                for (int i = 2; i <= count; i++) {
                                    emitter.send("sync " + customerName + " " + database + " " + i + " page start");
                                    ucmdbParam.setPage(i);
                                    UcmdbResult userInfo1 = ucmdbService.getData(ucmdbParam);
                                    List<PaDatabaseEntityDto> entityList1 = userInfo1.getEntityDtoList();
                                    if (entityList1 == null || entityList1.size() == 0) {
                                        continue;
                                    }
                                    if (databaseType.equals("DBT:O")) {
                                        this.dealOracleData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                                    } else if (databaseType.equals("DBT:M")) {
                                        this.dealMysqlData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                                    } else if (databaseType.equals("DBT:RA")) {
                                        this.dealPostgresqlData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                                    } else if (databaseType.equals("DBT:TI")) {
                                        this.dealTidbData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                                    } else if (databaseType.equals("DBT:MG")) {
                                        this.dealMongodbData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                                    } else if (databaseType.equals("DBT:R")) {
                                        this.dealRedisData(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, finalRedisDealList);
                                    }
                                }
                            }
                            emitter.send("sync " + customerName + " " + database + " end, add count:" + statisticsList.get(0) + ",update count:" + statisticsList.get(1));
                        }
                    }
                }

                emitter.send("sync instance end");
                emitter.complete();
            } catch (Exception e) {
                try {
                    emitter.send("error:" + e.getMessage() + " " + gson.toJson(e.getStackTrace()));
                    emitter.complete();
                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            }
        }).start();
        return emitter;

    }

    private List<Integer> dealRedisData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                               String[] redisDealList) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.REDIS.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);
        List<User> allUserList = userMapper.getAllUser();
        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);

        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();

            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = pi.getInstanceUuid();
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        if (isSave) {
                            PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                            saveSubInstances.add(paSubInstance);
                        }
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                instanceUuidList.add(subInstanceUuid);
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0,0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            String customerName = pe.getCustomerName();

            ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
            String dbVersion = pe.getDatabaseVersion();
            databaseVersionMap.setKey("database_version");
            databaseVersionMap.setValue(dbVersion);
            databaseVersionMap.setDescription("数据库版本");
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(databaseVersionMap);

            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser( pi.getServiceUser());

                        }
                        if (pi.getStatus() != null && !pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus(pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                String dRole = UCmdbConstants.paMap.get(pi.getDefaultRole());
                defaultRoleMap.setValue(dRole);
                defaultRoleMap.setDescription("默认角色");

                if (!"远程容灾".equals(dRole) && !"本地主库".equals(dRole)) {
                    continue;
                }

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                String entityName = pi.getInstanceName();
                entityName = this.dealRedisName(entityName, redisDealList);

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(entityName);
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

//                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
//                databaseVersionMap.setKey("database_version");
//                databaseVersionMap.setValue(pi.getDatabaseVersion());
//                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap architectureMap = new ConnectExtendMap();
                architectureMap.setKey("architecture_type");
                String role = UCmdbConstants.paMap.get(pi.getArchitectureType());
                architectureMap.setValue(role);
                architectureMap.setDescription("架构类型");

                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");

                Integer environment = this.getEnvironment(pi.getEnvironment());


                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(architectureMap);
                secondExtendedList.add(entityUuidMap);

                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                String instanceName = null;
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
//                        for (PaSubInstance dbPs : dbPaSubInstances) {
//                            if (ps.getInstanceUuid().equals(dbPs.getInstanceUuid())) {
//                                boolean isUpdate = false;
//                                PaSubInstance paSubInstance = new PaSubInstance();
//                                if (!ps.getInstanceName().equals(dbPs.getInstanceName())) {
//                                    updateProp.add("sub_instance_name");
//                                    isUpdate = true;
//                                    paSubInstance.setInstanceName(dbPs.getInstanceName());
//                                }
//                                if (isUpdate) {
//                                    updateSubInstances.add(paSubInstance);
//                                }
//                                break;
//                            }
//                        }
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                    }
                } else {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }

                boolean isConnectionSave = true;
                String syncId = null;
                String extendedAttributes = null;
                List<String> dbConnectIds = new ArrayList<>();
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (instanceUuid.equals(dbConnection.getSync())) {
                            dbConnectIds.add(dbConnection.getUnique_key());
                            isConnectionSave = false;
                            syncId = dbConnection.getSync();
                            extendedAttributes = dbConnection.getExtended_attributes();
                        }
                    }
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                if (isConnectionSave) {
                    saveCount += 1;
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    Integer securityRuleSetId = 0;
                    if (securityRuleSets != null && securityRuleSets.size() > 0) {
                        String ruleName = this.getRuleByCustomer(customerName);
                        for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                            if (DatabaseType.REDIS.getValue().equals(securityRuleSet.getDbType())) {
                                if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                    securityRuleSetId = securityRuleSet.getId();
                                }
                                if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-Redis规则集") ) {
                                    securityRuleSetId = securityRuleSet.getId();
                                    break;
                                }
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.REDIS.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }

                    if (connectUsers == null || connectUsers.size() == 0) {
                        continue;
                    }
                    if (StringUtils.isEmpty(dbVersion) || Integer.valueOf(dbVersion.substring(0,1)) < 6) {
                        Map uMap = new HashMap<>();
                        uMap.put("type", "2");
                        uMap.put("value", "damsmgr");
                        connectUsers = Arrays.asList(uMap);
                    }

                    for (Map umMap : connectUsers) {
                        List<ConnectExtendMap> thirdExtendedList = new ArrayList<>();
                        String type = (String) umMap.get("type");
                        String readDesc = "1".equals(type) ? "读写" : "只读";
                        String um = (String) umMap.get("value");
                        thirdExtendedList.addAll(secondExtendedList);
                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("cyberark_username");
                        subInstanceUuidMap.setValue(um);
                        subInstanceUuidMap.setDescription("cyberark用户名");
                        thirdExtendedList.add(subInstanceUuidMap);
                        DatabaseConnection databaseConnection = new DatabaseConnection();
                        String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                        databaseConnection.setUnique_key(uniqueKey);
                        String dcInstanceName = entityName + "_Redis_" + dRole + "_" + readDesc;
                        if ("单实例".equals(role) && StringUtils.isNotEmpty(instanceName)) {
                            dcInstanceName += "_" + instanceName;
                        }
                        databaseConnection.setInstance_name(dcInstanceName);
                        databaseConnection.setEnvironment(environment);
                        databaseConnection.setDb_type(DatabaseType.REDIS.getValue());
                        databaseConnection.setIp(ip);
                        databaseConnection.setPort(String.valueOf(port));
                        databaseConnection.setUsername(null);
                        if (StringUtils.isNotEmpty(dbVersion) && Integer.valueOf(dbVersion.substring(0,1)) >= 6) {
                            databaseConnection.setUsername(um);
                        }
                        databaseConnection.setAuth_source(1);
                        Integer connectType = 2;
                        if ("单实例".equals(role) || "redis主从".equals(role)) {
                            connectType = 1;
                        }
                        databaseConnection.setConnect_type(connectType);
                        databaseConnection.setCreator_id(sysUser.getUniqueKey());
                        databaseConnection.setPattern(2);
                        databaseConnection.setIs_active(0);
                        databaseConnection.setSync(instanceUuid);
                        databaseConnection.setExtended_attributes(gson.toJson(thirdExtendedList));
                        databaseConnection.setConnection(this.getConnect(DatabaseType.REDIS.getValue(), ip, port, null));
                        databaseConnection.setConnection_desc(this.getConnectDesc(DatabaseType.REDIS.getValue(), ip, port, null));
                        databaseConnection.setEntity(entityName);
//                    databaseConnection.setPort("6379");
//                    databaseConnection.setUsername("root");
//                    databaseConnection.setUsername(null);
//                    databaseConnection.setAuth_source(0);
//                    databaseConnection.setConnect_type(1);
//                    databaseConnection.setPassword("RNp2hvU7ydnKirNroRyh0w==");
//                    databaseConnection.setIp("*************");
                        Map<String, String> timeParams = new HashMap<>();
                        timeParams.put("key", "connectTimeout");
                        timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                        String driverId = this.testConnection(databaseConnection, timeParams, um);
                        if (!StringUtils.isEmpty(driverId)) {
                            databaseConnection.setDriver_id(driverId);
                            databaseConnection.setIs_active(1);
                        }
                        databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                        databaseConnection.setCategory_id(categoryId);
                        connectionMapper.insert(databaseConnection);
                        this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        logger.info("实例" + instanceName + "创建完成");
                        if (!StringUtils.isEmpty(driverId)) {
                            new Thread(() -> {
                                this.refreshSchema(uniqueKey);
                            }).start();
                        }
                    }

                } else {
                    if (updateProp.size() > 0) {
                        updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                        if (StringUtils.isNotEmpty(extendedAttributes)) {
                            secondExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                            }.getType());
                        }
                        for (ConnectExtendMap connectExtendMap : secondExtendedList) {
                            if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("manager_da"));
                            }
                            if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("back_da"));
                            }
                            if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                connectExtendMap.setValue((String) updateProp.get("service_user"));
                            }
                        }
                        String extendAttr = gson.toJson(secondExtendedList);
                        if (StringUtils.isNotEmpty(syncId)) {
                            Map<String, String> syncMap = new HashMap<>();
                            syncMap.put("sync", syncId);
                            syncMap.put("extended_attributes", extendAttr);
                            connectionMapper.updateBySyncId(syncMap);
                        }
                    }
                    this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                    this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);

                }
            }

        }

        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }


    private String dealRedisName(String entityName, String[] redisDealList) {

        if (redisDealList != null && redisDealList.length > 0) {
            for (String redisDeal : redisDealList) {
                if (entityName.endsWith(redisDeal)) {
                    entityName = entityName.replace(redisDeal, "");
                    return entityName;
                }
            }
        }
        return entityName;
    }

    private List<Integer> dealMongodbData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.MONGODB.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);
        List<User> allUserList = userMapper.getAllUser();
        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);


        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            for (PaInstanceDto pi : entityPaInstances) {
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();

                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = pi.getInstanceUuid();
                if (StringUtils.isNotEmpty(pi.getVip())) {
                    ip = pi.getVip();
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        if (isSave) {
                            PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                            saveSubInstances.add(paSubInstance);
                        }
                        if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                            ip = ps.getDomainName();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                instanceUuidList.add(subInstanceUuid);
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0,0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            String customerName = pe.getCustomerName();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser(pi.getServiceUser());

                        }
                        if (pi.getStatus() != null && !pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus(pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");
                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();

                ConnectExtendMap isSeparateMap = new ConnectExtendMap();
                isSeparateMap.setKey("is_separate");
                String isSeparate = UCmdbConstants.paMap.get(pi.getIsSeparate());
                isSeparateMap.setValue(isSeparate);
                isSeparateMap.setDescription("是否分片");

                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(entityUuidMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(isSeparateMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                if (StringUtils.isNotEmpty(pi.getVip())) {
                    ip = pi.getVip();
                }
//                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    String domainName = null;
                    String hostIp = null;
                    if (paSubInstances1 != null && paSubInstances1.size() > 0) {
                        for (int i = 0; i < paSubInstances1.size(); i++) {
                            PaSubInstanceDto ps = paSubInstances1.get(i);

                            domainName = ps.getDomainName();
                            if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                                port = ps.getPort();
                                ip = domainName;
                                instanceUuid = ps.getInstanceUuid();
                                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                                defaultRoleMap.setKey("default_role");
                                defaultRoleMap.setValue(UCmdbConstants.paMap.get(ps.getDefaultRole()));
                                defaultRoleMap.setDescription("默认角色");
                                secondExtendedList.add(defaultRoleMap);
                                ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                                subInstanceUuidMap.setKey("instance_uuid");
                                subInstanceUuidMap.setValue(instanceUuid);
                                subInstanceUuidMap.setDescription("实例UUID");
                                secondExtendedList.add(subInstanceUuidMap);
                                break;
                            } else {
                                if (paSubInstances1.size() - 1 == i && StringUtils.isNotEmpty(ps.getHostIp())) {
                                    if (ip == null) {
                                        hostIp = ps.getHostIp();
                                        ip = hostIp;
                                        port = ps.getPort();
                                        instanceUuid = ps.getInstanceUuid();
                                        ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                                        defaultRoleMap.setKey("default_role");
                                        defaultRoleMap.setValue(UCmdbConstants.paMap.get(ps.getDefaultRole()));
                                        defaultRoleMap.setDescription("默认角色");

                                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                                        subInstanceUuidMap.setKey("instance_uuid");
                                        subInstanceUuidMap.setValue(instanceUuid);
                                        subInstanceUuidMap.setDescription("实例UUID");
                                        secondExtendedList.add(defaultRoleMap);
                                        secondExtendedList.add(subInstanceUuidMap);
                                        break;
                                    }

                                }
                            }
                        }

                    }
//                } else {
                if (StringUtils.isEmpty(domainName) && hostIp == null) {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
//                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }

                boolean isConnectionSave = true;
                String extendedAttributes = null;
                String syncId = null;
                List<String> dbConnectIds = new ArrayList<>();
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (instanceUuid.equals(dbConnection.getSync())) {
                            isConnectionSave = false;
                            syncId = dbConnection.getSync();
                            dbConnectIds.add(dbConnection.getUnique_key());
                            extendedAttributes = dbConnection.getExtended_attributes();
                        }
                    }
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                if (isConnectionSave) {
                    saveCount += 1;
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    Integer securityRuleSetId = 0;
                    if (securityRuleSets != null && securityRuleSets.size() > 0) {
                        String ruleName = this.getRuleByCustomer(customerName);
                        for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                            if (DatabaseType.MONGODB.getValue().equals(securityRuleSet.getDbType())) {
                                if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                    securityRuleSetId = securityRuleSet.getId();
                                }
                                if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-MongoDB规则集") ) {
                                    securityRuleSetId = securityRuleSet.getId();
                                    break;
                                }
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.MONGODB.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }
                    if (connectUsers == null || connectUsers.size() == 0) {
                        continue;
                    }
                    for (Map umMap : connectUsers) {
                        String type = (String) umMap.get("type");
                        String readDesc = "1".equals(type) ? "读写" : "只读";
                        String um = (String) umMap.get("value");
                        String isSeparateDesc = "是".equals(isSeparate) ? "分片" : "副本集";
                        DatabaseConnection databaseConnection = new DatabaseConnection();
                        String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                        databaseConnection.setUnique_key(uniqueKey);
                        String iName = entityName + "_MongoDB_" + isSeparateDesc + "_" + readDesc;
                        databaseConnection.setInstance_name(iName);
                        databaseConnection.setEnvironment(environment);
                        databaseConnection.setDb_type(DatabaseType.MONGODB.getValue());
                        databaseConnection.setIp(ip);
                        databaseConnection.setPort(String.valueOf(port == null? "": port));
                        databaseConnection.setUsername(um);
                        databaseConnection.setService_name("admin"); //固定amdin
                        databaseConnection.setAuth_source(1);
                        databaseConnection.setCreator_id(sysUser.getUniqueKey());
                        databaseConnection.setPattern(2);
                        databaseConnection.setIs_active(0);
                        databaseConnection.setSync(instanceUuid);
                        databaseConnection.setExtended_attributes(gson.toJson(secondExtendedList));
                        databaseConnection.setConnection(this.getConnect(DatabaseType.MONGODB.getValue(), ip, port, null));
                        databaseConnection.setConnection_desc(this.getConnectDesc(DatabaseType.MONGODB.getValue(), ip, port, null));
                        databaseConnection.setEntity(entityName);

//                    databaseConnection.setPort("27017");
//                    databaseConnection.setUsername("admin");
//                    databaseConnection.setAuth_source(0);
//                    databaseConnection.setPassword("RNp2hvU7ydnKirNroRyh0w==");
//                    databaseConnection.setIp("************");
                        Map<String, String> timeParams = new HashMap<>();
                        timeParams.put("key", "connectTimeout");
                        timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                        String driverId = this.testConnection(databaseConnection, timeParams, null);
                        if (!StringUtils.isEmpty(driverId)) {
                            databaseConnection.setDriver_id(driverId);
                            databaseConnection.setIs_active(1);
                        }
                        databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                        databaseConnection.setCategory_id(categoryId);
                        connectionMapper.insert(databaseConnection);
                        this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        logger.info("实例" + iName + "创建完成");
                        if (!StringUtils.isEmpty(driverId)) {
                            new Thread(() -> {
                                this.refreshSchema(uniqueKey);
                            }).start();
                        }

                    }
                } else {
                    if (updateProp.size() > 0) {
                        updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                        if (StringUtils.isNotEmpty(extendedAttributes)) {
                            secondExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                            }.getType());
                        }
                        for (ConnectExtendMap connectExtendMap : secondExtendedList) {
                            if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("manager_da"));
                            }
                            if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("back_da"));
                            }
                            ;
                            if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                connectExtendMap.setValue((String) updateProp.get("service_user"));
                            }
                        }
                        String extendAttr = gson.toJson(secondExtendedList);
                        if (StringUtils.isNotEmpty(syncId)) {
                            Map<String, String> syncMap = new HashMap<>();
                            syncMap.put("sync", syncId);
                            syncMap.put("extended_attributes", extendAttr);
                            connectionMapper.updateBySyncId(syncMap);
                        }
                    }

                    this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                    this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);

                }
            }

        }

        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }

    private List<Integer> dealTidbData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.TIDB.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);
        List<User> allUserList = userMapper.getAllUser();
        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);


        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = pi.getInstanceUuid();
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        if (isSave) {
                            PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                            saveSubInstances.add(paSubInstance);
                        }
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                instanceUuidList.add(subInstanceUuid);
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0,0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            String customerName = pe.getCustomerName();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser(pi.getServiceUser());

                        }
                        if (pi.getStatus() != null && !pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus(pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                String role = UCmdbConstants.paMap.get(pi.getDefaultRole());
                defaultRoleMap.setValue(role);
                defaultRoleMap.setDescription("默认角色");

                if (!"远程容灾".equals(role) && !"主库".equals(role)) {
                    continue;
                }

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");
                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();

                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(entityUuidMap);

                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isClassMap);
                secondExtendedList.add(isCopyAuthMap);

                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                String instanceName = pi.getInstanceName();
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
//                        for (PaSubInstance dbPs : dbPaSubInstances) {
//                            if (ps.getInstanceUuid().equals(dbPs.getInstanceUuid())) {
//                                boolean isUpdate = false;
//                                PaSubInstance paSubInstance = new PaSubInstance();
//                                if (!ps.getInstanceName().equals(dbPs.getInstanceName())) {
//                                    updateProp.add("sub_instance_name");
//                                    isUpdate = true;
//                                    paSubInstance.setInstanceName(dbPs.getInstanceName());
//                                }
//                                if (isUpdate) {
//                                    updateSubInstances.add(paSubInstance);
//                                }
//                                break;
//                            }
//                        }

                        if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                            continue;
                        }

                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                    }
                } else {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }

                boolean isConnectionSave = true;
                String syncId = null;
                String extendedAttributes = null;
                List<String> dbConnectIds = new ArrayList<>();
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (instanceUuid.equals(dbConnection.getSync())) {
                            isConnectionSave = false;
                            syncId = dbConnection.getSync();
                            dbConnectIds.add(dbConnection.getUnique_key());
                            extendedAttributes = dbConnection.getExtended_attributes();
                        }
                    }
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                if (isConnectionSave) {
                    saveCount += 1;
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    Integer securityRuleSetId = 0;
                    if (securityRuleSets != null && securityRuleSets.size() > 0) {
                        String ruleName = this.getRuleByCustomer(customerName);
                        for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                            if (DatabaseType.TIDB.getValue().equals(securityRuleSet.getDbType())) {
                                if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                    securityRuleSetId = securityRuleSet.getId();
                                }
                                if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-TiDB规则集") ) {
                                    securityRuleSetId = securityRuleSet.getId();
                                    break;
                                }
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.TIDB.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }

                    if (connectUsers == null || connectUsers.size() == 0) {
                        continue;
                    }
                    for (Map umMap : connectUsers) {
                        String type = (String) umMap.get("type");
                        String readDesc = "1".equals(type) ? "读写" : "只读";
                        String um = (String) umMap.get("value");
                        DatabaseConnection databaseConnection = new DatabaseConnection();
                        String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                        databaseConnection.setUnique_key(uniqueKey);
                        databaseConnection.setInstance_name(entityName + "_tidb_" + role + "_" + readDesc);
                        databaseConnection.setEnvironment(environment);
                        databaseConnection.setDb_type(DatabaseType.TIDB.getValue());
                        databaseConnection.setIp(ip);
                        databaseConnection.setPort(String.valueOf(port));
                        databaseConnection.setUsername(um);
                        databaseConnection.setAuth_source(1);
                        databaseConnection.setCreator_id(sysUser.getUniqueKey());
                        databaseConnection.setPattern(2);
                        databaseConnection.setIs_active(0);
                        databaseConnection.setDb_role("Normal");
                        databaseConnection.setSync(instanceUuid);
                        databaseConnection.setExtended_attributes(gson.toJson(secondExtendedList));
                        databaseConnection.setConnection(this.getConnect(DatabaseType.TIDB.getValue(), ip, port, null));
                        databaseConnection.setConnection_desc(this.getConnectDesc(DatabaseType.TIDB.getValue(), ip, port, null));
                        databaseConnection.setEntity(entityName);
//                    databaseConnection.setPort("4000");
//                    databaseConnection.setUsername("root");
//                    databaseConnection.setAuth_source(0);
//                    databaseConnection.setPassword("RNp2hvU7ydnKirNroRyh0w==");
//                    databaseConnection.setIp("************");
                        Map<String, String> timeParams = new HashMap<>();
                        timeParams.put("key", "connectTimeout");
                        timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                        String driverId = this.testConnection(databaseConnection, timeParams, null);
                        if (!StringUtils.isEmpty(driverId)) {
                            databaseConnection.setDriver_id(driverId);
                            databaseConnection.setIs_active(1);
                        }
                        databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                        databaseConnection.setCategory_id(categoryId);
                        connectionMapper.insert(databaseConnection);
                        databaseConnection.getUnique_key();
                        this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        logger.info("实例" + instanceName + "创建完成");
                        if (!StringUtils.isEmpty(driverId)) {
                            new Thread(() -> {
                                this.refreshSchema(uniqueKey);
                            }).start();
                        }
                    }

                } else {
                    if (updateProp.size() > 0) {
                        updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                        if (StringUtils.isNotEmpty(extendedAttributes)) {
                            secondExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                            }.getType());
                        }
                        for (ConnectExtendMap connectExtendMap : secondExtendedList) {
                            if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("manager_da"));
                            }
                            ;

                            if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("back_da"));
                            }
                            ;
                            if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                connectExtendMap.setValue((String) updateProp.get("service_user"));
                            }
                        }
                        String extendAttr = gson.toJson(secondExtendedList);
                        if (StringUtils.isNotEmpty(syncId)) {
                            Map<String, String> syncMap = new HashMap<>();
                            syncMap.put("sync", syncId);
                            syncMap.put("extended_attributes", extendAttr);
                            connectionMapper.updateBySyncId(syncMap);
                        }
                    }
                    this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                    this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);

                }
            }

        }

        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }

    private List<Integer> dealPostgresqlData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.RASE_SQL.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);
        List<User> allUserList = userMapper.getAllUser();
        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);


        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            for (PaInstanceDto pi : entityPaInstances) {
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = null;
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    if (isSave) {
                        PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                        saveSubInstances.add(paSubInstance);
                    }

                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                        instanceUuidList.add(ps.getInstanceUuid());
                        continue;
                    }
                    if (StringUtils.isNotEmpty(ps.getVip())) {
                        ip = ps.getVip();
                        instanceUuidList.add(ps.getInstanceUuid());

                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0,0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            String customerName = pe.getCustomerName();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser(pi.getServiceUser());

                        }
                        if (!pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus( pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");


                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap systemNameMap = new ConnectExtendMap();
                systemNameMap.setKey("system_name");
                systemNameMap.setValue(pi.getSystemName());
                systemNameMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                Integer environment = this.getEnvironment(pi.getEnvironment());
                String entityName = pi.getInstanceName();


                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemNameMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
//                        for (PaSubInstance dbPs : dbPaSubInstances) {
//                            if (ps.getInstanceUuid().equals(dbPs.getInstanceUuid())) {
//                                boolean isUpdate = false;
//                                PaSubInstance paSubInstance = new PaSubInstance();
//                                if (!ps.getInstanceName().equals(dbPs.getInstanceName())) {
//                                    updateProp.add("sub_instance_name");
//                                    isUpdate = true;
//                                    paSubInstance.setInstanceName(dbPs.getInstanceName());
//                                }
//                                if (isUpdate) {
//                                    updateSubInstances.add(paSubInstance);
//                                }
//                                break;
//                            }
//                        }
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    String ip = null;
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                    } else {
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                        }
                    }

                    if (StringUtils.isNotEmpty(ip)) {
                        List<ConnectExtendMap> newExtendedList = new ArrayList<>();
                        Integer port = ps.getPort();
                        String instanceUuid = ps.getInstanceUuid();
                        String instanceName = ps.getInstanceName();

                        ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                        defaultRoleMap.setKey("default_role");
                        String role = UCmdbConstants.paMap.get(ps.getDefaultRole());
                        defaultRoleMap.setValue(role);
                        defaultRoleMap.setDescription("默认角色");

                        if (!"远程容灾".equals(role) && !"主库".equals(role)&& !"从库".equals(role)) {
                            continue;
                        }

                        ConnectExtendMap statusMap = new ConnectExtendMap();
                        statusMap.setKey("status");
                        statusMap.setValue(UCmdbConstants.paMap.get(ps.getStatus()));
                        statusMap.setDescription("状态");

                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        newExtendedList.add(defaultRoleMap);
                        newExtendedList.add(statusMap);
                        newExtendedList.add(subInstanceUuidMap);
                        newExtendedList.addAll(secondExtendedList);
                        boolean isConnectionSave = true;
                        String syncId = null;
                        String extendedAttributes = null;
                        List<String> dbConnectIds = new ArrayList<>();
                        if (dbConnections != null && dbConnections.size() > 0) {
                            for (DatabaseConnection dbConnection : dbConnections) {
                                if (instanceUuid.equals(dbConnection.getSync())) {
                                    isConnectionSave = false;
                                    syncId = dbConnection.getSync();
                                    dbConnectIds.add(dbConnection.getUnique_key());
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                }
                            }
                        }
                        if (isConnectionSave) {
                            saveCount += 1;
                            String categoryId = null;
                            if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                                for (Category category : dbCategoryList) {
                                    if (customerNumber.equals(category.getBusiness_id())) {
                                        categoryId = category.getUnique_key();
                                        break;
                                    }
                                }
                            }
                            Integer securityRuleSetId = 0;
                            if (securityRuleSets != null && securityRuleSets.size() > 0) {
                                String ruleName = this.getRuleByCustomer(customerName);
                                for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                                    if (DatabaseType.RASE_SQL.getValue().equals(securityRuleSet.getDbType())) {
                                        if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                            securityRuleSetId = securityRuleSet.getId();
                                        }
                                        if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-RASESQL规则集") ) {
                                            securityRuleSetId = securityRuleSet.getId();
                                            break;
                                        }
                                    }
                                }
                            }
                            boolean isSaveEntity = true;
                            if (dbEntity != null && dbEntity.size() > 0) {
                                for (DbEntity de : dbEntity) {
                                    if (entityName.equals(de.getEntityName())) {
                                        isSaveEntity = false;
                                        break;
                                    }
                                }
                            }

                            if (isSaveEntity) {
                                DbEntity dbEntity1 = new DbEntity();
                                dbEntity1.setEntityName(entityName);
                                dbEntity1.setDbType(DatabaseType.RASE_SQL.getValue());
                                entityMapper.insert(dbEntity1);
                                dbEntity.add(dbEntity1);
                            }
                            if (connectUsers == null || connectUsers.size() == 0) {
                                continue;
                            }
                            for (Map umMap : connectUsers) {
                                String type = (String) umMap.get("type");
                                String readDesc = "1".equals(type) ? "读写" : "只读";
                                String um = (String) umMap.get("value");
                                DatabaseConnection databaseConnection = new DatabaseConnection();
                                String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                                databaseConnection.setUnique_key(uniqueKey);
                                databaseConnection.setInstance_name(entityName + "_rasesql_" + role + "_" + readDesc);
                                databaseConnection.setEnvironment(environment);
                                Integer dbType = DatabaseType.RASE_SQL.getValue();
                                databaseConnection.setDb_type(dbType);
                                databaseConnection.setIp(ip);
                                databaseConnection.setPort(String.valueOf(port));
                                databaseConnection.setUsername(um);
                                databaseConnection.setAuth_source(1);
                                databaseConnection.setCreator_id(sysUser.getUniqueKey());
                                databaseConnection.setPattern(2);
                                databaseConnection.setIs_active(0);
                                databaseConnection.setSync(instanceUuid);
                                databaseConnection.setExtended_attributes(gson.toJson(newExtendedList));
                                databaseConnection.setConnection(this.getConnect(dbType, ip, port, null));
                                databaseConnection.setConnection_desc(this.getConnectDesc(dbType, ip, port, null));
                                databaseConnection.setEntity(entityName);
//                            databaseConnection.setPort("5432");
//                            databaseConnection.setUsername("postgres");
//                            databaseConnection.setAuth_source(0);
//                            databaseConnection.setPassword("RNp2hvU7ydnKirNroRyh0w==");
//                            databaseConnection.setIp("************");
                                Map<String, String> timeParams = new HashMap<>();
                                timeParams.put("key", "connectTimeout");
                                timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                                String driverId = this.testConnection(databaseConnection, timeParams, null);
                                if (!StringUtils.isEmpty(driverId)) {
                                    databaseConnection.setDriver_id(driverId);
                                    databaseConnection.setIs_active(1);
                                }
                                databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                                databaseConnection.setCategory_id(categoryId);
                                connectionMapper.insert(databaseConnection);
                                this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                                this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                                logger.info("实例" + instanceName + "创建完成");
                                if (!StringUtils.isEmpty(driverId)) {
                                    new Thread(() -> {
                                        this.refreshSchema(uniqueKey);
                                    }).start();
                                }
                            }

                        } else {
                            if (updateProp.size() > 0) {
                                updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                                if (StringUtils.isNotEmpty(extendedAttributes)) {
                                    newExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                                    }.getType());
                                }
                                for (ConnectExtendMap connectExtendMap : newExtendedList) {
                                    if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("manager_da"));
                                    }
                                    ;

                                    if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("back_da"));
                                    }
                                    ;
                                    if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("service_user"));
                                    }
                                }
                                String extendAttr = gson.toJson(newExtendedList);
                                if (StringUtils.isNotEmpty(syncId)) {
                                    Map<String, String> syncMap = new HashMap<>();
                                    syncMap.put("sync", syncId);
                                    syncMap.put("extended_attributes", extendAttr);
                                    connectionMapper.updateBySyncId(syncMap);
                                }
                            }
                            this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds,serviceUser);
                            this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);
                        }
                    }
                }
            }
        }
        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }

    private List<Integer> dealMysqlData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;

        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.MYSQL.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        List<User> allUserList = userMapper.getAllUser();

        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }
        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);

        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            for (PaInstanceDto pi : entityPaInstances) {
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = null;
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    if (isSave) {
                        PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                        saveSubInstances.add(paSubInstance);
                    }

                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                        instanceUuidList.add(ps.getInstanceUuid());
                        continue;
                    }
                    if (StringUtils.isNotEmpty(ps.getVip())) {
                        ip = ps.getVip();
                        instanceUuidList.add(ps.getInstanceUuid());

                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0,0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            String customerName = pe.getCustomerName();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser(pi.getServiceUser());

                        }
                        if (pi.getStatus() != null && !pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus(pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");


                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap systemNameMap = new ConnectExtendMap();
                systemNameMap.setKey("system_name");
                systemNameMap.setValue(pi.getSystemName());
                systemNameMap.setDescription("系统名称");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                Integer environment = this.getEnvironment(pi.getEnvironment());
                String entityName = pi.getInstanceName();
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemNameMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
//                        for (PaSubInstance dbPs : dbPaSubInstances) {
//                            if (ps.getInstanceUuid().equals(dbPs.getInstanceUuid())) {
//                                boolean isUpdate = false;
//                                PaSubInstance paSubInstance = new PaSubInstance();
//                                if (!ps.getInstanceName().equals(dbPs.getInstanceName())) {
//                                    updateProp.add("sub_instance_name");
//                                    isUpdate = true;
//                                    paSubInstance.setInstanceName(dbPs.getInstanceName());
//                                }
//                                if (isUpdate) {
//                                    updateSubInstances.add(paSubInstance);
//                                }
//                                break;
//                            }
//                        }
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    String ip = null;
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                    } else {
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                        }
                    }

                    if (StringUtils.isNotEmpty(ip)) {
                        List<ConnectExtendMap> newExtendedList = new ArrayList<>();
                        Integer port = ps.getPort();
                        String instanceUuid = ps.getInstanceUuid();
                        String instanceName = ps.getInstanceName();

                        ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                        defaultRoleMap.setKey("default_role");
                        String role = UCmdbConstants.paMap.get(ps.getDefaultRole());
                        defaultRoleMap.setValue(role);
                        defaultRoleMap.setDescription("默认角色");
                        if (!"远程容灾".equals(role) && !"主库".equals(role)&& !"从库".equals(role)) {
                            continue;
                        }

                        ConnectExtendMap statusMap = new ConnectExtendMap();
                        statusMap.setKey("status");
                        statusMap.setValue(UCmdbConstants.paMap.get(ps.getStatus()));
                        statusMap.setDescription("状态");

                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        newExtendedList.add(defaultRoleMap);
                        newExtendedList.add(statusMap);
                        newExtendedList.add(subInstanceUuidMap);
                        newExtendedList.addAll(secondExtendedList);
                        boolean isConnectionSave = true;
                        String extendedAttributes = null;
                        String syncId = null;
                        List<String> dbConnectIds = new ArrayList<>();
                        if (dbConnections != null && dbConnections.size() > 0) {
                            for (DatabaseConnection dbConnection : dbConnections) {
                                if (instanceUuid.equals(dbConnection.getSync())) {
                                    isConnectionSave = false;
                                    syncId = dbConnection.getSync();
                                    dbConnectIds.add(dbConnection.getUnique_key());
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                }
                            }
                        }
                        if (isConnectionSave) {
                            saveCount += 1;
                            String categoryId = null;
                            if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                                for (Category category : dbCategoryList) {
                                    if (customerNumber.equals(category.getBusiness_id())) {
                                        categoryId = category.getUnique_key();
                                        break;
                                    }
                                }
                            }
                            Integer securityRuleSetId = 0;
                            if (securityRuleSets != null && securityRuleSets.size() > 0) {

                                String ruleName = this.getRuleByCustomer(customerName);
                                for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                                    if (DatabaseType.MYSQL.getValue().equals(securityRuleSet.getDbType())) {
                                        if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                            securityRuleSetId = securityRuleSet.getId();
                                        }
                                        if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-MySQL规则集") ) {
                                            securityRuleSetId = securityRuleSet.getId();
                                            break;
                                        }
                                    }
                                }
                            }
                            boolean isSaveEntity = true;
                            if (dbEntity != null && dbEntity.size() > 0) {
                                for (DbEntity de : dbEntity) {
                                    if (entityName.equals(de.getEntityName())) {
                                        isSaveEntity = false;
                                        break;
                                    }
                                }
                            }

                            if (isSaveEntity) {
                                DbEntity dbEntity1 = new DbEntity();
                                dbEntity1.setEntityName(entityName);
                                dbEntity1.setDbType(DatabaseType.MYSQL.getValue());
                                entityMapper.insert(dbEntity1);
                                dbEntity.add(dbEntity1);
                            }
                            if (connectUsers == null || connectUsers.size() == 0) {
                                continue;
                            }
                            for (Map umMap : connectUsers) {
                                String type = (String) umMap.get("type");
                                String readDesc = "1".equals(type) ? "读写" : "只读";
                                String um = (String) umMap.get("value");
                                DatabaseConnection databaseConnection = new DatabaseConnection();
                                String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                                databaseConnection.setUnique_key(uniqueKey);
                                databaseConnection.setInstance_name(entityName + "_Mysql_" + role + "_" + readDesc);
                                databaseConnection.setEnvironment(environment);
                                databaseConnection.setCategory_id(categoryId);
                                databaseConnection.setDb_type(DatabaseType.MYSQL.getValue());
                                databaseConnection.setIp(ip);
                                databaseConnection.setPort(String.valueOf(port));
                                databaseConnection.setUsername(um);
                                databaseConnection.setAuth_source(1);
                                databaseConnection.setCreator_id(sysUser.getUniqueKey());
                                databaseConnection.setPattern(2);
                                databaseConnection.setIs_active(0);
                                databaseConnection.setSync(instanceUuid);
                                databaseConnection.setExtended_attributes(gson.toJson(newExtendedList));
                                databaseConnection.setConnection(this.getConnect(DatabaseType.MYSQL.getValue(), ip, port, null));
                                databaseConnection.setConnection_desc(this.getConnectDesc(DatabaseType.MYSQL.getValue(), ip, port, null));
                                databaseConnection.setEntity(entityName);
//                            databaseConnection.setPort("3307");
//                            databaseConnection.setUsername("root");
//                            databaseConnection.setAuth_source(0);
//                            databaseConnection.setPassword("RNp2hvU7ydnKirNroRyh0w==");
//                            databaseConnection.setIp("************");
                                Map<String, String> timeParams = new HashMap<>();
                                timeParams.put("key", "connectTimeout");
                                timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                                String driverId = this.testConnection(databaseConnection, timeParams, null);
                                if (!StringUtils.isEmpty(driverId)) {
                                    databaseConnection.setDriver_id(driverId);
                                    databaseConnection.setIs_active(1);
                                }
                                databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                                connectionMapper.insert(databaseConnection);
                                this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                                this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                                logger.info("实例" + instanceName + "创建完成");
                                if (!StringUtils.isEmpty(driverId)) {
                                    new Thread(() -> {
                                        this.refreshSchema(uniqueKey);
                                    }).start();
                                }
                            }

                        } else {
                            if (updateProp.size() > 0) {
                                updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                                if (StringUtils.isNotEmpty(extendedAttributes)) {
                                    newExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                                    }.getType());
                                }
                                for (ConnectExtendMap connectExtendMap : newExtendedList) {
                                    if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("manager_da"));
                                    }
                                    if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("back_da"));
                                    }
                                    ;
                                    if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                        connectExtendMap.setValue((String) updateProp.get("service_user"));
                                    }
                                }
                                String extendAttr = gson.toJson(newExtendedList);
                                if (StringUtils.isNotEmpty(syncId)) {
                                    Map<String, String> syncMap = new HashMap<>();
                                    syncMap.put("sync", syncId);
                                    syncMap.put("extended_attributes", extendAttr);
                                    connectionMapper.updateBySyncId(syncMap);
                                }
                            }
                            this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                            this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);
                        }
                    }
                }
            }
        }
        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }

    @Override
    public String saveUserIfNotExists(String serviceUserStr) {

        //处理逻辑，将serviceUser用户仅中存在PaUser中的用户创建dc关联账号
        List<PaUser> paUserData = null;
        if (StringUtils.isNotEmpty(serviceUserStr)) {
            String[] split = serviceUserStr.toUpperCase(Locale.ROOT).split("[,;]");  //同时支持多个分割符号 ,案例:um1,um3;um1
            ArrayList<String> desList = new ArrayList<>();
            Collections.addAll(desList, split);
            paUserData = paUserMapper.selectList(new QueryWrapper<PaUser>()
                    .select("um")
                    .in("UPPER(um)", desList));  //保存paUser 已经控制都大写
        }

        //只创建pa用户表中存在的
        if (ObjectUtils.isEmpty(paUserData)) {
            return null;
        }

        //平安账号和dcUsername一样
        List<String> umList = paUserData.stream().map(u -> u.getUm().toUpperCase(Locale.ROOT)).distinct().collect(Collectors.toList());
        List<User> userData = userMapper.selectList(new QueryWrapper<User>()
                .in("UPPER(username)", umList)
                .eq("is_delete", 0));
        Map<String, User> userMap = null;
        if (null != userData && userData.size() > 0) {
            userMap = userData.stream().collect(Collectors.toMap(u -> u.getUsername().toUpperCase(Locale.ROOT), t -> t, (k1, k2) -> k1));
        }

        List<String> uuidList = new ArrayList<>();
        for (PaUser pu : paUserData) {
            if (null != userMap && userMap.containsKey(pu.getUm().toUpperCase(Locale.ROOT))) {
                User user = userMap.get(pu.getUm().toUpperCase(Locale.ROOT));
                uuidList.add(user.getUniqueKey());
                continue;
            }
            User save = userService.save(pu.getUm().toUpperCase());
            uuidList.add(save.getUniqueKey());
        }
        return uuidList.stream().collect(Collectors.joining(","));
    }


    @Override
    public String saveUser(String serviceUserString, List<User> allUserList) {
        //逻辑将serviceUser存在PaUser表中的且不在dcUser的用户立刻创建，在将uuid返回
        List<PaUser> paUserData = null;
        if (StringUtils.isNotEmpty(serviceUserString)) {
            String[] split = serviceUserString.toUpperCase(Locale.ROOT).split("[,;]");  //同时支持多个分割符号 ,案例:um1,um3;um1
            ArrayList<String> desList = new ArrayList<>();
            Collections.addAll(desList, split);
            paUserData = paUserMapper.selectList(new QueryWrapper<PaUser>().in("UPPER(um)", desList));
        }

        if (ObjectUtils.isEmpty(paUserData)) {
            return null;

        }

        Map<String, User> userMap = null;
        if (null != allUserList && allUserList.size() > 0) {
            userMap = allUserList.stream().collect(Collectors.toMap(User::getUsername, t -> t, (k1, k2) -> k1));
        }

        List<String> uuidList = new ArrayList<>();
        for (PaUser pu : paUserData) {
            if (null != userMap && userMap.containsKey(pu.getUm())) {
                User user = userMap.get(pu.getUm());
                uuidList.add(user.getUniqueKey());
                continue;
            }
            User save = userService.save(pu.getUm().toUpperCase());
            uuidList.add(save.getUniqueKey());
            //引用变量,将新创建的记录到临时缓存
            allUserList.add(save);
        }
        return uuidList.stream().collect(Collectors.joining(","));
    }

    private Integer getEnvironment(String environment) {
        Integer environmentId = 4;
        if ("DBE:PRD".equals(environment)) {
            environmentId = 1;
        } else if ("DBE:DEV".equals(environment)) {
            environmentId = 2;
        } else if ("DBE:STG".equals(environment)) {
            environmentId = 3;
        }
        return environmentId;
    }

    @Override
    public void refreshSchema(String connectId) {
        try {
            String url = PathConfig.getInstance().getDcBackend() + "/api/v1/config/schemas/sync";
            Map params = new HashMap();
            params.put("connect_id", connectId);
            params.put("origin", "summer");
            String result = HttpClientUtils.doPost(url, new StringEntity(JSON.toJSONString(params), "UTF-8"));
            log.info("refresh schema result:" + result);
        } catch (Exception e) {
            log.info("refresh schema error:" + e.getMessage());
        }

    }

    private String testConnection(DatabaseConnection databaseConnection, Map<String, String> params, String cyberarkUsername) {
        String driverId = null;
        try {
            List driverPList = new ArrayList();
            driverPList.add(params);
            databaseConnection.setDriver_properties(gson.toJson(driverPList));
            DatabaseConnectionDto databaseConnectionDto = summerMapper.toDatabaseConnectionDto(databaseConnection);
            ConnectionConfig connectionConfig = databaseConnectionDto.buildConnectionConfig(null, null);
            TestConnectionMessage testConnectionMessage = summerMapper.toTestConnectionMessage(connectionConfig);
            if (StringUtils.isNotEmpty(cyberarkUsername)) {
                TestConnectionConfiguration testConnectionConfiguration = testConnectionMessage.getTestConnectionConfiguration();
                testConnectionConfiguration.setProviderProperty("@cyberark-user-name@", cyberarkUsername);
            }
            driverId = WebSQLContextInfo.test(testConnectionMessage);
            System.out.println("driverId is: " + driverId);
            // TODO 成功了，把驱动ID更新回 DatabaseConnection 中。

        } catch (Exception e) {
            // TODO 失败了做相应处理
            String message = "连接数据库失败：" + e.getMessage();

        }
        return driverId;
    }

    private List<Integer> dealOracleData(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser,
                                List<String> businessUserIds, List<Map<String, String>> connectUsers) {
        List<Integer> result = new ArrayList<>();
        Integer saveCount = 0;
        Integer updateCount = 0;
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.ORACLE.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);
        List<User> allUserList = userMapper.getAllUser();
        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        Map userMap = new HashMap();
        userMap.put("list", dbUuidList);
        List<PaDatabaseEntity> dbEntityList = paDatabaseEntityMapper.getByEntityUuids(userMap);


        List<PaDatabaseEntity> saveEntity = new ArrayList<>();
        List<PaDatabaseEntity> updateEntity = new ArrayList<>();

        List<PaInstance> saveInstances = new ArrayList<>();
        List<PaInstance> updateInstances = new ArrayList<>();
        List<PaSubInstance> saveSubInstances = new ArrayList<>();
        List<PaSubInstance> updateSubInstances = new ArrayList<>();


        List<PaInstance> dbPaInstances = new ArrayList<>();
        List<PaSubInstance> dbPaSubInstances = new ArrayList<>();
        List<String> entityUuidList = new ArrayList<>();
        if (dbEntityList != null && dbEntityList.size() > 0) {
            dbEntityList.forEach(user -> {
                entityUuidList.add(user.getEntityUuid());
            });
            userMap.put("list", entityUuidList);
            dbPaInstances = paInstanceMapper.getByEntityUuids(userMap);
            dbPaSubInstances = paSubInstanceMapper.getByEntityUuid(userMap);
        }


        List<String> instanceUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            boolean isSave = true;
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                PaDatabaseEntity paDatabaseEntity = PaModelConvertUtil.convert(pe);
                saveEntity.add(paDatabaseEntity);
            }
            for (PaInstanceDto pi : entityPaInstances) {
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                if (isSave) {
                    PaInstance paInstance = PaModelConvertUtil.convert(pi);
                    saveInstances.add(paInstance);
                }
                String ip = null;
                String subInstanceUuid = null;
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                for (PaSubInstanceDto ps : paSubInstances1) {

                    if (isSave) {
                        PaSubInstance paSubInstance = PaModelConvertUtil.convert(ps);
                        saveSubInstances.add(paSubInstance);
                    }
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                        subInstanceUuid = ps.getInstanceUuid();
                        break;
                    } else {
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                            subInstanceUuid = ps.getInstanceUuid();
                            break;
                        }
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                instanceUuidList.add(subInstanceUuid);
            }
        }
        if (instanceUuidList.size() == 0) {
            return Arrays.asList(0, 0);
        }
        List<DatabaseConnection> dbConnections = databaseConnectionMapper.getByUuids(instanceUuidList);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            for (PaDatabaseEntity dbPe : dbEntityList) {
                if (pe.getEntityUuid().equals(dbPe.getEntityUuid())) {
                    PaDatabaseEntity uEntity = new PaDatabaseEntity();
                    uEntity.setId(dbPe.getId());
                    if (pe.getBackDa() != null && !pe.getBackDa().equals(dbPe.getBackDa())) {
                        updateProp.put("back_da", pe.getBackDa());
                        uEntity.setBackDa(pe.getBackDa());
                    }
                    if (pe.getManagerDa() != null && !pe.getManagerDa().equals(dbPe.getManagerDa())) {
                        updateProp.put("manager_da", pe.getManagerDa());
                        uEntity.setManagerDa(pe.getManagerDa());
                    }
                    if (updateProp.size() > 0) {
                        updateEntity.add(uEntity);
                    }
                    break;
                }
            }
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");

            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
//            ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
//            databaseVersionMap.setKey("database_version");
//            databaseVersionMap.setValue(pe.getDatabaseVersion());
//            databaseVersionMap.setDescription("数据库版本");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
//            extendList.add(databaseVersionMap);
            extendList.add(infraTypeMap);
            String customerName = pe.getCustomerName();
            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                for (PaInstance dbPi : dbPaInstances) {
                    if (pi.getInstanceUuid().equals(dbPi.getInstanceUuid())) {
                        boolean isUpdate = false;
                        PaInstance updatePaInstance = new PaInstance();
                        updatePaInstance.setId(dbPi.getId());
                        if (pi.getServiceUser() != null && !pi.getServiceUser().equals(dbPi.getServiceUser())) {
                            updateProp.put("service_user", pi.getServiceUser());

                            isUpdate = true;
                            updatePaInstance.setServiceUser(pi.getServiceUser());

                        }
                        if (pi.getStatus() != null && !pi.getStatus().equals(dbPi.getStatus())) {
                            updateProp.put("status", pi.getStatus());
                            isUpdate = true;
                            updatePaInstance.setStatus(pi.getStatus());
                        }
                        if (isUpdate) {
                            updateInstances.add(updatePaInstance);
                        }
                        break;
                    }
                }

                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                String role = UCmdbConstants.paMap.get(pi.getDefaultRole());
                defaultRoleMap.setValue(role);
                defaultRoleMap.setDescription("默认角色");
                if (!"远程容灾".equals(role) && !"主库".equals(role)) {
                    continue;
                }
                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap architectureTypeMap = new ConnectExtendMap();
                architectureTypeMap.setKey("architecture_type");
                architectureTypeMap.setValue(UCmdbConstants.paMap.get(pi.getArchitectureType()));
                architectureTypeMap.setDescription("架构类型");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(architectureTypeMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                String ip = null;
                Integer port = null;
                String instanceUuid = null;
                String instanceName = null;
                for (PaSubInstanceDto ps : paSubInstances1) {
//                        for (PaSubInstance dbPs : dbPaSubInstances) {
//                            if (ps.getInstanceUuid().equals(dbPs.getInstanceUuid())) {
//                                boolean isUpdate = false;
//                                PaSubInstance paSubInstance = new PaSubInstance();
//                                if (!ps.getInstanceName().equals(dbPs.getInstanceName())) {
//                                    updateProp.add("sub_instance_name");
//                                    isUpdate = true;
//                                    paSubInstance.setInstanceName(dbPs.getInstanceName());
//                                }
//                                if (isUpdate) {
//                                    updateSubInstances.add(paSubInstance);
//                                }
//                                break;
//                            }
//                        }
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        ip = ps.getDomainName();
                        port = ps.getPort();
                        instanceUuid = ps.getInstanceUuid();
                        instanceName = ps.getInstanceName();
                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        secondExtendedList.add(subInstanceUuidMap);
                        break;
                    } else {
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();
                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                    }
                }

                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }

                boolean isConnectionSave = true;
                String syncId = null;
                String extendedAttributes = null;
                List<String> dbConnectIds = new ArrayList<>();
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (instanceUuid.equals(dbConnection.getSync())) {
                            isConnectionSave = false;
                            syncId = dbConnection.getSync();
                            dbConnectIds.add(dbConnection.getUnique_key());
                            extendedAttributes = dbConnection.getExtended_attributes();
                        }
                    }
                }
                if (StringUtils.isNotEmpty(serviceUser)) {
                    serviceUser = this.saveUser(serviceUser, allUserList);
                }
                if (isConnectionSave) {
                    saveCount += 1;
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    Integer securityRuleSetId = 0;
                    if (securityRuleSets != null && securityRuleSets.size() > 0) {
                        String ruleName = this.getRuleByCustomer(customerName);
                        for (SecurityRuleSet securityRuleSet : securityRuleSets) {
                            if (DatabaseType.ORACLE.getValue().equals(securityRuleSet.getDbType())) {
                                if (securityRuleSet.getIsBuiltIn().equals(1)) {
                                    securityRuleSetId = securityRuleSet.getId();
                                }
                                if (StringUtils.isNotEmpty(ruleName) && securityRuleSet.getRuleSetName().equals(ruleName + "-Oracle规则集") ) {
                                    securityRuleSetId = securityRuleSet.getId();
                                    break;
                                }
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.ORACLE.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }
                    if (connectUsers == null || connectUsers.size() == 0) {
                        continue;
                    }
                    for (Map umMap : connectUsers) {
                        String type = (String) umMap.get("type");
                        String readDesc = "1".equals(type) ? "读写" : "只读";
                        String um = (String) umMap.get("value");
                        DatabaseConnection databaseConnection = new DatabaseConnection();
                        String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                        databaseConnection.setUnique_key(uniqueKey);
                        databaseConnection.setInstance_name(entityName + "_Oracle_" + role + "_" + readDesc);
                        databaseConnection.setEnvironment(environment);
                        Integer dbType = DatabaseType.ORACLE.getValue();
                        databaseConnection.setDb_type(dbType);
                        databaseConnection.setIp(ip);
                        databaseConnection.setPort(String.valueOf(port));
                        databaseConnection.setUsername(um);
                        databaseConnection.setAuth_source(1);
                        databaseConnection.setCreator_id(sysUser.getUniqueKey());
                        databaseConnection.setConnect_type(2);
                        databaseConnection.setService_name(instanceName);
                        databaseConnection.setPattern(2);
                        databaseConnection.setIs_active(0);
                        databaseConnection.setDb_role("Normal");
                        databaseConnection.setSync(instanceUuid);
                        databaseConnection.setExtended_attributes(gson.toJson(secondExtendedList));
                        databaseConnection.setConnection(this.getConnect(dbType, ip, port, pi.getInstanceName()));
                        databaseConnection.setConnection_desc(this.getConnectDesc(dbType, ip, port, pi.getInstanceName()));
                        databaseConnection.setEntity(entityName);
//                    databaseConnection.setPort("1521");
//                    databaseConnection.setUsername("SYSTEM");
//                    databaseConnection.setConnect_type(1);
//                    databaseConnection.setService_name("helowin");
//                    databaseConnection.setAuth_source(0);
//                    databaseConnection.setPassword("iimbSYGvr5NN/grRHlRQwg==");
//                    databaseConnection.setIp("*************");
                        Map<String, String> timeParams = new HashMap<>();
                        timeParams.put("key", "oracle.net.CONNECT_TIMEOUT");
                        timeParams.put("value", String.valueOf(instanceTimeout * 1000));
                        String driverId = this.testConnection(databaseConnection, timeParams, null);
                        if (!StringUtils.isEmpty(driverId)) {
                            databaseConnection.setDriver_id(driverId);
                            databaseConnection.setIs_active(1);
                        }
                        databaseConnection.setSecurity_rule_set_id(securityRuleSetId);
                        databaseConnection.setCategory_id(categoryId);
                        connectionMapper.insert(databaseConnection);
                        this.createInstanceUser(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        this.createGroup(uniqueKey, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                        logger.info("实例" + instanceName + "创建完成");
                        if (!StringUtils.isEmpty(driverId)) {
                            new Thread(() -> {
                                this.refreshSchema(uniqueKey);
                            }).start();
                        }
                    }

                } else {
                    if (updateProp.size() > 0) {
                        updateCount += 1;
//                            if (updateProp.contains("status")) {
//                                databaseConnection.setPort(port.toString());
//                            }
                        if (StringUtils.isNotEmpty(extendedAttributes)) {
                            secondExtendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                            }.getType());
                        }
                        for (ConnectExtendMap connectExtendMap : secondExtendedList) {
                            if (connectExtendMap.getKey().equals("manager_da") && updateProp.get("manager_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("manager_da"));
                            }
                            if (connectExtendMap.getKey().equals("back_da") && updateProp.get("back_da") != null) {
                                connectExtendMap.setValue((String) updateProp.get("back_da"));
                            }
                            if (connectExtendMap.getKey().equals("service_user") && updateProp.get("service_user") != null) {
                                connectExtendMap.setValue((String) updateProp.get("service_user"));
                            }
                        }
                        String extendAttr = gson.toJson(secondExtendedList);
                        if (StringUtils.isNotEmpty(syncId)) {
                            Map<String, String> syncMap = new HashMap<>();
                            syncMap.put("sync", syncId);
                            syncMap.put("extended_attributes", extendAttr);
                            connectionMapper.updateBySyncId(syncMap);
                        }
                    }

                    this.updateInstanceUser(dbConnectIds, sysUser.getUniqueKey(), businessUserIds, serviceUser);
                    this.updateGroup(dbConnectIds, sysUser.getUniqueKey(), businessUserIds);

                }
            }

        }

        if (saveEntity.size() > 0) {
            paDatabaseEntityService.saveBatch(saveEntity);
        }
        if (saveInstances.size() > 0) {
            paInstanceService.saveBatch(saveInstances);
        }
        if (saveSubInstances.size() > 0) {
            paSubInstanceService.saveBatch(saveSubInstances);
        }
        if (updateEntity.size() > 0) {
            paDatabaseEntityService.updateBatchById(updateEntity);
        }
        if (updateInstances.size() > 0) {
            paInstanceService.updateBatchById(updateInstances);
        }
        if (updateSubInstances.size() > 0) {
            paSubInstanceService.updateBatchById(updateSubInstances);
        }
        result.add(saveCount);
        result.add(updateCount);
        return result;
    }

    private String getRuleByCustomer(String customerName) {
        if (StringUtils.isEmpty(customerName)) {
            return null;
        }
        for (Map.Entry<String,List<String>> entry : UCmdbConstants.ruleList.entrySet()) {
            List<String> value = entry.getValue();
            if (value.contains(customerName)) {
                return entry.getKey();
            }
        }
        return null;
    }

    @Override
    public SseEmitter syncBuss(SseEmitter sseEmitter) throws Exception {
        User sysUser = userService.getSystemUser();
        log.info("sync start");
        UcmdbService ucmdbService = com.dc.springboot.core.component.Resource.getBean(UcmdbService.class);
        ucmdbService.login();
        UcmdbParam ucmdbParam = new UcmdbParam();
        ucmdbParam.setPage(1);
        ucmdbParam.setRows(PAGE_SIZE);
        List<ResourceCatalog> dbCatalogs = resourceCatalogMapper.selectList(new QueryWrapper<ResourceCatalog>().eq("is_delete", 0));
        List<Category> dbCategores = categoryMapper.selectList(new QueryWrapper<Category>().eq("is_delete", 0));

        UcmdbCustomerResult userInfo = ucmdbService.getCustomerData(ucmdbParam);
        Integer totalCount = userInfo.getTotal();
        log.info("data:" + gson.toJson(userInfo));
        List<CustomerDto> customerList = userInfo.getItmisCustomer();

        if (customerList != null && customerList.size() > 0) {
            for (CustomerDto customer : customerList) {
                String customerNumber = customer.getCustomerNumber();
                String customerName = customer.getCustomerName();
                String bu = customer.getBusiOrg();
                Integer rcId = null;
                if (StringUtils.isEmpty(bu)) {
                    for (ResourceCatalog catalog : dbCatalogs) {
                        if (catalog.getIsBuiltIn().equals(1)) {
                            rcId = catalog.getId();
                            break;
                        }
                    }
                } else {
                    boolean isSave = true;
                    for (ResourceCatalog catalog : dbCatalogs) {
                        if (catalog.getCatalogName().equals(bu)) {
                            isSave = false;
                            rcId = catalog.getId();
                            break;
                        }
                    }
                    if (isSave) {
                        ResourceCatalog resourceCatalog = new ResourceCatalog();
                        resourceCatalog.setCatalogName(bu);
                        resourceCatalog.setDescription("");
                        resourceCatalog.setIsBuiltIn(0);
                        resourceCatalogMapper.insert(resourceCatalog);
                        dbCatalogs.add(resourceCatalog);
                        rcId = resourceCatalog.getId();
                    }
                }
                boolean isCategorySave = true;

                boolean isCategoryUpdate = false;
                Integer id = null;
                String cateUuid = null;
                for (Category category : dbCategores) {
                    if (category.getCategory_name().equals(customerName) && category.getBusiness_id() == null) {
                        isCategoryUpdate = true;
                        isCategorySave = false;
                        id = category.getId();
                        cateUuid = category.getUnique_key();
                        break;
                    }
                    if (category.getBusiness_id() != null && category.getBusiness_id().equals(customerNumber)) {
                        isCategorySave = false;
                        break;
                    }
                }
                if (isCategoryUpdate) {
                    Category category = new Category();
                    category.setId(id);
                    category.setBusiness_id(customerNumber);
                    categoryMapper.updateById(category);

//                    CatalogRelation catalogRelation = new CatalogRelation();
//                    catalogRelation.setCatalogId(rcId);
//                    catalogRelation.setRelType(1);
//                    catalogRelation.setRelId(cateUuid);
//                    catalogRelationMapper.insert(catalogRelation);
                }
                if (isCategorySave) {

                    Category category = new Category();
                    category.setCategory_name(customerName);
                    category.setCategory_names(gson.toJson(Arrays.asList(customerName)));
                    category.setBusiness_id(customerNumber);
                    String uniqueKey = UUID.randomUUID().toString().replace("-", "");
                    category.setUnique_key(uniqueKey);
                    category.setCategory_ids(gson.toJson(Arrays.asList(uniqueKey)));
                    category.setPid("");
                    category.setUid(sysUser.getUniqueKey());
                    categoryMapper.insert(category);
                    CatalogRelation catalogRelation = new CatalogRelation();
                    catalogRelation.setCatalogId(rcId);
                    catalogRelation.setRelType(1);
                    catalogRelation.setRelId(uniqueKey);
                    catalogRelationMapper.insert(catalogRelation);
                }
            }
        }
        log.info("sync catalog end");
        return null;
    }


    @Override
    public SseEmitter syncEntity(SseEmitter emitter) throws Exception {
        UcmdbService ucmdbService = com.dc.springboot.core.component.Resource.getBean(UcmdbService.class);
        ucmdbService.login();
        List<CustomizationConfig> customizationConfigList = customizationConfigMapper.selectList(new QueryWrapper<>());
        List<Map<String, String>> userList = new ArrayList<>();
        String[] redisDealList = null;
        List<String> businessUserIds = new ArrayList<>();

        for (CustomizationConfig customizationConfig : customizationConfigList) {
            String nameKey = customizationConfig.getNameKey();
            if ("leader_with_org".equals(nameKey)) {
                String orgId = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(orgId)) {
                    List<SysOrgUser> sysOrgUserList = sysOrgUserMapper.selectList(new QueryWrapper<SysOrgUser>().eq("org_id", orgId));
                    sysOrgUserList.forEach(sysOrgUser -> {
                        businessUserIds.add(sysOrgUser.getUid());
                    });
                }
            }

            if ("connect_account".equals(nameKey)) {
                String connectAccount = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(connectAccount)) {
                    Map<String, String> syncMap = gson.fromJson(connectAccount, Map.class);
                    String readonly = syncMap.get("readonly");
                    String wr = syncMap.get("wr");
                    if (StringUtils.isNotEmpty(readonly)) {
                        String[] readUserList = readonly.split(",");
                        for (String ru : readUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "2");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                    if (StringUtils.isNotEmpty(wr)) {
                        String[] wrUserList = wr.split(",");
                        for (String ru : wrUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "1");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                }

            }
        }
        if (businessUserIds == null || businessUserIds.size() == 0) {
            throw new Exception("实例负责人不能为空");
        }

        if (userList == null || userList.size() == 0) {
            throw new Exception("同步连接账号不能为空");
        }


//        List<String> mapList = Arrays.asList("DBT:O");

        List<String> mapList = Arrays.asList("DBT:M", "DBT:O","DBT:RA", "DBT:R","DBT:MG", "DBT:TI");

        emitter.send("sync entity start");
        String[] finalRedisDealList = redisDealList;
        new Thread(() -> {
            try {
                User sysUser = userService.getSystemUser();
                List<SecurityRuleSet> securityRuleSets = securityRuleSetService.getAllDefaultSecurityRuleSet();
                List<Category> dbCategoryList = categoryMapper.selectList(new QueryWrapper<>());

                List<DatabaseConnection> dbConnections = databaseConnectionMapper.getConnections();

                if (dbCategoryList != null && dbCategoryList.size() > 0) {
                    for (String databaseType : mapList) {
                        UcmdbParam ucmdbParam = new UcmdbParam();
                        ucmdbParam.setRows(PAGE_SIZE);
                        ucmdbParam.setDatabaseType(databaseType);
                        for (Category category : dbCategoryList) {
                            if (StringUtils.isNotEmpty(category.getBusiness_id())) {
                                ucmdbParam.setCustomerNumber(category.getBusiness_id());
                                ucmdbParam.setPage(1);
                                emitter.send("sync " + databaseType + " " + category.getBusiness_id() + " start");

                                UcmdbResult userInfo = ucmdbService.getData(ucmdbParam);
                                Integer totalCount = userInfo.getTotal();
                                List<PaDatabaseEntityDto> entityList = userInfo.getEntityDtoList();
                                if (databaseType.equals("DBT:O")) {
                                    this.dealOracleId(entityList,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, dbConnections);
                                } else if (databaseType.equals("DBT:M")) {
                                    this.dealMysqlId(entityList, securityRuleSets, dbCategoryList,sysUser, businessUserIds, userList, dbConnections);
                                } else if (databaseType.equals("DBT:RA")) {
                                    this.dealPostgresqlId(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, dbConnections);
                                } else if (databaseType.equals("DBT:TI")) {
                                    this.dealTidbId(entityList,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, dbConnections);
                                } else if (databaseType.equals("DBT:MG")) {
                                    this.dealMongodbId(entityList,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList,dbConnections);
                                } else if (databaseType.equals("DBT:R")) {
                                    this.dealRedisId(entityList,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, finalRedisDealList,dbConnections);
                                }


                                int count = totalCount % PAGE_SIZE == 0 ? (totalCount / PAGE_SIZE) : (totalCount / PAGE_SIZE + 1);
                                if (count > 1) {
                                    for (int i = 2; i <= count; i++) {
                                        ucmdbParam.setPage(i);
                                        UcmdbResult userInfo1 = ucmdbService.getData(ucmdbParam);
                                        List<PaDatabaseEntityDto> entityList1 = userInfo1.getEntityDtoList();
                                        if (databaseType.equals("DBT:O")) {
                                            this.dealOracleId(entityList1,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList,dbConnections);
                                        } else if (databaseType.equals("DBT:M")) {
                                            this.dealMysqlId(entityList1, securityRuleSets, dbCategoryList,sysUser, businessUserIds, userList,dbConnections);
                                        } else if (databaseType.equals("DBT:RA")) {
                                            this.dealPostgresqlId(entityList1, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList,dbConnections);
                                        } else if (databaseType.equals("DBT:TI")) {
                                            this.dealTidbId(entityList1,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList,dbConnections);
                                        } else if (databaseType.equals("DBT:MG")) {
                                            this.dealMongodbId(entityList1,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList,dbConnections);
                                        } else if (databaseType.equals("DBT:R")) {
                                            this.dealRedisId(entityList1,securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, finalRedisDealList,dbConnections);
                                        }
                                    }
                                }
                                emitter.send("sync " + databaseType + " " + category.getBusiness_id() + " end");
                            }
                        }
                    }
                }

                emitter.send("sync entity end");
                emitter.complete();
            } catch (Exception e) {
                try {
                    emitter.send("error:" + e.getMessage() + " " + gson.toJson(e.getStackTrace()));
                    emitter.complete();
                } catch (IOException ioException) {
                    ioException.printStackTrace();
                }
            }
        }).start();
        return emitter;
    }

    @Override
    public void syncByEntityName(EntityMessage entityMessage) throws Exception {

        UcmdbService ucmdbService = com.dc.springboot.core.component.Resource.getBean(UcmdbService.class);
        ucmdbService.login();
        List<CustomizationConfig> customizationConfigList = customizationConfigMapper.selectList(new QueryWrapper<>());
        List<Map<String, String>> userList = new ArrayList<>();
        String[] redisDealList = null;
        List<String> businessUserIds = new ArrayList<>();

        for (CustomizationConfig customizationConfig : customizationConfigList) {
            String nameKey = customizationConfig.getNameKey();
            if ("leader_with_org".equals(nameKey)) {
                String orgId = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(orgId)) {
                    List<SysOrgUser> sysOrgUserList = sysOrgUserMapper.selectList(new QueryWrapper<SysOrgUser>().eq("org_id", orgId));
                    sysOrgUserList.forEach(sysOrgUser -> {
                        businessUserIds.add(sysOrgUser.getUid());
                    });
                }
            }

            if ("connect_account".equals(nameKey)) {
                String connectAccount = customizationConfig.getContent();
                if (StringUtils.isNotEmpty(connectAccount)) {
                    Map<String, String> syncMap = gson.fromJson(connectAccount, Map.class);
                    String readonly = syncMap.get("readonly");
                    String wr = syncMap.get("wr");
                    if (StringUtils.isNotEmpty(readonly)) {
                        String[] readUserList = readonly.split(",");
                        for (String ru : readUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "2");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                    if (StringUtils.isNotEmpty(wr)) {
                        String[] wrUserList = wr.split(",");
                        for (String ru : wrUserList) {
                            Map rMap = new HashMap<String, String>();
                            rMap.put("type", "1");
                            rMap.put("value", ru);
                            userList.add(rMap);
                        }
                    }
                }
                if ("redis_suffix".equals(nameKey)) {
                    String redisDeal = customizationConfig.getContent();
                    if (StringUtils.isNotEmpty(redisDeal)) {
                        redisDealList = redisDeal.split(",");
                    }
                }

            }
        }


        if (businessUserIds == null || businessUserIds.size() == 0) {
            throw new Exception("sync instance leader is null");
        }

        if (userList == null || userList.size() == 0) {
            throw new Exception("sync connect account is null");
        }
        if (StringUtils.isEmpty(entityMessage.getEntity())) {
            throw new Exception("sync entityName is null");
        }
        UcmdbParam ucmdbParam = new UcmdbParam();
        ucmdbParam.setPage(1);
        ucmdbParam.setRows(PAGE_SIZE);
        ucmdbParam.setCustomerNumber(entityMessage.getCustomerName());
        ucmdbParam.setEntityName(entityMessage.getEntity());
        String databaseType = this.translateType(entityMessage.getDbType());
        if (databaseType == null) {
            throw new Exception("db type is null");
        }
        ucmdbParam.setDatabaseType(databaseType);
        UcmdbResult userInfo = ucmdbService.getData(ucmdbParam);
        List<PaDatabaseEntityDto> entityList = userInfo.getEntityDtoList();
        User sysUser = userService.getSystemUser();
        List<SecurityRuleSet> securityRuleSets = securityRuleSetService.getAllDefaultSecurityRuleSet();
        List<Category> dbCategoryList = categoryMapper.selectList(new QueryWrapper<>());
        if (entityList == null || entityList.size() == 0) {
            log.info("实体为空");
            return;
        }
        String[] finalRedisDealList = redisDealList;
        new Thread(() -> {
            try {
                if (databaseType.equals("DBT:O")) {
                    this.dealOracleData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                } else if (databaseType.equals("DBT:M")) {
                    this.dealMysqlData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                } else if (databaseType.equals("DBT:RA")) {
                    this.dealPostgresqlData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                } else if (databaseType.equals("DBT:TI")) {
                    this.dealTidbData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                } else if (databaseType.equals("DBT:MG")) {
                    this.dealMongodbData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList);
                } else if (databaseType.equals("DBT:R")) {
                    this.dealRedisData(entityList, securityRuleSets, dbCategoryList, sysUser, businessUserIds, userList, finalRedisDealList);
                }
            } catch (Exception e) {
                log.info("同步失败：" + e.getMessage());
            }
        }).start();

    }


    private String translateType(Integer dbType) {
        String resultType = null;
        List<Integer> dbTypeList = Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.MYSQL.getValue(), DatabaseType.RASE_SQL.getValue(), DatabaseType.TIDB.getValue(), DatabaseType.REDIS.getValue(), DatabaseType.MONGODB.getValue());
        if (dbTypeList.contains(dbType)) {
            DatabaseType of = DatabaseType.of(dbType);
            resultType = UCmdbConstants.dbMap.get(of.getDataSourceId().equals("postgresql") ? "rasesql" : of.getDataSourceId());
        }
        return resultType;
    }


    private void dealGroupUser(String instanceId, String userId, List<String> businessUserIds) {
        QueryWrapper<CiGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("connect_id", instanceId);
        List<CiGroup> groups = groupMapper.selectList(queryWrapper);
        Date date = new Date();
        if (groups != null && groups.size() > 1) {
            List<Integer> groupIds = new ArrayList<>();
            for (CiGroup group: groups) {
                groupIds.add(group.getId());
            }
            QueryWrapper<CiGroupUser> groupUserWrapper = new QueryWrapper<>();
            groupUserWrapper.in("group_id", groupIds);
            groupUserMapper.delete(groupUserWrapper);

            List<CiGroupUser> ciGroupUsers = new ArrayList<>();
            for (int i = 0; i < 2; i++) {
                for (String s : businessUserIds) {
                    CiGroupUser ciGroupUser = new CiGroupUser();
                    ciGroupUser.setGroupId(groupIds.get(i));
                    ciGroupUser.setUserId(s);
                    ciGroupUser.setOrganizationId("");
                    ciGroupUser.setBeginTime(date.getTime());
                    ciGroupUser.setEndTime(4102415999L); // 2099-12-31 23:59:59
                    ciGroupUser.setOperator(userId);
                    ciGroupUser.setGmtCreate(date);
                    ciGroupUser.setGmtModified(date);
                    ciGroupUser.setIsDelete(0);
                    ciGroupUsers.add(ciGroupUser);
                }
            }
            groupUserMapper.insertBatchSomeColumn(ciGroupUsers);
        }



    }

    private void deleteInstanceUser(String instanceId) {
        QueryWrapper<DatabaseConnectionUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("connect_id", instanceId);
        collectionUserMapper.delete(queryWrapper);
    }

//    private void updateInstance(String instanceId, String businessId) {
//        QueryWrapper<DatabaseConnection> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("unique_key", instanceId);
//        queryWrapper.eq("is_delete", 0);
//        DatabaseConnection databaseConnection = new DatabaseConnection();
//        databaseConnection.setCategoryId(businessId);
//        connectionMapper.update(databaseConnection, queryWrapper);
//    }

    @Override
    public void createInstanceUser(String instanceId, String userId, List<String> businessUserIds, String serviceUser) {
        if (businessUserIds == null || businessUserIds.size() == 0) {
            return;
        }
        List<DatabaseConnectionUser> categoryUsers = new ArrayList<>();
        Date date = new Date();
        for (String user : businessUserIds) {

//            for (int i = 0; i < 2; i++) {
                DatabaseConnectionUser databaseCategoryUser = new DatabaseConnectionUser();
                databaseCategoryUser.setConnectId(instanceId);
                databaseCategoryUser.setRelationType("connect_manager");
                databaseCategoryUser.setUserId(user);
                databaseCategoryUser.setIsDelete(0);
                databaseCategoryUser.setOperatorId(userId);
                databaseCategoryUser.setGmtCreate(date);
                databaseCategoryUser.setGmtModified(date);
                categoryUsers.add(databaseCategoryUser);
//            }

        }
        if (StringUtils.isNotEmpty(serviceUser)) {
            String[] serviceUsers = serviceUser.split(",");
            for(String sUser: serviceUsers) {
                DatabaseConnectionUser databaseCategoryUser = new DatabaseConnectionUser();
                databaseCategoryUser.setConnectId(instanceId);
                databaseCategoryUser.setRelationType("sensitive_manager");
                databaseCategoryUser.setUserId(sUser);
                databaseCategoryUser.setIsDelete(0);
                databaseCategoryUser.setOperatorId(userId);
                databaseCategoryUser.setGmtCreate(date);
                databaseCategoryUser.setGmtModified(date);
                categoryUsers.add(databaseCategoryUser);
            }

        }
        collectionUserMapper.insertBatchSomeColumn(categoryUsers);


    }

    @Override
    public void updateInstanceUser(List<String> instanceIds, String userId, List<String> businessUserIds, String serviceUser) {
        if (businessUserIds == null || businessUserIds.size() == 0 ) {
            return;
        }

        List<DatabaseConnectionUser> dbConnUser = collectionUserMapper.selectList(new QueryWrapper<DatabaseConnectionUser>().eq("connect_id", instanceIds.get(0)).eq("relation_type", "connect_manager").eq("is_delete", 0));
        boolean isInstanceUserSave = false;
        for (String bu :businessUserIds) {
            boolean isExist = false;
            for (DatabaseConnectionUser dc: dbConnUser) {
                if (bu.equals(dc.getUserId())) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                isInstanceUserSave = true;
                break;
            }
        }


        List<DatabaseConnectionUser> categoryUsers = new ArrayList<>();
        Date date = new Date();
        if (isInstanceUserSave ) {
            collectionUserMapper.delete(new QueryWrapper<DatabaseConnectionUser>().in("connect_id", instanceIds).eq("relation_type", "connect_manager"));
            for (String user : businessUserIds) {
//            for (int i = 0; i < 2; i++) {
                for (String s : instanceIds) {
                    DatabaseConnectionUser databaseCategoryUser = new DatabaseConnectionUser();
                    databaseCategoryUser.setConnectId(s);
                    databaseCategoryUser.setRelationType("connect_manager");
                    databaseCategoryUser.setUserId(user);
                    databaseCategoryUser.setIsDelete(0);
                    databaseCategoryUser.setOperatorId(userId);
                    databaseCategoryUser.setGmtCreate(date);
                    databaseCategoryUser.setGmtModified(date);
                    categoryUsers.add(databaseCategoryUser);
                }
//            }

            }
        }
        if (StringUtils.isNotEmpty(serviceUser)) {
            List<DatabaseConnectionUser> dbSConnUser = collectionUserMapper.selectList(new QueryWrapper<DatabaseConnectionUser>().eq("connect_id", instanceIds.get(0)).eq("relation_type", "sensitive_manager").eq("is_delete", 0));
                boolean isSave = true;
                for (DatabaseConnectionUser dc: dbSConnUser) {
                    if (serviceUser.equals(dc.getUserId())) {
                        isSave = false;
                        break;
                    }
                }
                if (isSave) {
                    collectionUserMapper.delete(new QueryWrapper<DatabaseConnectionUser>().in("connect_id", instanceIds).eq("relation_type", "sensitive_manager"));
                    for (String s : instanceIds) {
                        DatabaseConnectionUser databaseCategoryUser = new DatabaseConnectionUser();
                        databaseCategoryUser.setConnectId(s);
                        databaseCategoryUser.setRelationType("sensitive_manager");
                        databaseCategoryUser.setUserId(serviceUser);
                        databaseCategoryUser.setIsDelete(0);
                        databaseCategoryUser.setOperatorId(userId);
                        databaseCategoryUser.setGmtCreate(date);
                        databaseCategoryUser.setGmtModified(date);
                        categoryUsers.add(databaseCategoryUser);
                    }
                }
        }
        if (categoryUsers.size() > 0) {
            try {
                collectionUserMapper.insertBatchSomeColumn(categoryUsers);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
        }
    }


    private String getConnectDesc(Integer dbType, String ip, Integer port, String serviceName) {

        String desc = "";
        if (dbType.equals(DatabaseType.ORACLE.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        } else if (dbType.equals(DatabaseType.MYSQL.getValue())) {
            desc = ip + ":" + port;
        }  else if (dbType.equals(DatabaseType.RASE_SQL.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        } else if (dbType.equals(DatabaseType.TIDB.getValue())) {
            desc = ip + ":" + port;
        } else if (dbType.equals(DatabaseType.REDIS.getValue())) {
            desc = ip + ":" + port;
        } else if (dbType.equals(DatabaseType.MONGODB.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        }
        return desc;
    }

    private String getConnect(Integer dbType, String ip, Integer port, String serviceName) {
        String desc = "";
        if (dbType.equals(DatabaseType.ORACLE.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        } else if (dbType.equals(DatabaseType.MYSQL.getValue())) {
            desc = ip + ":" + port;
        }  else if (dbType.equals(DatabaseType.RASE_SQL.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        } else if (dbType.equals(DatabaseType.TIDB.getValue())) {
            desc = ip + ":" + port;

        } else if (dbType.equals(DatabaseType.REDIS.getValue())) {
            desc = ip + ":" + port;
        } else if (dbType.equals(DatabaseType.MONGODB.getValue())) {
            desc = ip + ":" + port;
            if (!StringUtils.isEmpty(serviceName)) {
                desc += "/" + serviceName;
            }
        }
        return desc;
    }


//    private String buildServiceName(ZjInstanceInfo zjInstanceInfo) {
//        Integer dbType = this.getType(zjInstanceInfo.getDbType());
//        String serviceName = "";
//        if (dbType.equals(DbConnectionType.ORACLE)) {
//            if (StringUtils.isEmpty(zjInstanceInfo.getServiceName())) {
//                serviceName = zjInstanceInfo.getSId();
//            } else {
//                serviceName = zjInstanceInfo.getServiceName();
//            }
//        } else if (dbType.equals(DbConnectionType.DB_PG)) {
//            serviceName = zjInstanceInfo.getDbName();
//        } else if (dbType.equals(DbConnectionType.DB_MONGO)) {
//            serviceName = zjInstanceInfo.getDbName();
//        }
//        return serviceName;
//    }

//    //实例别名
//    public String  buildInstanceName(ZjInstanceInfo info){
//        String serviceName = (StringUtils.isNotEmpty(info.getVIp()) || StringUtils.isNotEmpty(info.getScanIp()))?(info.getServiceName()):(info.getInstanceName());
//        String instance = "";
//        if (StringUtils.isNotEmpty(info.getHostIp()))  {
//            instance = info.getHostIp();
//        }
//        if (StringUtils.isNotEmpty(serviceName))  {
//            if (instance.equals("")) {
//                instance = serviceName;
//            } else {
//                instance += "_" + serviceName;
//            }
//        }
//        if (StringUtils.isNotEmpty(info.getRole()))  {
//            if (instance.equals("")) {
//                instance = info.getRole();
//            } else {
//                instance += "_" + info.getRole();
//            }
//        }
//        return instance;
//    }

//    public Integer getType(String type){
//        Map<String, Integer> dbTypeObject = new HashMap<>() {{
//            put("oracle", DbConnectionType.ORACLE);
//            put("mysql", DbConnectionType.MYSQL);
//            put("sqlserver", DbConnectionType.SQLSERVER);
//            put("pg", DbConnectionType.DB_PG);
//            put("dm", DbConnectionType.DM);
//            put("ob-oracle", DbConnectionType.OBORACLE);
//            put("ob-mysql", DbConnectionType.OBMYSQL);
//            put("tidb", DbConnectionType.TIDB);
//            put("redis", DbConnectionType.DB_REDIS);
//            put("mongodb", DbConnectionType.DB_MONGO);
//        }};
//        Integer dbType = null;
//        if (dbTypeObject.containsKey(type)) {
//            dbType = dbTypeObject.get(type);
//        }
//        return dbType;
//    }


    private void removeCategoryUsers(String category_id) {
        QueryWrapper<DatabaseCategoryUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", category_id);
        categoryUserMapper.delete(queryWrapper);
    }

    private void bindManager(String id, List<String> businessUserIds, String userId) {

        List<DatabaseCategoryUser> categoryUsers = new ArrayList<>();
        Date date = new Date();
        for (String user : businessUserIds) {
            DatabaseCategoryUser databaseCategoryUser = new DatabaseCategoryUser();
            databaseCategoryUser.setCategoryId(id);
            databaseCategoryUser.setUserId(user);
            databaseCategoryUser.setIsDelete(0);
            databaseCategoryUser.setOperatorId(userId);
            databaseCategoryUser.setGmtCreate(date);
            databaseCategoryUser.setGmtModified(date);
            categoryUsers.add(databaseCategoryUser);
        }
        categoryUserMapper.insertBatchSomeColumn(categoryUsers);
    }

    @Override
    public void createGroup(String instanceId, String userId, List<String> businessUserIds, String serviceUser ) {
        if (businessUserIds == null || businessUserIds.size() == 0) {
            return;
        }
        Date date  = new Date();
        List<CiGroup> ciGroups = new ArrayList<>();
        int count = 1;
        if (StringUtils.isNotEmpty(serviceUser)) {
            count = 2;
        }
        for (int i = 0; i < count; i++) {
            CiGroup ciGroup = new CiGroup();
            ciGroup.setGroupType(2);
            ciGroup.setGroupName(UUID.randomUUID().toString().replace("-", ""));
            ciGroup.setOperator(userId);
            ciGroup.setIntro("虚拟组");
            ciGroup.setIsActive(1);
            ciGroup.setIsDelete(0);
            ciGroup.setGmtCreate(date);
            ciGroup.setGmtModified(date);
//            ciGroup.setConnectId(instanceId);
            ciGroups.add(ciGroup);
        }
        groupMapper.insertBatchSomeColumn(ciGroups);

        List<CiGroupRule> groupRules = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            CiGroupRule ciGroupRule = new CiGroupRule();
            ciGroupRule.setGroupId(ciGroups.get(i).getId());
            ciGroupRule.setBaseType(i + 1);
            ciGroupRule.setApplyType(0);
            ciGroupRule.setOperator(userId);
            ciGroupRule.setOrigin(4);
            ciGroupRule.setConnectId(instanceId);
            ciGroupRule.setSchemaId("");
            ciGroupRule.setIsInstanceRole(1);
            ciGroupRule.setResourceType(1);
            ciGroupRule.setObjectName("");
            ciGroupRule.setColumnName("");
            ciGroupRule.setIsMarket(0);
            ciGroupRule.setIsDelete(0);
            ciGroupRule.setGmtCreate(date);
            ciGroupRule.setGmtModified(date);
            groupRules.add(ciGroupRule);
        }
        groupRuleMapper.insertBatchSomeColumn(groupRules);

        List<CiGroupPermission> groupPermissions = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            CiGroupPermission ciGroupPermission = new CiGroupPermission();
            ciGroupPermission.setRuleId(groupRules.get(i).getId());
            String permission = i == 0 ? "leader_role_instance_auth" : "sensitive_head";;
            ciGroupPermission.setActionKey(permission);
            ciGroupPermission.setIsDelete(0);
            ciGroupPermission.setGmtCreate(date);
            ciGroupPermission.setGmtModified(date);
            groupPermissions.add(ciGroupPermission);
        }
        groupPermissionMapper.insertBatchSomeColumn(groupPermissions);

        List<CiGroupUser> ciGroupUsers = new ArrayList<>();
        for (String s : businessUserIds) {
            CiGroupUser ciGroupUser = new CiGroupUser();
            ciGroupUser.setGroupId(ciGroups.get(0).getId());
            ciGroupUser.setUserId(s);
            ciGroupUser.setOrganizationId("");
            ciGroupUser.setBeginTime(date.getTime() / 1000);
            ciGroupUser.setEndTime(4102415999L); // 2099-12-31 23:59:59
            ciGroupUser.setOperator(userId);
            ciGroupUser.setGmtCreate(date);
            ciGroupUser.setGmtModified(date);
            ciGroupUser.setIsDelete(0);
            ciGroupUsers.add(ciGroupUser);
        }
        if (StringUtils.isNotEmpty(serviceUser)) {
            String[] serviceUsers = serviceUser.split(",");
            for(String sUser: serviceUsers) {
                CiGroupUser ciGroupUser = new CiGroupUser();
                ciGroupUser.setGroupId(ciGroups.get(1).getId());
                ciGroupUser.setUserId(sUser);
                ciGroupUser.setOrganizationId("");
                ciGroupUser.setBeginTime(date.getTime() / 1000);
                ciGroupUser.setEndTime(4102415999L); // 2099-12-31 23:59:59
                ciGroupUser.setOperator(userId);
                ciGroupUser.setGmtCreate(date);
                ciGroupUser.setGmtModified(date);
                ciGroupUser.setIsDelete(0);
                ciGroupUsers.add(ciGroupUser);
            }

        }
        groupUserMapper.insertBatchSomeColumn(ciGroupUsers);

    }

    @Override
    public void updateGroup(List<String> instanceIds, String userId, List<String> businessUserIds ) {
        if (businessUserIds == null || businessUserIds.size() == 0) {
            return;
        }
        List<CiGroupRule> dbGroupRules = groupRuleMapper.selectList(new QueryWrapper<CiGroupRule>().in("connect_id", instanceIds).eq("base_type", 1).eq("is_instance_role", 1).eq("is_delete", 0));
        if (dbGroupRules.size() == 0) {
            return;
        }
        List<Integer> groupIdList = new ArrayList<>();
        for (CiGroupRule cg: dbGroupRules) {
            groupIdList.add(cg.getGroupId());
        }
        List<CiGroupUser> dbGroupUsers  = groupUserMapper.selectList(new QueryWrapper<CiGroupUser>().eq("group_id", dbGroupRules.get(0).getGroupId()).eq("is_delete", 0));
        if (dbGroupUsers.size() == 0) {
            return;
        }
        boolean isInstanceUserSave = false;
        for (String bu :businessUserIds) {
            boolean isExist = false;
            for (CiGroupUser dc: dbGroupUsers) {
                if (bu.equals(dc.getUserId())) {
                    isExist = true;
                    break;
                }
            }
            if (!isExist) {
                isInstanceUserSave = true;
                break;
            }
        }

        if (isInstanceUserSave) {
            groupUserMapper.delete(new QueryWrapper<CiGroupUser>().in("group_id", groupIdList));
            List<CiGroupUser> ciGroupUsers = new ArrayList<>();
//        for (int i = 0; i < 2; i++) {
            Date date = new Date();
            for (String s : businessUserIds) {
                for (CiGroupRule  cg: dbGroupRules) {
                    CiGroupUser ciGroupUser = new CiGroupUser();
                    ciGroupUser.setGroupId(cg.getGroupId());
                    ciGroupUser.setUserId(s);
                    ciGroupUser.setOrganizationId("");
                    ciGroupUser.setBeginTime(date.getTime() / 1000);
                    ciGroupUser.setEndTime(4102415999L); // 2099-12-31 23:59:59
                    ciGroupUser.setOperator(userId);
                    ciGroupUser.setGmtCreate(date);
                    ciGroupUser.setGmtModified(date);
                    ciGroupUser.setIsDelete(0);
                    ciGroupUsers.add(ciGroupUser);
                }
            }
//        }
            groupUserMapper.insertBatchSomeColumn(ciGroupUsers);
        }
    }

    private List<List<String>> getCustomerList(String filePath, String fileName) {
        List<List<String>> data = null;
        try {
            String url = SummerConfig.getPathInstance().getDcBackend() + ApiConfig.DOWNLOAD.getPath() + filePath;
            url = FileUtil.buildDownloadUrl(url, filePath);
            String newFileName = FileUtil.downloadHttpUrl(url, Global.getDOWNLOAD(), fileName);
            data = XlsxUtil.readXlsx(newFileName);
        } catch (Exception e) {
            log.info("读取文件失败：" + e.getMessage());
        }
        return data;

    }


    private void dealOracleId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser,
                                List<String> businessUserIds, List<Map<String, String>> connectUsers, List<DatabaseConnection> dbConnections) {
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.ORACLE.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        List<String> dbUuidList = new ArrayList<>();
        for (PaDatabaseEntityDto pe : entityList) {
            dbUuidList.add(pe.getEntityUuid());
        }

        for (PaDatabaseEntityDto pe : entityList) {
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");

            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
//            ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
//            databaseVersionMap.setKey("database_version");
//            databaseVersionMap.setValue(pe.getDatabaseVersion());
//            databaseVersionMap.setDescription("数据库版本");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
//            extendList.add(databaseVersionMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                String role = UCmdbConstants.paMap.get(pi.getDefaultRole());
                defaultRoleMap.setValue(role);
                defaultRoleMap.setDescription("默认角色");

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                String serviceUser = pi.getServiceUser();
                serviceUserMap.setValue(serviceUser);
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap architectureTypeMap = new ConnectExtendMap();
                architectureTypeMap.setKey("architecture_type");
                architectureTypeMap.setValue(UCmdbConstants.paMap.get(pi.getArchitectureType()));
                architectureTypeMap.setDescription("架构类型");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(architectureTypeMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }

                for (PaSubInstanceDto ps : paSubInstances1) {
                    String ip = null;
                    Integer port = null;
                    String instanceUuid = null;
                    String domainName = null;

                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        domainName = ps.getDomainName();
                        port = ps.getPort();
                        instanceUuid = ps.getInstanceUuid();
                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        secondExtendedList.add(subInstanceUuidMap);
                    }
                    if (StringUtils.isNotEmpty(ps.getVip())) {
                        ip = ps.getVip();
                        port = ps.getPort();
                        instanceUuid = ps.getInstanceUuid();
                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        secondExtendedList.add(subInstanceUuidMap);
                    }
                    if (StringUtils.isEmpty(ip) && StringUtils.isEmpty(domainName)) {  // ip为空，则不保存
                        continue;
                    }

                    String extendedAttributes = null;
                    Integer connectId = null;
                    Integer security_rule_set_id = null;
                    if (dbConnections != null && dbConnections.size() > 0) {
                        for (DatabaseConnection dbConnection : dbConnections) {

                            if (DatabaseType.ORACLE.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(domainName)
                                    && dbConnection.getPort().equals(port.toString())) {
                                connectId = dbConnection.getId();
                                security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                extendedAttributes = dbConnection.getExtended_attributes();
                                break;
                            }

                            if (DatabaseType.ORACLE.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                    && dbConnection.getPort().equals(port.toString())) {
                                connectId = dbConnection.getId();
                                security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                extendedAttributes = dbConnection.getExtended_attributes();
                                break;
                            }
                        }
                    }

                    if (connectId != null) {
                        String categoryId = null;
                        if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                            for (Category category : dbCategoryList) {
                                if (customerNumber.equals(category.getBusiness_id())) {
                                    categoryId = category.getUnique_key();
                                    break;
                                }
                            }
                        }
                        boolean isSaveEntity = true;
                        if (dbEntity != null && dbEntity.size() > 0) {
                            for (DbEntity de : dbEntity) {
                                if (entityName.equals(de.getEntityName())) {
                                    isSaveEntity = false;
                                    break;
                                }
                            }
                        }

                        if (isSaveEntity) {
                            DbEntity dbEntity1 = new DbEntity();
                            dbEntity1.setEntityName(entityName);
                            dbEntity1.setDbType(DatabaseType.ORACLE.getValue());
                            entityMapper.insert(dbEntity1);
                            dbEntity.add(dbEntity1);
                        }

                        if (StringUtils.isNotEmpty(extendedAttributes)) {
                            List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                            }.getType());
                            secondExtendedList.addAll(extendedList);
                        }
                        String extendAttr = gson.toJson(secondExtendedList);
                        DatabaseConnection databaseConnection = new DatabaseConnection();
                        databaseConnection.setEntity(entityName);
                        databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                        databaseConnection.setId(connectId);
                        databaseConnection.setExtended_attributes(extendAttr);
                        databaseConnection.setSync(instanceUuid);
                        connectionMapper.updateById(databaseConnection);
                    }
                }
            }
        }
    }



    private void dealMysqlId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                             List<DatabaseConnection> dbConnections) {


        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.MYSQL.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        for (PaDatabaseEntityDto pe : entityList) {
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");

            String customerNumber = pe.getCustomerNumber();

            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                serviceUserMap.setValue(pi.getServiceUser());
                serviceUserMap.setDescription("应用接口人");


                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap systemNameMap = new ConnectExtendMap();
                systemNameMap.setKey("system_name");
                systemNameMap.setValue(pi.getSystemName());
                systemNameMap.setDescription("系统名称");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                Integer environment = this.getEnvironment(pi.getEnvironment());
                String entityName = pi.getInstanceName();
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemNameMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);
                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    String ip = null;
                    String domainName = null;
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        domainName = ps.getDomainName();
                    }
                    if (StringUtils.isNotEmpty(ps.getVip())) {
                        ip = ps.getVip();
                    }


                    if (StringUtils.isNotEmpty(ip) || StringUtils.isNotEmpty(domainName)) {
                        List<ConnectExtendMap> newExtendedList = new ArrayList<>();
                        Integer port = ps.getPort();
                        String instanceUuid = ps.getInstanceUuid();
                        String instanceName = ps.getInstanceName();

                        ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                        defaultRoleMap.setKey("default_role");
                        String role = UCmdbConstants.paMap.get(ps.getDefaultRole());
                        defaultRoleMap.setValue(role);
                        defaultRoleMap.setDescription("默认角色");

                        ConnectExtendMap statusMap = new ConnectExtendMap();
                        statusMap.setKey("status");
                        statusMap.setValue(UCmdbConstants.paMap.get(ps.getStatus()));
                        statusMap.setDescription("状态");

                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        newExtendedList.add(defaultRoleMap);
                        newExtendedList.add(statusMap);
                        newExtendedList.add(subInstanceUuidMap);
                        newExtendedList.addAll(secondExtendedList);


                        String extendedAttributes = null;
                        Integer connectId = null;
                        Integer security_rule_set_id = null;
                        if (dbConnections != null && dbConnections.size() > 0) {
                            for (DatabaseConnection dbConnection : dbConnections) {
                                if (DatabaseType.MYSQL.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(domainName)
                                        && dbConnection.getPort().equals(port.toString())) {
                                    connectId = dbConnection.getId();
                                    security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                    break;
                                }

                                if (DatabaseType.MYSQL.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                        && dbConnection.getPort().equals(port.toString())) {
                                    connectId = dbConnection.getId();
                                    security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                    break;
                                }
                            }
                        }

                        if (connectId != null) {
                            String categoryId = null;
                            if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                                for (Category category : dbCategoryList) {
                                    if (customerNumber.equals(category.getBusiness_id())) {
                                        categoryId = category.getUnique_key();
                                        break;
                                    }
                                }
                            }
                            boolean isSaveEntity = true;
                            if (dbEntity != null && dbEntity.size() > 0) {
                                for (DbEntity de : dbEntity) {
                                    if (entityName.equals(de.getEntityName())) {
                                        isSaveEntity = false;
                                        break;
                                    }
                                }
                            }

                            if (isSaveEntity) {
                                DbEntity dbEntity1 = new DbEntity();
                                dbEntity1.setEntityName(entityName);
                                dbEntity1.setDbType(DatabaseType.MYSQL.getValue());
                                entityMapper.insert(dbEntity1);
                                dbEntity.add(dbEntity1);
                            }

                            if (StringUtils.isNotEmpty(extendedAttributes)) {
                                List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                                }.getType());
                                newExtendedList.addAll(extendedList);
                            }
                            String extendAttr = gson.toJson(newExtendedList);
                            DatabaseConnection databaseConnection = new DatabaseConnection();
                            databaseConnection.setEntity(entityName);
                            databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                            databaseConnection.setId(connectId);
                            databaseConnection.setExtended_attributes(extendAttr);
                            databaseConnection.setSync(instanceUuid);
                            connectionMapper.updateById(databaseConnection);
                        }
                    }
                }
            }
        }
    }


    private void dealRedisId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                               String[] redisDealList,List<DatabaseConnection> dbConnections) {
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.REDIS.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }

                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                defaultRoleMap.setValue(UCmdbConstants.paMap.get(pi.getDefaultRole()));
                defaultRoleMap.setDescription("默认角色");

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                serviceUserMap.setValue(pi.getServiceUser());
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                String entityName = pi.getInstanceName();
                entityName = this.dealRedisName(entityName, redisDealList);

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(entityName);
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap architectureMap = new ConnectExtendMap();
                architectureMap.setKey("architecture_type");
                String role = UCmdbConstants.paMap.get(pi.getArchitectureType());
                architectureMap.setValue(role);
                architectureMap.setDescription("架构类型");

                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");

                Integer environment = this.getEnvironment(pi.getEnvironment());


                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(architectureMap);
                secondExtendedList.add(entityUuidMap);

                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                String instanceName = null;
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        if (StringUtils.isNotEmpty(ps.getVip())) {
                            ip = ps.getVip();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                    }
                } else {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }

                String extendedAttributes = null;
                Integer connectId = null;
                Integer security_rule_set_id = null;
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (DatabaseType.REDIS.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                && dbConnection.getPort().equals(port.toString())) {
                            connectId = dbConnection.getId();
                            security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                            extendedAttributes = dbConnection.getExtended_attributes();
                            break;
                        }
                    }
                }

                if (connectId != null) {
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.REDIS.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }

                    if (StringUtils.isNotEmpty(extendedAttributes)) {
                        List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                        }.getType());
                        secondExtendedList.addAll(extendedList);
                    }
                    String extendAttr = gson.toJson(secondExtendedList);
                    DatabaseConnection databaseConnection = new DatabaseConnection();
                    databaseConnection.setEntity(entityName);
                    databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                    databaseConnection.setId(connectId);
                    databaseConnection.setExtended_attributes(extendAttr);
                    databaseConnection.setSync(instanceUuid);
                    connectionMapper.updateById(databaseConnection);
                }
            }
        }

    }


    private void dealMongodbId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                               List<DatabaseConnection> dbConnections) {
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.MONGODB.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();

            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }
                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                serviceUserMap.setValue(pi.getServiceUser());
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");
                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");
                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();

                ConnectExtendMap isSeparateMap = new ConnectExtendMap();
                isSeparateMap.setKey("is_separate");
                String isSeparate = UCmdbConstants.paMap.get(pi.getIsSeparate());
                isSeparateMap.setValue(isSeparate);
                isSeparateMap.setDescription("是否分片");

                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(entityUuidMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(isSeparateMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                if (StringUtils.isNotEmpty(pi.getVip())) {
                    ip = pi.getVip();
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        String domainName = ps.getDomainName();
                        if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                            ip = domainName;
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                            defaultRoleMap.setKey("default_role");
                            defaultRoleMap.setValue(UCmdbConstants.paMap.get(ps.getDefaultRole()));
                            defaultRoleMap.setDescription("默认角色");
                            secondExtendedList.add(defaultRoleMap);
                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        } else {
                            if (StringUtils.isNotEmpty(ps.getHostIp())) {
                                ip = ps.getHostIp();
                                port = ps.getPort();
                                instanceUuid = ps.getInstanceUuid();
                                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                                defaultRoleMap.setKey("default_role");
                                defaultRoleMap.setValue(UCmdbConstants.paMap.get(ps.getDefaultRole()));
                                defaultRoleMap.setDescription("默认角色");

                                ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                                subInstanceUuidMap.setKey("instance_uuid");
                                subInstanceUuidMap.setValue(instanceUuid);
                                subInstanceUuidMap.setDescription("实例UUID");
                                secondExtendedList.add(defaultRoleMap);
                                secondExtendedList.add(subInstanceUuidMap);
                                break;
                            }
                        }
                    }
                } else {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                String extendedAttributes = null;
                Integer connectId = null;
                Integer security_rule_set_id = null;
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (DatabaseType.MONGODB.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                && dbConnection.getPort().equals(port.toString())) {
                            connectId = dbConnection.getId();
                            security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                            extendedAttributes = dbConnection.getExtended_attributes();
                            break;
                        }
                    }
                }

                if (connectId != null) {
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.MONGODB.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }

                    if (StringUtils.isNotEmpty(extendedAttributes)) {
                        List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                        }.getType());
                        secondExtendedList.addAll(extendedList);
                    }
                    String extendAttr = gson.toJson(secondExtendedList);
                    DatabaseConnection databaseConnection = new DatabaseConnection();
                    databaseConnection.setEntity(entityName);
                    databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                    databaseConnection.setId(connectId);
                    databaseConnection.setExtended_attributes(extendAttr);
                    databaseConnection.setSync(instanceUuid);
                    connectionMapper.updateById(databaseConnection);
                }
            }
        }
    }

    private void dealTidbId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                            List<DatabaseConnection> dbConnections) {
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.TIDB.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();

            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {
                if (!"DBS:ON".equals(pi.getStatus()) && !"DBS:MT".equals(pi.getStatus())) {
                    continue;
                }

                ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                defaultRoleMap.setKey("default_role");
                String role = UCmdbConstants.paMap.get(pi.getDefaultRole());
                defaultRoleMap.setValue(role);
                defaultRoleMap.setDescription("默认角色");

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                serviceUserMap.setValue(pi.getServiceUser());
                serviceUserMap.setDescription("应用接口人");

                ConnectExtendMap statusMap = new ConnectExtendMap();
                statusMap.setKey("status");
                statusMap.setValue(UCmdbConstants.paMap.get(pi.getStatus()));
                statusMap.setDescription("状态");

                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");
                ConnectExtendMap systemMap = new ConnectExtendMap();
                systemMap.setKey("system_name");
                systemMap.setValue(pi.getSystemName());
                systemMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                String entityName = pi.getInstanceName();
                Integer environment = this.getEnvironment(pi.getEnvironment());
                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();

                secondExtendedList.add(defaultRoleMap);
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(statusMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemMap);
                secondExtendedList.add(entityUuidMap);

                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isClassMap);
                secondExtendedList.add(isCopyAuthMap);

                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                String ip = null;
                Integer port = pi.getPort();
                String instanceUuid = pi.getInstanceUuid();
                String instanceName = pi.getInstanceName();
                String domainName = pi.getDomainName();
                if (StringUtils.isNotEmpty(domainName) && !"N/A".equals(domainName)) {
                    ip = domainName;
                } else {
                    if (StringUtils.isNotEmpty(pi.getVip())) {
                        ip = pi.getVip();
                    }
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                        continue;
                    }
                    for (PaSubInstanceDto ps : paSubInstances1) {
                        if (StringUtils.isNotEmpty(ps.getHostIp())) {
                            ip = ps.getHostIp();
                            port = ps.getPort();
                            instanceUuid = ps.getInstanceUuid();
                            instanceName = ps.getInstanceName();

                            ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                            subInstanceUuidMap.setKey("instance_uuid");
                            subInstanceUuidMap.setValue(instanceUuid);
                            subInstanceUuidMap.setDescription("实例UUID");
                            secondExtendedList.add(subInstanceUuidMap);
                            break;
                        }
                    }
                } else {
                    ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                    subInstanceUuidMap.setKey("instance_uuid");
                    subInstanceUuidMap.setValue(instanceUuid);
                    subInstanceUuidMap.setDescription("实例UUID");
                    secondExtendedList.add(subInstanceUuidMap);
                }
                if (StringUtils.isEmpty(ip)) {  // ip为空，则不保存
                    continue;
                }
                String extendedAttributes = null;
                Integer connectId = null;
                Integer security_rule_set_id = null;
                if (dbConnections != null && dbConnections.size() > 0) {
                    for (DatabaseConnection dbConnection : dbConnections) {
                        if (DatabaseType.TIDB.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                && dbConnection.getPort().equals(port.toString())) {
                            connectId = dbConnection.getId();
                            security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                            extendedAttributes = dbConnection.getExtended_attributes();
                            break;
                        }
                    }
                }

                if (connectId != null) {
                    String categoryId = null;
                    if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                        for (Category category : dbCategoryList) {
                            if (customerNumber.equals(category.getBusiness_id())) {
                                categoryId = category.getUnique_key();
                                break;
                            }
                        }
                    }
                    boolean isSaveEntity = true;
                    if (dbEntity != null && dbEntity.size() > 0) {
                        for (DbEntity de : dbEntity) {
                            if (entityName.equals(de.getEntityName())) {
                                isSaveEntity = false;
                                break;
                            }
                        }
                    }

                    if (isSaveEntity) {
                        DbEntity dbEntity1 = new DbEntity();
                        dbEntity1.setEntityName(entityName);
                        dbEntity1.setDbType(DatabaseType.TIDB.getValue());
                        entityMapper.insert(dbEntity1);
                        dbEntity.add(dbEntity1);
                    }

                    if (StringUtils.isNotEmpty(extendedAttributes)) {
                        List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                        }.getType());
                        secondExtendedList.addAll(extendedList);
                    }
                    String extendAttr = gson.toJson(secondExtendedList);
                    DatabaseConnection databaseConnection = new DatabaseConnection();
                    databaseConnection.setEntity(entityName);
                    databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                    databaseConnection.setId(connectId);
                    databaseConnection.setExtended_attributes(extendAttr);
                    databaseConnection.setSync(instanceUuid);
                    connectionMapper.updateById(databaseConnection);
                }

            }

        }

    }

    private void dealPostgresqlId(List<PaDatabaseEntityDto> entityList, List<SecurityRuleSet> securityRuleSets, List<Category> dbCategoryList, User sysUser, List<String> businessUserIds, List<Map<String, String>> connectUsers,
                                  List<DatabaseConnection> dbConnections) {
        QueryWrapper<DbEntity> entityQueryWrapper = new QueryWrapper<>();
        entityQueryWrapper.eq("db_type", DatabaseType.RASE_SQL.getValue());
        List<DbEntity> dbEntity = entityMapper.selectList(entityQueryWrapper);

        for (PaDatabaseEntityDto pe : entityList) {
            Map updateProp = new HashMap<>();
            List<ConnectExtendMap> extendList = new ArrayList<>();
            ConnectExtendMap isManagedMap = new ConnectExtendMap();
            isManagedMap.setKey("is_managed");
            String isManaged = UCmdbConstants.paMap.get(pe.getIsManaged());
            if ("否".equals(isManaged)) {
                continue;
            }
            isManagedMap.setValue(isManaged);
            isManagedMap.setDescription("是否科技DBA运维");
            ConnectExtendMap customerNameMap = new ConnectExtendMap();
            customerNameMap.setKey("bu");
            customerNameMap.setValue(pe.getBu());
            customerNameMap.setDescription("BU");
            ConnectExtendMap managerDaMap = new ConnectExtendMap();
            managerDaMap.setKey("manager_da");
            managerDaMap.setValue(pe.getManagerDa());
            managerDaMap.setDescription("主管DA");
            ConnectExtendMap backDaMap = new ConnectExtendMap();
            backDaMap.setKey("back_da");
            backDaMap.setValue(pe.getBackDa());
            backDaMap.setDescription("备选DA");
            ConnectExtendMap infraTypeMap = new ConnectExtendMap();
            infraTypeMap.setKey("infra_type");
            infraTypeMap.setValue(UCmdbConstants.paMap.get(pe.getInfraType()));
            infraTypeMap.setDescription("基础架构类型");
            String customerNumber = pe.getCustomerNumber();
            extendList.add(isManagedMap);
            extendList.add(customerNameMap);
            extendList.add(managerDaMap);
            extendList.add(backDaMap);
            extendList.add(infraTypeMap);

            List<PaInstanceDto> entityPaInstances = pe.getPaInstanceDto();
            if (entityPaInstances == null || entityPaInstances.size() == 0) {
                continue;
            }
            for (PaInstanceDto pi : entityPaInstances) {

                ConnectExtendMap serviceUserMap = new ConnectExtendMap();
                serviceUserMap.setKey("service_user");
                serviceUserMap.setValue(pi.getServiceUser());
                serviceUserMap.setDescription("应用接口人");


                ConnectExtendMap cyberarkEntityNameMap = new ConnectExtendMap();
                cyberarkEntityNameMap.setKey("cyberark_entity_name");
                cyberarkEntityNameMap.setValue(pi.getInstanceName());
                cyberarkEntityNameMap.setDescription("Cyberark实体名");

                ConnectExtendMap databaseVersionMap = new ConnectExtendMap();
                databaseVersionMap.setKey("database_version");
                databaseVersionMap.setValue(pi.getDatabaseVersion());
                databaseVersionMap.setDescription("数据库版本");

                ConnectExtendMap createWayMap = new ConnectExtendMap();
                createWayMap.setKey("create_method");
                createWayMap.setValue(UCmdbConstants.paMap.get(pi.getCreateMethod()));
                createWayMap.setDescription("创建方式");

                ConnectExtendMap deployEcologyMap = new ConnectExtendMap();
                deployEcologyMap.setKey("deploy_ecology");
                deployEcologyMap.setValue(UCmdbConstants.paMap.get(pi.getDeployEcology()));
                deployEcologyMap.setDescription("部署生态");

                ConnectExtendMap systemNameMap = new ConnectExtendMap();
                systemNameMap.setKey("system_name");
                systemNameMap.setValue(pi.getSystemName());
                systemNameMap.setDescription("系统名称");

                ConnectExtendMap entityUuidMap = new ConnectExtendMap();
                entityUuidMap.setKey("entity_uuid");
                entityUuidMap.setValue(pi.getInstanceUuid());
                entityUuidMap.setDescription("实体UUID");
                Integer environment = this.getEnvironment(pi.getEnvironment());
                String entityName = pi.getInstanceName();


                List<ConnectExtendMap> secondExtendedList = new ArrayList<>();
                secondExtendedList.add(serviceUserMap);
                secondExtendedList.add(cyberarkEntityNameMap);
                secondExtendedList.add(databaseVersionMap);
                secondExtendedList.add(createWayMap);
                secondExtendedList.add(deployEcologyMap);
                secondExtendedList.add(systemNameMap);
                secondExtendedList.add(entityUuidMap);
                ConnectExtendMap isClassMap = new ConnectExtendMap();
                isClassMap.setKey("is_classification");
                isClassMap.setDescription("是否分级分类");
                isClassMap.setValue("否");
                secondExtendedList.add(isClassMap);
                ConnectExtendMap isCopyAuthMap = new ConnectExtendMap();
                isCopyAuthMap.setKey("is_copy_auth");
                isCopyAuthMap.setDescription("是否明文复制");
                isCopyAuthMap.setValue("否");
                secondExtendedList.add(isCopyAuthMap);
                secondExtendedList.addAll(extendList);

                List<PaSubInstanceDto> paSubInstances1 = pi.getPaSubInstanceDto();
                if (paSubInstances1 == null || paSubInstances1.size() == 0) {
                    continue;
                }
                for (PaSubInstanceDto ps : paSubInstances1) {
                    if (!"DBS:ON".equals(ps.getStatus()) && !"DBS:MT".equals(ps.getStatus())) {
                        continue;
                    }
                    String ip = null;
                    String domainName = null;
                    if (StringUtils.isNotEmpty(ps.getDomainName()) && !"N/A".equals(ps.getDomainName())) {
                        domainName = ps.getDomainName();
                    }
                    if (StringUtils.isNotEmpty(ps.getVip())) {
                        ip = ps.getVip();
                    }

                    if (StringUtils.isNotEmpty(ip) || StringUtils.isNotEmpty(domainName)) {
                        List<ConnectExtendMap> newExtendedList = new ArrayList<>();
                        Integer port = ps.getPort();
                        String instanceUuid = ps.getInstanceUuid();
                        String instanceName = ps.getInstanceName();

                        ConnectExtendMap defaultRoleMap = new ConnectExtendMap();
                        defaultRoleMap.setKey("default_role");
                        String role = UCmdbConstants.paMap.get(ps.getDefaultRole());
                        defaultRoleMap.setValue(role);
                        defaultRoleMap.setDescription("默认角色");

                        ConnectExtendMap statusMap = new ConnectExtendMap();
                        statusMap.setKey("status");
                        statusMap.setValue(UCmdbConstants.paMap.get(ps.getStatus()));
                        statusMap.setDescription("状态");

                        ConnectExtendMap subInstanceUuidMap = new ConnectExtendMap();
                        subInstanceUuidMap.setKey("instance_uuid");
                        subInstanceUuidMap.setValue(instanceUuid);
                        subInstanceUuidMap.setDescription("实例UUID");
                        newExtendedList.add(defaultRoleMap);
                        newExtendedList.add(statusMap);
                        newExtendedList.add(subInstanceUuidMap);
                        newExtendedList.addAll(secondExtendedList);
                        String extendedAttributes = null;
                        Integer connectId = null;
                        Integer security_rule_set_id = null;
                        if (dbConnections != null && dbConnections.size() > 0) {
                            for (DatabaseConnection dbConnection : dbConnections) {
                                if (DatabaseType.RASE_SQL.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(domainName)
                                        && dbConnection.getPort().equals(port.toString())) {
                                    connectId = dbConnection.getId();
                                    security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                    break;
                                }

                                if (DatabaseType.RASE_SQL.getValue().equals(dbConnection.getDb_type()) && dbConnection.getIp().equals(ip)
                                        && dbConnection.getPort().equals(port.toString())) {
                                    connectId = dbConnection.getId();
                                    security_rule_set_id = dbConnection.getSecurity_rule_set_id();
                                    extendedAttributes = dbConnection.getExtended_attributes();
                                    break;
                                }
                            }
                        }

                        if (connectId != null) {
                            String categoryId = null;
                            if (customerNumber != null && dbCategoryList != null && dbCategoryList.size() > 0) {
                                for (Category category : dbCategoryList) {
                                    if (customerNumber.equals(category.getBusiness_id())) {
                                        categoryId = category.getUnique_key();
                                        break;
                                    }
                                }
                            }
                            boolean isSaveEntity = true;
                            if (dbEntity != null && dbEntity.size() > 0) {
                                for (DbEntity de : dbEntity) {
                                    if (entityName.equals(de.getEntityName())) {
                                        isSaveEntity = false;
                                        break;
                                    }
                                }
                            }

                            if (isSaveEntity) {
                                DbEntity dbEntity1 = new DbEntity();
                                dbEntity1.setEntityName(entityName);
                                dbEntity1.setDbType(DatabaseType.RASE_SQL.getValue());
                                entityMapper.insert(dbEntity1);
                                dbEntity.add(dbEntity1);
                            }

                            if (StringUtils.isNotEmpty(extendedAttributes)) {
                                List<ConnectExtendMap> extendedList = gson.fromJson(extendedAttributes, new TypeToken<List<ConnectExtendMap>>() {
                                }.getType());
                                newExtendedList.addAll(extendedList);
                            }
                            String extendAttr = gson.toJson(newExtendedList);
                            DatabaseConnection databaseConnection = new DatabaseConnection();
                            databaseConnection.setEntity(entityName);
                            databaseConnection.setSecurity_rule_set_id(security_rule_set_id);
                            databaseConnection.setId(connectId);
                            databaseConnection.setExtended_attributes(extendAttr);
                            databaseConnection.setSync(instanceUuid);
                            connectionMapper.updateById(databaseConnection);
                        }
                    }
                }
            }
        }

    }

    @Override
    public void syncEntityHistory() throws Exception {
        List<DatabaseConnection> connectEntity = databaseConnectionMapper.getDbEntity();

        List<DbEntity> dbEntities = entityMapper.selectList(new QueryWrapper<>());
        List<DbEntity> saveList = new ArrayList<>();
        for (DatabaseConnection connect : connectEntity) {
            boolean isSave = true;
            for (DbEntity entity : dbEntities) {
                if (connect.getDb_type().equals(entity.getDbType()) && connect.getEntity().equals(entity.getEntityName())) {
                    isSave = false;
                    break;
                }
            }
            if (isSave) {
                DbEntity dbEntity = new DbEntity();
                dbEntity.setEntityName(connect.getEntity());
                dbEntity.setDbType(connect.getDb_type());
                saveList.add(dbEntity);
            }
        }
        if (saveList.size() > 0) {
            dbEntityService.saveBatch(saveList);
        }
    }

    @Override
    public void syncEntityVersion() throws Exception {

        List<DatabaseConnection> databaseConnections = databaseConnectionMapper.getRaseSqlConnections();
        for (DatabaseConnection databaseConnection : databaseConnections) {
            try {
                this.refreshSchema(databaseConnection.getUnique_key());
            } catch (Exception e) {
                log.error("refresh schema error", gson.toJson(e.getStackTrace()));
            }
        }
    }
}


