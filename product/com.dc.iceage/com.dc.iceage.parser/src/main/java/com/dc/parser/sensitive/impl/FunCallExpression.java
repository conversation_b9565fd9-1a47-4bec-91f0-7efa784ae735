package com.dc.parser.sensitive.impl;

import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TExpressionList;
import com.dc.sqlparser.nodes.TFunctionCall;

/**
 * @description function_t
 */
public class FunCallExpression extends AbstractExpression {


    @Override
    public void handle(TExpression expression) {

        TFunctionCall functionCall = null;
        if (null != expression) {
            functionCall = expression.getFunctionCall();
        }

        StatementContext context = super.getContext().createContext();
        context.setUseAlias(false);
        context.addEps(new BaseExpression());

        //------- 
        if (null != functionCall) {

            //用例1：count(name)
            if (null != functionCall.getArgs()) {
                TExpressionList tExprArgsList = functionCall.getArgs();
                for (TExpression expr : tExprArgsList) {
                    context.addNode(expr);
                }
            }

            //用例2：：substring(name from 1 for 3)
            if (null != functionCall.getExpr1() || null != functionCall.getExpr2() || null != functionCall.getExpr3()) {
                context.addNode(functionCall.getExpr1());
                context.addNode(functionCall.getExpr2());
                context.addNode(functionCall.getExpr3());
            }

            //用例3： GROUP_CONCAT(pwd,id)
            if (null != functionCall.getGroupConcatParam() && null != functionCall.getGroupConcatParam().getExprList()) {
                TExpressionList exprList = functionCall.getGroupConcatParam().getExprList();
                for (TExpression expr : exprList) {
                    context.addNode(expr);
                }
            }
        }

        context.search();
    }
}
