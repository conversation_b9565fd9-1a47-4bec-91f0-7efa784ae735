server:
  port: ${dc_job_port}
  servlet:
    context-path: /dc-job
  tomcat:
    max-http-form-post-size: -1
  shutdown: graceful

job:
  upload-location: /opt/uploadfile
  path:
    dc-backend: http://${dc_backend_ip}:${dc_backend_port}
    dc-summer: http://${dc_summer_ip}:${dc_summer_port}
    dc-iceage: http://${dc_iceage_ip}:${dc_iceage_port}
    dc-spi: http://${dc_spi_ip}:${dc_spi_port}
  oracle: "{'********-********': '/oracle12.1/instantclient_12_1',
          '********-********.0':'/oracle12.1/instantclient_12_1',
            '********.0-********.0': '/oracle12.1/instantclient_12_1',
                '********.0-********.0': '/oracle12.1/instantclient_12_1',
                '********.0-********.0': '/oracle12.1/instantclient_12_1',
                '********.0-********.0': '/oracle12.2/instantclient_12_2',
                '********.0-*********.0': '/oracle19.19/instantclient_19_19',
                '*********.0-********.0': '/oracle19.19/instantclient_19_19'
                }"
  tool-path: "{'PT-Online 3.5.4' : '/usr/local/bin/pt-online-schema-change'}"

spring:
  lifecycle:
    timeout-per-shutdown-phase: 5s
  redis:
    key-prefix: ${redis_keyPrefix:dc_}        # key 前缀
    read-model: ${redis_read_model:1}         # 读取模式：0，从节点读取；1，主节点读取；2，主从节点读取。
    sentinel-servers-config:                              # 哨兵模式，优先顺序：1 ----------------------------------------------------------------
      sentinel-addresses: ${redis_nodes_list_sentinel:}   # 哨兵地址，如果不为空，那么使用哨兵模式 ！！！
      password: ${redis_password}                         # 密码
      master-name: ${redis_master}                        # 主节点名称
      master-connection-pool-size: 30                     # 主节点连接池最大数量，默认是：64
      master-connection-minimum-idle-size: 10             # 主节点连接池最小空闲，默认是：24
      slave-connection-pool-size: 30                      # 从节点连接池最大数量，默认是：64
      slave-connection-minimum-idle-size: 10              # 从节点连接池最小空闲，默认是：24
      timeout: 30000                                      # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                              # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                      # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}            # 重试次数，默认是：3
      retry-interval: 1500                                # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000                     # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
    cluster-servers-config:                         # 集群模式，优先顺序：2 ----------------------------------------------------------------
      node-addresses: ${redis_nodes_list_cluster:}  # 集群地址，如果不为空，那么使用集群模式 ！！！
      password: ${redis_password}                   # 密码
      master-connection-pool-size: 30               # 主节点连接池最大数量，默认是：64
      master-connection-minimum-idle-size: 10       # 主节点连接池最小空闲，默认是：24
      slave-connection-pool-size: 30                # 从节点连接池最大数量，默认是：64
      slave-connection-minimum-idle-size: 10        # 从节点连接池最小空闲，默认是：24
      timeout: 30000                                # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                        # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}      # 重试次数，默认是：3
      retry-interval: 1500                          # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000               # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
    single-server-config:                                       # 单机模式，优先顺序：3 ----------------------------------------------------------------
      address: ${redis_ip}:${redis_port}                        # 单机地址，如果不为空，那么使用单机模式 ！！！
      password: ${redis_password}                               # 密码
      database: ${redis_database:2}                             # 用于Redis连接的数据库索引，默认是：0
      connection-pool-size: 30                                  # 连接池最大数量，默认是：64
      connection-minimum-idle-size: 10                          # 连接池最小空闲，默认是：24
      timeout: 30000                                            # Redis服务器响应超时。Redis命令发送成功后开始倒计时。值（以毫秒为单位）。默认是：3000
      connect-timeout: 10000                                    # 连接到任何Redis服务器时超时。值（以毫秒为单位）。默认是：10000
      idle-connection-timeout: 10000                            # 如果池连接在超时时间内未使用，并且当前连接数大于最小空闲连接池大小，则它将关闭并从池中删除。值（以毫秒为单位）。默认是：10000
      retry-attempts: ${redis_max_redirects:3}                  # 重试次数，默认是：3
      retry-interval: 1500                                      # 重试间隔。值（以毫秒为单位）。默认是：1500
      ping-connection-interval: 60000                           # Ping 连接服务器命令的间隔。值（以毫秒为单位）。默认是：30000
  mysql:
    node-server-config: # 节点模式。根据 url 中的 ip 不为空，激活节点。单机，配置 normal 下的信息即可。双主、主从：配置 master 和 standby 下的信息即可。
      - name: master                                                # master: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip_master:}:${mysql_port_master}/${mysql_database_name_master}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username_master}                          # 用户名
        password: ${mysql_password_master}                          # 密码
        initialSize: 5                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 20                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
      - name: standby                                               # standby: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip_standby:}:${mysql_port_standby}/${mysql_database_name_standby}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username_standby}                         # 用户名
        password: ${mysql_password_standby}                         # 密码
        initialSize: 5                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 20                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
      - name: normal                                                # normal: 节点名称，可随意修改。
        url: jdbc:mysql://${mysql_ip:}:${mysql_port}/${mysql_database_name}?useUnicode=true&autoReconnectForPools=true&serverTimezone=Asia/Shanghai&useSSL=false&allowMultiQueries=true
        username: ${mysql_username}                                 # 用户名
        password: ${mysql_password}                                 # 密码
        initialSize: 5                                              # 初始连接数，默认是：0
        minIdle: 5                                                  # 最小连接池数量，默认是：0
        maxActive: 20                                               # 最大连接池数量，默认是：8
        maxWait: 1000                                               # 配置获取连接等待超时的时间
        breakAfterAcquireFailure: true                              # 宕机自动重连
        connectTimeout: 60000                                       # 连接超时时间
        socketTimeout: 3600000                                      # 数据超时时间
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
xxl:
  job:
    admin:
      addresses: http://${dc_job_admin_ip}:${dc_job_admin_port}/dc-job-admin
    accessToken:
    executor:
      appname: ${job_appname}
      address:
      ip: ${job_server_ip}
      port: ${job_server_port}
      logpath: ./data/applogs/xxl-job/jobhandler
      logretentiondays: 30

auth:
  api-conf:
    secret-key: ${auth_api_conf_secret_key:}                  # 接口验证私钥，内置了默认值，建议使用自定义值。
    access-key: ${auth_api_conf_access_key:}                  # 接口验证公钥，内置了默认值，建议使用自定义值。
    validity-period: ${auth_api_conf_validity_period:}        # 接口验证过期时间，默认30秒。(参数: 10s, 30s, 1m, 3m...)

zombie-object:
  handle-thread: ${zombie_object.handle_thread:2}       #僵尸对象任务处理线程数