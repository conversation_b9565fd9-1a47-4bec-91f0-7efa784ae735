//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package org.cugos.wkg.internal;

import java.util.List;
import org.antlr.v4.runtime.NoViableAltException;
import org.antlr.v4.runtime.Parser;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.RuntimeMetaData;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.Vocabulary;
import org.antlr.v4.runtime.VocabularyImpl;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.ParserATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.tree.ParseTreeListener;
import org.antlr.v4.runtime.tree.ParseTreeVisitor;
import org.antlr.v4.runtime.tree.TerminalNode;

public class WKTParser extends Parser {
    protected static final DFA[] _decisionToDFA;
    protected static final PredictionContextCache _sharedContextCache;
    public static final int T__0 = 1;
    public static final int T__1 = 2;
    public static final int T__2 = 3;
    public static final int T__3 = 4;
    public static final int T__4 = 5;
    public static final int T__5 = 6;
    public static final int T__6 = 7;
    public static final int T__7 = 8;
    public static final int T__8 = 9;
    public static final int T__9 = 10;
    public static final int T__10 = 11;
    public static final int T__11 = 12;
    public static final int T__12 = 13;
    public static final int T__13 = 14;
    public static final int T__14 = 15;
    public static final int T__15 = 16;
    public static final int T__16 = 17;
    public static final int T__17 = 18;
    public static final int T__18 = 19;
    public static final int T__19 = 20;
    public static final int T__20 = 21;
    public static final int M = 22;
    public static final int Z = 23;
    public static final int ZM = 24;
    public static final int Number = 25;
    public static final int WhiteSpace = 26;
    public static final int NewLine = 27;
    public static final int RULE_wkt = 0;
    public static final int RULE_point = 1;
    public static final int RULE_lineString = 2;
    public static final int RULE_polygon = 3;
    public static final int RULE_triangle = 4;
    public static final int RULE_multiPoint = 5;
    public static final int RULE_multiLineString = 6;
    public static final int RULE_circularString = 7;
    public static final int RULE_tin = 8;
    public static final int RULE_polyHedralSurface = 9;
    public static final int RULE_multiPolygon = 10;
    public static final int RULE_curvePolygon = 11;
    public static final int RULE_curvePolygonItems = 12;
    public static final int RULE_curvePolygonElements = 13;
    public static final int RULE_compoundCurve = 14;
    public static final int RULE_compoundCurveItems = 15;
    public static final int RULE_compoundCurveElements = 16;
    public static final int RULE_multiCurve = 17;
    public static final int RULE_multiCurveItems = 18;
    public static final int RULE_multiCurveElements = 19;
    public static final int RULE_multiSurface = 20;
    public static final int RULE_multiSurfaceItems = 21;
    public static final int RULE_multiSurfaceElements = 22;
    public static final int RULE_geometryCollection = 23;
    public static final int RULE_geometryCollectionItems = 24;
    public static final int RULE_geometryCollectionElements = 25;
    public static final int RULE_lineStringCoordinates = 26;
    public static final int RULE_polygonCoordinates = 27;
    public static final int RULE_coordinate = 28;
    public static final int RULE_coordinates = 29;
    public static final int RULE_coordinatesets = 30;
    public static final int RULE_coordinatesetsset = 31;
    public static final int RULE_empty = 32;
    public static final int RULE_srid = 33;
    public static final int RULE_dimension = 34;
    public static final String[] ruleNames;
    private static final String[] _LITERAL_NAMES;
    private static final String[] _SYMBOLIC_NAMES;
    public static final Vocabulary VOCABULARY;
    /** @deprecated */
    @Deprecated
    public static final String[] tokenNames;
    public static final String _serializedATN = "\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u001d˩\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007\u0004\b\t\b\u0004\t\t\t\u0004\n\t\n\u0004\u000b\t\u000b\u0004\f\t\f\u0004\r\t\r\u0004\u000e\t\u000e\u0004\u000f\t\u000f\u0004\u0010\t\u0010\u0004\u0011\t\u0011\u0004\u0012\t\u0012\u0004\u0013\t\u0013\u0004\u0014\t\u0014\u0004\u0015\t\u0015\u0004\u0016\t\u0016\u0004\u0017\t\u0017\u0004\u0018\t\u0018\u0004\u0019\t\u0019\u0004\u001a\t\u001a\u0004\u001b\t\u001b\u0004\u001c\t\u001c\u0004\u001d\t\u001d\u0004\u001e\t\u001e\u0004\u001f\t\u001f\u0004 \t \u0004!\t!\u0004\"\t\"\u0004#\t#\u0004$\t$\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0005\u0002X\n\u0002\u0003\u0003\u0005\u0003[\n\u0003\u0003\u0003\u0003\u0003\u0005\u0003_\n\u0003\u0003\u0003\u0007\u0003b\n\u0003\f\u0003\u000e\u0003e\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003l\n\u0003\u0003\u0004\u0005\u0004o\n\u0004\u0003\u0004\u0003\u0004\u0005\u0004s\n\u0004\u0003\u0004\u0007\u0004v\n\u0004\f\u0004\u000e\u0004y\u000b\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0005\u0004\u0080\n\u0004\u0003\u0005\u0005\u0005\u0083\n\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0087\n\u0005\u0003\u0005\u0007\u0005\u008a\n\u0005\f\u0005\u000e\u0005\u008d\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0094\n\u0005\u0003\u0006\u0005\u0006\u0097\n\u0006\u0003\u0006\u0003\u0006\u0005\u0006\u009b\n\u0006\u0003\u0006\u0007\u0006\u009e\n\u0006\f\u0006\u000e\u0006¡\u000b\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u0006¨\n\u0006\u0003\u0007\u0005\u0007«\n\u0007\u0003\u0007\u0003\u0007\u0005\u0007¯\n\u0007\u0003\u0007\u0007\u0007²\n\u0007\f\u0007\u000e\u0007µ\u000b\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0005\u0007À\n\u0007\u0003\b\u0005\bÃ\n\b\u0003\b\u0003\b\u0005\bÇ\n\b\u0003\b\u0007\bÊ\n\b\f\b\u000e\bÍ\u000b\b\u0003\b\u0003\b\u0003\b\u0003\b\u0003\b\u0005\bÔ\n\b\u0003\t\u0005\t×\n\t\u0003\t\u0003\t\u0005\tÛ\n\t\u0003\t\u0007\tÞ\n\t\f\t\u000e\tá\u000b\t\u0003\t\u0003\t\u0003\t\u0003\t\u0003\t\u0005\tè\n\t\u0003\n\u0005\në\n\n\u0003\n\u0003\n\u0005\nï\n\n\u0003\n\u0007\nò\n\n\f\n\u000e\nõ\u000b\n\u0003\n\u0003\n\u0003\n\u0003\n\u0003\n\u0005\nü\n\n\u0003\u000b\u0005\u000bÿ\n\u000b\u0003\u000b\u0003\u000b\u0005\u000bă\n\u000b\u0003\u000b\u0007\u000bĆ\n\u000b\f\u000b\u000e\u000bĉ\u000b\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0005\u000bĐ\n\u000b\u0003\f\u0005\fē\n\f\u0003\f\u0003\f\u0005\fė\n\f\u0003\f\u0007\fĚ\n\f\f\f\u000e\fĝ\u000b\f\u0003\f\u0003\f\u0003\f\u0003\f\u0003\f\u0005\fĤ\n\f\u0003\r\u0005\rħ\n\r\u0003\r\u0003\r\u0005\rī\n\r\u0003\r\u0007\rĮ\n\r\f\r\u000e\rı\u000b\r\u0003\r\u0003\r\u0003\r\u0003\r\u0003\r\u0005\rĸ\n\r\u0003\u000e\u0003\u000e\u0007\u000eļ\n\u000e\f\u000e\u000e\u000eĿ\u000b\u000e\u0003\u000e\u0003\u000e\u0007\u000eŃ\n\u000e\f\u000e\u000e\u000eņ\u000b\u000e\u0003\u000e\u0007\u000eŉ\n\u000e\f\u000e\u000e\u000eŌ\u000b\u000e\u0003\u000f\u0003\u000f\u0003\u000f\u0005\u000fő\n\u000f\u0003\u0010\u0005\u0010Ŕ\n\u0010\u0003\u0010\u0003\u0010\u0005\u0010Ř\n\u0010\u0003\u0010\u0007\u0010ś\n\u0010\f\u0010\u000e\u0010Ş\u000b\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0005\u0010ť\n\u0010\u0003\u0011\u0003\u0011\u0007\u0011ũ\n\u0011\f\u0011\u000e\u0011Ŭ\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011Ű\n\u0011\f\u0011\u000e\u0011ų\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011ŷ\n\u0011\f\u0011\u000e\u0011ź\u000b\u0011\u0007\u0011ż\n\u0011\f\u0011\u000e\u0011ſ\u000b\u0011\u0003\u0012\u0003\u0012\u0005\u0012ƃ\n\u0012\u0003\u0013\u0005\u0013Ɔ\n\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɗ\n\u0013\u0003\u0013\u0007\u0013ƍ\n\u0013\f\u0013\u000e\u0013Ɛ\u000b\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɨ\n\u0013\u0003\u0014\u0003\u0014\u0007\u0014ƛ\n\u0014\f\u0014\u000e\u0014ƞ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ƣ\n\u0014\f\u0014\u000e\u0014ƥ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ʃ\n\u0014\f\u0014\u000e\u0014Ƭ\u000b\u0014\u0007\u0014Ʈ\n\u0014\f\u0014\u000e\u0014Ʊ\u000b\u0014\u0003\u0015\u0003\u0015\u0003\u0015\u0005\u0015ƶ\n\u0015\u0003\u0016\u0005\u0016ƹ\n\u0016\u0003\u0016\u0003\u0016\u0005\u0016ƽ\n\u0016\u0003\u0016\u0007\u0016ǀ\n\u0016\f\u0016\u000e\u0016ǃ\u000b\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0005\u0016Ǌ\n\u0016\u0003\u0017\u0003\u0017\u0007\u0017ǎ\n\u0017\f\u0017\u000e\u0017Ǒ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017Ǖ\n\u0017\f\u0017\u000e\u0017ǘ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017ǜ\n\u0017\f\u0017\u000e\u0017ǟ\u000b\u0017\u0007\u0017ǡ\n\u0017\f\u0017\u000e\u0017Ǥ\u000b\u0017\u0003\u0018\u0003\u0018\u0005\u0018Ǩ\n\u0018\u0003\u0019\u0005\u0019ǫ\n\u0019\u0003\u0019\u0003\u0019\u0005\u0019ǯ\n\u0019\u0003\u0019\u0007\u0019ǲ\n\u0019\f\u0019\u000e\u0019ǵ\u000b\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0005\u0019Ǽ\n\u0019\u0003\u001a\u0003\u001a\u0007\u001aȀ\n\u001a\f\u001a\u000e\u001aȃ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȇ\n\u001a\f\u001a\u000e\u001aȊ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȎ\n\u001a\f\u001a\u000e\u001aȑ\u000b\u001a\u0007\u001aȓ\n\u001a\f\u001a\u000e\u001aȖ\u000b\u001a\u0003\u001b\u0003\u001b\u0003\u001c\u0003\u001c\u0007\u001cȜ\n\u001c\f\u001c\u000e\u001cȟ\u000b\u001c\u0003\u001c\u0003\u001c\u0007\u001cȣ\n\u001c\f\u001c\u000e\u001cȦ\u000b\u001c\u0003\u001c\u0003\u001c\u0003\u001d\u0003\u001d\u0007\u001dȬ\n\u001d\f\u001d\u000e\u001dȯ\u000b\u001d\u0003\u001d\u0003\u001d\u0007\u001dȳ\n\u001d\f\u001d\u000e\u001dȶ\u000b\u001d\u0003\u001d\u0003\u001d\u0003\u001e\u0007\u001eȻ\n\u001e\f\u001e\u000e\u001eȾ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɂ\n\u001e\f\u001e\u000e\u001eɅ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɉ\n\u001e\f\u001e\u000e\u001eɌ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɐ\n\u001e\f\u001e\u000e\u001eɓ\u000b\u001e\u0005\u001eɕ\n\u001e\u0003\u001e\u0003\u001e\u0007\u001eə\n\u001e\f\u001e\u000e\u001eɜ\u000b\u001e\u0005\u001eɞ\n\u001e\u0003\u001f\u0003\u001f\u0007\u001fɢ\n\u001f\f\u001f\u000e\u001fɥ\u000b\u001f\u0003\u001f\u0003\u001f\u0007\u001fɩ\n\u001f\f\u001f\u000e\u001fɬ\u000b\u001f\u0003\u001f\u0007\u001fɯ\n\u001f\f\u001f\u000e\u001fɲ\u000b\u001f\u0003 \u0003 \u0007 ɶ\n \f \u000e ɹ\u000b \u0003 \u0003 \u0007 ɽ\n \f \u000e ʀ\u000b \u0003 \u0003 \u0007 ʄ\n \f \u000e ʇ\u000b \u0003 \u0003 \u0007 ʋ\n \f \u000e ʎ\u000b \u0003 \u0003 \u0007 ʒ\n \f \u000e ʕ\u000b \u0003 \u0003 \u0007 ʙ\n \f \u000e ʜ\u000b \u0003 \u0003 \u0007 ʠ\n \f \u000e ʣ\u000b \u0003!\u0003!\u0007!ʧ\n!\f!\u000e!ʪ\u000b!\u0003!\u0003!\u0007!ʮ\n!\f!\u000e!ʱ\u000b!\u0003!\u0003!\u0007!ʵ\n!\f!\u000e!ʸ\u000b!\u0003!\u0003!\u0007!ʼ\n!\f!\u000e!ʿ\u000b!\u0003!\u0003!\u0007!˃\n!\f!\u000e!ˆ\u000b!\u0003!\u0003!\u0007!ˊ\n!\f!\u000e!ˍ\u000b!\u0003!\u0003!\u0007!ˑ\n!\f!\u000e!˔\u000b!\u0003\"\u0003\"\u0003#\u0003#\u0003#\u0003#\u0003$\u0007$˝\n$\f$\u000e$ˠ\u000b$\u0003$\u0003$\u0007$ˤ\n$\f$\u000e$˧\u000b$\u0003$\u0002\u0002%\u0002\u0004\u0006\b\n\f\u000e\u0010\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.02468:<>@BDF\u0002\u0003\u0003\u0002\u0018\u001a\u0002͇\u0002W\u0003\u0002\u0002\u0002\u0004Z\u0003\u0002\u0002\u0002\u0006n\u0003\u0002\u0002\u0002\b\u0082\u0003\u0002\u0002\u0002\n\u0096\u0003\u0002\u0002\u0002\fª\u0003\u0002\u0002\u0002\u000eÂ\u0003\u0002\u0002\u0002\u0010Ö\u0003\u0002\u0002\u0002\u0012ê\u0003\u0002\u0002\u0002\u0014þ\u0003\u0002\u0002\u0002\u0016Ē\u0003\u0002\u0002\u0002\u0018Ħ\u0003\u0002\u0002\u0002\u001aĹ\u0003\u0002\u0002\u0002\u001cŐ\u0003\u0002\u0002\u0002\u001eœ\u0003\u0002\u0002\u0002 Ŧ\u0003\u0002\u0002\u0002\"Ƃ\u0003\u0002\u0002\u0002$ƅ\u0003\u0002\u0002\u0002&Ƙ\u0003\u0002\u0002\u0002(Ƶ\u0003\u0002\u0002\u0002*Ƹ\u0003\u0002\u0002\u0002,ǋ\u0003\u0002\u0002\u0002.ǧ\u0003\u0002\u0002\u00020Ǫ\u0003\u0002\u0002\u00022ǽ\u0003\u0002\u0002\u00024ȗ\u0003\u0002\u0002\u00026ș\u0003\u0002\u0002\u00028ȩ\u0003\u0002\u0002\u0002:ȼ\u0003\u0002\u0002\u0002<ɟ\u0003\u0002\u0002\u0002>ɳ\u0003\u0002\u0002\u0002@ʤ\u0003\u0002\u0002\u0002B˕\u0003\u0002\u0002\u0002D˗\u0003\u0002\u0002\u0002F˞\u0003\u0002\u0002\u0002HX\u0005\u0004\u0003\u0002IX\u0005\u0006\u0004\u0002JX\u0005\b\u0005\u0002KX\u0005\f\u0007\u0002LX\u0005\u000e\b\u0002MX\u0005\u0010\t\u0002NX\u0005\n\u0006\u0002OX\u0005\u0012\n\u0002PX\u0005\u0014\u000b\u0002QX\u0005\u0016\f\u0002RX\u0005\u001e\u0010\u0002SX\u0005\u0018\r\u0002TX\u0005$\u0013\u0002UX\u0005*\u0016\u0002VX\u00050\u0019\u0002WH\u0003\u0002\u0002\u0002WI\u0003\u0002\u0002\u0002WJ\u0003\u0002\u0002\u0002WK\u0003\u0002\u0002\u0002WL\u0003\u0002\u0002\u0002WM\u0003\u0002\u0002\u0002WN\u0003\u0002\u0002\u0002WO\u0003\u0002\u0002\u0002WP\u0003\u0002\u0002\u0002WQ\u0003\u0002\u0002\u0002WR\u0003\u0002\u0002\u0002WS\u0003\u0002\u0002\u0002WT\u0003\u0002\u0002\u0002WU\u0003\u0002\u0002\u0002WV\u0003\u0002\u0002\u0002X\u0003\u0003\u0002\u0002\u0002Y[\u0005D#\u0002ZY\u0003\u0002\u0002\u0002Z[\u0003\u0002\u0002\u0002[\\\u0003\u0002\u0002\u0002\\^\u0007\u0003\u0002\u0002]_\u0005F$\u0002^]\u0003\u0002\u0002\u0002^_\u0003\u0002\u0002\u0002_c\u0003\u0002\u0002\u0002`b\u0007\u001c\u0002\u0002a`\u0003\u0002\u0002\u0002be\u0003\u0002\u0002\u0002ca\u0003\u0002\u0002\u0002cd\u0003\u0002\u0002\u0002dk\u0003\u0002\u0002\u0002ec\u0003\u0002\u0002\u0002fg\u0007\u0004\u0002\u0002gh\u0005:\u001e\u0002hi\u0007\u0005\u0002\u0002il\u0003\u0002\u0002\u0002jl\u0007\u0006\u0002\u0002kf\u0003\u0002\u0002\u0002kj\u0003\u0002\u0002\u0002l\u0005\u0003\u0002\u0002\u0002mo\u0005D#\u0002nm\u0003\u0002\u0002\u0002no\u0003\u0002\u0002\u0002op\u0003\u0002\u0002\u0002pr\u0007\u0007\u0002\u0002qs\u0005F$\u0002rq\u0003\u0002\u0002\u0002rs\u0003\u0002\u0002\u0002sw\u0003\u0002\u0002\u0002tv\u0007\u001c\u0002\u0002ut\u0003\u0002\u0002\u0002vy\u0003\u0002\u0002\u0002wu\u0003\u0002\u0002\u0002wx\u0003\u0002\u0002\u0002x\u007f\u0003\u0002\u0002\u0002yw\u0003\u0002\u0002\u0002z{\u0007\u0004\u0002\u0002{|\u0005<\u001f\u0002|}\u0007\u0005\u0002\u0002}\u0080\u0003\u0002\u0002\u0002~\u0080\u0007\u0006\u0002\u0002\u007fz\u0003\u0002\u0002\u0002\u007f~\u0003\u0002\u0002\u0002\u0080\u0007\u0003\u0002\u0002\u0002\u0081\u0083\u0005D#\u0002\u0082\u0081\u0003\u0002\u0002\u0002\u0082\u0083\u0003\u0002\u0002\u0002\u0083\u0084\u0003\u0002\u0002\u0002\u0084\u0086\u0007\b\u0002\u0002\u0085\u0087\u0005F$\u0002\u0086\u0085\u0003\u0002\u0002\u0002\u0086\u0087\u0003\u0002\u0002\u0002\u0087\u008b\u0003\u0002\u0002\u0002\u0088\u008a\u0007\u001c\u0002\u0002\u0089\u0088\u0003\u0002\u0002\u0002\u008a\u008d\u0003\u0002\u0002\u0002\u008b\u0089\u0003\u0002\u0002\u0002\u008b\u008c\u0003\u0002\u0002\u0002\u008c\u0093\u0003\u0002\u0002\u0002\u008d\u008b\u0003\u0002\u0002\u0002\u008e\u008f\u0007\u0004\u0002\u0002\u008f\u0090\u0005> \u0002\u0090\u0091\u0007\u0005\u0002\u0002\u0091\u0094\u0003\u0002\u0002\u0002\u0092\u0094\u0007\u0006\u0002\u0002\u0093\u008e\u0003\u0002\u0002\u0002\u0093\u0092\u0003\u0002\u0002\u0002\u0094\t\u0003\u0002\u0002\u0002\u0095\u0097\u0005D#\u0002\u0096\u0095\u0003\u0002\u0002\u0002\u0096\u0097\u0003\u0002\u0002\u0002\u0097\u0098\u0003\u0002\u0002\u0002\u0098\u009a\u0007\t\u0002\u0002\u0099\u009b\u0005F$\u0002\u009a\u0099\u0003\u0002\u0002\u0002\u009a\u009b\u0003\u0002\u0002\u0002\u009b\u009f\u0003\u0002\u0002\u0002\u009c\u009e\u0007\u001c\u0002\u0002\u009d\u009c\u0003\u0002\u0002\u0002\u009e¡\u0003\u0002\u0002\u0002\u009f\u009d\u0003\u0002\u0002\u0002\u009f \u0003\u0002\u0002\u0002 §\u0003\u0002\u0002\u0002¡\u009f\u0003\u0002\u0002\u0002¢£\u0007\u0004\u0002\u0002£¤\u0005> \u0002¤¥\u0007\u0005\u0002\u0002¥¨\u0003\u0002\u0002\u0002¦¨\u0007\u0006\u0002\u0002§¢\u0003\u0002\u0002\u0002§¦\u0003\u0002\u0002\u0002¨\u000b\u0003\u0002\u0002\u0002©«\u0005D#\u0002ª©\u0003\u0002\u0002\u0002ª«\u0003\u0002\u0002\u0002«¬\u0003\u0002\u0002\u0002¬®\u0007\n\u0002\u0002\u00ad¯\u0005F$\u0002®\u00ad\u0003\u0002\u0002\u0002®¯\u0003\u0002\u0002\u0002¯³\u0003\u0002\u0002\u0002°²\u0007\u001c\u0002\u0002±°\u0003\u0002\u0002\u0002²µ\u0003\u0002\u0002\u0002³±\u0003\u0002\u0002\u0002³´\u0003\u0002\u0002\u0002´¿\u0003\u0002\u0002\u0002µ³\u0003\u0002\u0002\u0002¶·\u0007\u0004\u0002\u0002·¸\u0005<\u001f\u0002¸¹\u0007\u0005\u0002\u0002¹À\u0003\u0002\u0002\u0002º»\u0007\u0004\u0002\u0002»¼\u0005> \u0002¼½\u0007\u0005\u0002\u0002½À\u0003\u0002\u0002\u0002¾À\u0007\u0006\u0002\u0002¿¶\u0003\u0002\u0002\u0002¿º\u0003\u0002\u0002\u0002¿¾\u0003\u0002\u0002\u0002À\r\u0003\u0002\u0002\u0002ÁÃ\u0005D#\u0002ÂÁ\u0003\u0002\u0002\u0002ÂÃ\u0003\u0002\u0002\u0002ÃÄ\u0003\u0002\u0002\u0002ÄÆ\u0007\u000b\u0002\u0002ÅÇ\u0005F$\u0002ÆÅ\u0003\u0002\u0002\u0002ÆÇ\u0003\u0002\u0002\u0002ÇË\u0003\u0002\u0002\u0002ÈÊ\u0007\u001c\u0002\u0002ÉÈ\u0003\u0002\u0002\u0002ÊÍ\u0003\u0002\u0002\u0002ËÉ\u0003\u0002\u0002\u0002ËÌ\u0003\u0002\u0002\u0002ÌÓ\u0003\u0002\u0002\u0002ÍË\u0003\u0002\u0002\u0002ÎÏ\u0007\u0004\u0002\u0002ÏÐ\u0005> \u0002ÐÑ\u0007\u0005\u0002\u0002ÑÔ\u0003\u0002\u0002\u0002ÒÔ\u0007\u0006\u0002\u0002ÓÎ\u0003\u0002\u0002\u0002ÓÒ\u0003\u0002\u0002\u0002Ô\u000f\u0003\u0002\u0002\u0002Õ×\u0005D#\u0002ÖÕ\u0003\u0002\u0002\u0002Ö×\u0003\u0002\u0002\u0002×Ø\u0003\u0002\u0002\u0002ØÚ\u0007\f\u0002\u0002ÙÛ\u0005F$\u0002ÚÙ\u0003\u0002\u0002\u0002ÚÛ\u0003\u0002\u0002\u0002Ûß\u0003\u0002\u0002\u0002ÜÞ\u0007\u001c\u0002\u0002ÝÜ\u0003\u0002\u0002\u0002Þá\u0003\u0002\u0002\u0002ßÝ\u0003\u0002\u0002\u0002ßà\u0003\u0002\u0002\u0002àç\u0003\u0002\u0002\u0002áß\u0003\u0002\u0002\u0002âã\u0007\u0004\u0002\u0002ãä\u0005<\u001f\u0002äå\u0007\u0005\u0002\u0002åè\u0003\u0002\u0002\u0002æè\u0007\u0006\u0002\u0002çâ\u0003\u0002\u0002\u0002çæ\u0003\u0002\u0002\u0002è\u0011\u0003\u0002\u0002\u0002éë\u0005D#\u0002êé\u0003\u0002\u0002\u0002êë\u0003\u0002\u0002\u0002ëì\u0003\u0002\u0002\u0002ìî\u0007\r\u0002\u0002íï\u0005F$\u0002îí\u0003\u0002\u0002\u0002îï\u0003\u0002\u0002\u0002ïó\u0003\u0002\u0002\u0002ðò\u0007\u001c\u0002\u0002ñð\u0003\u0002\u0002\u0002òõ\u0003\u0002\u0002\u0002óñ\u0003\u0002\u0002\u0002óô\u0003\u0002\u0002\u0002ôû\u0003\u0002\u0002\u0002õó\u0003\u0002\u0002\u0002ö÷\u0007\u0004\u0002\u0002÷ø\u0005@!\u0002øù\u0007\u0005\u0002\u0002ùü\u0003\u0002\u0002\u0002úü\u0007\u0006\u0002\u0002ûö\u0003\u0002\u0002\u0002ûú\u0003\u0002\u0002\u0002ü\u0013\u0003\u0002\u0002\u0002ýÿ\u0005D#\u0002þý\u0003\u0002\u0002\u0002þÿ\u0003\u0002\u0002\u0002ÿĀ\u0003\u0002\u0002\u0002ĀĂ\u0007\u000e\u0002\u0002āă\u0005F$\u0002Ăā\u0003\u0002\u0002\u0002Ăă\u0003\u0002\u0002\u0002ăć\u0003\u0002\u0002\u0002ĄĆ\u0007\u001c\u0002\u0002ąĄ\u0003\u0002\u0002\u0002Ćĉ\u0003\u0002\u0002\u0002ćą\u0003\u0002\u0002\u0002ćĈ\u0003\u0002\u0002\u0002Ĉď\u0003\u0002\u0002\u0002ĉć\u0003\u0002\u0002\u0002Ċċ\u0007\u0004\u0002\u0002ċČ\u0005@!\u0002Čč\u0007\u0005\u0002\u0002čĐ\u0003\u0002\u0002\u0002ĎĐ\u0007\u0006\u0002\u0002ďĊ\u0003\u0002\u0002\u0002ďĎ\u0003\u0002\u0002\u0002Đ\u0015\u0003\u0002\u0002\u0002đē\u0005D#\u0002Ēđ\u0003\u0002\u0002\u0002Ēē\u0003\u0002\u0002\u0002ēĔ\u0003\u0002\u0002\u0002ĔĖ\u0007\u000f\u0002\u0002ĕė\u0005F$\u0002Ėĕ\u0003\u0002\u0002\u0002Ėė\u0003\u0002\u0002\u0002ėě\u0003\u0002\u0002\u0002ĘĚ\u0007\u001c\u0002\u0002ęĘ\u0003\u0002\u0002\u0002Ěĝ\u0003\u0002\u0002\u0002ěę\u0003\u0002\u0002\u0002ěĜ\u0003\u0002\u0002\u0002Ĝģ\u0003\u0002\u0002\u0002ĝě\u0003\u0002\u0002\u0002Ğğ\u0007\u0004\u0002\u0002ğĠ\u0005@!\u0002Ġġ\u0007\u0005\u0002\u0002ġĤ\u0003\u0002\u0002\u0002ĢĤ\u0007\u0006\u0002\u0002ģĞ\u0003\u0002\u0002\u0002ģĢ\u0003\u0002\u0002\u0002Ĥ\u0017\u0003\u0002\u0002\u0002ĥħ\u0005D#\u0002Ħĥ\u0003\u0002\u0002\u0002Ħħ\u0003\u0002\u0002\u0002ħĨ\u0003\u0002\u0002\u0002ĨĪ\u0007\u0010\u0002\u0002ĩī\u0005F$\u0002Īĩ\u0003\u0002\u0002\u0002Īī\u0003\u0002\u0002\u0002īį\u0003\u0002\u0002\u0002ĬĮ\u0007\u001c\u0002\u0002ĭĬ\u0003\u0002\u0002\u0002Įı\u0003\u0002\u0002\u0002įĭ\u0003\u0002\u0002\u0002įİ\u0003\u0002\u0002\u0002İķ\u0003\u0002\u0002\u0002ıį\u0003\u0002\u0002\u0002Ĳĳ\u0007\u0004\u0002\u0002ĳĴ\u0005\u001a\u000e\u0002Ĵĵ\u0007\u0005\u0002\u0002ĵĸ\u0003\u0002\u0002\u0002Ķĸ\u0007\u0006\u0002\u0002ķĲ\u0003\u0002\u0002\u0002ķĶ\u0003\u0002\u0002\u0002ĸ\u0019\u0003\u0002\u0002\u0002ĹŊ\u0005\u001c\u000f\u0002ĺļ\u0007\u001c\u0002\u0002Ļĺ\u0003\u0002\u0002\u0002ļĿ\u0003\u0002\u0002\u0002ĽĻ\u0003\u0002\u0002\u0002Ľľ\u0003\u0002\u0002\u0002ľŀ\u0003\u0002\u0002\u0002ĿĽ\u0003\u0002\u0002\u0002ŀń\u0007\u0011\u0002\u0002ŁŃ\u0007\u001c\u0002\u0002łŁ\u0003\u0002\u0002\u0002Ńņ\u0003\u0002\u0002\u0002ńł\u0003\u0002\u0002\u0002ńŅ\u0003\u0002\u0002\u0002ŅŇ\u0003\u0002\u0002\u0002ņń\u0003\u0002\u0002\u0002Ňŉ\u0005\u001c\u000f\u0002ňĽ\u0003\u0002\u0002\u0002ŉŌ\u0003\u0002\u0002\u0002Ŋň\u0003\u0002\u0002\u0002Ŋŋ\u0003\u0002\u0002\u0002ŋ\u001b\u0003\u0002\u0002\u0002ŌŊ\u0003\u0002\u0002\u0002ōő\u0005\u001e\u0010\u0002Ŏő\u0005\u0010\t\u0002ŏő\u00056\u001c\u0002Őō\u0003\u0002\u0002\u0002ŐŎ\u0003\u0002\u0002\u0002Őŏ\u0003\u0002\u0002\u0002ő\u001d\u0003\u0002\u0002\u0002ŒŔ\u0005D#\u0002œŒ\u0003\u0002\u0002\u0002œŔ\u0003\u0002\u0002\u0002Ŕŕ\u0003\u0002\u0002\u0002ŕŗ\u0007\u0012\u0002\u0002ŖŘ\u0005F$\u0002ŗŖ\u0003\u0002\u0002\u0002ŗŘ\u0003\u0002\u0002\u0002ŘŜ\u0003\u0002\u0002\u0002řś\u0007\u001c\u0002\u0002Śř\u0003\u0002\u0002\u0002śŞ\u0003\u0002\u0002\u0002ŜŚ\u0003\u0002\u0002\u0002Ŝŝ\u0003\u0002\u0002\u0002ŝŤ\u0003\u0002\u0002\u0002ŞŜ\u0003\u0002\u0002\u0002şŠ\u0007\u0004\u0002\u0002Šš\u0005 \u0011\u0002šŢ\u0007\u0005\u0002\u0002Ţť\u0003\u0002\u0002\u0002ţť\u0007\u0006\u0002\u0002Ťş\u0003\u0002\u0002\u0002Ťţ\u0003\u0002\u0002\u0002ť\u001f\u0003\u0002\u0002\u0002ŦŽ\u0005\"\u0012\u0002ŧũ\u0007\u001c\u0002\u0002Ũŧ\u0003\u0002\u0002\u0002ũŬ\u0003\u0002\u0002\u0002ŪŨ\u0003\u0002\u0002\u0002Ūū\u0003\u0002\u0002\u0002ūŭ\u0003\u0002\u0002\u0002ŬŪ\u0003\u0002\u0002\u0002ŭű\u0007\u0011\u0002\u0002ŮŰ\u0007\u001c\u0002\u0002ůŮ\u0003\u0002\u0002\u0002Űų\u0003\u0002\u0002\u0002űů\u0003\u0002\u0002\u0002űŲ\u0003\u0002\u0002\u0002ŲŴ\u0003\u0002\u0002\u0002ųű\u0003\u0002\u0002\u0002ŴŸ\u0005\"\u0012\u0002ŵŷ\u0007\u001c\u0002\u0002Ŷŵ\u0003\u0002\u0002\u0002ŷź\u0003\u0002\u0002\u0002ŸŶ\u0003\u0002\u0002\u0002ŸŹ\u0003\u0002\u0002\u0002Źż\u0003\u0002\u0002\u0002źŸ\u0003\u0002\u0002\u0002ŻŪ\u0003\u0002\u0002\u0002żſ\u0003\u0002\u0002\u0002ŽŻ\u0003\u0002\u0002\u0002Žž\u0003\u0002\u0002\u0002ž!\u0003\u0002\u0002\u0002ſŽ\u0003\u0002\u0002\u0002ƀƃ\u0005\u0010\t\u0002Ɓƃ\u00056\u001c\u0002Ƃƀ\u0003\u0002\u0002\u0002ƂƁ\u0003\u0002\u0002\u0002ƃ#\u0003\u0002\u0002\u0002ƄƆ\u0005D#\u0002ƅƄ\u0003\u0002\u0002\u0002ƅƆ\u0003\u0002\u0002\u0002ƆƇ\u0003\u0002\u0002\u0002ƇƉ\u0007\u0013\u0002\u0002ƈƊ\u0005F$\u0002Ɖƈ\u0003\u0002\u0002\u0002ƉƊ\u0003\u0002\u0002\u0002ƊƎ\u0003\u0002\u0002\u0002Ƌƍ\u0007\u001c\u0002\u0002ƌƋ\u0003\u0002\u0002\u0002ƍƐ\u0003\u0002\u0002\u0002Ǝƌ\u0003\u0002\u0002\u0002ƎƏ\u0003\u0002\u0002\u0002ƏƖ\u0003\u0002\u0002\u0002ƐƎ\u0003\u0002\u0002\u0002Ƒƒ\u0007\u0004\u0002\u0002ƒƓ\u0005&\u0014\u0002ƓƔ\u0007\u0005\u0002\u0002ƔƗ\u0003\u0002\u0002\u0002ƕƗ\u0007\u0006\u0002\u0002ƖƑ\u0003\u0002\u0002\u0002Ɩƕ\u0003\u0002\u0002\u0002Ɨ%\u0003\u0002\u0002\u0002ƘƯ\u0005(\u0015\u0002ƙƛ\u0007\u001c\u0002\u0002ƚƙ\u0003\u0002\u0002\u0002ƛƞ\u0003\u0002\u0002\u0002Ɯƚ\u0003\u0002\u0002\u0002ƜƝ\u0003\u0002\u0002\u0002ƝƟ\u0003\u0002\u0002\u0002ƞƜ\u0003\u0002\u0002\u0002Ɵƣ\u0007\u0011\u0002\u0002ƠƢ\u0007\u001c\u0002\u0002ơƠ\u0003\u0002\u0002\u0002Ƣƥ\u0003\u0002\u0002\u0002ƣơ\u0003\u0002\u0002\u0002ƣƤ\u0003\u0002\u0002\u0002ƤƦ\u0003\u0002\u0002\u0002ƥƣ\u0003\u0002\u0002\u0002Ʀƪ\u0005(\u0015\u0002ƧƩ\u0007\u001c\u0002\u0002ƨƧ\u0003\u0002\u0002\u0002ƩƬ\u0003\u0002\u0002\u0002ƪƨ\u0003\u0002\u0002\u0002ƪƫ\u0003\u0002\u0002\u0002ƫƮ\u0003\u0002\u0002\u0002Ƭƪ\u0003\u0002\u0002\u0002ƭƜ\u0003\u0002\u0002\u0002ƮƱ\u0003\u0002\u0002\u0002Ưƭ\u0003\u0002\u0002\u0002Ưư\u0003\u0002\u0002\u0002ư'\u0003\u0002\u0002\u0002ƱƯ\u0003\u0002\u0002\u0002Ʋƶ\u0005\u001e\u0010\u0002Ƴƶ\u0005\u0010\t\u0002ƴƶ\u00056\u001c\u0002ƵƲ\u0003\u0002\u0002\u0002ƵƳ\u0003\u0002\u0002\u0002Ƶƴ\u0003\u0002\u0002\u0002ƶ)\u0003\u0002\u0002\u0002Ʒƹ\u0005D#\u0002ƸƷ\u0003\u0002\u0002\u0002Ƹƹ\u0003\u0002\u0002\u0002ƹƺ\u0003\u0002\u0002\u0002ƺƼ\u0007\u0014\u0002\u0002ƻƽ\u0005F$\u0002Ƽƻ\u0003\u0002\u0002\u0002Ƽƽ\u0003\u0002\u0002\u0002ƽǁ\u0003\u0002\u0002\u0002ƾǀ\u0007\u001c\u0002\u0002ƿƾ\u0003\u0002\u0002\u0002ǀǃ\u0003\u0002\u0002\u0002ǁƿ\u0003\u0002\u0002\u0002ǁǂ\u0003\u0002\u0002\u0002ǂǉ\u0003\u0002\u0002\u0002ǃǁ\u0003\u0002\u0002\u0002Ǆǅ\u0007\u0004\u0002\u0002ǅǆ\u0005,\u0017\u0002ǆǇ\u0007\u0005\u0002\u0002ǇǊ\u0003\u0002\u0002\u0002ǈǊ\u0007\u0006\u0002\u0002ǉǄ\u0003\u0002\u0002\u0002ǉǈ\u0003\u0002\u0002\u0002Ǌ+\u0003\u0002\u0002\u0002ǋǢ\u0005.\u0018\u0002ǌǎ\u0007\u001c\u0002\u0002Ǎǌ\u0003\u0002\u0002\u0002ǎǑ\u0003\u0002\u0002\u0002ǏǍ\u0003\u0002\u0002\u0002Ǐǐ\u0003\u0002\u0002\u0002ǐǒ\u0003\u0002\u0002\u0002ǑǏ\u0003\u0002\u0002\u0002ǒǖ\u0007\u0011\u0002\u0002ǓǕ\u0007\u001c\u0002\u0002ǔǓ\u0003\u0002\u0002\u0002Ǖǘ\u0003\u0002\u0002\u0002ǖǔ\u0003\u0002\u0002\u0002ǖǗ\u0003\u0002\u0002\u0002ǗǙ\u0003\u0002\u0002\u0002ǘǖ\u0003\u0002\u0002\u0002Ǚǝ\u0005.\u0018\u0002ǚǜ\u0007\u001c\u0002\u0002Ǜǚ\u0003\u0002\u0002\u0002ǜǟ\u0003\u0002\u0002\u0002ǝǛ\u0003\u0002\u0002\u0002ǝǞ\u0003\u0002\u0002\u0002Ǟǡ\u0003\u0002\u0002\u0002ǟǝ\u0003\u0002\u0002\u0002ǠǏ\u0003\u0002\u0002\u0002ǡǤ\u0003\u0002\u0002\u0002ǢǠ\u0003\u0002\u0002\u0002Ǣǣ\u0003\u0002\u0002\u0002ǣ-\u0003\u0002\u0002\u0002ǤǢ\u0003\u0002\u0002\u0002ǥǨ\u0005\u0018\r\u0002ǦǨ\u00058\u001d\u0002ǧǥ\u0003\u0002\u0002\u0002ǧǦ\u0003\u0002\u0002\u0002Ǩ/\u0003\u0002\u0002\u0002ǩǫ\u0005D#\u0002Ǫǩ\u0003\u0002\u0002\u0002Ǫǫ\u0003\u0002\u0002\u0002ǫǬ\u0003\u0002\u0002\u0002ǬǮ\u0007\u0015\u0002\u0002ǭǯ\u0005F$\u0002Ǯǭ\u0003\u0002\u0002\u0002Ǯǯ\u0003\u0002\u0002\u0002ǯǳ\u0003\u0002\u0002\u0002ǰǲ\u0007\u001c\u0002\u0002Ǳǰ\u0003\u0002\u0002\u0002ǲǵ\u0003\u0002\u0002\u0002ǳǱ\u0003\u0002\u0002\u0002ǳǴ\u0003\u0002\u0002\u0002Ǵǻ\u0003\u0002\u0002\u0002ǵǳ\u0003\u0002\u0002\u0002ǶǷ\u0007\u0004\u0002\u0002ǷǸ\u00052\u001a\u0002Ǹǹ\u0007\u0005\u0002\u0002ǹǼ\u0003\u0002\u0002\u0002ǺǼ\u0007\u0006\u0002\u0002ǻǶ\u0003\u0002\u0002\u0002ǻǺ\u0003\u0002\u0002\u0002Ǽ1\u0003\u0002\u0002\u0002ǽȔ\u00054\u001b\u0002ǾȀ\u0007\u001c\u0002\u0002ǿǾ\u0003\u0002\u0002\u0002Ȁȃ\u0003\u0002\u0002\u0002ȁǿ\u0003\u0002\u0002\u0002ȁȂ\u0003\u0002\u0002\u0002ȂȄ\u0003\u0002\u0002\u0002ȃȁ\u0003\u0002\u0002\u0002ȄȈ\u0007\u0011\u0002\u0002ȅȇ\u0007\u001c\u0002\u0002Ȇȅ\u0003\u0002\u0002\u0002ȇȊ\u0003\u0002\u0002\u0002ȈȆ\u0003\u0002\u0002\u0002Ȉȉ\u0003\u0002\u0002\u0002ȉȋ\u0003\u0002\u0002\u0002ȊȈ\u0003\u0002\u0002\u0002ȋȏ\u00054\u001b\u0002ȌȎ\u0007\u001c\u0002\u0002ȍȌ\u0003\u0002\u0002\u0002Ȏȑ\u0003\u0002\u0002\u0002ȏȍ\u0003\u0002\u0002\u0002ȏȐ\u0003\u0002\u0002\u0002Ȑȓ\u0003\u0002\u0002\u0002ȑȏ\u0003\u0002\u0002\u0002Ȓȁ\u0003\u0002\u0002\u0002ȓȖ\u0003\u0002\u0002\u0002ȔȒ\u0003\u0002\u0002\u0002Ȕȕ\u0003\u0002\u0002\u0002ȕ3\u0003\u0002\u0002\u0002ȖȔ\u0003\u0002\u0002\u0002ȗȘ\u0005\u0002\u0002\u0002Ș5\u0003\u0002\u0002\u0002șȝ\u0007\u0004\u0002\u0002ȚȜ\u0007\u001c\u0002\u0002țȚ\u0003\u0002\u0002\u0002Ȝȟ\u0003\u0002\u0002\u0002ȝț\u0003\u0002\u0002\u0002ȝȞ\u0003\u0002\u0002\u0002ȞȠ\u0003\u0002\u0002\u0002ȟȝ\u0003\u0002\u0002\u0002ȠȤ\u0005<\u001f\u0002ȡȣ\u0007\u001c\u0002\u0002Ȣȡ\u0003\u0002\u0002\u0002ȣȦ\u0003\u0002\u0002\u0002ȤȢ\u0003\u0002\u0002\u0002Ȥȥ\u0003\u0002\u0002\u0002ȥȧ\u0003\u0002\u0002\u0002ȦȤ\u0003\u0002\u0002\u0002ȧȨ\u0007\u0005\u0002\u0002Ȩ7\u0003\u0002\u0002\u0002ȩȭ\u0007\u0004\u0002\u0002ȪȬ\u0007\u001c\u0002\u0002ȫȪ\u0003\u0002\u0002\u0002Ȭȯ\u0003\u0002\u0002\u0002ȭȫ\u0003\u0002\u0002\u0002ȭȮ\u0003\u0002\u0002\u0002ȮȰ\u0003\u0002\u0002\u0002ȯȭ\u0003\u0002\u0002\u0002Ȱȴ\u0005> \u0002ȱȳ\u0007\u001c\u0002\u0002Ȳȱ\u0003\u0002\u0002\u0002ȳȶ\u0003\u0002\u0002\u0002ȴȲ\u0003\u0002\u0002\u0002ȴȵ\u0003\u0002\u0002\u0002ȵȷ\u0003\u0002\u0002\u0002ȶȴ\u0003\u0002\u0002\u0002ȷȸ\u0007\u0005\u0002\u0002ȸ9\u0003\u0002\u0002\u0002ȹȻ\u0007\u001c\u0002\u0002Ⱥȹ\u0003\u0002\u0002\u0002ȻȾ\u0003\u0002\u0002\u0002ȼȺ\u0003\u0002\u0002\u0002ȼȽ\u0003\u0002\u0002\u0002Ƚȿ\u0003\u0002\u0002\u0002Ⱦȼ\u0003\u0002\u0002\u0002ȿɃ\u0007\u001b\u0002\u0002ɀɂ\u0007\u001c\u0002\u0002Ɂɀ\u0003\u0002\u0002\u0002ɂɅ\u0003\u0002\u0002\u0002ɃɁ\u0003\u0002\u0002\u0002ɃɄ\u0003\u0002\u0002\u0002ɄɆ\u0003\u0002\u0002\u0002ɅɃ\u0003\u0002\u0002\u0002ɆɊ\u0007\u001b\u0002\u0002ɇɉ\u0007\u001c\u0002\u0002Ɉɇ\u0003\u0002\u0002\u0002ɉɌ\u0003\u0002\u0002\u0002ɊɈ\u0003\u0002\u0002\u0002Ɋɋ\u0003\u0002\u0002\u0002ɋɔ\u0003\u0002\u0002\u0002ɌɊ\u0003\u0002\u0002\u0002ɍɑ\u0007\u001b\u0002\u0002Ɏɐ\u0007\u001c\u0002\u0002ɏɎ\u0003\u0002\u0002\u0002ɐɓ\u0003\u0002\u0002\u0002ɑɏ\u0003\u0002\u0002\u0002ɑɒ\u0003\u0002\u0002\u0002ɒɕ\u0003\u0002\u0002\u0002ɓɑ\u0003\u0002\u0002\u0002ɔɍ\u0003\u0002\u0002\u0002ɔɕ\u0003\u0002\u0002\u0002ɕɝ\u0003\u0002\u0002\u0002ɖɚ\u0007\u001b\u0002\u0002ɗə\u0007\u001c\u0002\u0002ɘɗ\u0003\u0002\u0002\u0002əɜ\u0003\u0002\u0002\u0002ɚɘ\u0003\u0002\u0002\u0002ɚɛ\u0003\u0002\u0002\u0002ɛɞ\u0003\u0002\u0002\u0002ɜɚ\u0003\u0002\u0002\u0002ɝɖ\u0003\u0002\u0002\u0002ɝɞ\u0003\u0002\u0002\u0002ɞ;\u0003\u0002\u0002\u0002ɟɰ\u0005:\u001e\u0002ɠɢ\u0007\u001c\u0002\u0002ɡɠ\u0003\u0002\u0002\u0002ɢɥ\u0003\u0002\u0002\u0002ɣɡ\u0003\u0002\u0002\u0002ɣɤ\u0003\u0002\u0002\u0002ɤɦ\u0003\u0002\u0002\u0002ɥɣ\u0003\u0002\u0002\u0002ɦɪ\u0007\u0011\u0002\u0002ɧɩ\u0007\u001c\u0002\u0002ɨɧ\u0003\u0002\u0002\u0002ɩɬ\u0003\u0002\u0002\u0002ɪɨ\u0003\u0002\u0002\u0002ɪɫ\u0003\u0002\u0002\u0002ɫɭ\u0003\u0002\u0002\u0002ɬɪ\u0003\u0002\u0002\u0002ɭɯ\u0005:\u001e\u0002ɮɣ\u0003\u0002\u0002\u0002ɯɲ\u0003\u0002\u0002\u0002ɰɮ\u0003\u0002\u0002\u0002ɰɱ\u0003\u0002\u0002\u0002ɱ=\u0003\u0002\u0002\u0002ɲɰ\u0003\u0002\u0002\u0002ɳɷ\u0007\u0004\u0002\u0002ɴɶ\u0007\u001c\u0002\u0002ɵɴ\u0003\u0002\u0002\u0002ɶɹ\u0003\u0002\u0002\u0002ɷɵ\u0003\u0002\u0002\u0002ɷɸ\u0003\u0002\u0002\u0002ɸɺ\u0003\u0002\u0002\u0002ɹɷ\u0003\u0002\u0002\u0002ɺɾ\u0005<\u001f\u0002ɻɽ\u0007\u001c\u0002\u0002ɼɻ\u0003\u0002\u0002\u0002ɽʀ\u0003\u0002\u0002\u0002ɾɼ\u0003\u0002\u0002\u0002ɾɿ\u0003\u0002\u0002\u0002ɿʁ\u0003\u0002\u0002\u0002ʀɾ\u0003\u0002\u0002\u0002ʁʡ\u0007\u0005\u0002\u0002ʂʄ\u0007\u001c\u0002\u0002ʃʂ\u0003\u0002\u0002\u0002ʄʇ\u0003\u0002\u0002\u0002ʅʃ\u0003\u0002\u0002\u0002ʅʆ\u0003\u0002\u0002\u0002ʆʈ\u0003\u0002\u0002\u0002ʇʅ\u0003\u0002\u0002\u0002ʈʌ\u0007\u0011\u0002\u0002ʉʋ\u0007\u001c\u0002\u0002ʊʉ\u0003\u0002\u0002\u0002ʋʎ\u0003\u0002\u0002\u0002ʌʊ\u0003\u0002\u0002\u0002ʌʍ\u0003\u0002\u0002\u0002ʍʏ\u0003\u0002\u0002\u0002ʎʌ\u0003\u0002\u0002\u0002ʏʓ\u0007\u0004\u0002\u0002ʐʒ\u0007\u001c\u0002\u0002ʑʐ\u0003\u0002\u0002\u0002ʒʕ\u0003\u0002\u0002\u0002ʓʑ\u0003\u0002\u0002\u0002ʓʔ\u0003\u0002\u0002\u0002ʔʖ\u0003\u0002\u0002\u0002ʕʓ\u0003\u0002\u0002\u0002ʖʚ\u0005<\u001f\u0002ʗʙ\u0007\u001c\u0002\u0002ʘʗ\u0003\u0002\u0002\u0002ʙʜ\u0003\u0002\u0002\u0002ʚʘ\u0003\u0002\u0002\u0002ʚʛ\u0003\u0002\u0002\u0002ʛʝ\u0003\u0002\u0002\u0002ʜʚ\u0003\u0002\u0002\u0002ʝʞ\u0007\u0005\u0002\u0002ʞʠ\u0003\u0002\u0002\u0002ʟʅ\u0003\u0002\u0002\u0002ʠʣ\u0003\u0002\u0002\u0002ʡʟ\u0003\u0002\u0002\u0002ʡʢ\u0003\u0002\u0002\u0002ʢ?\u0003\u0002\u0002\u0002ʣʡ\u0003\u0002\u0002\u0002ʤʨ\u0007\u0004\u0002\u0002ʥʧ\u0007\u001c\u0002\u0002ʦʥ\u0003\u0002\u0002\u0002ʧʪ\u0003\u0002\u0002\u0002ʨʦ\u0003\u0002\u0002\u0002ʨʩ\u0003\u0002\u0002\u0002ʩʫ\u0003\u0002\u0002\u0002ʪʨ\u0003\u0002\u0002\u0002ʫʯ\u0005> \u0002ʬʮ\u0007\u001c\u0002\u0002ʭʬ\u0003\u0002\u0002\u0002ʮʱ\u0003\u0002\u0002\u0002ʯʭ\u0003\u0002\u0002\u0002ʯʰ\u0003\u0002\u0002\u0002ʰʲ\u0003\u0002\u0002\u0002ʱʯ\u0003\u0002\u0002\u0002ʲ˒\u0007\u0005\u0002\u0002ʳʵ\u0007\u001c\u0002\u0002ʴʳ\u0003\u0002\u0002\u0002ʵʸ\u0003\u0002\u0002\u0002ʶʴ\u0003\u0002\u0002\u0002ʶʷ\u0003\u0002\u0002\u0002ʷʹ\u0003\u0002\u0002\u0002ʸʶ\u0003\u0002\u0002\u0002ʹʽ\u0007\u0011\u0002\u0002ʺʼ\u0007\u001c\u0002\u0002ʻʺ\u0003\u0002\u0002\u0002ʼʿ\u0003\u0002\u0002\u0002ʽʻ\u0003\u0002\u0002\u0002ʽʾ\u0003\u0002\u0002\u0002ʾˀ\u0003\u0002\u0002\u0002ʿʽ\u0003\u0002\u0002\u0002ˀ˄\u0007\u0004\u0002\u0002ˁ˃\u0007\u001c\u0002\u0002˂ˁ\u0003\u0002\u0002\u0002˃ˆ\u0003\u0002\u0002\u0002˄˂\u0003\u0002\u0002\u0002˄˅\u0003\u0002\u0002\u0002˅ˇ\u0003\u0002\u0002\u0002ˆ˄\u0003\u0002\u0002\u0002ˇˋ\u0005> \u0002ˈˊ\u0007\u001c\u0002\u0002ˉˈ\u0003\u0002\u0002\u0002ˊˍ\u0003\u0002\u0002\u0002ˋˉ\u0003\u0002\u0002\u0002ˋˌ\u0003\u0002\u0002\u0002ˌˎ\u0003\u0002\u0002\u0002ˍˋ\u0003\u0002\u0002\u0002ˎˏ\u0007\u0005\u0002\u0002ˏˑ\u0003\u0002\u0002\u0002ːʶ\u0003\u0002\u0002\u0002ˑ˔\u0003\u0002\u0002\u0002˒ː\u0003\u0002\u0002\u0002˒˓\u0003\u0002\u0002\u0002˓A\u0003\u0002\u0002\u0002˔˒\u0003\u0002\u0002\u0002˕˖\u0007\u0006\u0002\u0002˖C\u0003\u0002\u0002\u0002˗˘\u0007\u0016\u0002\u0002˘˙\u0007\u001b\u0002\u0002˙˚\u0007\u0017\u0002\u0002˚E\u0003\u0002\u0002\u0002˛˝\u0007\u001c\u0002\u0002˜˛\u0003\u0002\u0002\u0002˝ˠ\u0003\u0002\u0002\u0002˞˜\u0003\u0002\u0002\u0002˞˟\u0003\u0002\u0002\u0002˟ˡ\u0003\u0002\u0002\u0002ˠ˞\u0003\u0002\u0002\u0002ˡ˥\t\u0002\u0002\u0002ˢˤ\u0007\u001c\u0002\u0002ˣˢ\u0003\u0002\u0002\u0002ˤ˧\u0003\u0002\u0002\u0002˥ˣ\u0003\u0002\u0002\u0002˥˦\u0003\u0002\u0002\u0002˦G\u0003\u0002\u0002\u0002˧˥\u0003\u0002\u0002\u0002tWZ^cknrw\u007f\u0082\u0086\u008b\u0093\u0096\u009a\u009f§ª®³¿ÂÆËÓÖÚßçêîóûþĂćďĒĖěģĦĪįķĽńŊŐœŗŜŤŪűŸŽƂƅƉƎƖƜƣƪƯƵƸƼǁǉǏǖǝǢǧǪǮǳǻȁȈȏȔȝȤȭȴȼɃɊɑɔɚɝɣɪɰɷɾʅʌʓʚʡʨʯʶʽ˄ˋ˒˞˥";
    public static final ATN _ATN;

    /** @deprecated */
    @Deprecated
    public String[] getTokenNames() {
        return tokenNames;
    }

    public Vocabulary getVocabulary() {
        return VOCABULARY;
    }

    public String getGrammarFileName() {
        return "WKT.g4";
    }

    public String[] getRuleNames() {
        return ruleNames;
    }

    public String getSerializedATN() {
        return "\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u001d˩\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007\u0004\b\t\b\u0004\t\t\t\u0004\n\t\n\u0004\u000b\t\u000b\u0004\f\t\f\u0004\r\t\r\u0004\u000e\t\u000e\u0004\u000f\t\u000f\u0004\u0010\t\u0010\u0004\u0011\t\u0011\u0004\u0012\t\u0012\u0004\u0013\t\u0013\u0004\u0014\t\u0014\u0004\u0015\t\u0015\u0004\u0016\t\u0016\u0004\u0017\t\u0017\u0004\u0018\t\u0018\u0004\u0019\t\u0019\u0004\u001a\t\u001a\u0004\u001b\t\u001b\u0004\u001c\t\u001c\u0004\u001d\t\u001d\u0004\u001e\t\u001e\u0004\u001f\t\u001f\u0004 \t \u0004!\t!\u0004\"\t\"\u0004#\t#\u0004$\t$\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0005\u0002X\n\u0002\u0003\u0003\u0005\u0003[\n\u0003\u0003\u0003\u0003\u0003\u0005\u0003_\n\u0003\u0003\u0003\u0007\u0003b\n\u0003\f\u0003\u000e\u0003e\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003l\n\u0003\u0003\u0004\u0005\u0004o\n\u0004\u0003\u0004\u0003\u0004\u0005\u0004s\n\u0004\u0003\u0004\u0007\u0004v\n\u0004\f\u0004\u000e\u0004y\u000b\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0005\u0004\u0080\n\u0004\u0003\u0005\u0005\u0005\u0083\n\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0087\n\u0005\u0003\u0005\u0007\u0005\u008a\n\u0005\f\u0005\u000e\u0005\u008d\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0094\n\u0005\u0003\u0006\u0005\u0006\u0097\n\u0006\u0003\u0006\u0003\u0006\u0005\u0006\u009b\n\u0006\u0003\u0006\u0007\u0006\u009e\n\u0006\f\u0006\u000e\u0006¡\u000b\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u0006¨\n\u0006\u0003\u0007\u0005\u0007«\n\u0007\u0003\u0007\u0003\u0007\u0005\u0007¯\n\u0007\u0003\u0007\u0007\u0007²\n\u0007\f\u0007\u000e\u0007µ\u000b\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0005\u0007À\n\u0007\u0003\b\u0005\bÃ\n\b\u0003\b\u0003\b\u0005\bÇ\n\b\u0003\b\u0007\bÊ\n\b\f\b\u000e\bÍ\u000b\b\u0003\b\u0003\b\u0003\b\u0003\b\u0003\b\u0005\bÔ\n\b\u0003\t\u0005\t×\n\t\u0003\t\u0003\t\u0005\tÛ\n\t\u0003\t\u0007\tÞ\n\t\f\t\u000e\tá\u000b\t\u0003\t\u0003\t\u0003\t\u0003\t\u0003\t\u0005\tè\n\t\u0003\n\u0005\në\n\n\u0003\n\u0003\n\u0005\nï\n\n\u0003\n\u0007\nò\n\n\f\n\u000e\nõ\u000b\n\u0003\n\u0003\n\u0003\n\u0003\n\u0003\n\u0005\nü\n\n\u0003\u000b\u0005\u000bÿ\n\u000b\u0003\u000b\u0003\u000b\u0005\u000bă\n\u000b\u0003\u000b\u0007\u000bĆ\n\u000b\f\u000b\u000e\u000bĉ\u000b\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0005\u000bĐ\n\u000b\u0003\f\u0005\fē\n\f\u0003\f\u0003\f\u0005\fė\n\f\u0003\f\u0007\fĚ\n\f\f\f\u000e\fĝ\u000b\f\u0003\f\u0003\f\u0003\f\u0003\f\u0003\f\u0005\fĤ\n\f\u0003\r\u0005\rħ\n\r\u0003\r\u0003\r\u0005\rī\n\r\u0003\r\u0007\rĮ\n\r\f\r\u000e\rı\u000b\r\u0003\r\u0003\r\u0003\r\u0003\r\u0003\r\u0005\rĸ\n\r\u0003\u000e\u0003\u000e\u0007\u000eļ\n\u000e\f\u000e\u000e\u000eĿ\u000b\u000e\u0003\u000e\u0003\u000e\u0007\u000eŃ\n\u000e\f\u000e\u000e\u000eņ\u000b\u000e\u0003\u000e\u0007\u000eŉ\n\u000e\f\u000e\u000e\u000eŌ\u000b\u000e\u0003\u000f\u0003\u000f\u0003\u000f\u0005\u000fő\n\u000f\u0003\u0010\u0005\u0010Ŕ\n\u0010\u0003\u0010\u0003\u0010\u0005\u0010Ř\n\u0010\u0003\u0010\u0007\u0010ś\n\u0010\f\u0010\u000e\u0010Ş\u000b\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0005\u0010ť\n\u0010\u0003\u0011\u0003\u0011\u0007\u0011ũ\n\u0011\f\u0011\u000e\u0011Ŭ\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011Ű\n\u0011\f\u0011\u000e\u0011ų\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011ŷ\n\u0011\f\u0011\u000e\u0011ź\u000b\u0011\u0007\u0011ż\n\u0011\f\u0011\u000e\u0011ſ\u000b\u0011\u0003\u0012\u0003\u0012\u0005\u0012ƃ\n\u0012\u0003\u0013\u0005\u0013Ɔ\n\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɗ\n\u0013\u0003\u0013\u0007\u0013ƍ\n\u0013\f\u0013\u000e\u0013Ɛ\u000b\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɨ\n\u0013\u0003\u0014\u0003\u0014\u0007\u0014ƛ\n\u0014\f\u0014\u000e\u0014ƞ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ƣ\n\u0014\f\u0014\u000e\u0014ƥ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ʃ\n\u0014\f\u0014\u000e\u0014Ƭ\u000b\u0014\u0007\u0014Ʈ\n\u0014\f\u0014\u000e\u0014Ʊ\u000b\u0014\u0003\u0015\u0003\u0015\u0003\u0015\u0005\u0015ƶ\n\u0015\u0003\u0016\u0005\u0016ƹ\n\u0016\u0003\u0016\u0003\u0016\u0005\u0016ƽ\n\u0016\u0003\u0016\u0007\u0016ǀ\n\u0016\f\u0016\u000e\u0016ǃ\u000b\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0005\u0016Ǌ\n\u0016\u0003\u0017\u0003\u0017\u0007\u0017ǎ\n\u0017\f\u0017\u000e\u0017Ǒ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017Ǖ\n\u0017\f\u0017\u000e\u0017ǘ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017ǜ\n\u0017\f\u0017\u000e\u0017ǟ\u000b\u0017\u0007\u0017ǡ\n\u0017\f\u0017\u000e\u0017Ǥ\u000b\u0017\u0003\u0018\u0003\u0018\u0005\u0018Ǩ\n\u0018\u0003\u0019\u0005\u0019ǫ\n\u0019\u0003\u0019\u0003\u0019\u0005\u0019ǯ\n\u0019\u0003\u0019\u0007\u0019ǲ\n\u0019\f\u0019\u000e\u0019ǵ\u000b\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0005\u0019Ǽ\n\u0019\u0003\u001a\u0003\u001a\u0007\u001aȀ\n\u001a\f\u001a\u000e\u001aȃ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȇ\n\u001a\f\u001a\u000e\u001aȊ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȎ\n\u001a\f\u001a\u000e\u001aȑ\u000b\u001a\u0007\u001aȓ\n\u001a\f\u001a\u000e\u001aȖ\u000b\u001a\u0003\u001b\u0003\u001b\u0003\u001c\u0003\u001c\u0007\u001cȜ\n\u001c\f\u001c\u000e\u001cȟ\u000b\u001c\u0003\u001c\u0003\u001c\u0007\u001cȣ\n\u001c\f\u001c\u000e\u001cȦ\u000b\u001c\u0003\u001c\u0003\u001c\u0003\u001d\u0003\u001d\u0007\u001dȬ\n\u001d\f\u001d\u000e\u001dȯ\u000b\u001d\u0003\u001d\u0003\u001d\u0007\u001dȳ\n\u001d\f\u001d\u000e\u001dȶ\u000b\u001d\u0003\u001d\u0003\u001d\u0003\u001e\u0007\u001eȻ\n\u001e\f\u001e\u000e\u001eȾ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɂ\n\u001e\f\u001e\u000e\u001eɅ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɉ\n\u001e\f\u001e\u000e\u001eɌ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɐ\n\u001e\f\u001e\u000e\u001eɓ\u000b\u001e\u0005\u001eɕ\n\u001e\u0003\u001e\u0003\u001e\u0007\u001eə\n\u001e\f\u001e\u000e\u001eɜ\u000b\u001e\u0005\u001eɞ\n\u001e\u0003\u001f\u0003\u001f\u0007\u001fɢ\n\u001f\f\u001f\u000e\u001fɥ\u000b\u001f\u0003\u001f\u0003\u001f\u0007\u001fɩ\n\u001f\f\u001f\u000e\u001fɬ\u000b\u001f\u0003\u001f\u0007\u001fɯ\n\u001f\f\u001f\u000e\u001fɲ\u000b\u001f\u0003 \u0003 \u0007 ɶ\n \f \u000e ɹ\u000b \u0003 \u0003 \u0007 ɽ\n \f \u000e ʀ\u000b \u0003 \u0003 \u0007 ʄ\n \f \u000e ʇ\u000b \u0003 \u0003 \u0007 ʋ\n \f \u000e ʎ\u000b \u0003 \u0003 \u0007 ʒ\n \f \u000e ʕ\u000b \u0003 \u0003 \u0007 ʙ\n \f \u000e ʜ\u000b \u0003 \u0003 \u0007 ʠ\n \f \u000e ʣ\u000b \u0003!\u0003!\u0007!ʧ\n!\f!\u000e!ʪ\u000b!\u0003!\u0003!\u0007!ʮ\n!\f!\u000e!ʱ\u000b!\u0003!\u0003!\u0007!ʵ\n!\f!\u000e!ʸ\u000b!\u0003!\u0003!\u0007!ʼ\n!\f!\u000e!ʿ\u000b!\u0003!\u0003!\u0007!˃\n!\f!\u000e!ˆ\u000b!\u0003!\u0003!\u0007!ˊ\n!\f!\u000e!ˍ\u000b!\u0003!\u0003!\u0007!ˑ\n!\f!\u000e!˔\u000b!\u0003\"\u0003\"\u0003#\u0003#\u0003#\u0003#\u0003$\u0007$˝\n$\f$\u000e$ˠ\u000b$\u0003$\u0003$\u0007$ˤ\n$\f$\u000e$˧\u000b$\u0003$\u0002\u0002%\u0002\u0004\u0006\b\n\f\u000e\u0010\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.02468:<>@BDF\u0002\u0003\u0003\u0002\u0018\u001a\u0002͇\u0002W\u0003\u0002\u0002\u0002\u0004Z\u0003\u0002\u0002\u0002\u0006n\u0003\u0002\u0002\u0002\b\u0082\u0003\u0002\u0002\u0002\n\u0096\u0003\u0002\u0002\u0002\fª\u0003\u0002\u0002\u0002\u000eÂ\u0003\u0002\u0002\u0002\u0010Ö\u0003\u0002\u0002\u0002\u0012ê\u0003\u0002\u0002\u0002\u0014þ\u0003\u0002\u0002\u0002\u0016Ē\u0003\u0002\u0002\u0002\u0018Ħ\u0003\u0002\u0002\u0002\u001aĹ\u0003\u0002\u0002\u0002\u001cŐ\u0003\u0002\u0002\u0002\u001eœ\u0003\u0002\u0002\u0002 Ŧ\u0003\u0002\u0002\u0002\"Ƃ\u0003\u0002\u0002\u0002$ƅ\u0003\u0002\u0002\u0002&Ƙ\u0003\u0002\u0002\u0002(Ƶ\u0003\u0002\u0002\u0002*Ƹ\u0003\u0002\u0002\u0002,ǋ\u0003\u0002\u0002\u0002.ǧ\u0003\u0002\u0002\u00020Ǫ\u0003\u0002\u0002\u00022ǽ\u0003\u0002\u0002\u00024ȗ\u0003\u0002\u0002\u00026ș\u0003\u0002\u0002\u00028ȩ\u0003\u0002\u0002\u0002:ȼ\u0003\u0002\u0002\u0002<ɟ\u0003\u0002\u0002\u0002>ɳ\u0003\u0002\u0002\u0002@ʤ\u0003\u0002\u0002\u0002B˕\u0003\u0002\u0002\u0002D˗\u0003\u0002\u0002\u0002F˞\u0003\u0002\u0002\u0002HX\u0005\u0004\u0003\u0002IX\u0005\u0006\u0004\u0002JX\u0005\b\u0005\u0002KX\u0005\f\u0007\u0002LX\u0005\u000e\b\u0002MX\u0005\u0010\t\u0002NX\u0005\n\u0006\u0002OX\u0005\u0012\n\u0002PX\u0005\u0014\u000b\u0002QX\u0005\u0016\f\u0002RX\u0005\u001e\u0010\u0002SX\u0005\u0018\r\u0002TX\u0005$\u0013\u0002UX\u0005*\u0016\u0002VX\u00050\u0019\u0002WH\u0003\u0002\u0002\u0002WI\u0003\u0002\u0002\u0002WJ\u0003\u0002\u0002\u0002WK\u0003\u0002\u0002\u0002WL\u0003\u0002\u0002\u0002WM\u0003\u0002\u0002\u0002WN\u0003\u0002\u0002\u0002WO\u0003\u0002\u0002\u0002WP\u0003\u0002\u0002\u0002WQ\u0003\u0002\u0002\u0002WR\u0003\u0002\u0002\u0002WS\u0003\u0002\u0002\u0002WT\u0003\u0002\u0002\u0002WU\u0003\u0002\u0002\u0002WV\u0003\u0002\u0002\u0002X\u0003\u0003\u0002\u0002\u0002Y[\u0005D#\u0002ZY\u0003\u0002\u0002\u0002Z[\u0003\u0002\u0002\u0002[\\\u0003\u0002\u0002\u0002\\^\u0007\u0003\u0002\u0002]_\u0005F$\u0002^]\u0003\u0002\u0002\u0002^_\u0003\u0002\u0002\u0002_c\u0003\u0002\u0002\u0002`b\u0007\u001c\u0002\u0002a`\u0003\u0002\u0002\u0002be\u0003\u0002\u0002\u0002ca\u0003\u0002\u0002\u0002cd\u0003\u0002\u0002\u0002dk\u0003\u0002\u0002\u0002ec\u0003\u0002\u0002\u0002fg\u0007\u0004\u0002\u0002gh\u0005:\u001e\u0002hi\u0007\u0005\u0002\u0002il\u0003\u0002\u0002\u0002jl\u0007\u0006\u0002\u0002kf\u0003\u0002\u0002\u0002kj\u0003\u0002\u0002\u0002l\u0005\u0003\u0002\u0002\u0002mo\u0005D#\u0002nm\u0003\u0002\u0002\u0002no\u0003\u0002\u0002\u0002op\u0003\u0002\u0002\u0002pr\u0007\u0007\u0002\u0002qs\u0005F$\u0002rq\u0003\u0002\u0002\u0002rs\u0003\u0002\u0002\u0002sw\u0003\u0002\u0002\u0002tv\u0007\u001c\u0002\u0002ut\u0003\u0002\u0002\u0002vy\u0003\u0002\u0002\u0002wu\u0003\u0002\u0002\u0002wx\u0003\u0002\u0002\u0002x\u007f\u0003\u0002\u0002\u0002yw\u0003\u0002\u0002\u0002z{\u0007\u0004\u0002\u0002{|\u0005<\u001f\u0002|}\u0007\u0005\u0002\u0002}\u0080\u0003\u0002\u0002\u0002~\u0080\u0007\u0006\u0002\u0002\u007fz\u0003\u0002\u0002\u0002\u007f~\u0003\u0002\u0002\u0002\u0080\u0007\u0003\u0002\u0002\u0002\u0081\u0083\u0005D#\u0002\u0082\u0081\u0003\u0002\u0002\u0002\u0082\u0083\u0003\u0002\u0002\u0002\u0083\u0084\u0003\u0002\u0002\u0002\u0084\u0086\u0007\b\u0002\u0002\u0085\u0087\u0005F$\u0002\u0086\u0085\u0003\u0002\u0002\u0002\u0086\u0087\u0003\u0002\u0002\u0002\u0087\u008b\u0003\u0002\u0002\u0002\u0088\u008a\u0007\u001c\u0002\u0002\u0089\u0088\u0003\u0002\u0002\u0002\u008a\u008d\u0003\u0002\u0002\u0002\u008b\u0089\u0003\u0002\u0002\u0002\u008b\u008c\u0003\u0002\u0002\u0002\u008c\u0093\u0003\u0002\u0002\u0002\u008d\u008b\u0003\u0002\u0002\u0002\u008e\u008f\u0007\u0004\u0002\u0002\u008f\u0090\u0005> \u0002\u0090\u0091\u0007\u0005\u0002\u0002\u0091\u0094\u0003\u0002\u0002\u0002\u0092\u0094\u0007\u0006\u0002\u0002\u0093\u008e\u0003\u0002\u0002\u0002\u0093\u0092\u0003\u0002\u0002\u0002\u0094\t\u0003\u0002\u0002\u0002\u0095\u0097\u0005D#\u0002\u0096\u0095\u0003\u0002\u0002\u0002\u0096\u0097\u0003\u0002\u0002\u0002\u0097\u0098\u0003\u0002\u0002\u0002\u0098\u009a\u0007\t\u0002\u0002\u0099\u009b\u0005F$\u0002\u009a\u0099\u0003\u0002\u0002\u0002\u009a\u009b\u0003\u0002\u0002\u0002\u009b\u009f\u0003\u0002\u0002\u0002\u009c\u009e\u0007\u001c\u0002\u0002\u009d\u009c\u0003\u0002\u0002\u0002\u009e¡\u0003\u0002\u0002\u0002\u009f\u009d\u0003\u0002\u0002\u0002\u009f \u0003\u0002\u0002\u0002 §\u0003\u0002\u0002\u0002¡\u009f\u0003\u0002\u0002\u0002¢£\u0007\u0004\u0002\u0002£¤\u0005> \u0002¤¥\u0007\u0005\u0002\u0002¥¨\u0003\u0002\u0002\u0002¦¨\u0007\u0006\u0002\u0002§¢\u0003\u0002\u0002\u0002§¦\u0003\u0002\u0002\u0002¨\u000b\u0003\u0002\u0002\u0002©«\u0005D#\u0002ª©\u0003\u0002\u0002\u0002ª«\u0003\u0002\u0002\u0002«¬\u0003\u0002\u0002\u0002¬®\u0007\n\u0002\u0002\u00ad¯\u0005F$\u0002®\u00ad\u0003\u0002\u0002\u0002®¯\u0003\u0002\u0002\u0002¯³\u0003\u0002\u0002\u0002°²\u0007\u001c\u0002\u0002±°\u0003\u0002\u0002\u0002²µ\u0003\u0002\u0002\u0002³±\u0003\u0002\u0002\u0002³´\u0003\u0002\u0002\u0002´¿\u0003\u0002\u0002\u0002µ³\u0003\u0002\u0002\u0002¶·\u0007\u0004\u0002\u0002·¸\u0005<\u001f\u0002¸¹\u0007\u0005\u0002\u0002¹À\u0003\u0002\u0002\u0002º»\u0007\u0004\u0002\u0002»¼\u0005> \u0002¼½\u0007\u0005\u0002\u0002½À\u0003\u0002\u0002\u0002¾À\u0007\u0006\u0002\u0002¿¶\u0003\u0002\u0002\u0002¿º\u0003\u0002\u0002\u0002¿¾\u0003\u0002\u0002\u0002À\r\u0003\u0002\u0002\u0002ÁÃ\u0005D#\u0002ÂÁ\u0003\u0002\u0002\u0002ÂÃ\u0003\u0002\u0002\u0002ÃÄ\u0003\u0002\u0002\u0002ÄÆ\u0007\u000b\u0002\u0002ÅÇ\u0005F$\u0002ÆÅ\u0003\u0002\u0002\u0002ÆÇ\u0003\u0002\u0002\u0002ÇË\u0003\u0002\u0002\u0002ÈÊ\u0007\u001c\u0002\u0002ÉÈ\u0003\u0002\u0002\u0002ÊÍ\u0003\u0002\u0002\u0002ËÉ\u0003\u0002\u0002\u0002ËÌ\u0003\u0002\u0002\u0002ÌÓ\u0003\u0002\u0002\u0002ÍË\u0003\u0002\u0002\u0002ÎÏ\u0007\u0004\u0002\u0002ÏÐ\u0005> \u0002ÐÑ\u0007\u0005\u0002\u0002ÑÔ\u0003\u0002\u0002\u0002ÒÔ\u0007\u0006\u0002\u0002ÓÎ\u0003\u0002\u0002\u0002ÓÒ\u0003\u0002\u0002\u0002Ô\u000f\u0003\u0002\u0002\u0002Õ×\u0005D#\u0002ÖÕ\u0003\u0002\u0002\u0002Ö×\u0003\u0002\u0002\u0002×Ø\u0003\u0002\u0002\u0002ØÚ\u0007\f\u0002\u0002ÙÛ\u0005F$\u0002ÚÙ\u0003\u0002\u0002\u0002ÚÛ\u0003\u0002\u0002\u0002Ûß\u0003\u0002\u0002\u0002ÜÞ\u0007\u001c\u0002\u0002ÝÜ\u0003\u0002\u0002\u0002Þá\u0003\u0002\u0002\u0002ßÝ\u0003\u0002\u0002\u0002ßà\u0003\u0002\u0002\u0002àç\u0003\u0002\u0002\u0002áß\u0003\u0002\u0002\u0002âã\u0007\u0004\u0002\u0002ãä\u0005<\u001f\u0002äå\u0007\u0005\u0002\u0002åè\u0003\u0002\u0002\u0002æè\u0007\u0006\u0002\u0002çâ\u0003\u0002\u0002\u0002çæ\u0003\u0002\u0002\u0002è\u0011\u0003\u0002\u0002\u0002éë\u0005D#\u0002êé\u0003\u0002\u0002\u0002êë\u0003\u0002\u0002\u0002ëì\u0003\u0002\u0002\u0002ìî\u0007\r\u0002\u0002íï\u0005F$\u0002îí\u0003\u0002\u0002\u0002îï\u0003\u0002\u0002\u0002ïó\u0003\u0002\u0002\u0002ðò\u0007\u001c\u0002\u0002ñð\u0003\u0002\u0002\u0002òõ\u0003\u0002\u0002\u0002óñ\u0003\u0002\u0002\u0002óô\u0003\u0002\u0002\u0002ôû\u0003\u0002\u0002\u0002õó\u0003\u0002\u0002\u0002ö÷\u0007\u0004\u0002\u0002÷ø\u0005@!\u0002øù\u0007\u0005\u0002\u0002ùü\u0003\u0002\u0002\u0002úü\u0007\u0006\u0002\u0002ûö\u0003\u0002\u0002\u0002ûú\u0003\u0002\u0002\u0002ü\u0013\u0003\u0002\u0002\u0002ýÿ\u0005D#\u0002þý\u0003\u0002\u0002\u0002þÿ\u0003\u0002\u0002\u0002ÿĀ\u0003\u0002\u0002\u0002ĀĂ\u0007\u000e\u0002\u0002āă\u0005F$\u0002Ăā\u0003\u0002\u0002\u0002Ăă\u0003\u0002\u0002\u0002ăć\u0003\u0002\u0002\u0002ĄĆ\u0007\u001c\u0002\u0002ąĄ\u0003\u0002\u0002\u0002Ćĉ\u0003\u0002\u0002\u0002ćą\u0003\u0002\u0002\u0002ćĈ\u0003\u0002\u0002\u0002Ĉď\u0003\u0002\u0002\u0002ĉć\u0003\u0002\u0002\u0002Ċċ\u0007\u0004\u0002\u0002ċČ\u0005@!\u0002Čč\u0007\u0005\u0002\u0002čĐ\u0003\u0002\u0002\u0002ĎĐ\u0007\u0006\u0002\u0002ďĊ\u0003\u0002\u0002\u0002ďĎ\u0003\u0002\u0002\u0002Đ\u0015\u0003\u0002\u0002\u0002đē\u0005D#\u0002Ēđ\u0003\u0002\u0002\u0002Ēē\u0003\u0002\u0002\u0002ēĔ\u0003\u0002\u0002\u0002ĔĖ\u0007\u000f\u0002\u0002ĕė\u0005F$\u0002Ėĕ\u0003\u0002\u0002\u0002Ėė\u0003\u0002\u0002\u0002ėě\u0003\u0002\u0002\u0002ĘĚ\u0007\u001c\u0002\u0002ęĘ\u0003\u0002\u0002\u0002Ěĝ\u0003\u0002\u0002\u0002ěę\u0003\u0002\u0002\u0002ěĜ\u0003\u0002\u0002\u0002Ĝģ\u0003\u0002\u0002\u0002ĝě\u0003\u0002\u0002\u0002Ğğ\u0007\u0004\u0002\u0002ğĠ\u0005@!\u0002Ġġ\u0007\u0005\u0002\u0002ġĤ\u0003\u0002\u0002\u0002ĢĤ\u0007\u0006\u0002\u0002ģĞ\u0003\u0002\u0002\u0002ģĢ\u0003\u0002\u0002\u0002Ĥ\u0017\u0003\u0002\u0002\u0002ĥħ\u0005D#\u0002Ħĥ\u0003\u0002\u0002\u0002Ħħ\u0003\u0002\u0002\u0002ħĨ\u0003\u0002\u0002\u0002ĨĪ\u0007\u0010\u0002\u0002ĩī\u0005F$\u0002Īĩ\u0003\u0002\u0002\u0002Īī\u0003\u0002\u0002\u0002īį\u0003\u0002\u0002\u0002ĬĮ\u0007\u001c\u0002\u0002ĭĬ\u0003\u0002\u0002\u0002Įı\u0003\u0002\u0002\u0002įĭ\u0003\u0002\u0002\u0002įİ\u0003\u0002\u0002\u0002İķ\u0003\u0002\u0002\u0002ıį\u0003\u0002\u0002\u0002Ĳĳ\u0007\u0004\u0002\u0002ĳĴ\u0005\u001a\u000e\u0002Ĵĵ\u0007\u0005\u0002\u0002ĵĸ\u0003\u0002\u0002\u0002Ķĸ\u0007\u0006\u0002\u0002ķĲ\u0003\u0002\u0002\u0002ķĶ\u0003\u0002\u0002\u0002ĸ\u0019\u0003\u0002\u0002\u0002ĹŊ\u0005\u001c\u000f\u0002ĺļ\u0007\u001c\u0002\u0002Ļĺ\u0003\u0002\u0002\u0002ļĿ\u0003\u0002\u0002\u0002ĽĻ\u0003\u0002\u0002\u0002Ľľ\u0003\u0002\u0002\u0002ľŀ\u0003\u0002\u0002\u0002ĿĽ\u0003\u0002\u0002\u0002ŀń\u0007\u0011\u0002\u0002ŁŃ\u0007\u001c\u0002\u0002łŁ\u0003\u0002\u0002\u0002Ńņ\u0003\u0002\u0002\u0002ńł\u0003\u0002\u0002\u0002ńŅ\u0003\u0002\u0002\u0002ŅŇ\u0003\u0002\u0002\u0002ņń\u0003\u0002\u0002\u0002Ňŉ\u0005\u001c\u000f\u0002ňĽ\u0003\u0002\u0002\u0002ŉŌ\u0003\u0002\u0002\u0002Ŋň\u0003\u0002\u0002\u0002Ŋŋ\u0003\u0002\u0002\u0002ŋ\u001b\u0003\u0002\u0002\u0002ŌŊ\u0003\u0002\u0002\u0002ōő\u0005\u001e\u0010\u0002Ŏő\u0005\u0010\t\u0002ŏő\u00056\u001c\u0002Őō\u0003\u0002\u0002\u0002ŐŎ\u0003\u0002\u0002\u0002Őŏ\u0003\u0002\u0002\u0002ő\u001d\u0003\u0002\u0002\u0002ŒŔ\u0005D#\u0002œŒ\u0003\u0002\u0002\u0002œŔ\u0003\u0002\u0002\u0002Ŕŕ\u0003\u0002\u0002\u0002ŕŗ\u0007\u0012\u0002\u0002ŖŘ\u0005F$\u0002ŗŖ\u0003\u0002\u0002\u0002ŗŘ\u0003\u0002\u0002\u0002ŘŜ\u0003\u0002\u0002\u0002řś\u0007\u001c\u0002\u0002Śř\u0003\u0002\u0002\u0002śŞ\u0003\u0002\u0002\u0002ŜŚ\u0003\u0002\u0002\u0002Ŝŝ\u0003\u0002\u0002\u0002ŝŤ\u0003\u0002\u0002\u0002ŞŜ\u0003\u0002\u0002\u0002şŠ\u0007\u0004\u0002\u0002Šš\u0005 \u0011\u0002šŢ\u0007\u0005\u0002\u0002Ţť\u0003\u0002\u0002\u0002ţť\u0007\u0006\u0002\u0002Ťş\u0003\u0002\u0002\u0002Ťţ\u0003\u0002\u0002\u0002ť\u001f\u0003\u0002\u0002\u0002ŦŽ\u0005\"\u0012\u0002ŧũ\u0007\u001c\u0002\u0002Ũŧ\u0003\u0002\u0002\u0002ũŬ\u0003\u0002\u0002\u0002ŪŨ\u0003\u0002\u0002\u0002Ūū\u0003\u0002\u0002\u0002ūŭ\u0003\u0002\u0002\u0002ŬŪ\u0003\u0002\u0002\u0002ŭű\u0007\u0011\u0002\u0002ŮŰ\u0007\u001c\u0002\u0002ůŮ\u0003\u0002\u0002\u0002Űų\u0003\u0002\u0002\u0002űů\u0003\u0002\u0002\u0002űŲ\u0003\u0002\u0002\u0002ŲŴ\u0003\u0002\u0002\u0002ųű\u0003\u0002\u0002\u0002ŴŸ\u0005\"\u0012\u0002ŵŷ\u0007\u001c\u0002\u0002Ŷŵ\u0003\u0002\u0002\u0002ŷź\u0003\u0002\u0002\u0002ŸŶ\u0003\u0002\u0002\u0002ŸŹ\u0003\u0002\u0002\u0002Źż\u0003\u0002\u0002\u0002źŸ\u0003\u0002\u0002\u0002ŻŪ\u0003\u0002\u0002\u0002żſ\u0003\u0002\u0002\u0002ŽŻ\u0003\u0002\u0002\u0002Žž\u0003\u0002\u0002\u0002ž!\u0003\u0002\u0002\u0002ſŽ\u0003\u0002\u0002\u0002ƀƃ\u0005\u0010\t\u0002Ɓƃ\u00056\u001c\u0002Ƃƀ\u0003\u0002\u0002\u0002ƂƁ\u0003\u0002\u0002\u0002ƃ#\u0003\u0002\u0002\u0002ƄƆ\u0005D#\u0002ƅƄ\u0003\u0002\u0002\u0002ƅƆ\u0003\u0002\u0002\u0002ƆƇ\u0003\u0002\u0002\u0002ƇƉ\u0007\u0013\u0002\u0002ƈƊ\u0005F$\u0002Ɖƈ\u0003\u0002\u0002\u0002ƉƊ\u0003\u0002\u0002\u0002ƊƎ\u0003\u0002\u0002\u0002Ƌƍ\u0007\u001c\u0002\u0002ƌƋ\u0003\u0002\u0002\u0002ƍƐ\u0003\u0002\u0002\u0002Ǝƌ\u0003\u0002\u0002\u0002ƎƏ\u0003\u0002\u0002\u0002ƏƖ\u0003\u0002\u0002\u0002ƐƎ\u0003\u0002\u0002\u0002Ƒƒ\u0007\u0004\u0002\u0002ƒƓ\u0005&\u0014\u0002ƓƔ\u0007\u0005\u0002\u0002ƔƗ\u0003\u0002\u0002\u0002ƕƗ\u0007\u0006\u0002\u0002ƖƑ\u0003\u0002\u0002\u0002Ɩƕ\u0003\u0002\u0002\u0002Ɨ%\u0003\u0002\u0002\u0002ƘƯ\u0005(\u0015\u0002ƙƛ\u0007\u001c\u0002\u0002ƚƙ\u0003\u0002\u0002\u0002ƛƞ\u0003\u0002\u0002\u0002Ɯƚ\u0003\u0002\u0002\u0002ƜƝ\u0003\u0002\u0002\u0002ƝƟ\u0003\u0002\u0002\u0002ƞƜ\u0003\u0002\u0002\u0002Ɵƣ\u0007\u0011\u0002\u0002ƠƢ\u0007\u001c\u0002\u0002ơƠ\u0003\u0002\u0002\u0002Ƣƥ\u0003\u0002\u0002\u0002ƣơ\u0003\u0002\u0002\u0002ƣƤ\u0003\u0002\u0002\u0002ƤƦ\u0003\u0002\u0002\u0002ƥƣ\u0003\u0002\u0002\u0002Ʀƪ\u0005(\u0015\u0002ƧƩ\u0007\u001c\u0002\u0002ƨƧ\u0003\u0002\u0002\u0002ƩƬ\u0003\u0002\u0002\u0002ƪƨ\u0003\u0002\u0002\u0002ƪƫ\u0003\u0002\u0002\u0002ƫƮ\u0003\u0002\u0002\u0002Ƭƪ\u0003\u0002\u0002\u0002ƭƜ\u0003\u0002\u0002\u0002ƮƱ\u0003\u0002\u0002\u0002Ưƭ\u0003\u0002\u0002\u0002Ưư\u0003\u0002\u0002\u0002ư'\u0003\u0002\u0002\u0002ƱƯ\u0003\u0002\u0002\u0002Ʋƶ\u0005\u001e\u0010\u0002Ƴƶ\u0005\u0010\t\u0002ƴƶ\u00056\u001c\u0002ƵƲ\u0003\u0002\u0002\u0002ƵƳ\u0003\u0002\u0002\u0002Ƶƴ\u0003\u0002\u0002\u0002ƶ)\u0003\u0002\u0002\u0002Ʒƹ\u0005D#\u0002ƸƷ\u0003\u0002\u0002\u0002Ƹƹ\u0003\u0002\u0002\u0002ƹƺ\u0003\u0002\u0002\u0002ƺƼ\u0007\u0014\u0002\u0002ƻƽ\u0005F$\u0002Ƽƻ\u0003\u0002\u0002\u0002Ƽƽ\u0003\u0002\u0002\u0002ƽǁ\u0003\u0002\u0002\u0002ƾǀ\u0007\u001c\u0002\u0002ƿƾ\u0003\u0002\u0002\u0002ǀǃ\u0003\u0002\u0002\u0002ǁƿ\u0003\u0002\u0002\u0002ǁǂ\u0003\u0002\u0002\u0002ǂǉ\u0003\u0002\u0002\u0002ǃǁ\u0003\u0002\u0002\u0002Ǆǅ\u0007\u0004\u0002\u0002ǅǆ\u0005,\u0017\u0002ǆǇ\u0007\u0005\u0002\u0002ǇǊ\u0003\u0002\u0002\u0002ǈǊ\u0007\u0006\u0002\u0002ǉǄ\u0003\u0002\u0002\u0002ǉǈ\u0003\u0002\u0002\u0002Ǌ+\u0003\u0002\u0002\u0002ǋǢ\u0005.\u0018\u0002ǌǎ\u0007\u001c\u0002\u0002Ǎǌ\u0003\u0002\u0002\u0002ǎǑ\u0003\u0002\u0002\u0002ǏǍ\u0003\u0002\u0002\u0002Ǐǐ\u0003\u0002\u0002\u0002ǐǒ\u0003\u0002\u0002\u0002ǑǏ\u0003\u0002\u0002\u0002ǒǖ\u0007\u0011\u0002\u0002ǓǕ\u0007\u001c\u0002\u0002ǔǓ\u0003\u0002\u0002\u0002Ǖǘ\u0003\u0002\u0002\u0002ǖǔ\u0003\u0002\u0002\u0002ǖǗ\u0003\u0002\u0002\u0002ǗǙ\u0003\u0002\u0002\u0002ǘǖ\u0003\u0002\u0002\u0002Ǚǝ\u0005.\u0018\u0002ǚǜ\u0007\u001c\u0002\u0002Ǜǚ\u0003\u0002\u0002\u0002ǜǟ\u0003\u0002\u0002\u0002ǝǛ\u0003\u0002\u0002\u0002ǝǞ\u0003\u0002\u0002\u0002Ǟǡ\u0003\u0002\u0002\u0002ǟǝ\u0003\u0002\u0002\u0002ǠǏ\u0003\u0002\u0002\u0002ǡǤ\u0003\u0002\u0002\u0002ǢǠ\u0003\u0002\u0002\u0002Ǣǣ\u0003\u0002\u0002\u0002ǣ-\u0003\u0002\u0002\u0002ǤǢ\u0003\u0002\u0002\u0002ǥǨ\u0005\u0018\r\u0002ǦǨ\u00058\u001d\u0002ǧǥ\u0003\u0002\u0002\u0002ǧǦ\u0003\u0002\u0002\u0002Ǩ/\u0003\u0002\u0002\u0002ǩǫ\u0005D#\u0002Ǫǩ\u0003\u0002\u0002\u0002Ǫǫ\u0003\u0002\u0002\u0002ǫǬ\u0003\u0002\u0002\u0002ǬǮ\u0007\u0015\u0002\u0002ǭǯ\u0005F$\u0002Ǯǭ\u0003\u0002\u0002\u0002Ǯǯ\u0003\u0002\u0002\u0002ǯǳ\u0003\u0002\u0002\u0002ǰǲ\u0007\u001c\u0002\u0002Ǳǰ\u0003\u0002\u0002\u0002ǲǵ\u0003\u0002\u0002\u0002ǳǱ\u0003\u0002\u0002\u0002ǳǴ\u0003\u0002\u0002\u0002Ǵǻ\u0003\u0002\u0002\u0002ǵǳ\u0003\u0002\u0002\u0002ǶǷ\u0007\u0004\u0002\u0002ǷǸ\u00052\u001a\u0002Ǹǹ\u0007\u0005\u0002\u0002ǹǼ\u0003\u0002\u0002\u0002ǺǼ\u0007\u0006\u0002\u0002ǻǶ\u0003\u0002\u0002\u0002ǻǺ\u0003\u0002\u0002\u0002Ǽ1\u0003\u0002\u0002\u0002ǽȔ\u00054\u001b\u0002ǾȀ\u0007\u001c\u0002\u0002ǿǾ\u0003\u0002\u0002\u0002Ȁȃ\u0003\u0002\u0002\u0002ȁǿ\u0003\u0002\u0002\u0002ȁȂ\u0003\u0002\u0002\u0002ȂȄ\u0003\u0002\u0002\u0002ȃȁ\u0003\u0002\u0002\u0002ȄȈ\u0007\u0011\u0002\u0002ȅȇ\u0007\u001c\u0002\u0002Ȇȅ\u0003\u0002\u0002\u0002ȇȊ\u0003\u0002\u0002\u0002ȈȆ\u0003\u0002\u0002\u0002Ȉȉ\u0003\u0002\u0002\u0002ȉȋ\u0003\u0002\u0002\u0002ȊȈ\u0003\u0002\u0002\u0002ȋȏ\u00054\u001b\u0002ȌȎ\u0007\u001c\u0002\u0002ȍȌ\u0003\u0002\u0002\u0002Ȏȑ\u0003\u0002\u0002\u0002ȏȍ\u0003\u0002\u0002\u0002ȏȐ\u0003\u0002\u0002\u0002Ȑȓ\u0003\u0002\u0002\u0002ȑȏ\u0003\u0002\u0002\u0002Ȓȁ\u0003\u0002\u0002\u0002ȓȖ\u0003\u0002\u0002\u0002ȔȒ\u0003\u0002\u0002\u0002Ȕȕ\u0003\u0002\u0002\u0002ȕ3\u0003\u0002\u0002\u0002ȖȔ\u0003\u0002\u0002\u0002ȗȘ\u0005\u0002\u0002\u0002Ș5\u0003\u0002\u0002\u0002șȝ\u0007\u0004\u0002\u0002ȚȜ\u0007\u001c\u0002\u0002țȚ\u0003\u0002\u0002\u0002Ȝȟ\u0003\u0002\u0002\u0002ȝț\u0003\u0002\u0002\u0002ȝȞ\u0003\u0002\u0002\u0002ȞȠ\u0003\u0002\u0002\u0002ȟȝ\u0003\u0002\u0002\u0002ȠȤ\u0005<\u001f\u0002ȡȣ\u0007\u001c\u0002\u0002Ȣȡ\u0003\u0002\u0002\u0002ȣȦ\u0003\u0002\u0002\u0002ȤȢ\u0003\u0002\u0002\u0002Ȥȥ\u0003\u0002\u0002\u0002ȥȧ\u0003\u0002\u0002\u0002ȦȤ\u0003\u0002\u0002\u0002ȧȨ\u0007\u0005\u0002\u0002Ȩ7\u0003\u0002\u0002\u0002ȩȭ\u0007\u0004\u0002\u0002ȪȬ\u0007\u001c\u0002\u0002ȫȪ\u0003\u0002\u0002\u0002Ȭȯ\u0003\u0002\u0002\u0002ȭȫ\u0003\u0002\u0002\u0002ȭȮ\u0003\u0002\u0002\u0002ȮȰ\u0003\u0002\u0002\u0002ȯȭ\u0003\u0002\u0002\u0002Ȱȴ\u0005> \u0002ȱȳ\u0007\u001c\u0002\u0002Ȳȱ\u0003\u0002\u0002\u0002ȳȶ\u0003\u0002\u0002\u0002ȴȲ\u0003\u0002\u0002\u0002ȴȵ\u0003\u0002\u0002\u0002ȵȷ\u0003\u0002\u0002\u0002ȶȴ\u0003\u0002\u0002\u0002ȷȸ\u0007\u0005\u0002\u0002ȸ9\u0003\u0002\u0002\u0002ȹȻ\u0007\u001c\u0002\u0002Ⱥȹ\u0003\u0002\u0002\u0002ȻȾ\u0003\u0002\u0002\u0002ȼȺ\u0003\u0002\u0002\u0002ȼȽ\u0003\u0002\u0002\u0002Ƚȿ\u0003\u0002\u0002\u0002Ⱦȼ\u0003\u0002\u0002\u0002ȿɃ\u0007\u001b\u0002\u0002ɀɂ\u0007\u001c\u0002\u0002Ɂɀ\u0003\u0002\u0002\u0002ɂɅ\u0003\u0002\u0002\u0002ɃɁ\u0003\u0002\u0002\u0002ɃɄ\u0003\u0002\u0002\u0002ɄɆ\u0003\u0002\u0002\u0002ɅɃ\u0003\u0002\u0002\u0002ɆɊ\u0007\u001b\u0002\u0002ɇɉ\u0007\u001c\u0002\u0002Ɉɇ\u0003\u0002\u0002\u0002ɉɌ\u0003\u0002\u0002\u0002ɊɈ\u0003\u0002\u0002\u0002Ɋɋ\u0003\u0002\u0002\u0002ɋɔ\u0003\u0002\u0002\u0002ɌɊ\u0003\u0002\u0002\u0002ɍɑ\u0007\u001b\u0002\u0002Ɏɐ\u0007\u001c\u0002\u0002ɏɎ\u0003\u0002\u0002\u0002ɐɓ\u0003\u0002\u0002\u0002ɑɏ\u0003\u0002\u0002\u0002ɑɒ\u0003\u0002\u0002\u0002ɒɕ\u0003\u0002\u0002\u0002ɓɑ\u0003\u0002\u0002\u0002ɔɍ\u0003\u0002\u0002\u0002ɔɕ\u0003\u0002\u0002\u0002ɕɝ\u0003\u0002\u0002\u0002ɖɚ\u0007\u001b\u0002\u0002ɗə\u0007\u001c\u0002\u0002ɘɗ\u0003\u0002\u0002\u0002əɜ\u0003\u0002\u0002\u0002ɚɘ\u0003\u0002\u0002\u0002ɚɛ\u0003\u0002\u0002\u0002ɛɞ\u0003\u0002\u0002\u0002ɜɚ\u0003\u0002\u0002\u0002ɝɖ\u0003\u0002\u0002\u0002ɝɞ\u0003\u0002\u0002\u0002ɞ;\u0003\u0002\u0002\u0002ɟɰ\u0005:\u001e\u0002ɠɢ\u0007\u001c\u0002\u0002ɡɠ\u0003\u0002\u0002\u0002ɢɥ\u0003\u0002\u0002\u0002ɣɡ\u0003\u0002\u0002\u0002ɣɤ\u0003\u0002\u0002\u0002ɤɦ\u0003\u0002\u0002\u0002ɥɣ\u0003\u0002\u0002\u0002ɦɪ\u0007\u0011\u0002\u0002ɧɩ\u0007\u001c\u0002\u0002ɨɧ\u0003\u0002\u0002\u0002ɩɬ\u0003\u0002\u0002\u0002ɪɨ\u0003\u0002\u0002\u0002ɪɫ\u0003\u0002\u0002\u0002ɫɭ\u0003\u0002\u0002\u0002ɬɪ\u0003\u0002\u0002\u0002ɭɯ\u0005:\u001e\u0002ɮɣ\u0003\u0002\u0002\u0002ɯɲ\u0003\u0002\u0002\u0002ɰɮ\u0003\u0002\u0002\u0002ɰɱ\u0003\u0002\u0002\u0002ɱ=\u0003\u0002\u0002\u0002ɲɰ\u0003\u0002\u0002\u0002ɳɷ\u0007\u0004\u0002\u0002ɴɶ\u0007\u001c\u0002\u0002ɵɴ\u0003\u0002\u0002\u0002ɶɹ\u0003\u0002\u0002\u0002ɷɵ\u0003\u0002\u0002\u0002ɷɸ\u0003\u0002\u0002\u0002ɸɺ\u0003\u0002\u0002\u0002ɹɷ\u0003\u0002\u0002\u0002ɺɾ\u0005<\u001f\u0002ɻɽ\u0007\u001c\u0002\u0002ɼɻ\u0003\u0002\u0002\u0002ɽʀ\u0003\u0002\u0002\u0002ɾɼ\u0003\u0002\u0002\u0002ɾɿ\u0003\u0002\u0002\u0002ɿʁ\u0003\u0002\u0002\u0002ʀɾ\u0003\u0002\u0002\u0002ʁʡ\u0007\u0005\u0002\u0002ʂʄ\u0007\u001c\u0002\u0002ʃʂ\u0003\u0002\u0002\u0002ʄʇ\u0003\u0002\u0002\u0002ʅʃ\u0003\u0002\u0002\u0002ʅʆ\u0003\u0002\u0002\u0002ʆʈ\u0003\u0002\u0002\u0002ʇʅ\u0003\u0002\u0002\u0002ʈʌ\u0007\u0011\u0002\u0002ʉʋ\u0007\u001c\u0002\u0002ʊʉ\u0003\u0002\u0002\u0002ʋʎ\u0003\u0002\u0002\u0002ʌʊ\u0003\u0002\u0002\u0002ʌʍ\u0003\u0002\u0002\u0002ʍʏ\u0003\u0002\u0002\u0002ʎʌ\u0003\u0002\u0002\u0002ʏʓ\u0007\u0004\u0002\u0002ʐʒ\u0007\u001c\u0002\u0002ʑʐ\u0003\u0002\u0002\u0002ʒʕ\u0003\u0002\u0002\u0002ʓʑ\u0003\u0002\u0002\u0002ʓʔ\u0003\u0002\u0002\u0002ʔʖ\u0003\u0002\u0002\u0002ʕʓ\u0003\u0002\u0002\u0002ʖʚ\u0005<\u001f\u0002ʗʙ\u0007\u001c\u0002\u0002ʘʗ\u0003\u0002\u0002\u0002ʙʜ\u0003\u0002\u0002\u0002ʚʘ\u0003\u0002\u0002\u0002ʚʛ\u0003\u0002\u0002\u0002ʛʝ\u0003\u0002\u0002\u0002ʜʚ\u0003\u0002\u0002\u0002ʝʞ\u0007\u0005\u0002\u0002ʞʠ\u0003\u0002\u0002\u0002ʟʅ\u0003\u0002\u0002\u0002ʠʣ\u0003\u0002\u0002\u0002ʡʟ\u0003\u0002\u0002\u0002ʡʢ\u0003\u0002\u0002\u0002ʢ?\u0003\u0002\u0002\u0002ʣʡ\u0003\u0002\u0002\u0002ʤʨ\u0007\u0004\u0002\u0002ʥʧ\u0007\u001c\u0002\u0002ʦʥ\u0003\u0002\u0002\u0002ʧʪ\u0003\u0002\u0002\u0002ʨʦ\u0003\u0002\u0002\u0002ʨʩ\u0003\u0002\u0002\u0002ʩʫ\u0003\u0002\u0002\u0002ʪʨ\u0003\u0002\u0002\u0002ʫʯ\u0005> \u0002ʬʮ\u0007\u001c\u0002\u0002ʭʬ\u0003\u0002\u0002\u0002ʮʱ\u0003\u0002\u0002\u0002ʯʭ\u0003\u0002\u0002\u0002ʯʰ\u0003\u0002\u0002\u0002ʰʲ\u0003\u0002\u0002\u0002ʱʯ\u0003\u0002\u0002\u0002ʲ˒\u0007\u0005\u0002\u0002ʳʵ\u0007\u001c\u0002\u0002ʴʳ\u0003\u0002\u0002\u0002ʵʸ\u0003\u0002\u0002\u0002ʶʴ\u0003\u0002\u0002\u0002ʶʷ\u0003\u0002\u0002\u0002ʷʹ\u0003\u0002\u0002\u0002ʸʶ\u0003\u0002\u0002\u0002ʹʽ\u0007\u0011\u0002\u0002ʺʼ\u0007\u001c\u0002\u0002ʻʺ\u0003\u0002\u0002\u0002ʼʿ\u0003\u0002\u0002\u0002ʽʻ\u0003\u0002\u0002\u0002ʽʾ\u0003\u0002\u0002\u0002ʾˀ\u0003\u0002\u0002\u0002ʿʽ\u0003\u0002\u0002\u0002ˀ˄\u0007\u0004\u0002\u0002ˁ˃\u0007\u001c\u0002\u0002˂ˁ\u0003\u0002\u0002\u0002˃ˆ\u0003\u0002\u0002\u0002˄˂\u0003\u0002\u0002\u0002˄˅\u0003\u0002\u0002\u0002˅ˇ\u0003\u0002\u0002\u0002ˆ˄\u0003\u0002\u0002\u0002ˇˋ\u0005> \u0002ˈˊ\u0007\u001c\u0002\u0002ˉˈ\u0003\u0002\u0002\u0002ˊˍ\u0003\u0002\u0002\u0002ˋˉ\u0003\u0002\u0002\u0002ˋˌ\u0003\u0002\u0002\u0002ˌˎ\u0003\u0002\u0002\u0002ˍˋ\u0003\u0002\u0002\u0002ˎˏ\u0007\u0005\u0002\u0002ˏˑ\u0003\u0002\u0002\u0002ːʶ\u0003\u0002\u0002\u0002ˑ˔\u0003\u0002\u0002\u0002˒ː\u0003\u0002\u0002\u0002˒˓\u0003\u0002\u0002\u0002˓A\u0003\u0002\u0002\u0002˔˒\u0003\u0002\u0002\u0002˕˖\u0007\u0006\u0002\u0002˖C\u0003\u0002\u0002\u0002˗˘\u0007\u0016\u0002\u0002˘˙\u0007\u001b\u0002\u0002˙˚\u0007\u0017\u0002\u0002˚E\u0003\u0002\u0002\u0002˛˝\u0007\u001c\u0002\u0002˜˛\u0003\u0002\u0002\u0002˝ˠ\u0003\u0002\u0002\u0002˞˜\u0003\u0002\u0002\u0002˞˟\u0003\u0002\u0002\u0002˟ˡ\u0003\u0002\u0002\u0002ˠ˞\u0003\u0002\u0002\u0002ˡ˥\t\u0002\u0002\u0002ˢˤ\u0007\u001c\u0002\u0002ˣˢ\u0003\u0002\u0002\u0002ˤ˧\u0003\u0002\u0002\u0002˥ˣ\u0003\u0002\u0002\u0002˥˦\u0003\u0002\u0002\u0002˦G\u0003\u0002\u0002\u0002˧˥\u0003\u0002\u0002\u0002tWZ^cknrw\u007f\u0082\u0086\u008b\u0093\u0096\u009a\u009f§ª®³¿ÂÆËÓÖÚßçêîóûþĂćďĒĖěģĦĪįķĽńŊŐœŗŜŤŪűŸŽƂƅƉƎƖƜƣƪƯƵƸƼǁǉǏǖǝǢǧǪǮǳǻȁȈȏȔȝȤȭȴȼɃɊɑɔɚɝɣɪɰɷɾʅʌʓʚʡʨʯʶʽ˄ˋ˒˞˥";
    }

    public ATN getATN() {
        return _ATN;
    }

    public WKTParser(TokenStream input) {
        super(input);
        this._interp = new ParserATNSimulator(this, _ATN, _decisionToDFA, _sharedContextCache);
    }

    public final WktContext wkt() throws RecognitionException {
        WktContext _localctx = new WktContext(this._ctx, this.getState());
        this.enterRule(_localctx, 0, 0);

        try {
            this.setState(85);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 0, this._ctx)) {
                case 1:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(70);
                    this.point();
                    break;
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(71);
                    this.lineString();
                    break;
                case 3:
                    this.enterOuterAlt(_localctx, 3);
                    this.setState(72);
                    this.polygon();
                    break;
                case 4:
                    this.enterOuterAlt(_localctx, 4);
                    this.setState(73);
                    this.multiPoint();
                    break;
                case 5:
                    this.enterOuterAlt(_localctx, 5);
                    this.setState(74);
                    this.multiLineString();
                    break;
                case 6:
                    this.enterOuterAlt(_localctx, 6);
                    this.setState(75);
                    this.circularString();
                    break;
                case 7:
                    this.enterOuterAlt(_localctx, 7);
                    this.setState(76);
                    this.triangle();
                    break;
                case 8:
                    this.enterOuterAlt(_localctx, 8);
                    this.setState(77);
                    this.tin();
                    break;
                case 9:
                    this.enterOuterAlt(_localctx, 9);
                    this.setState(78);
                    this.polyHedralSurface();
                    break;
                case 10:
                    this.enterOuterAlt(_localctx, 10);
                    this.setState(79);
                    this.multiPolygon();
                    break;
                case 11:
                    this.enterOuterAlt(_localctx, 11);
                    this.setState(80);
                    this.compoundCurve();
                    break;
                case 12:
                    this.enterOuterAlt(_localctx, 12);
                    this.setState(81);
                    this.curvePolygon();
                    break;
                case 13:
                    this.enterOuterAlt(_localctx, 13);
                    this.setState(82);
                    this.multiCurve();
                    break;
                case 14:
                    this.enterOuterAlt(_localctx, 14);
                    this.setState(83);
                    this.multiSurface();
                    break;
                case 15:
                    this.enterOuterAlt(_localctx, 15);
                    this.setState(84);
                    this.geometryCollection();
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final PointContext point() throws RecognitionException {
        PointContext _localctx = new PointContext(this._ctx, this.getState());
        this.enterRule(_localctx, 2, 1);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(88);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(87);
                this.srid();
            }

            this.setState(90);
            this.match(1);
            this.setState(92);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 2, this._ctx)) {
                case 1:
                    this.setState(91);
                    this.dimension();
                default:
                    this.setState(97);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(94);
                this.match(26);
                this.setState(99);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(105);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(100);
                    this.match(2);
                    this.setState(101);
                    this.coordinate();
                    this.setState(102);
                    this.match(3);
                    break;
                case 4:
                    this.setState(104);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final LineStringContext lineString() throws RecognitionException {
        LineStringContext _localctx = new LineStringContext(this._ctx, this.getState());
        this.enterRule(_localctx, 4, 2);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(108);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(107);
                this.srid();
            }

            this.setState(110);
            this.match(5);
            this.setState(112);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 6, this._ctx)) {
                case 1:
                    this.setState(111);
                    this.dimension();
                default:
                    this.setState(117);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(114);
                this.match(26);
                this.setState(119);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(125);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(120);
                    this.match(2);
                    this.setState(121);
                    this.coordinates();
                    this.setState(122);
                    this.match(3);
                    break;
                case 4:
                    this.setState(124);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final PolygonContext polygon() throws RecognitionException {
        PolygonContext _localctx = new PolygonContext(this._ctx, this.getState());
        this.enterRule(_localctx, 6, 3);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(128);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(127);
                this.srid();
            }

            this.setState(130);
            this.match(6);
            this.setState(132);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 10, this._ctx)) {
                case 1:
                    this.setState(131);
                    this.dimension();
                default:
                    this.setState(137);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(134);
                this.match(26);
                this.setState(139);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(145);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(140);
                    this.match(2);
                    this.setState(141);
                    this.coordinatesets();
                    this.setState(142);
                    this.match(3);
                    break;
                case 4:
                    this.setState(144);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final TriangleContext triangle() throws RecognitionException {
        TriangleContext _localctx = new TriangleContext(this._ctx, this.getState());
        this.enterRule(_localctx, 8, 4);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(148);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(147);
                this.srid();
            }

            this.setState(150);
            this.match(7);
            this.setState(152);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 14, this._ctx)) {
                case 1:
                    this.setState(151);
                    this.dimension();
                default:
                    this.setState(157);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(154);
                this.match(26);
                this.setState(159);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(165);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(160);
                    this.match(2);
                    this.setState(161);
                    this.coordinatesets();
                    this.setState(162);
                    this.match(3);
                    break;
                case 4:
                    this.setState(164);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiPointContext multiPoint() throws RecognitionException {
        MultiPointContext _localctx = new MultiPointContext(this._ctx, this.getState());
        this.enterRule(_localctx, 10, 5);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(168);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(167);
                this.srid();
            }

            this.setState(170);
            this.match(8);
            this.setState(172);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 18, this._ctx)) {
                case 1:
                    this.setState(171);
                    this.dimension();
                default:
                    this.setState(177);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(174);
                this.match(26);
                this.setState(179);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(189);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 20, this._ctx)) {
                case 1:
                    this.setState(180);
                    this.match(2);
                    this.setState(181);
                    this.coordinates();
                    this.setState(182);
                    this.match(3);
                    break;
                case 2:
                    this.setState(184);
                    this.match(2);
                    this.setState(185);
                    this.coordinatesets();
                    this.setState(186);
                    this.match(3);
                    break;
                case 3:
                    this.setState(188);
                    this.match(4);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiLineStringContext multiLineString() throws RecognitionException {
        MultiLineStringContext _localctx = new MultiLineStringContext(this._ctx, this.getState());
        this.enterRule(_localctx, 12, 6);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(192);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(191);
                this.srid();
            }

            this.setState(194);
            this.match(9);
            this.setState(196);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 22, this._ctx)) {
                case 1:
                    this.setState(195);
                    this.dimension();
                default:
                    this.setState(201);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(198);
                this.match(26);
                this.setState(203);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(209);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(204);
                    this.match(2);
                    this.setState(205);
                    this.coordinatesets();
                    this.setState(206);
                    this.match(3);
                    break;
                case 4:
                    this.setState(208);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CircularStringContext circularString() throws RecognitionException {
        CircularStringContext _localctx = new CircularStringContext(this._ctx, this.getState());
        this.enterRule(_localctx, 14, 7);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(212);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(211);
                this.srid();
            }

            this.setState(214);
            this.match(10);
            this.setState(216);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 26, this._ctx)) {
                case 1:
                    this.setState(215);
                    this.dimension();
                default:
                    this.setState(221);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(218);
                this.match(26);
                this.setState(223);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(229);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(224);
                    this.match(2);
                    this.setState(225);
                    this.coordinates();
                    this.setState(226);
                    this.match(3);
                    break;
                case 4:
                    this.setState(228);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final TinContext tin() throws RecognitionException {
        TinContext _localctx = new TinContext(this._ctx, this.getState());
        this.enterRule(_localctx, 16, 8);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(232);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(231);
                this.srid();
            }

            this.setState(234);
            this.match(11);
            this.setState(236);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 30, this._ctx)) {
                case 1:
                    this.setState(235);
                    this.dimension();
                default:
                    this.setState(241);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(238);
                this.match(26);
                this.setState(243);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(249);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(244);
                    this.match(2);
                    this.setState(245);
                    this.coordinatesetsset();
                    this.setState(246);
                    this.match(3);
                    break;
                case 4:
                    this.setState(248);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final PolyHedralSurfaceContext polyHedralSurface() throws RecognitionException {
        PolyHedralSurfaceContext _localctx = new PolyHedralSurfaceContext(this._ctx, this.getState());
        this.enterRule(_localctx, 18, 9);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(252);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(251);
                this.srid();
            }

            this.setState(254);
            this.match(12);
            this.setState(256);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 34, this._ctx)) {
                case 1:
                    this.setState(255);
                    this.dimension();
                default:
                    this.setState(261);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(258);
                this.match(26);
                this.setState(263);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(269);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(264);
                    this.match(2);
                    this.setState(265);
                    this.coordinatesetsset();
                    this.setState(266);
                    this.match(3);
                    break;
                case 4:
                    this.setState(268);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiPolygonContext multiPolygon() throws RecognitionException {
        MultiPolygonContext _localctx = new MultiPolygonContext(this._ctx, this.getState());
        this.enterRule(_localctx, 20, 10);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(272);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(271);
                this.srid();
            }

            this.setState(274);
            this.match(13);
            this.setState(276);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 38, this._ctx)) {
                case 1:
                    this.setState(275);
                    this.dimension();
                default:
                    this.setState(281);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(278);
                this.match(26);
                this.setState(283);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(289);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(284);
                    this.match(2);
                    this.setState(285);
                    this.coordinatesetsset();
                    this.setState(286);
                    this.match(3);
                    break;
                case 4:
                    this.setState(288);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CurvePolygonContext curvePolygon() throws RecognitionException {
        CurvePolygonContext _localctx = new CurvePolygonContext(this._ctx, this.getState());
        this.enterRule(_localctx, 22, 11);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(292);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(291);
                this.srid();
            }

            this.setState(294);
            this.match(14);
            this.setState(296);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 42, this._ctx)) {
                case 1:
                    this.setState(295);
                    this.dimension();
                default:
                    this.setState(301);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(298);
                this.match(26);
                this.setState(303);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(309);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(304);
                    this.match(2);
                    this.setState(305);
                    this.curvePolygonItems();
                    this.setState(306);
                    this.match(3);
                    break;
                case 4:
                    this.setState(308);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CurvePolygonItemsContext curvePolygonItems() throws RecognitionException {
        CurvePolygonItemsContext _localctx = new CurvePolygonItemsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 24, 12);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(311);
            this.curvePolygonElements();
            this.setState(328);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(315);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(312);
                    this.match(26);
                    this.setState(317);
                    this._errHandler.sync(this);
                }

                this.setState(318);
                this.match(15);
                this.setState(322);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(319);
                    this.match(26);
                    this.setState(324);
                    this._errHandler.sync(this);
                }

                this.setState(325);
                this.curvePolygonElements();
                this.setState(330);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CurvePolygonElementsContext curvePolygonElements() throws RecognitionException {
        CurvePolygonElementsContext _localctx = new CurvePolygonElementsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 26, 13);

        try {
            this.setState(334);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 48, this._ctx)) {
                case 1:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(331);
                    this.compoundCurve();
                    break;
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(332);
                    this.circularString();
                    break;
                case 3:
                    this.enterOuterAlt(_localctx, 3);
                    this.setState(333);
                    this.lineStringCoordinates();
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CompoundCurveContext compoundCurve() throws RecognitionException {
        CompoundCurveContext _localctx = new CompoundCurveContext(this._ctx, this.getState());
        this.enterRule(_localctx, 28, 14);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(337);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(336);
                this.srid();
            }

            this.setState(339);
            this.match(16);
            this.setState(341);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 50, this._ctx)) {
                case 1:
                    this.setState(340);
                    this.dimension();
                default:
                    this.setState(346);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(343);
                this.match(26);
                this.setState(348);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(354);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(349);
                    this.match(2);
                    this.setState(350);
                    this.compoundCurveItems();
                    this.setState(351);
                    this.match(3);
                    break;
                case 4:
                    this.setState(353);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CompoundCurveItemsContext compoundCurveItems() throws RecognitionException {
        CompoundCurveItemsContext _localctx = new CompoundCurveItemsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 30, 15);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(356);
            this.compoundCurveElements();
            this.setState(379);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(360);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(357);
                    this.match(26);
                    this.setState(362);
                    this._errHandler.sync(this);
                }

                this.setState(363);
                this.match(15);
                this.setState(367);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(364);
                    this.match(26);
                    this.setState(369);
                    this._errHandler.sync(this);
                }

                this.setState(370);
                this.compoundCurveElements();
                this.setState(374);
                this._errHandler.sync(this);

                for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 55, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 55, this._ctx)) {
                    if (_alt == 1) {
                        this.setState(371);
                        this.match(26);
                    }

                    this.setState(376);
                    this._errHandler.sync(this);
                }

                this.setState(381);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CompoundCurveElementsContext compoundCurveElements() throws RecognitionException {
        CompoundCurveElementsContext _localctx = new CompoundCurveElementsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 32, 16);

        try {
            this.setState(384);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(383);
                    this.lineStringCoordinates();
                    break;
                case 10:
                case 20:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(382);
                    this.circularString();
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiCurveContext multiCurve() throws RecognitionException {
        MultiCurveContext _localctx = new MultiCurveContext(this._ctx, this.getState());
        this.enterRule(_localctx, 34, 17);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(387);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(386);
                this.srid();
            }

            this.setState(389);
            this.match(17);
            this.setState(391);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 59, this._ctx)) {
                case 1:
                    this.setState(390);
                    this.dimension();
                default:
                    this.setState(396);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(393);
                this.match(26);
                this.setState(398);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(404);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(399);
                    this.match(2);
                    this.setState(400);
                    this.multiCurveItems();
                    this.setState(401);
                    this.match(3);
                    break;
                case 4:
                    this.setState(403);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiCurveItemsContext multiCurveItems() throws RecognitionException {
        MultiCurveItemsContext _localctx = new MultiCurveItemsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 36, 18);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(406);
            this.multiCurveElements();
            this.setState(429);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(410);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(407);
                    this.match(26);
                    this.setState(412);
                    this._errHandler.sync(this);
                }

                this.setState(413);
                this.match(15);
                this.setState(417);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(414);
                    this.match(26);
                    this.setState(419);
                    this._errHandler.sync(this);
                }

                this.setState(420);
                this.multiCurveElements();
                this.setState(424);
                this._errHandler.sync(this);

                for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 64, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 64, this._ctx)) {
                    if (_alt == 1) {
                        this.setState(421);
                        this.match(26);
                    }

                    this.setState(426);
                    this._errHandler.sync(this);
                }

                this.setState(431);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiCurveElementsContext multiCurveElements() throws RecognitionException {
        MultiCurveElementsContext _localctx = new MultiCurveElementsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 38, 19);

        try {
            this.setState(435);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 66, this._ctx)) {
                case 1:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(432);
                    this.compoundCurve();
                    break;
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(433);
                    this.circularString();
                    break;
                case 3:
                    this.enterOuterAlt(_localctx, 3);
                    this.setState(434);
                    this.lineStringCoordinates();
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiSurfaceContext multiSurface() throws RecognitionException {
        MultiSurfaceContext _localctx = new MultiSurfaceContext(this._ctx, this.getState());
        this.enterRule(_localctx, 40, 20);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(438);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(437);
                this.srid();
            }

            this.setState(440);
            this.match(18);
            this.setState(442);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 68, this._ctx)) {
                case 1:
                    this.setState(441);
                    this.dimension();
                default:
                    this.setState(447);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(444);
                this.match(26);
                this.setState(449);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(455);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(450);
                    this.match(2);
                    this.setState(451);
                    this.multiSurfaceItems();
                    this.setState(452);
                    this.match(3);
                    break;
                case 4:
                    this.setState(454);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiSurfaceItemsContext multiSurfaceItems() throws RecognitionException {
        MultiSurfaceItemsContext _localctx = new MultiSurfaceItemsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 42, 21);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(457);
            this.multiSurfaceElements();
            this.setState(480);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(461);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(458);
                    this.match(26);
                    this.setState(463);
                    this._errHandler.sync(this);
                }

                this.setState(464);
                this.match(15);
                this.setState(468);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(465);
                    this.match(26);
                    this.setState(470);
                    this._errHandler.sync(this);
                }

                this.setState(471);
                this.multiSurfaceElements();
                this.setState(475);
                this._errHandler.sync(this);

                for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 73, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 73, this._ctx)) {
                    if (_alt == 1) {
                        this.setState(472);
                        this.match(26);
                    }

                    this.setState(477);
                    this._errHandler.sync(this);
                }

                this.setState(482);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final MultiSurfaceElementsContext multiSurfaceElements() throws RecognitionException {
        MultiSurfaceElementsContext _localctx = new MultiSurfaceElementsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 44, 22);

        try {
            this.setState(485);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.enterOuterAlt(_localctx, 2);
                    this.setState(484);
                    this.polygonCoordinates();
                    break;
                case 14:
                case 20:
                    this.enterOuterAlt(_localctx, 1);
                    this.setState(483);
                    this.curvePolygon();
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final GeometryCollectionContext geometryCollection() throws RecognitionException {
        GeometryCollectionContext _localctx = new GeometryCollectionContext(this._ctx, this.getState());
        this.enterRule(_localctx, 46, 23);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(488);
            this._errHandler.sync(this);
            int _la = this._input.LA(1);
            if (_la == 20) {
                this.setState(487);
                this.srid();
            }

            this.setState(490);
            this.match(19);
            this.setState(492);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 77, this._ctx)) {
                case 1:
                    this.setState(491);
                    this.dimension();
                default:
                    this.setState(497);
                    this._errHandler.sync(this);
                    _la = this._input.LA(1);
            }

            while(_la == 26) {
                this.setState(494);
                this.match(26);
                this.setState(499);
                this._errHandler.sync(this);
                _la = this._input.LA(1);
            }

            this.setState(505);
            this._errHandler.sync(this);
            switch (this._input.LA(1)) {
                case 2:
                    this.setState(500);
                    this.match(2);
                    this.setState(501);
                    this.geometryCollectionItems();
                    this.setState(502);
                    this.match(3);
                    break;
                case 4:
                    this.setState(504);
                    this.match(4);
                    break;
                default:
                    throw new NoViableAltException(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final GeometryCollectionItemsContext geometryCollectionItems() throws RecognitionException {
        GeometryCollectionItemsContext _localctx = new GeometryCollectionItemsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 48, 24);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(507);
            this.geometryCollectionElements();
            this.setState(530);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(511);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(508);
                    this.match(26);
                    this.setState(513);
                    this._errHandler.sync(this);
                }

                this.setState(514);
                this.match(15);
                this.setState(518);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(515);
                    this.match(26);
                    this.setState(520);
                    this._errHandler.sync(this);
                }

                this.setState(521);
                this.geometryCollectionElements();
                this.setState(525);
                this._errHandler.sync(this);

                for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 82, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 82, this._ctx)) {
                    if (_alt == 1) {
                        this.setState(522);
                        this.match(26);
                    }

                    this.setState(527);
                    this._errHandler.sync(this);
                }

                this.setState(532);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final GeometryCollectionElementsContext geometryCollectionElements() throws RecognitionException {
        GeometryCollectionElementsContext _localctx = new GeometryCollectionElementsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 50, 25);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(533);
            this.wkt();
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final LineStringCoordinatesContext lineStringCoordinates() throws RecognitionException {
        LineStringCoordinatesContext _localctx = new LineStringCoordinatesContext(this._ctx, this.getState());
        this.enterRule(_localctx, 52, 26);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(535);
            this.match(2);
            this.setState(539);
            this._errHandler.sync(this);

            for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 84, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 84, this._ctx)) {
                if (_alt == 1) {
                    this.setState(536);
                    this.match(26);
                }

                this.setState(541);
                this._errHandler.sync(this);
            }

            this.setState(542);
            this.coordinates();
            this.setState(546);
            this._errHandler.sync(this);

            for(int _la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(543);
                this.match(26);
                this.setState(548);
                this._errHandler.sync(this);
            }

            this.setState(549);
            this.match(3);
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final PolygonCoordinatesContext polygonCoordinates() throws RecognitionException {
        PolygonCoordinatesContext _localctx = new PolygonCoordinatesContext(this._ctx, this.getState());
        this.enterRule(_localctx, 54, 27);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(551);
            this.match(2);
            this.setState(555);
            this._errHandler.sync(this);

            int _la;
            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(552);
                this.match(26);
                this.setState(557);
                this._errHandler.sync(this);
            }

            this.setState(558);
            this.coordinatesets();
            this.setState(562);
            this._errHandler.sync(this);

            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(559);
                this.match(26);
                this.setState(564);
                this._errHandler.sync(this);
            }

            this.setState(565);
            this.match(3);
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CoordinateContext coordinate() throws RecognitionException {
        CoordinateContext _localctx = new CoordinateContext(this._ctx, this.getState());
        this.enterRule(_localctx, 56, 28);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(570);
            this._errHandler.sync(this);

            int _la;
            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(567);
                this.match(26);
                this.setState(572);
                this._errHandler.sync(this);
            }

            this.setState(573);
            this.match(25);
            this.setState(577);
            this._errHandler.sync(this);

            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(574);
                this.match(26);
                this.setState(579);
                this._errHandler.sync(this);
            }

            this.setState(580);
            this.match(25);
            this.setState(584);
            this._errHandler.sync(this);

            int _alt;
            for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 90, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 90, this._ctx)) {
                if (_alt == 1) {
                    this.setState(581);
                    this.match(26);
                }

                this.setState(586);
                this._errHandler.sync(this);
            }

            this.setState(594);
            this._errHandler.sync(this);
            switch (((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 92, this._ctx)) {
                case 1:
                    this.setState(587);
                    this.match(25);
                    this.setState(591);
                    this._errHandler.sync(this);

                    for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 91, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 91, this._ctx)) {
                        if (_alt == 1) {
                            this.setState(588);
                            this.match(26);
                        }

                        this.setState(593);
                        this._errHandler.sync(this);
                    }
            }

            this.setState(603);
            this._errHandler.sync(this);
            _la = this._input.LA(1);
            if (_la == 25) {
                this.setState(596);
                this.match(25);
                this.setState(600);
                this._errHandler.sync(this);

                for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 93, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 93, this._ctx)) {
                    if (_alt == 1) {
                        this.setState(597);
                        this.match(26);
                    }

                    this.setState(602);
                    this._errHandler.sync(this);
                }
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CoordinatesContext coordinates() throws RecognitionException {
        CoordinatesContext _localctx = new CoordinatesContext(this._ctx, this.getState());
        this.enterRule(_localctx, 58, 29);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(605);
            this.coordinate();
            this.setState(622);
            this._errHandler.sync(this);

            for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 97, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 97, this._ctx)) {
                if (_alt == 1) {
                    this.setState(609);
                    this._errHandler.sync(this);

                    for(int _la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                        this.setState(606);
                        this.match(26);
                        this.setState(611);
                        this._errHandler.sync(this);
                    }

                    this.setState(612);
                    this.match(15);
                    this.setState(616);
                    this._errHandler.sync(this);

                    for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 96, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 96, this._ctx)) {
                        if (_alt == 1) {
                            this.setState(613);
                            this.match(26);
                        }

                        this.setState(618);
                        this._errHandler.sync(this);
                    }

                    this.setState(619);
                    this.coordinate();
                }

                this.setState(624);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CoordinatesetsContext coordinatesets() throws RecognitionException {
        CoordinatesetsContext _localctx = new CoordinatesetsContext(this._ctx, this.getState());
        this.enterRule(_localctx, 60, 30);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(625);
            this.match(2);
            this.setState(629);
            this._errHandler.sync(this);

            int _alt;
            for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 98, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 98, this._ctx)) {
                if (_alt == 1) {
                    this.setState(626);
                    this.match(26);
                }

                this.setState(631);
                this._errHandler.sync(this);
            }

            this.setState(632);
            this.coordinates();
            this.setState(636);
            this._errHandler.sync(this);

            int _la;
            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(633);
                this.match(26);
                this.setState(638);
                this._errHandler.sync(this);
            }

            this.setState(639);
            this.match(3);
            this.setState(671);
            this._errHandler.sync(this);

            for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 104, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 104, this._ctx)) {
                if (_alt == 1) {
                    this.setState(643);
                    this._errHandler.sync(this);

                    for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                        this.setState(640);
                        this.match(26);
                        this.setState(645);
                        this._errHandler.sync(this);
                    }

                    this.setState(646);
                    this.match(15);
                    this.setState(650);
                    this._errHandler.sync(this);

                    for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                        this.setState(647);
                        this.match(26);
                        this.setState(652);
                        this._errHandler.sync(this);
                    }

                    this.setState(653);
                    this.match(2);
                    this.setState(657);
                    this._errHandler.sync(this);

                    for(_alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 102, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 102, this._ctx)) {
                        if (_alt == 1) {
                            this.setState(654);
                            this.match(26);
                        }

                        this.setState(659);
                        this._errHandler.sync(this);
                    }

                    this.setState(660);
                    this.coordinates();
                    this.setState(664);
                    this._errHandler.sync(this);

                    for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                        this.setState(661);
                        this.match(26);
                        this.setState(666);
                        this._errHandler.sync(this);
                    }

                    this.setState(667);
                    this.match(3);
                }

                this.setState(673);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final CoordinatesetssetContext coordinatesetsset() throws RecognitionException {
        CoordinatesetssetContext _localctx = new CoordinatesetssetContext(this._ctx, this.getState());
        this.enterRule(_localctx, 62, 31);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(674);
            this.match(2);
            this.setState(678);
            this._errHandler.sync(this);

            int _la;
            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(675);
                this.match(26);
                this.setState(680);
                this._errHandler.sync(this);
            }

            this.setState(681);
            this.coordinatesets();
            this.setState(685);
            this._errHandler.sync(this);

            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(682);
                this.match(26);
                this.setState(687);
                this._errHandler.sync(this);
            }

            this.setState(688);
            this.match(3);
            this.setState(720);
            this._errHandler.sync(this);

            for(_la = this._input.LA(1); _la == 15 || _la == 26; _la = this._input.LA(1)) {
                this.setState(692);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(689);
                    this.match(26);
                    this.setState(694);
                    this._errHandler.sync(this);
                }

                this.setState(695);
                this.match(15);
                this.setState(699);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(696);
                    this.match(26);
                    this.setState(701);
                    this._errHandler.sync(this);
                }

                this.setState(702);
                this.match(2);
                this.setState(706);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(703);
                    this.match(26);
                    this.setState(708);
                    this._errHandler.sync(this);
                }

                this.setState(709);
                this.coordinatesets();
                this.setState(713);
                this._errHandler.sync(this);

                for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                    this.setState(710);
                    this.match(26);
                    this.setState(715);
                    this._errHandler.sync(this);
                }

                this.setState(716);
                this.match(3);
                this.setState(722);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final EmptyContext empty() throws RecognitionException {
        EmptyContext _localctx = new EmptyContext(this._ctx, this.getState());
        this.enterRule(_localctx, 64, 32);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(723);
            this.match(4);
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final SridContext srid() throws RecognitionException {
        SridContext _localctx = new SridContext(this._ctx, this.getState());
        this.enterRule(_localctx, 66, 33);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(725);
            this.match(20);
            this.setState(726);
            this.match(25);
            this.setState(727);
            this.match(21);
        } catch (RecognitionException var6) {
            _localctx.exception = var6;
            this._errHandler.reportError(this, var6);
            this._errHandler.recover(this, var6);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    public final DimensionContext dimension() throws RecognitionException {
        DimensionContext _localctx = new DimensionContext(this._ctx, this.getState());
        this.enterRule(_localctx, 68, 34);

        try {
            this.enterOuterAlt(_localctx, 1);
            this.setState(732);
            this._errHandler.sync(this);

            int _la;
            for(_la = this._input.LA(1); _la == 26; _la = this._input.LA(1)) {
                this.setState(729);
                this.match(26);
                this.setState(734);
                this._errHandler.sync(this);
            }

            this.setState(735);
            _la = this._input.LA(1);
            if ((_la & -64) == 0 && (1L << _la & 29360128L) != 0L) {
                if (this._input.LA(1) == -1) {
                    this.matchedEOF = true;
                }

                this._errHandler.reportMatch(this);
                this.consume();
            } else {
                this._errHandler.recoverInline(this);
            }

            this.setState(739);
            this._errHandler.sync(this);

            for(int _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 113, this._ctx); _alt != 2 && _alt != 0; _alt = ((ParserATNSimulator)this.getInterpreter()).adaptivePredict(this._input, 113, this._ctx)) {
                if (_alt == 1) {
                    this.setState(736);
                    this.match(26);
                }

                this.setState(741);
                this._errHandler.sync(this);
            }
        } catch (RecognitionException var7) {
            _localctx.exception = var7;
            this._errHandler.reportError(this, var7);
            this._errHandler.recover(this, var7);
        } finally {
            this.exitRule();
        }

        return _localctx;
    }

    static {
        RuntimeMetaData.checkVersion("4.7.1", "4.7.1");
        _sharedContextCache = new PredictionContextCache();
        ruleNames = new String[]{"wkt", "point", "lineString", "polygon", "triangle", "multiPoint", "multiLineString", "circularString", "tin", "polyHedralSurface", "multiPolygon", "curvePolygon", "curvePolygonItems", "curvePolygonElements", "compoundCurve", "compoundCurveItems", "compoundCurveElements", "multiCurve", "multiCurveItems", "multiCurveElements", "multiSurface", "multiSurfaceItems", "multiSurfaceElements", "geometryCollection", "geometryCollectionItems", "geometryCollectionElements", "lineStringCoordinates", "polygonCoordinates", "coordinate", "coordinates", "coordinatesets", "coordinatesetsset", "empty", "srid", "dimension"};
        _LITERAL_NAMES = new String[]{null, "'POINT'", "'('", "')'", "'EMPTY'", "'LINESTRING'", "'POLYGON'", "'TRIANGLE'", "'MULTIPOINT'", "'MULTILINESTRING'", "'CIRCULARSTRING'", "'TIN'", "'POLYHEDRALSURFACE'", "'MULTIPOLYGON'", "'CURVEPOLYGON'", "','", "'COMPOUNDCURVE'", "'MULTICURVE'", "'MULTISURFACE'", "'GEOMETRYCOLLECTION'", "'SRID='", "';'", "'M'", "'Z'", "'ZM'"};
        _SYMBOLIC_NAMES = new String[]{null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "M", "Z", "ZM", "Number", "WhiteSpace", "NewLine"};
        VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);
        tokenNames = new String[_SYMBOLIC_NAMES.length];

        int i;
        for(i = 0; i < tokenNames.length; ++i) {
            tokenNames[i] = VOCABULARY.getLiteralName(i);
            if (tokenNames[i] == null) {
                tokenNames[i] = VOCABULARY.getSymbolicName(i);
            }

            if (tokenNames[i] == null) {
                tokenNames[i] = "<INVALID>";
            }
        }

        _ATN = (new ATNDeserializer()).deserialize("\u0003悋Ꜫ脳맭䅼㯧瞆奤\u0003\u001d˩\u0004\u0002\t\u0002\u0004\u0003\t\u0003\u0004\u0004\t\u0004\u0004\u0005\t\u0005\u0004\u0006\t\u0006\u0004\u0007\t\u0007\u0004\b\t\b\u0004\t\t\t\u0004\n\t\n\u0004\u000b\t\u000b\u0004\f\t\f\u0004\r\t\r\u0004\u000e\t\u000e\u0004\u000f\t\u000f\u0004\u0010\t\u0010\u0004\u0011\t\u0011\u0004\u0012\t\u0012\u0004\u0013\t\u0013\u0004\u0014\t\u0014\u0004\u0015\t\u0015\u0004\u0016\t\u0016\u0004\u0017\t\u0017\u0004\u0018\t\u0018\u0004\u0019\t\u0019\u0004\u001a\t\u001a\u0004\u001b\t\u001b\u0004\u001c\t\u001c\u0004\u001d\t\u001d\u0004\u001e\t\u001e\u0004\u001f\t\u001f\u0004 \t \u0004!\t!\u0004\"\t\"\u0004#\t#\u0004$\t$\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0003\u0002\u0005\u0002X\n\u0002\u0003\u0003\u0005\u0003[\n\u0003\u0003\u0003\u0003\u0003\u0005\u0003_\n\u0003\u0003\u0003\u0007\u0003b\n\u0003\f\u0003\u000e\u0003e\u000b\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0003\u0005\u0003l\n\u0003\u0003\u0004\u0005\u0004o\n\u0004\u0003\u0004\u0003\u0004\u0005\u0004s\n\u0004\u0003\u0004\u0007\u0004v\n\u0004\f\u0004\u000e\u0004y\u000b\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0003\u0004\u0005\u0004\u0080\n\u0004\u0003\u0005\u0005\u0005\u0083\n\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0087\n\u0005\u0003\u0005\u0007\u0005\u008a\n\u0005\f\u0005\u000e\u0005\u008d\u000b\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0003\u0005\u0005\u0005\u0094\n\u0005\u0003\u0006\u0005\u0006\u0097\n\u0006\u0003\u0006\u0003\u0006\u0005\u0006\u009b\n\u0006\u0003\u0006\u0007\u0006\u009e\n\u0006\f\u0006\u000e\u0006¡\u000b\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0003\u0006\u0005\u0006¨\n\u0006\u0003\u0007\u0005\u0007«\n\u0007\u0003\u0007\u0003\u0007\u0005\u0007¯\n\u0007\u0003\u0007\u0007\u0007²\n\u0007\f\u0007\u000e\u0007µ\u000b\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0003\u0007\u0005\u0007À\n\u0007\u0003\b\u0005\bÃ\n\b\u0003\b\u0003\b\u0005\bÇ\n\b\u0003\b\u0007\bÊ\n\b\f\b\u000e\bÍ\u000b\b\u0003\b\u0003\b\u0003\b\u0003\b\u0003\b\u0005\bÔ\n\b\u0003\t\u0005\t×\n\t\u0003\t\u0003\t\u0005\tÛ\n\t\u0003\t\u0007\tÞ\n\t\f\t\u000e\tá\u000b\t\u0003\t\u0003\t\u0003\t\u0003\t\u0003\t\u0005\tè\n\t\u0003\n\u0005\në\n\n\u0003\n\u0003\n\u0005\nï\n\n\u0003\n\u0007\nò\n\n\f\n\u000e\nõ\u000b\n\u0003\n\u0003\n\u0003\n\u0003\n\u0003\n\u0005\nü\n\n\u0003\u000b\u0005\u000bÿ\n\u000b\u0003\u000b\u0003\u000b\u0005\u000bă\n\u000b\u0003\u000b\u0007\u000bĆ\n\u000b\f\u000b\u000e\u000bĉ\u000b\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0003\u000b\u0005\u000bĐ\n\u000b\u0003\f\u0005\fē\n\f\u0003\f\u0003\f\u0005\fė\n\f\u0003\f\u0007\fĚ\n\f\f\f\u000e\fĝ\u000b\f\u0003\f\u0003\f\u0003\f\u0003\f\u0003\f\u0005\fĤ\n\f\u0003\r\u0005\rħ\n\r\u0003\r\u0003\r\u0005\rī\n\r\u0003\r\u0007\rĮ\n\r\f\r\u000e\rı\u000b\r\u0003\r\u0003\r\u0003\r\u0003\r\u0003\r\u0005\rĸ\n\r\u0003\u000e\u0003\u000e\u0007\u000eļ\n\u000e\f\u000e\u000e\u000eĿ\u000b\u000e\u0003\u000e\u0003\u000e\u0007\u000eŃ\n\u000e\f\u000e\u000e\u000eņ\u000b\u000e\u0003\u000e\u0007\u000eŉ\n\u000e\f\u000e\u000e\u000eŌ\u000b\u000e\u0003\u000f\u0003\u000f\u0003\u000f\u0005\u000fő\n\u000f\u0003\u0010\u0005\u0010Ŕ\n\u0010\u0003\u0010\u0003\u0010\u0005\u0010Ř\n\u0010\u0003\u0010\u0007\u0010ś\n\u0010\f\u0010\u000e\u0010Ş\u000b\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0003\u0010\u0005\u0010ť\n\u0010\u0003\u0011\u0003\u0011\u0007\u0011ũ\n\u0011\f\u0011\u000e\u0011Ŭ\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011Ű\n\u0011\f\u0011\u000e\u0011ų\u000b\u0011\u0003\u0011\u0003\u0011\u0007\u0011ŷ\n\u0011\f\u0011\u000e\u0011ź\u000b\u0011\u0007\u0011ż\n\u0011\f\u0011\u000e\u0011ſ\u000b\u0011\u0003\u0012\u0003\u0012\u0005\u0012ƃ\n\u0012\u0003\u0013\u0005\u0013Ɔ\n\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɗ\n\u0013\u0003\u0013\u0007\u0013ƍ\n\u0013\f\u0013\u000e\u0013Ɛ\u000b\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0003\u0013\u0005\u0013Ɨ\n\u0013\u0003\u0014\u0003\u0014\u0007\u0014ƛ\n\u0014\f\u0014\u000e\u0014ƞ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ƣ\n\u0014\f\u0014\u000e\u0014ƥ\u000b\u0014\u0003\u0014\u0003\u0014\u0007\u0014Ʃ\n\u0014\f\u0014\u000e\u0014Ƭ\u000b\u0014\u0007\u0014Ʈ\n\u0014\f\u0014\u000e\u0014Ʊ\u000b\u0014\u0003\u0015\u0003\u0015\u0003\u0015\u0005\u0015ƶ\n\u0015\u0003\u0016\u0005\u0016ƹ\n\u0016\u0003\u0016\u0003\u0016\u0005\u0016ƽ\n\u0016\u0003\u0016\u0007\u0016ǀ\n\u0016\f\u0016\u000e\u0016ǃ\u000b\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0003\u0016\u0005\u0016Ǌ\n\u0016\u0003\u0017\u0003\u0017\u0007\u0017ǎ\n\u0017\f\u0017\u000e\u0017Ǒ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017Ǖ\n\u0017\f\u0017\u000e\u0017ǘ\u000b\u0017\u0003\u0017\u0003\u0017\u0007\u0017ǜ\n\u0017\f\u0017\u000e\u0017ǟ\u000b\u0017\u0007\u0017ǡ\n\u0017\f\u0017\u000e\u0017Ǥ\u000b\u0017\u0003\u0018\u0003\u0018\u0005\u0018Ǩ\n\u0018\u0003\u0019\u0005\u0019ǫ\n\u0019\u0003\u0019\u0003\u0019\u0005\u0019ǯ\n\u0019\u0003\u0019\u0007\u0019ǲ\n\u0019\f\u0019\u000e\u0019ǵ\u000b\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0003\u0019\u0005\u0019Ǽ\n\u0019\u0003\u001a\u0003\u001a\u0007\u001aȀ\n\u001a\f\u001a\u000e\u001aȃ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȇ\n\u001a\f\u001a\u000e\u001aȊ\u000b\u001a\u0003\u001a\u0003\u001a\u0007\u001aȎ\n\u001a\f\u001a\u000e\u001aȑ\u000b\u001a\u0007\u001aȓ\n\u001a\f\u001a\u000e\u001aȖ\u000b\u001a\u0003\u001b\u0003\u001b\u0003\u001c\u0003\u001c\u0007\u001cȜ\n\u001c\f\u001c\u000e\u001cȟ\u000b\u001c\u0003\u001c\u0003\u001c\u0007\u001cȣ\n\u001c\f\u001c\u000e\u001cȦ\u000b\u001c\u0003\u001c\u0003\u001c\u0003\u001d\u0003\u001d\u0007\u001dȬ\n\u001d\f\u001d\u000e\u001dȯ\u000b\u001d\u0003\u001d\u0003\u001d\u0007\u001dȳ\n\u001d\f\u001d\u000e\u001dȶ\u000b\u001d\u0003\u001d\u0003\u001d\u0003\u001e\u0007\u001eȻ\n\u001e\f\u001e\u000e\u001eȾ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɂ\n\u001e\f\u001e\u000e\u001eɅ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɉ\n\u001e\f\u001e\u000e\u001eɌ\u000b\u001e\u0003\u001e\u0003\u001e\u0007\u001eɐ\n\u001e\f\u001e\u000e\u001eɓ\u000b\u001e\u0005\u001eɕ\n\u001e\u0003\u001e\u0003\u001e\u0007\u001eə\n\u001e\f\u001e\u000e\u001eɜ\u000b\u001e\u0005\u001eɞ\n\u001e\u0003\u001f\u0003\u001f\u0007\u001fɢ\n\u001f\f\u001f\u000e\u001fɥ\u000b\u001f\u0003\u001f\u0003\u001f\u0007\u001fɩ\n\u001f\f\u001f\u000e\u001fɬ\u000b\u001f\u0003\u001f\u0007\u001fɯ\n\u001f\f\u001f\u000e\u001fɲ\u000b\u001f\u0003 \u0003 \u0007 ɶ\n \f \u000e ɹ\u000b \u0003 \u0003 \u0007 ɽ\n \f \u000e ʀ\u000b \u0003 \u0003 \u0007 ʄ\n \f \u000e ʇ\u000b \u0003 \u0003 \u0007 ʋ\n \f \u000e ʎ\u000b \u0003 \u0003 \u0007 ʒ\n \f \u000e ʕ\u000b \u0003 \u0003 \u0007 ʙ\n \f \u000e ʜ\u000b \u0003 \u0003 \u0007 ʠ\n \f \u000e ʣ\u000b \u0003!\u0003!\u0007!ʧ\n!\f!\u000e!ʪ\u000b!\u0003!\u0003!\u0007!ʮ\n!\f!\u000e!ʱ\u000b!\u0003!\u0003!\u0007!ʵ\n!\f!\u000e!ʸ\u000b!\u0003!\u0003!\u0007!ʼ\n!\f!\u000e!ʿ\u000b!\u0003!\u0003!\u0007!˃\n!\f!\u000e!ˆ\u000b!\u0003!\u0003!\u0007!ˊ\n!\f!\u000e!ˍ\u000b!\u0003!\u0003!\u0007!ˑ\n!\f!\u000e!˔\u000b!\u0003\"\u0003\"\u0003#\u0003#\u0003#\u0003#\u0003$\u0007$˝\n$\f$\u000e$ˠ\u000b$\u0003$\u0003$\u0007$ˤ\n$\f$\u000e$˧\u000b$\u0003$\u0002\u0002%\u0002\u0004\u0006\b\n\f\u000e\u0010\u0012\u0014\u0016\u0018\u001a\u001c\u001e \"$&(*,.02468:<>@BDF\u0002\u0003\u0003\u0002\u0018\u001a\u0002͇\u0002W\u0003\u0002\u0002\u0002\u0004Z\u0003\u0002\u0002\u0002\u0006n\u0003\u0002\u0002\u0002\b\u0082\u0003\u0002\u0002\u0002\n\u0096\u0003\u0002\u0002\u0002\fª\u0003\u0002\u0002\u0002\u000eÂ\u0003\u0002\u0002\u0002\u0010Ö\u0003\u0002\u0002\u0002\u0012ê\u0003\u0002\u0002\u0002\u0014þ\u0003\u0002\u0002\u0002\u0016Ē\u0003\u0002\u0002\u0002\u0018Ħ\u0003\u0002\u0002\u0002\u001aĹ\u0003\u0002\u0002\u0002\u001cŐ\u0003\u0002\u0002\u0002\u001eœ\u0003\u0002\u0002\u0002 Ŧ\u0003\u0002\u0002\u0002\"Ƃ\u0003\u0002\u0002\u0002$ƅ\u0003\u0002\u0002\u0002&Ƙ\u0003\u0002\u0002\u0002(Ƶ\u0003\u0002\u0002\u0002*Ƹ\u0003\u0002\u0002\u0002,ǋ\u0003\u0002\u0002\u0002.ǧ\u0003\u0002\u0002\u00020Ǫ\u0003\u0002\u0002\u00022ǽ\u0003\u0002\u0002\u00024ȗ\u0003\u0002\u0002\u00026ș\u0003\u0002\u0002\u00028ȩ\u0003\u0002\u0002\u0002:ȼ\u0003\u0002\u0002\u0002<ɟ\u0003\u0002\u0002\u0002>ɳ\u0003\u0002\u0002\u0002@ʤ\u0003\u0002\u0002\u0002B˕\u0003\u0002\u0002\u0002D˗\u0003\u0002\u0002\u0002F˞\u0003\u0002\u0002\u0002HX\u0005\u0004\u0003\u0002IX\u0005\u0006\u0004\u0002JX\u0005\b\u0005\u0002KX\u0005\f\u0007\u0002LX\u0005\u000e\b\u0002MX\u0005\u0010\t\u0002NX\u0005\n\u0006\u0002OX\u0005\u0012\n\u0002PX\u0005\u0014\u000b\u0002QX\u0005\u0016\f\u0002RX\u0005\u001e\u0010\u0002SX\u0005\u0018\r\u0002TX\u0005$\u0013\u0002UX\u0005*\u0016\u0002VX\u00050\u0019\u0002WH\u0003\u0002\u0002\u0002WI\u0003\u0002\u0002\u0002WJ\u0003\u0002\u0002\u0002WK\u0003\u0002\u0002\u0002WL\u0003\u0002\u0002\u0002WM\u0003\u0002\u0002\u0002WN\u0003\u0002\u0002\u0002WO\u0003\u0002\u0002\u0002WP\u0003\u0002\u0002\u0002WQ\u0003\u0002\u0002\u0002WR\u0003\u0002\u0002\u0002WS\u0003\u0002\u0002\u0002WT\u0003\u0002\u0002\u0002WU\u0003\u0002\u0002\u0002WV\u0003\u0002\u0002\u0002X\u0003\u0003\u0002\u0002\u0002Y[\u0005D#\u0002ZY\u0003\u0002\u0002\u0002Z[\u0003\u0002\u0002\u0002[\\\u0003\u0002\u0002\u0002\\^\u0007\u0003\u0002\u0002]_\u0005F$\u0002^]\u0003\u0002\u0002\u0002^_\u0003\u0002\u0002\u0002_c\u0003\u0002\u0002\u0002`b\u0007\u001c\u0002\u0002a`\u0003\u0002\u0002\u0002be\u0003\u0002\u0002\u0002ca\u0003\u0002\u0002\u0002cd\u0003\u0002\u0002\u0002dk\u0003\u0002\u0002\u0002ec\u0003\u0002\u0002\u0002fg\u0007\u0004\u0002\u0002gh\u0005:\u001e\u0002hi\u0007\u0005\u0002\u0002il\u0003\u0002\u0002\u0002jl\u0007\u0006\u0002\u0002kf\u0003\u0002\u0002\u0002kj\u0003\u0002\u0002\u0002l\u0005\u0003\u0002\u0002\u0002mo\u0005D#\u0002nm\u0003\u0002\u0002\u0002no\u0003\u0002\u0002\u0002op\u0003\u0002\u0002\u0002pr\u0007\u0007\u0002\u0002qs\u0005F$\u0002rq\u0003\u0002\u0002\u0002rs\u0003\u0002\u0002\u0002sw\u0003\u0002\u0002\u0002tv\u0007\u001c\u0002\u0002ut\u0003\u0002\u0002\u0002vy\u0003\u0002\u0002\u0002wu\u0003\u0002\u0002\u0002wx\u0003\u0002\u0002\u0002x\u007f\u0003\u0002\u0002\u0002yw\u0003\u0002\u0002\u0002z{\u0007\u0004\u0002\u0002{|\u0005<\u001f\u0002|}\u0007\u0005\u0002\u0002}\u0080\u0003\u0002\u0002\u0002~\u0080\u0007\u0006\u0002\u0002\u007fz\u0003\u0002\u0002\u0002\u007f~\u0003\u0002\u0002\u0002\u0080\u0007\u0003\u0002\u0002\u0002\u0081\u0083\u0005D#\u0002\u0082\u0081\u0003\u0002\u0002\u0002\u0082\u0083\u0003\u0002\u0002\u0002\u0083\u0084\u0003\u0002\u0002\u0002\u0084\u0086\u0007\b\u0002\u0002\u0085\u0087\u0005F$\u0002\u0086\u0085\u0003\u0002\u0002\u0002\u0086\u0087\u0003\u0002\u0002\u0002\u0087\u008b\u0003\u0002\u0002\u0002\u0088\u008a\u0007\u001c\u0002\u0002\u0089\u0088\u0003\u0002\u0002\u0002\u008a\u008d\u0003\u0002\u0002\u0002\u008b\u0089\u0003\u0002\u0002\u0002\u008b\u008c\u0003\u0002\u0002\u0002\u008c\u0093\u0003\u0002\u0002\u0002\u008d\u008b\u0003\u0002\u0002\u0002\u008e\u008f\u0007\u0004\u0002\u0002\u008f\u0090\u0005> \u0002\u0090\u0091\u0007\u0005\u0002\u0002\u0091\u0094\u0003\u0002\u0002\u0002\u0092\u0094\u0007\u0006\u0002\u0002\u0093\u008e\u0003\u0002\u0002\u0002\u0093\u0092\u0003\u0002\u0002\u0002\u0094\t\u0003\u0002\u0002\u0002\u0095\u0097\u0005D#\u0002\u0096\u0095\u0003\u0002\u0002\u0002\u0096\u0097\u0003\u0002\u0002\u0002\u0097\u0098\u0003\u0002\u0002\u0002\u0098\u009a\u0007\t\u0002\u0002\u0099\u009b\u0005F$\u0002\u009a\u0099\u0003\u0002\u0002\u0002\u009a\u009b\u0003\u0002\u0002\u0002\u009b\u009f\u0003\u0002\u0002\u0002\u009c\u009e\u0007\u001c\u0002\u0002\u009d\u009c\u0003\u0002\u0002\u0002\u009e¡\u0003\u0002\u0002\u0002\u009f\u009d\u0003\u0002\u0002\u0002\u009f \u0003\u0002\u0002\u0002 §\u0003\u0002\u0002\u0002¡\u009f\u0003\u0002\u0002\u0002¢£\u0007\u0004\u0002\u0002£¤\u0005> \u0002¤¥\u0007\u0005\u0002\u0002¥¨\u0003\u0002\u0002\u0002¦¨\u0007\u0006\u0002\u0002§¢\u0003\u0002\u0002\u0002§¦\u0003\u0002\u0002\u0002¨\u000b\u0003\u0002\u0002\u0002©«\u0005D#\u0002ª©\u0003\u0002\u0002\u0002ª«\u0003\u0002\u0002\u0002«¬\u0003\u0002\u0002\u0002¬®\u0007\n\u0002\u0002\u00ad¯\u0005F$\u0002®\u00ad\u0003\u0002\u0002\u0002®¯\u0003\u0002\u0002\u0002¯³\u0003\u0002\u0002\u0002°²\u0007\u001c\u0002\u0002±°\u0003\u0002\u0002\u0002²µ\u0003\u0002\u0002\u0002³±\u0003\u0002\u0002\u0002³´\u0003\u0002\u0002\u0002´¿\u0003\u0002\u0002\u0002µ³\u0003\u0002\u0002\u0002¶·\u0007\u0004\u0002\u0002·¸\u0005<\u001f\u0002¸¹\u0007\u0005\u0002\u0002¹À\u0003\u0002\u0002\u0002º»\u0007\u0004\u0002\u0002»¼\u0005> \u0002¼½\u0007\u0005\u0002\u0002½À\u0003\u0002\u0002\u0002¾À\u0007\u0006\u0002\u0002¿¶\u0003\u0002\u0002\u0002¿º\u0003\u0002\u0002\u0002¿¾\u0003\u0002\u0002\u0002À\r\u0003\u0002\u0002\u0002ÁÃ\u0005D#\u0002ÂÁ\u0003\u0002\u0002\u0002ÂÃ\u0003\u0002\u0002\u0002ÃÄ\u0003\u0002\u0002\u0002ÄÆ\u0007\u000b\u0002\u0002ÅÇ\u0005F$\u0002ÆÅ\u0003\u0002\u0002\u0002ÆÇ\u0003\u0002\u0002\u0002ÇË\u0003\u0002\u0002\u0002ÈÊ\u0007\u001c\u0002\u0002ÉÈ\u0003\u0002\u0002\u0002ÊÍ\u0003\u0002\u0002\u0002ËÉ\u0003\u0002\u0002\u0002ËÌ\u0003\u0002\u0002\u0002ÌÓ\u0003\u0002\u0002\u0002ÍË\u0003\u0002\u0002\u0002ÎÏ\u0007\u0004\u0002\u0002ÏÐ\u0005> \u0002ÐÑ\u0007\u0005\u0002\u0002ÑÔ\u0003\u0002\u0002\u0002ÒÔ\u0007\u0006\u0002\u0002ÓÎ\u0003\u0002\u0002\u0002ÓÒ\u0003\u0002\u0002\u0002Ô\u000f\u0003\u0002\u0002\u0002Õ×\u0005D#\u0002ÖÕ\u0003\u0002\u0002\u0002Ö×\u0003\u0002\u0002\u0002×Ø\u0003\u0002\u0002\u0002ØÚ\u0007\f\u0002\u0002ÙÛ\u0005F$\u0002ÚÙ\u0003\u0002\u0002\u0002ÚÛ\u0003\u0002\u0002\u0002Ûß\u0003\u0002\u0002\u0002ÜÞ\u0007\u001c\u0002\u0002ÝÜ\u0003\u0002\u0002\u0002Þá\u0003\u0002\u0002\u0002ßÝ\u0003\u0002\u0002\u0002ßà\u0003\u0002\u0002\u0002àç\u0003\u0002\u0002\u0002áß\u0003\u0002\u0002\u0002âã\u0007\u0004\u0002\u0002ãä\u0005<\u001f\u0002äå\u0007\u0005\u0002\u0002åè\u0003\u0002\u0002\u0002æè\u0007\u0006\u0002\u0002çâ\u0003\u0002\u0002\u0002çæ\u0003\u0002\u0002\u0002è\u0011\u0003\u0002\u0002\u0002éë\u0005D#\u0002êé\u0003\u0002\u0002\u0002êë\u0003\u0002\u0002\u0002ëì\u0003\u0002\u0002\u0002ìî\u0007\r\u0002\u0002íï\u0005F$\u0002îí\u0003\u0002\u0002\u0002îï\u0003\u0002\u0002\u0002ïó\u0003\u0002\u0002\u0002ðò\u0007\u001c\u0002\u0002ñð\u0003\u0002\u0002\u0002òõ\u0003\u0002\u0002\u0002óñ\u0003\u0002\u0002\u0002óô\u0003\u0002\u0002\u0002ôû\u0003\u0002\u0002\u0002õó\u0003\u0002\u0002\u0002ö÷\u0007\u0004\u0002\u0002÷ø\u0005@!\u0002øù\u0007\u0005\u0002\u0002ùü\u0003\u0002\u0002\u0002úü\u0007\u0006\u0002\u0002ûö\u0003\u0002\u0002\u0002ûú\u0003\u0002\u0002\u0002ü\u0013\u0003\u0002\u0002\u0002ýÿ\u0005D#\u0002þý\u0003\u0002\u0002\u0002þÿ\u0003\u0002\u0002\u0002ÿĀ\u0003\u0002\u0002\u0002ĀĂ\u0007\u000e\u0002\u0002āă\u0005F$\u0002Ăā\u0003\u0002\u0002\u0002Ăă\u0003\u0002\u0002\u0002ăć\u0003\u0002\u0002\u0002ĄĆ\u0007\u001c\u0002\u0002ąĄ\u0003\u0002\u0002\u0002Ćĉ\u0003\u0002\u0002\u0002ćą\u0003\u0002\u0002\u0002ćĈ\u0003\u0002\u0002\u0002Ĉď\u0003\u0002\u0002\u0002ĉć\u0003\u0002\u0002\u0002Ċċ\u0007\u0004\u0002\u0002ċČ\u0005@!\u0002Čč\u0007\u0005\u0002\u0002čĐ\u0003\u0002\u0002\u0002ĎĐ\u0007\u0006\u0002\u0002ďĊ\u0003\u0002\u0002\u0002ďĎ\u0003\u0002\u0002\u0002Đ\u0015\u0003\u0002\u0002\u0002đē\u0005D#\u0002Ēđ\u0003\u0002\u0002\u0002Ēē\u0003\u0002\u0002\u0002ēĔ\u0003\u0002\u0002\u0002ĔĖ\u0007\u000f\u0002\u0002ĕė\u0005F$\u0002Ėĕ\u0003\u0002\u0002\u0002Ėė\u0003\u0002\u0002\u0002ėě\u0003\u0002\u0002\u0002ĘĚ\u0007\u001c\u0002\u0002ęĘ\u0003\u0002\u0002\u0002Ěĝ\u0003\u0002\u0002\u0002ěę\u0003\u0002\u0002\u0002ěĜ\u0003\u0002\u0002\u0002Ĝģ\u0003\u0002\u0002\u0002ĝě\u0003\u0002\u0002\u0002Ğğ\u0007\u0004\u0002\u0002ğĠ\u0005@!\u0002Ġġ\u0007\u0005\u0002\u0002ġĤ\u0003\u0002\u0002\u0002ĢĤ\u0007\u0006\u0002\u0002ģĞ\u0003\u0002\u0002\u0002ģĢ\u0003\u0002\u0002\u0002Ĥ\u0017\u0003\u0002\u0002\u0002ĥħ\u0005D#\u0002Ħĥ\u0003\u0002\u0002\u0002Ħħ\u0003\u0002\u0002\u0002ħĨ\u0003\u0002\u0002\u0002ĨĪ\u0007\u0010\u0002\u0002ĩī\u0005F$\u0002Īĩ\u0003\u0002\u0002\u0002Īī\u0003\u0002\u0002\u0002īį\u0003\u0002\u0002\u0002ĬĮ\u0007\u001c\u0002\u0002ĭĬ\u0003\u0002\u0002\u0002Įı\u0003\u0002\u0002\u0002įĭ\u0003\u0002\u0002\u0002įİ\u0003\u0002\u0002\u0002İķ\u0003\u0002\u0002\u0002ıį\u0003\u0002\u0002\u0002Ĳĳ\u0007\u0004\u0002\u0002ĳĴ\u0005\u001a\u000e\u0002Ĵĵ\u0007\u0005\u0002\u0002ĵĸ\u0003\u0002\u0002\u0002Ķĸ\u0007\u0006\u0002\u0002ķĲ\u0003\u0002\u0002\u0002ķĶ\u0003\u0002\u0002\u0002ĸ\u0019\u0003\u0002\u0002\u0002ĹŊ\u0005\u001c\u000f\u0002ĺļ\u0007\u001c\u0002\u0002Ļĺ\u0003\u0002\u0002\u0002ļĿ\u0003\u0002\u0002\u0002ĽĻ\u0003\u0002\u0002\u0002Ľľ\u0003\u0002\u0002\u0002ľŀ\u0003\u0002\u0002\u0002ĿĽ\u0003\u0002\u0002\u0002ŀń\u0007\u0011\u0002\u0002ŁŃ\u0007\u001c\u0002\u0002łŁ\u0003\u0002\u0002\u0002Ńņ\u0003\u0002\u0002\u0002ńł\u0003\u0002\u0002\u0002ńŅ\u0003\u0002\u0002\u0002ŅŇ\u0003\u0002\u0002\u0002ņń\u0003\u0002\u0002\u0002Ňŉ\u0005\u001c\u000f\u0002ňĽ\u0003\u0002\u0002\u0002ŉŌ\u0003\u0002\u0002\u0002Ŋň\u0003\u0002\u0002\u0002Ŋŋ\u0003\u0002\u0002\u0002ŋ\u001b\u0003\u0002\u0002\u0002ŌŊ\u0003\u0002\u0002\u0002ōő\u0005\u001e\u0010\u0002Ŏő\u0005\u0010\t\u0002ŏő\u00056\u001c\u0002Őō\u0003\u0002\u0002\u0002ŐŎ\u0003\u0002\u0002\u0002Őŏ\u0003\u0002\u0002\u0002ő\u001d\u0003\u0002\u0002\u0002ŒŔ\u0005D#\u0002œŒ\u0003\u0002\u0002\u0002œŔ\u0003\u0002\u0002\u0002Ŕŕ\u0003\u0002\u0002\u0002ŕŗ\u0007\u0012\u0002\u0002ŖŘ\u0005F$\u0002ŗŖ\u0003\u0002\u0002\u0002ŗŘ\u0003\u0002\u0002\u0002ŘŜ\u0003\u0002\u0002\u0002řś\u0007\u001c\u0002\u0002Śř\u0003\u0002\u0002\u0002śŞ\u0003\u0002\u0002\u0002ŜŚ\u0003\u0002\u0002\u0002Ŝŝ\u0003\u0002\u0002\u0002ŝŤ\u0003\u0002\u0002\u0002ŞŜ\u0003\u0002\u0002\u0002şŠ\u0007\u0004\u0002\u0002Šš\u0005 \u0011\u0002šŢ\u0007\u0005\u0002\u0002Ţť\u0003\u0002\u0002\u0002ţť\u0007\u0006\u0002\u0002Ťş\u0003\u0002\u0002\u0002Ťţ\u0003\u0002\u0002\u0002ť\u001f\u0003\u0002\u0002\u0002ŦŽ\u0005\"\u0012\u0002ŧũ\u0007\u001c\u0002\u0002Ũŧ\u0003\u0002\u0002\u0002ũŬ\u0003\u0002\u0002\u0002ŪŨ\u0003\u0002\u0002\u0002Ūū\u0003\u0002\u0002\u0002ūŭ\u0003\u0002\u0002\u0002ŬŪ\u0003\u0002\u0002\u0002ŭű\u0007\u0011\u0002\u0002ŮŰ\u0007\u001c\u0002\u0002ůŮ\u0003\u0002\u0002\u0002Űų\u0003\u0002\u0002\u0002űů\u0003\u0002\u0002\u0002űŲ\u0003\u0002\u0002\u0002ŲŴ\u0003\u0002\u0002\u0002ųű\u0003\u0002\u0002\u0002ŴŸ\u0005\"\u0012\u0002ŵŷ\u0007\u001c\u0002\u0002Ŷŵ\u0003\u0002\u0002\u0002ŷź\u0003\u0002\u0002\u0002ŸŶ\u0003\u0002\u0002\u0002ŸŹ\u0003\u0002\u0002\u0002Źż\u0003\u0002\u0002\u0002źŸ\u0003\u0002\u0002\u0002ŻŪ\u0003\u0002\u0002\u0002żſ\u0003\u0002\u0002\u0002ŽŻ\u0003\u0002\u0002\u0002Žž\u0003\u0002\u0002\u0002ž!\u0003\u0002\u0002\u0002ſŽ\u0003\u0002\u0002\u0002ƀƃ\u0005\u0010\t\u0002Ɓƃ\u00056\u001c\u0002Ƃƀ\u0003\u0002\u0002\u0002ƂƁ\u0003\u0002\u0002\u0002ƃ#\u0003\u0002\u0002\u0002ƄƆ\u0005D#\u0002ƅƄ\u0003\u0002\u0002\u0002ƅƆ\u0003\u0002\u0002\u0002ƆƇ\u0003\u0002\u0002\u0002ƇƉ\u0007\u0013\u0002\u0002ƈƊ\u0005F$\u0002Ɖƈ\u0003\u0002\u0002\u0002ƉƊ\u0003\u0002\u0002\u0002ƊƎ\u0003\u0002\u0002\u0002Ƌƍ\u0007\u001c\u0002\u0002ƌƋ\u0003\u0002\u0002\u0002ƍƐ\u0003\u0002\u0002\u0002Ǝƌ\u0003\u0002\u0002\u0002ƎƏ\u0003\u0002\u0002\u0002ƏƖ\u0003\u0002\u0002\u0002ƐƎ\u0003\u0002\u0002\u0002Ƒƒ\u0007\u0004\u0002\u0002ƒƓ\u0005&\u0014\u0002ƓƔ\u0007\u0005\u0002\u0002ƔƗ\u0003\u0002\u0002\u0002ƕƗ\u0007\u0006\u0002\u0002ƖƑ\u0003\u0002\u0002\u0002Ɩƕ\u0003\u0002\u0002\u0002Ɨ%\u0003\u0002\u0002\u0002ƘƯ\u0005(\u0015\u0002ƙƛ\u0007\u001c\u0002\u0002ƚƙ\u0003\u0002\u0002\u0002ƛƞ\u0003\u0002\u0002\u0002Ɯƚ\u0003\u0002\u0002\u0002ƜƝ\u0003\u0002\u0002\u0002ƝƟ\u0003\u0002\u0002\u0002ƞƜ\u0003\u0002\u0002\u0002Ɵƣ\u0007\u0011\u0002\u0002ƠƢ\u0007\u001c\u0002\u0002ơƠ\u0003\u0002\u0002\u0002Ƣƥ\u0003\u0002\u0002\u0002ƣơ\u0003\u0002\u0002\u0002ƣƤ\u0003\u0002\u0002\u0002ƤƦ\u0003\u0002\u0002\u0002ƥƣ\u0003\u0002\u0002\u0002Ʀƪ\u0005(\u0015\u0002ƧƩ\u0007\u001c\u0002\u0002ƨƧ\u0003\u0002\u0002\u0002ƩƬ\u0003\u0002\u0002\u0002ƪƨ\u0003\u0002\u0002\u0002ƪƫ\u0003\u0002\u0002\u0002ƫƮ\u0003\u0002\u0002\u0002Ƭƪ\u0003\u0002\u0002\u0002ƭƜ\u0003\u0002\u0002\u0002ƮƱ\u0003\u0002\u0002\u0002Ưƭ\u0003\u0002\u0002\u0002Ưư\u0003\u0002\u0002\u0002ư'\u0003\u0002\u0002\u0002ƱƯ\u0003\u0002\u0002\u0002Ʋƶ\u0005\u001e\u0010\u0002Ƴƶ\u0005\u0010\t\u0002ƴƶ\u00056\u001c\u0002ƵƲ\u0003\u0002\u0002\u0002ƵƳ\u0003\u0002\u0002\u0002Ƶƴ\u0003\u0002\u0002\u0002ƶ)\u0003\u0002\u0002\u0002Ʒƹ\u0005D#\u0002ƸƷ\u0003\u0002\u0002\u0002Ƹƹ\u0003\u0002\u0002\u0002ƹƺ\u0003\u0002\u0002\u0002ƺƼ\u0007\u0014\u0002\u0002ƻƽ\u0005F$\u0002Ƽƻ\u0003\u0002\u0002\u0002Ƽƽ\u0003\u0002\u0002\u0002ƽǁ\u0003\u0002\u0002\u0002ƾǀ\u0007\u001c\u0002\u0002ƿƾ\u0003\u0002\u0002\u0002ǀǃ\u0003\u0002\u0002\u0002ǁƿ\u0003\u0002\u0002\u0002ǁǂ\u0003\u0002\u0002\u0002ǂǉ\u0003\u0002\u0002\u0002ǃǁ\u0003\u0002\u0002\u0002Ǆǅ\u0007\u0004\u0002\u0002ǅǆ\u0005,\u0017\u0002ǆǇ\u0007\u0005\u0002\u0002ǇǊ\u0003\u0002\u0002\u0002ǈǊ\u0007\u0006\u0002\u0002ǉǄ\u0003\u0002\u0002\u0002ǉǈ\u0003\u0002\u0002\u0002Ǌ+\u0003\u0002\u0002\u0002ǋǢ\u0005.\u0018\u0002ǌǎ\u0007\u001c\u0002\u0002Ǎǌ\u0003\u0002\u0002\u0002ǎǑ\u0003\u0002\u0002\u0002ǏǍ\u0003\u0002\u0002\u0002Ǐǐ\u0003\u0002\u0002\u0002ǐǒ\u0003\u0002\u0002\u0002ǑǏ\u0003\u0002\u0002\u0002ǒǖ\u0007\u0011\u0002\u0002ǓǕ\u0007\u001c\u0002\u0002ǔǓ\u0003\u0002\u0002\u0002Ǖǘ\u0003\u0002\u0002\u0002ǖǔ\u0003\u0002\u0002\u0002ǖǗ\u0003\u0002\u0002\u0002ǗǙ\u0003\u0002\u0002\u0002ǘǖ\u0003\u0002\u0002\u0002Ǚǝ\u0005.\u0018\u0002ǚǜ\u0007\u001c\u0002\u0002Ǜǚ\u0003\u0002\u0002\u0002ǜǟ\u0003\u0002\u0002\u0002ǝǛ\u0003\u0002\u0002\u0002ǝǞ\u0003\u0002\u0002\u0002Ǟǡ\u0003\u0002\u0002\u0002ǟǝ\u0003\u0002\u0002\u0002ǠǏ\u0003\u0002\u0002\u0002ǡǤ\u0003\u0002\u0002\u0002ǢǠ\u0003\u0002\u0002\u0002Ǣǣ\u0003\u0002\u0002\u0002ǣ-\u0003\u0002\u0002\u0002ǤǢ\u0003\u0002\u0002\u0002ǥǨ\u0005\u0018\r\u0002ǦǨ\u00058\u001d\u0002ǧǥ\u0003\u0002\u0002\u0002ǧǦ\u0003\u0002\u0002\u0002Ǩ/\u0003\u0002\u0002\u0002ǩǫ\u0005D#\u0002Ǫǩ\u0003\u0002\u0002\u0002Ǫǫ\u0003\u0002\u0002\u0002ǫǬ\u0003\u0002\u0002\u0002ǬǮ\u0007\u0015\u0002\u0002ǭǯ\u0005F$\u0002Ǯǭ\u0003\u0002\u0002\u0002Ǯǯ\u0003\u0002\u0002\u0002ǯǳ\u0003\u0002\u0002\u0002ǰǲ\u0007\u001c\u0002\u0002Ǳǰ\u0003\u0002\u0002\u0002ǲǵ\u0003\u0002\u0002\u0002ǳǱ\u0003\u0002\u0002\u0002ǳǴ\u0003\u0002\u0002\u0002Ǵǻ\u0003\u0002\u0002\u0002ǵǳ\u0003\u0002\u0002\u0002ǶǷ\u0007\u0004\u0002\u0002ǷǸ\u00052\u001a\u0002Ǹǹ\u0007\u0005\u0002\u0002ǹǼ\u0003\u0002\u0002\u0002ǺǼ\u0007\u0006\u0002\u0002ǻǶ\u0003\u0002\u0002\u0002ǻǺ\u0003\u0002\u0002\u0002Ǽ1\u0003\u0002\u0002\u0002ǽȔ\u00054\u001b\u0002ǾȀ\u0007\u001c\u0002\u0002ǿǾ\u0003\u0002\u0002\u0002Ȁȃ\u0003\u0002\u0002\u0002ȁǿ\u0003\u0002\u0002\u0002ȁȂ\u0003\u0002\u0002\u0002ȂȄ\u0003\u0002\u0002\u0002ȃȁ\u0003\u0002\u0002\u0002ȄȈ\u0007\u0011\u0002\u0002ȅȇ\u0007\u001c\u0002\u0002Ȇȅ\u0003\u0002\u0002\u0002ȇȊ\u0003\u0002\u0002\u0002ȈȆ\u0003\u0002\u0002\u0002Ȉȉ\u0003\u0002\u0002\u0002ȉȋ\u0003\u0002\u0002\u0002ȊȈ\u0003\u0002\u0002\u0002ȋȏ\u00054\u001b\u0002ȌȎ\u0007\u001c\u0002\u0002ȍȌ\u0003\u0002\u0002\u0002Ȏȑ\u0003\u0002\u0002\u0002ȏȍ\u0003\u0002\u0002\u0002ȏȐ\u0003\u0002\u0002\u0002Ȑȓ\u0003\u0002\u0002\u0002ȑȏ\u0003\u0002\u0002\u0002Ȓȁ\u0003\u0002\u0002\u0002ȓȖ\u0003\u0002\u0002\u0002ȔȒ\u0003\u0002\u0002\u0002Ȕȕ\u0003\u0002\u0002\u0002ȕ3\u0003\u0002\u0002\u0002ȖȔ\u0003\u0002\u0002\u0002ȗȘ\u0005\u0002\u0002\u0002Ș5\u0003\u0002\u0002\u0002șȝ\u0007\u0004\u0002\u0002ȚȜ\u0007\u001c\u0002\u0002țȚ\u0003\u0002\u0002\u0002Ȝȟ\u0003\u0002\u0002\u0002ȝț\u0003\u0002\u0002\u0002ȝȞ\u0003\u0002\u0002\u0002ȞȠ\u0003\u0002\u0002\u0002ȟȝ\u0003\u0002\u0002\u0002ȠȤ\u0005<\u001f\u0002ȡȣ\u0007\u001c\u0002\u0002Ȣȡ\u0003\u0002\u0002\u0002ȣȦ\u0003\u0002\u0002\u0002ȤȢ\u0003\u0002\u0002\u0002Ȥȥ\u0003\u0002\u0002\u0002ȥȧ\u0003\u0002\u0002\u0002ȦȤ\u0003\u0002\u0002\u0002ȧȨ\u0007\u0005\u0002\u0002Ȩ7\u0003\u0002\u0002\u0002ȩȭ\u0007\u0004\u0002\u0002ȪȬ\u0007\u001c\u0002\u0002ȫȪ\u0003\u0002\u0002\u0002Ȭȯ\u0003\u0002\u0002\u0002ȭȫ\u0003\u0002\u0002\u0002ȭȮ\u0003\u0002\u0002\u0002ȮȰ\u0003\u0002\u0002\u0002ȯȭ\u0003\u0002\u0002\u0002Ȱȴ\u0005> \u0002ȱȳ\u0007\u001c\u0002\u0002Ȳȱ\u0003\u0002\u0002\u0002ȳȶ\u0003\u0002\u0002\u0002ȴȲ\u0003\u0002\u0002\u0002ȴȵ\u0003\u0002\u0002\u0002ȵȷ\u0003\u0002\u0002\u0002ȶȴ\u0003\u0002\u0002\u0002ȷȸ\u0007\u0005\u0002\u0002ȸ9\u0003\u0002\u0002\u0002ȹȻ\u0007\u001c\u0002\u0002Ⱥȹ\u0003\u0002\u0002\u0002ȻȾ\u0003\u0002\u0002\u0002ȼȺ\u0003\u0002\u0002\u0002ȼȽ\u0003\u0002\u0002\u0002Ƚȿ\u0003\u0002\u0002\u0002Ⱦȼ\u0003\u0002\u0002\u0002ȿɃ\u0007\u001b\u0002\u0002ɀɂ\u0007\u001c\u0002\u0002Ɂɀ\u0003\u0002\u0002\u0002ɂɅ\u0003\u0002\u0002\u0002ɃɁ\u0003\u0002\u0002\u0002ɃɄ\u0003\u0002\u0002\u0002ɄɆ\u0003\u0002\u0002\u0002ɅɃ\u0003\u0002\u0002\u0002ɆɊ\u0007\u001b\u0002\u0002ɇɉ\u0007\u001c\u0002\u0002Ɉɇ\u0003\u0002\u0002\u0002ɉɌ\u0003\u0002\u0002\u0002ɊɈ\u0003\u0002\u0002\u0002Ɋɋ\u0003\u0002\u0002\u0002ɋɔ\u0003\u0002\u0002\u0002ɌɊ\u0003\u0002\u0002\u0002ɍɑ\u0007\u001b\u0002\u0002Ɏɐ\u0007\u001c\u0002\u0002ɏɎ\u0003\u0002\u0002\u0002ɐɓ\u0003\u0002\u0002\u0002ɑɏ\u0003\u0002\u0002\u0002ɑɒ\u0003\u0002\u0002\u0002ɒɕ\u0003\u0002\u0002\u0002ɓɑ\u0003\u0002\u0002\u0002ɔɍ\u0003\u0002\u0002\u0002ɔɕ\u0003\u0002\u0002\u0002ɕɝ\u0003\u0002\u0002\u0002ɖɚ\u0007\u001b\u0002\u0002ɗə\u0007\u001c\u0002\u0002ɘɗ\u0003\u0002\u0002\u0002əɜ\u0003\u0002\u0002\u0002ɚɘ\u0003\u0002\u0002\u0002ɚɛ\u0003\u0002\u0002\u0002ɛɞ\u0003\u0002\u0002\u0002ɜɚ\u0003\u0002\u0002\u0002ɝɖ\u0003\u0002\u0002\u0002ɝɞ\u0003\u0002\u0002\u0002ɞ;\u0003\u0002\u0002\u0002ɟɰ\u0005:\u001e\u0002ɠɢ\u0007\u001c\u0002\u0002ɡɠ\u0003\u0002\u0002\u0002ɢɥ\u0003\u0002\u0002\u0002ɣɡ\u0003\u0002\u0002\u0002ɣɤ\u0003\u0002\u0002\u0002ɤɦ\u0003\u0002\u0002\u0002ɥɣ\u0003\u0002\u0002\u0002ɦɪ\u0007\u0011\u0002\u0002ɧɩ\u0007\u001c\u0002\u0002ɨɧ\u0003\u0002\u0002\u0002ɩɬ\u0003\u0002\u0002\u0002ɪɨ\u0003\u0002\u0002\u0002ɪɫ\u0003\u0002\u0002\u0002ɫɭ\u0003\u0002\u0002\u0002ɬɪ\u0003\u0002\u0002\u0002ɭɯ\u0005:\u001e\u0002ɮɣ\u0003\u0002\u0002\u0002ɯɲ\u0003\u0002\u0002\u0002ɰɮ\u0003\u0002\u0002\u0002ɰɱ\u0003\u0002\u0002\u0002ɱ=\u0003\u0002\u0002\u0002ɲɰ\u0003\u0002\u0002\u0002ɳɷ\u0007\u0004\u0002\u0002ɴɶ\u0007\u001c\u0002\u0002ɵɴ\u0003\u0002\u0002\u0002ɶɹ\u0003\u0002\u0002\u0002ɷɵ\u0003\u0002\u0002\u0002ɷɸ\u0003\u0002\u0002\u0002ɸɺ\u0003\u0002\u0002\u0002ɹɷ\u0003\u0002\u0002\u0002ɺɾ\u0005<\u001f\u0002ɻɽ\u0007\u001c\u0002\u0002ɼɻ\u0003\u0002\u0002\u0002ɽʀ\u0003\u0002\u0002\u0002ɾɼ\u0003\u0002\u0002\u0002ɾɿ\u0003\u0002\u0002\u0002ɿʁ\u0003\u0002\u0002\u0002ʀɾ\u0003\u0002\u0002\u0002ʁʡ\u0007\u0005\u0002\u0002ʂʄ\u0007\u001c\u0002\u0002ʃʂ\u0003\u0002\u0002\u0002ʄʇ\u0003\u0002\u0002\u0002ʅʃ\u0003\u0002\u0002\u0002ʅʆ\u0003\u0002\u0002\u0002ʆʈ\u0003\u0002\u0002\u0002ʇʅ\u0003\u0002\u0002\u0002ʈʌ\u0007\u0011\u0002\u0002ʉʋ\u0007\u001c\u0002\u0002ʊʉ\u0003\u0002\u0002\u0002ʋʎ\u0003\u0002\u0002\u0002ʌʊ\u0003\u0002\u0002\u0002ʌʍ\u0003\u0002\u0002\u0002ʍʏ\u0003\u0002\u0002\u0002ʎʌ\u0003\u0002\u0002\u0002ʏʓ\u0007\u0004\u0002\u0002ʐʒ\u0007\u001c\u0002\u0002ʑʐ\u0003\u0002\u0002\u0002ʒʕ\u0003\u0002\u0002\u0002ʓʑ\u0003\u0002\u0002\u0002ʓʔ\u0003\u0002\u0002\u0002ʔʖ\u0003\u0002\u0002\u0002ʕʓ\u0003\u0002\u0002\u0002ʖʚ\u0005<\u001f\u0002ʗʙ\u0007\u001c\u0002\u0002ʘʗ\u0003\u0002\u0002\u0002ʙʜ\u0003\u0002\u0002\u0002ʚʘ\u0003\u0002\u0002\u0002ʚʛ\u0003\u0002\u0002\u0002ʛʝ\u0003\u0002\u0002\u0002ʜʚ\u0003\u0002\u0002\u0002ʝʞ\u0007\u0005\u0002\u0002ʞʠ\u0003\u0002\u0002\u0002ʟʅ\u0003\u0002\u0002\u0002ʠʣ\u0003\u0002\u0002\u0002ʡʟ\u0003\u0002\u0002\u0002ʡʢ\u0003\u0002\u0002\u0002ʢ?\u0003\u0002\u0002\u0002ʣʡ\u0003\u0002\u0002\u0002ʤʨ\u0007\u0004\u0002\u0002ʥʧ\u0007\u001c\u0002\u0002ʦʥ\u0003\u0002\u0002\u0002ʧʪ\u0003\u0002\u0002\u0002ʨʦ\u0003\u0002\u0002\u0002ʨʩ\u0003\u0002\u0002\u0002ʩʫ\u0003\u0002\u0002\u0002ʪʨ\u0003\u0002\u0002\u0002ʫʯ\u0005> \u0002ʬʮ\u0007\u001c\u0002\u0002ʭʬ\u0003\u0002\u0002\u0002ʮʱ\u0003\u0002\u0002\u0002ʯʭ\u0003\u0002\u0002\u0002ʯʰ\u0003\u0002\u0002\u0002ʰʲ\u0003\u0002\u0002\u0002ʱʯ\u0003\u0002\u0002\u0002ʲ˒\u0007\u0005\u0002\u0002ʳʵ\u0007\u001c\u0002\u0002ʴʳ\u0003\u0002\u0002\u0002ʵʸ\u0003\u0002\u0002\u0002ʶʴ\u0003\u0002\u0002\u0002ʶʷ\u0003\u0002\u0002\u0002ʷʹ\u0003\u0002\u0002\u0002ʸʶ\u0003\u0002\u0002\u0002ʹʽ\u0007\u0011\u0002\u0002ʺʼ\u0007\u001c\u0002\u0002ʻʺ\u0003\u0002\u0002\u0002ʼʿ\u0003\u0002\u0002\u0002ʽʻ\u0003\u0002\u0002\u0002ʽʾ\u0003\u0002\u0002\u0002ʾˀ\u0003\u0002\u0002\u0002ʿʽ\u0003\u0002\u0002\u0002ˀ˄\u0007\u0004\u0002\u0002ˁ˃\u0007\u001c\u0002\u0002˂ˁ\u0003\u0002\u0002\u0002˃ˆ\u0003\u0002\u0002\u0002˄˂\u0003\u0002\u0002\u0002˄˅\u0003\u0002\u0002\u0002˅ˇ\u0003\u0002\u0002\u0002ˆ˄\u0003\u0002\u0002\u0002ˇˋ\u0005> \u0002ˈˊ\u0007\u001c\u0002\u0002ˉˈ\u0003\u0002\u0002\u0002ˊˍ\u0003\u0002\u0002\u0002ˋˉ\u0003\u0002\u0002\u0002ˋˌ\u0003\u0002\u0002\u0002ˌˎ\u0003\u0002\u0002\u0002ˍˋ\u0003\u0002\u0002\u0002ˎˏ\u0007\u0005\u0002\u0002ˏˑ\u0003\u0002\u0002\u0002ːʶ\u0003\u0002\u0002\u0002ˑ˔\u0003\u0002\u0002\u0002˒ː\u0003\u0002\u0002\u0002˒˓\u0003\u0002\u0002\u0002˓A\u0003\u0002\u0002\u0002˔˒\u0003\u0002\u0002\u0002˕˖\u0007\u0006\u0002\u0002˖C\u0003\u0002\u0002\u0002˗˘\u0007\u0016\u0002\u0002˘˙\u0007\u001b\u0002\u0002˙˚\u0007\u0017\u0002\u0002˚E\u0003\u0002\u0002\u0002˛˝\u0007\u001c\u0002\u0002˜˛\u0003\u0002\u0002\u0002˝ˠ\u0003\u0002\u0002\u0002˞˜\u0003\u0002\u0002\u0002˞˟\u0003\u0002\u0002\u0002˟ˡ\u0003\u0002\u0002\u0002ˠ˞\u0003\u0002\u0002\u0002ˡ˥\t\u0002\u0002\u0002ˢˤ\u0007\u001c\u0002\u0002ˣˢ\u0003\u0002\u0002\u0002ˤ˧\u0003\u0002\u0002\u0002˥ˣ\u0003\u0002\u0002\u0002˥˦\u0003\u0002\u0002\u0002˦G\u0003\u0002\u0002\u0002˧˥\u0003\u0002\u0002\u0002tWZ^cknrw\u007f\u0082\u0086\u008b\u0093\u0096\u009a\u009f§ª®³¿ÂÆËÓÖÚßçêîóûþĂćďĒĖěģĦĪįķĽńŊŐœŗŜŤŪűŸŽƂƅƉƎƖƜƣƪƯƵƸƼǁǉǏǖǝǢǧǪǮǳǻȁȈȏȔȝȤȭȴȼɃɊɑɔɚɝɣɪɰɷɾʅʌʓʚʡʨʯʶʽ˄ˋ˒˞˥".toCharArray());
        _decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];

        for(i = 0; i < _ATN.getNumberOfDecisions(); ++i) {
            _decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
        }

    }

    public static class DimensionContext extends ParserRuleContext {
        public TerminalNode M() {
            return this.getToken(22, 0);
        }

        public TerminalNode Z() {
            return this.getToken(23, 0);
        }

        public TerminalNode ZM() {
            return this.getToken(24, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public DimensionContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 34;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterDimension(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitDimension(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitDimension(this) : visitor.visitChildren(this);
        }
    }

    public static class SridContext extends ParserRuleContext {
        public TerminalNode Number() {
            return this.getToken(25, 0);
        }

        public SridContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 33;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterSrid(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitSrid(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitSrid(this) : visitor.visitChildren(this);
        }
    }

    public static class EmptyContext extends ParserRuleContext {
        public EmptyContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 32;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterEmpty(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitEmpty(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitEmpty(this) : visitor.visitChildren(this);
        }
    }

    public static class CoordinatesetssetContext extends ParserRuleContext {
        public List<CoordinatesetsContext> coordinatesets() {
            return this.getRuleContexts(CoordinatesetsContext.class);
        }

        public CoordinatesetsContext coordinatesets(int i) {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetssetContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 31;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCoordinatesetsset(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCoordinatesetsset(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCoordinatesetsset(this) : visitor.visitChildren(this);
        }
    }

    public static class CoordinatesetsContext extends ParserRuleContext {
        public List<CoordinatesContext> coordinates() {
            return this.getRuleContexts(CoordinatesContext.class);
        }

        public CoordinatesContext coordinates(int i) {
            return (CoordinatesContext)this.getRuleContext(CoordinatesContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 30;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCoordinatesets(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCoordinatesets(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCoordinatesets(this) : visitor.visitChildren(this);
        }
    }

    public static class CoordinatesContext extends ParserRuleContext {
        public List<CoordinateContext> coordinate() {
            return this.getRuleContexts(CoordinateContext.class);
        }

        public CoordinateContext coordinate(int i) {
            return (CoordinateContext)this.getRuleContext(CoordinateContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 29;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCoordinates(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCoordinates(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCoordinates(this) : visitor.visitChildren(this);
        }
    }

    public static class CoordinateContext extends ParserRuleContext {
        public List<TerminalNode> Number() {
            return this.getTokens(25);
        }

        public TerminalNode Number(int i) {
            return this.getToken(25, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinateContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 28;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCoordinate(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCoordinate(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCoordinate(this) : visitor.visitChildren(this);
        }
    }

    public static class PolygonCoordinatesContext extends ParserRuleContext {
        public CoordinatesetsContext coordinatesets() {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public PolygonCoordinatesContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 27;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterPolygonCoordinates(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitPolygonCoordinates(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitPolygonCoordinates(this) : visitor.visitChildren(this);
        }
    }

    public static class LineStringCoordinatesContext extends ParserRuleContext {
        public CoordinatesContext coordinates() {
            return (CoordinatesContext)this.getRuleContext(CoordinatesContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public LineStringCoordinatesContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 26;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterLineStringCoordinates(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitLineStringCoordinates(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitLineStringCoordinates(this) : visitor.visitChildren(this);
        }
    }

    public static class GeometryCollectionElementsContext extends ParserRuleContext {
        public WktContext wkt() {
            return (WktContext)this.getRuleContext(WktContext.class, 0);
        }

        public GeometryCollectionElementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 25;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterGeometryCollectionElements(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitGeometryCollectionElements(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitGeometryCollectionElements(this) : visitor.visitChildren(this);
        }
    }

    public static class GeometryCollectionItemsContext extends ParserRuleContext {
        public List<GeometryCollectionElementsContext> geometryCollectionElements() {
            return this.getRuleContexts(GeometryCollectionElementsContext.class);
        }

        public GeometryCollectionElementsContext geometryCollectionElements(int i) {
            return (GeometryCollectionElementsContext)this.getRuleContext(GeometryCollectionElementsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public GeometryCollectionItemsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 24;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterGeometryCollectionItems(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitGeometryCollectionItems(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitGeometryCollectionItems(this) : visitor.visitChildren(this);
        }
    }

    public static class GeometryCollectionContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public GeometryCollectionItemsContext geometryCollectionItems() {
            return (GeometryCollectionItemsContext)this.getRuleContext(GeometryCollectionItemsContext.class, 0);
        }

        public GeometryCollectionContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 23;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterGeometryCollection(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitGeometryCollection(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitGeometryCollection(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiSurfaceElementsContext extends ParserRuleContext {
        public CurvePolygonContext curvePolygon() {
            return (CurvePolygonContext)this.getRuleContext(CurvePolygonContext.class, 0);
        }

        public PolygonCoordinatesContext polygonCoordinates() {
            return (PolygonCoordinatesContext)this.getRuleContext(PolygonCoordinatesContext.class, 0);
        }

        public MultiSurfaceElementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 22;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiSurfaceElements(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiSurfaceElements(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiSurfaceElements(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiSurfaceItemsContext extends ParserRuleContext {
        public List<MultiSurfaceElementsContext> multiSurfaceElements() {
            return this.getRuleContexts(MultiSurfaceElementsContext.class);
        }

        public MultiSurfaceElementsContext multiSurfaceElements(int i) {
            return (MultiSurfaceElementsContext)this.getRuleContext(MultiSurfaceElementsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public MultiSurfaceItemsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 21;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiSurfaceItems(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiSurfaceItems(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiSurfaceItems(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiSurfaceContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public MultiSurfaceItemsContext multiSurfaceItems() {
            return (MultiSurfaceItemsContext)this.getRuleContext(MultiSurfaceItemsContext.class, 0);
        }

        public MultiSurfaceContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 20;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiSurface(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiSurface(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiSurface(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiCurveElementsContext extends ParserRuleContext {
        public CompoundCurveContext compoundCurve() {
            return (CompoundCurveContext)this.getRuleContext(CompoundCurveContext.class, 0);
        }

        public CircularStringContext circularString() {
            return (CircularStringContext)this.getRuleContext(CircularStringContext.class, 0);
        }

        public LineStringCoordinatesContext lineStringCoordinates() {
            return (LineStringCoordinatesContext)this.getRuleContext(LineStringCoordinatesContext.class, 0);
        }

        public MultiCurveElementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 19;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiCurveElements(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiCurveElements(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiCurveElements(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiCurveItemsContext extends ParserRuleContext {
        public List<MultiCurveElementsContext> multiCurveElements() {
            return this.getRuleContexts(MultiCurveElementsContext.class);
        }

        public MultiCurveElementsContext multiCurveElements(int i) {
            return (MultiCurveElementsContext)this.getRuleContext(MultiCurveElementsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public MultiCurveItemsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 18;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiCurveItems(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiCurveItems(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiCurveItems(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiCurveContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public MultiCurveItemsContext multiCurveItems() {
            return (MultiCurveItemsContext)this.getRuleContext(MultiCurveItemsContext.class, 0);
        }

        public MultiCurveContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 17;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiCurve(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiCurve(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiCurve(this) : visitor.visitChildren(this);
        }
    }

    public static class CompoundCurveElementsContext extends ParserRuleContext {
        public CircularStringContext circularString() {
            return (CircularStringContext)this.getRuleContext(CircularStringContext.class, 0);
        }

        public LineStringCoordinatesContext lineStringCoordinates() {
            return (LineStringCoordinatesContext)this.getRuleContext(LineStringCoordinatesContext.class, 0);
        }

        public CompoundCurveElementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 16;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCompoundCurveElements(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCompoundCurveElements(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCompoundCurveElements(this) : visitor.visitChildren(this);
        }
    }

    public static class CompoundCurveItemsContext extends ParserRuleContext {
        public List<CompoundCurveElementsContext> compoundCurveElements() {
            return this.getRuleContexts(CompoundCurveElementsContext.class);
        }

        public CompoundCurveElementsContext compoundCurveElements(int i) {
            return (CompoundCurveElementsContext)this.getRuleContext(CompoundCurveElementsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CompoundCurveItemsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 15;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCompoundCurveItems(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCompoundCurveItems(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCompoundCurveItems(this) : visitor.visitChildren(this);
        }
    }

    public static class CompoundCurveContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CompoundCurveItemsContext compoundCurveItems() {
            return (CompoundCurveItemsContext)this.getRuleContext(CompoundCurveItemsContext.class, 0);
        }

        public CompoundCurveContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 14;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCompoundCurve(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCompoundCurve(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCompoundCurve(this) : visitor.visitChildren(this);
        }
    }

    public static class CurvePolygonElementsContext extends ParserRuleContext {
        public CompoundCurveContext compoundCurve() {
            return (CompoundCurveContext)this.getRuleContext(CompoundCurveContext.class, 0);
        }

        public CircularStringContext circularString() {
            return (CircularStringContext)this.getRuleContext(CircularStringContext.class, 0);
        }

        public LineStringCoordinatesContext lineStringCoordinates() {
            return (LineStringCoordinatesContext)this.getRuleContext(LineStringCoordinatesContext.class, 0);
        }

        public CurvePolygonElementsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 13;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCurvePolygonElements(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCurvePolygonElements(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCurvePolygonElements(this) : visitor.visitChildren(this);
        }
    }

    public static class CurvePolygonItemsContext extends ParserRuleContext {
        public List<CurvePolygonElementsContext> curvePolygonElements() {
            return this.getRuleContexts(CurvePolygonElementsContext.class);
        }

        public CurvePolygonElementsContext curvePolygonElements(int i) {
            return (CurvePolygonElementsContext)this.getRuleContext(CurvePolygonElementsContext.class, i);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CurvePolygonItemsContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 12;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCurvePolygonItems(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCurvePolygonItems(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCurvePolygonItems(this) : visitor.visitChildren(this);
        }
    }

    public static class CurvePolygonContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CurvePolygonItemsContext curvePolygonItems() {
            return (CurvePolygonItemsContext)this.getRuleContext(CurvePolygonItemsContext.class, 0);
        }

        public CurvePolygonContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 11;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCurvePolygon(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCurvePolygon(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCurvePolygon(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiPolygonContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetssetContext coordinatesetsset() {
            return (CoordinatesetssetContext)this.getRuleContext(CoordinatesetssetContext.class, 0);
        }

        public MultiPolygonContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 10;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiPolygon(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiPolygon(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiPolygon(this) : visitor.visitChildren(this);
        }
    }

    public static class PolyHedralSurfaceContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetssetContext coordinatesetsset() {
            return (CoordinatesetssetContext)this.getRuleContext(CoordinatesetssetContext.class, 0);
        }

        public PolyHedralSurfaceContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 9;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterPolyHedralSurface(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitPolyHedralSurface(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitPolyHedralSurface(this) : visitor.visitChildren(this);
        }
    }

    public static class TinContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetssetContext coordinatesetsset() {
            return (CoordinatesetssetContext)this.getRuleContext(CoordinatesetssetContext.class, 0);
        }

        public TinContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 8;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterTin(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitTin(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitTin(this) : visitor.visitChildren(this);
        }
    }

    public static class CircularStringContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesContext coordinates() {
            return (CoordinatesContext)this.getRuleContext(CoordinatesContext.class, 0);
        }

        public CircularStringContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 7;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterCircularString(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitCircularString(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitCircularString(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiLineStringContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetsContext coordinatesets() {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, 0);
        }

        public MultiLineStringContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 6;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiLineString(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiLineString(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiLineString(this) : visitor.visitChildren(this);
        }
    }

    public static class MultiPointContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesContext coordinates() {
            return (CoordinatesContext)this.getRuleContext(CoordinatesContext.class, 0);
        }

        public CoordinatesetsContext coordinatesets() {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, 0);
        }

        public MultiPointContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 5;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterMultiPoint(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitMultiPoint(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitMultiPoint(this) : visitor.visitChildren(this);
        }
    }

    public static class TriangleContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetsContext coordinatesets() {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, 0);
        }

        public TriangleContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 4;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterTriangle(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitTriangle(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitTriangle(this) : visitor.visitChildren(this);
        }
    }

    public static class PolygonContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesetsContext coordinatesets() {
            return (CoordinatesetsContext)this.getRuleContext(CoordinatesetsContext.class, 0);
        }

        public PolygonContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 3;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterPolygon(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitPolygon(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitPolygon(this) : visitor.visitChildren(this);
        }
    }

    public static class LineStringContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinatesContext coordinates() {
            return (CoordinatesContext)this.getRuleContext(CoordinatesContext.class, 0);
        }

        public LineStringContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 2;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterLineString(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitLineString(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitLineString(this) : visitor.visitChildren(this);
        }
    }

    public static class PointContext extends ParserRuleContext {
        public SridContext srid() {
            return (SridContext)this.getRuleContext(SridContext.class, 0);
        }

        public DimensionContext dimension() {
            return (DimensionContext)this.getRuleContext(DimensionContext.class, 0);
        }

        public List<TerminalNode> WhiteSpace() {
            return this.getTokens(26);
        }

        public TerminalNode WhiteSpace(int i) {
            return this.getToken(26, i);
        }

        public CoordinateContext coordinate() {
            return (CoordinateContext)this.getRuleContext(CoordinateContext.class, 0);
        }

        public PointContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 1;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterPoint(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitPoint(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitPoint(this) : visitor.visitChildren(this);
        }
    }

    public static class WktContext extends ParserRuleContext {
        public PointContext point() {
            return (PointContext)this.getRuleContext(PointContext.class, 0);
        }

        public LineStringContext lineString() {
            return (LineStringContext)this.getRuleContext(LineStringContext.class, 0);
        }

        public PolygonContext polygon() {
            return (PolygonContext)this.getRuleContext(PolygonContext.class, 0);
        }

        public MultiPointContext multiPoint() {
            return (MultiPointContext)this.getRuleContext(MultiPointContext.class, 0);
        }

        public MultiLineStringContext multiLineString() {
            return (MultiLineStringContext)this.getRuleContext(MultiLineStringContext.class, 0);
        }

        public CircularStringContext circularString() {
            return (CircularStringContext)this.getRuleContext(CircularStringContext.class, 0);
        }

        public TriangleContext triangle() {
            return (TriangleContext)this.getRuleContext(TriangleContext.class, 0);
        }

        public TinContext tin() {
            return (TinContext)this.getRuleContext(TinContext.class, 0);
        }

        public PolyHedralSurfaceContext polyHedralSurface() {
            return (PolyHedralSurfaceContext)this.getRuleContext(PolyHedralSurfaceContext.class, 0);
        }

        public MultiPolygonContext multiPolygon() {
            return (MultiPolygonContext)this.getRuleContext(MultiPolygonContext.class, 0);
        }

        public CompoundCurveContext compoundCurve() {
            return (CompoundCurveContext)this.getRuleContext(CompoundCurveContext.class, 0);
        }

        public CurvePolygonContext curvePolygon() {
            return (CurvePolygonContext)this.getRuleContext(CurvePolygonContext.class, 0);
        }

        public MultiCurveContext multiCurve() {
            return (MultiCurveContext)this.getRuleContext(MultiCurveContext.class, 0);
        }

        public MultiSurfaceContext multiSurface() {
            return (MultiSurfaceContext)this.getRuleContext(MultiSurfaceContext.class, 0);
        }

        public GeometryCollectionContext geometryCollection() {
            return (GeometryCollectionContext)this.getRuleContext(GeometryCollectionContext.class, 0);
        }

        public WktContext(ParserRuleContext parent, int invokingState) {
            super(parent, invokingState);
        }

        public int getRuleIndex() {
            return 0;
        }

        public void enterRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).enterWkt(this);
            }

        }

        public void exitRule(ParseTreeListener listener) {
            if (listener instanceof WKTListener) {
                ((WKTListener)listener).exitWkt(this);
            }

        }

        public <T> T accept(ParseTreeVisitor<? extends T> visitor) {
            return visitor instanceof WKTVisitor ? (T) ((WKTVisitor)visitor).visitWkt(this) : visitor.visitChildren(this);
        }
    }
}
