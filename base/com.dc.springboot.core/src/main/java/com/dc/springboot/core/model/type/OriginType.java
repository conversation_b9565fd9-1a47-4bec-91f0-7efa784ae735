package com.dc.springboot.core.model.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OriginType {

    NONE(0, null),
    BROWSER(1, "SQL窗口"),
    CLIENT(2, "客户端"),
    SCRIPT(3, "脚本"),
    IMPORT(4, "导入"),
    INVALID(5, "编译无效对象"),
    CONVERSATION(6, "会话窗口"),
    EXECUTE_LOCAL_SQL(7, "运行本地SQL"),
    RECYCLE_BIN(8, "回收站"),
    JOB_EXECUTE(9, "任务中心"),
    DYNAMIC_SQL(12, "动态SQL"),

    ORDER_EXPORT(13, "导出工单"),
    PYTHON_SDK(14, "pyddbc"),
    CROSS_DATABASE_QUERY(15, "跨库查询"),
    PRIVILEGE_CHANGE(16, "权限变更"),
    ACCOUNT_APPLICATION(17, "账号申请"),
    JOB_SYNC_PRIVILEGE(18, "权限同步"),
    BATCH_SCRIPT(19, "批量变更")
    ;

    private final int value;
    private final String name;

    public static OriginType of(Number value) {
        if (value != null) {
            for (OriginType originType : OriginType.values()) {
                if (originType.getValue() == value.intValue()) {
                    return originType;
                }
            }
        }
        return NONE;
    }

    public static boolean isBrowser(Integer value) {
        return value == BROWSER.getValue();
    }

    public static boolean isPrivilegeChange(Integer value) {
        return value == PRIVILEGE_CHANGE.getValue();
    }

}
