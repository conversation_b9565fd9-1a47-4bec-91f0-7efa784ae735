package com.dc.repository.redis.service.impl;

import com.dc.repository.redis.service.RedisService;
import com.dc.springboot.core.component.FilterIgnoreFiled;
import com.dc.springboot.core.component.JSON;
import com.dc.utils.StackTraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
public abstract class AbstractRedisService implements RedisService {

    public abstract RedisTemplate<String, Object> getRedisTemplate();

    public abstract boolean printLog();

    @Resource
    private FilterIgnoreFiled filterIgnoreFiled;

    /**
     * 返回数据库中名称为key的string的value
     *
     * @param key
     * @return
     */
    @Override
    public String get(String key) {
        return command(() -> {
            if (key != null) {
                Object o = getRedisTemplate().opsForValue().get(key);
                if (o != null) {
                    return o.toString();
                }
            }
            return null;
        }, key);
    }

    /**
     * 给数据库中名称为key的string赋予值value
     *
     * @param key
     * @return
     */
    @Override
    public boolean set(String key, Object value) {
        return command(() -> {
            try {
                getRedisTemplate().opsForValue().set(key, value);
                return true;
            } catch (Exception e) {
                log.error("set error : ", e);
                return false;
            }
        }, key, value);
    }

    /**
     * 返回名称为key的hash中field对应的value
     *
     * @param key
     * @param item
     * @return
     */
    @Override
    public Object hget(String key, String item) {
        return command(() -> getRedisTemplate().opsForHash().get(key, item), key, item);
    }

    @Override
    public boolean expire(String key, long second) {
        return command(() -> {
            try {
                if (second > 0) {
                    getRedisTemplate().expire(key, second, TimeUnit.SECONDS);
                }
                return true;
            } catch (Exception e) {
                log.error("expire error : ", e);
                return false;
            }
        }, key, second);
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     * @return
     */
    @Override
    public long del(String... key) {
        return command(() -> {
            if (key != null && key.length > 0) {
                if (key.length == 1) {
                    return Boolean.TRUE.equals(getRedisTemplate().delete(key[0])) ? 1 : 0;
                } else {
                    return getRedisTemplate().delete(Arrays.asList(key));
                }
            }
            return 0;
        }, key).longValue();
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    @Override
    public long getExpire(String key) {
        return command(() -> getRedisTemplate().getExpire(key, TimeUnit.SECONDS), key);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    @Override
    public boolean hasKey(String key) {
        return command(() -> {
            try {
                return getRedisTemplate().hasKey(key);
            } catch (Exception e) {
                log.error("hasKey error : ", e);
                return false;
            }
        }, key);
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    @Override
    public boolean set(String key, Object value, long time) {
        return command(() -> {
            try {
                if (time > 0) {
                    getRedisTemplate().opsForValue().set(key, value, time, TimeUnit.SECONDS);
                } else {
                    set(key, value);
                }
                return true;
            } catch (Exception e) {
                log.error("set error : ", e);
                return false;
            }
        }, key, value, time);
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    @Override
    public long incr(String key, long delta) {
        return command(() -> {
            if (delta < 0) {
                throw new RuntimeException("递增因子必须大于0");
            }
            return getRedisTemplate().opsForValue().increment(key, delta);
        }, key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    @Override
    public long decr(String key, long delta) {
        return command(() -> {
            if (delta < 0) {
                throw new RuntimeException("递减因子必须大于0");
            }
            return getRedisTemplate().opsForValue().increment(key, -delta);
        }, key, delta);
    }

    // ================================Map=================================

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    @Override
    public Map<Object, Object> hmget(String key) {
        return command(() -> getRedisTemplate().opsForHash().entries(key), key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    @Override
    public boolean hmset(String key, Map<String, Object> map) {
        return command(() -> {
            try {
                getRedisTemplate().opsForHash().putAll(key, map);
                return true;
            } catch (Exception e) {
                log.error("hmset error : ", e);
                return false;
            }
        }, key, map);
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    @Override
    public boolean hmset(String key, Map<String, Object> map, long time) {
        return command(() -> {
            try {
                getRedisTemplate().opsForHash().putAll(key, map);
                if (time > 0) {
                    expire(key, time);
                }
                return true;
            } catch (Exception e) {
                log.error("hmset error : ", e);
                return false;
            }
        }, key, map, time);
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    @Override
    public boolean hset(String key, String item, Object value) {
        return command(() -> {
            try {
                getRedisTemplate().opsForHash().put(key, item, value);
                return true;
            } catch (Exception e) {
                log.error("hset error : ", e);
                return false;
            }
        }, key, item, value);
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    @Override
    public boolean hset(String key, String item, Object value, long time) {
        return command(() -> {
            try {
                getRedisTemplate().opsForHash().put(key, item, value);
                if (time > 0) {
                    expire(key, time);
                }
                return true;
            } catch (Exception e) {
                log.error("hset error : ", e);
                return false;
            }
        }, key, item, value, time);
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     * @return
     */
    @Override
    public long hdel(String key, Object... item) {
        return command(() -> getRedisTemplate().opsForHash().delete(key, item), key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    @Override
    public boolean hHasKey(String key, String item) {
        return command(() -> getRedisTemplate().opsForHash().hasKey(key, item), key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    @Override
    public double hincr(String key, String item, double by) {
        return command(() -> getRedisTemplate().opsForHash().increment(key, item, by), key, item, by);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    @Override
    public long hincr(String key, String item, long by) {
        return command(() -> getRedisTemplate().opsForHash().increment(key, item, by), key, item, by);
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    @Override
    public double hdecr(String key, String item, double by) {
        return command(() -> getRedisTemplate().opsForHash().increment(key, item, -by), key, item, by);
    }

    // ============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    @Override
    public Set<Object> sGet(String key) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForSet().members(key);
            } catch (Exception e) {
                log.error("sGet error : ", e);
                return null;
            }
        }, key);
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    @Override
    public boolean sHasKey(String key, Object value) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForSet().isMember(key, value);
            } catch (Exception e) {
                log.error("sHasKey error : ", e);
                return false;
            }
        }, key, value);
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    @Override
    public long sSet(String key, Object... values) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForSet().add(key, values);
            } catch (Exception e) {
                log.error("sSet error : ", e);
                return 0;
            }
        }, key, values).longValue();
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    @Override
    public long sSetAndTime(String key, long time, Object... values) {
        return command(() -> {
            try {
                Long count = getRedisTemplate().opsForSet().add(key, values);
                if (time > 0) {
                    expire(key, time);
                }
                return count;
            } catch (Exception e) {
                log.error("sSetAndTime error : ", e);
                return 0;
            }
        }, key, time, values).longValue();
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    @Override
    public long sGetSetSize(String key) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForSet().size(key);
            } catch (Exception e) {
                log.error("sGetSetSize error : ", e);
                return 0;
            }
        }, key).longValue();
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    @Override
    public long setRemove(String key, Object... values) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForSet().remove(key, values);
            } catch (Exception e) {
                log.error("setRemove error : ", e);
                return 0;
            }
        }, key, values).longValue();
    }
    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return
     */
    @Override
    public List<Object> lGet(String key, long start, long end) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForList().range(key, start, end);
            } catch (Exception e) {
                log.error("lGet error : ", e);
                return null;
            }
        }, key, start, end);
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    @Override
    public long lGetListSize(String key) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForList().size(key);
            } catch (Exception e) {
                log.error("lGetListSize error : ", e);
                return 0;
            }
        }, key).longValue();
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    @Override
    public Object lGetIndex(String key, long index) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForList().index(key, index);
            } catch (Exception e) {
                log.error("lGetIndex error : ", e);
                return null;
            }
        }, key, index);
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    @Override
    public boolean lSet(String key, Object value) {
        return command(() -> {
            try {
                getRedisTemplate().opsForList().rightPush(key, value);
                return true;
            } catch (Exception e) {
                log.error("lSet error : ", e);
                return false;
            }
        }, key, value);
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    @Override
    public Long lSet(String key, Object value, long time) {
        return command(() -> {
            try {
                Long aLong = getRedisTemplate().opsForList().rightPush(key, value);
                if (time > 0) {
                    expire(key, time);
                }
                return aLong;
            } catch (Exception e) {
                log.error("lSet error : ", e);
                return -1L;
            }
        }, key, value, time);
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    @Override
    public boolean lSet(String key, List<Object> value) {
        return command(() -> {
            try {
                getRedisTemplate().opsForList().rightPushAll(key, value);
                return true;
            } catch (Exception e) {
                log.error("lSet error : ", e);
                return false;
            }
        }, key, value);
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    @Override
    public boolean lSet(String key, List<Object> value, long time) {
        return command(() -> {
            try {
                getRedisTemplate().opsForList().rightPushAll(key, value);
                if (time > 0) {
                    expire(key, time);
                }
                return true;
            } catch (Exception e) {
                log.error("lSet error : ", e);
                return false;
            }
        }, key, value, time);
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    @Override
    public boolean lUpdateIndex(String key, long index, Object value) {
        return command(() -> {
            try {
                getRedisTemplate().opsForList().set(key, index, value);
                return true;
            } catch (Exception e) {
                log.error("lUpdateIndex error : ", e);
                return false;
            }
        }, key, index, value);
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    @Override
    public long lRemove(String key, long count, Object value) {
        return command(() -> {
            try {
                return getRedisTemplate().opsForList().remove(key, count, value);
            } catch (Exception e) {
                log.error("lRemove error : ", e);
                return 0;
            }
        }, key, count, value).longValue();
    }

    @Override
    public String lRpop(String key) {
        return command(() -> {
            if (key != null) {
                Object value = getRedisTemplate().opsForList().rightPop(key);
                if (value != null) {
                    return value.toString();
                }
            }
            return null;
        }, key);
    }

    @Override
    public long lLPush(String key, String value, long time) {
        return command(() -> {
            try {
                Long result = getRedisTemplate().opsForList().leftPush(key, value);

                if (time > 0) {
                    expire(key, time);
                }
                return result;
            } catch (Exception e) {
                log.error("lLPush error : ", e);
                return 0;
            }
        }, key, value).longValue();
    }

    private  <T> T command(Supplier<T> supplier, Object... args) {

        long ctm = System.currentTimeMillis();

        final String format = "%11s: %s";

        try {
            log.debug("==>{}", String.format(format, "Command", StackTraceUtils.getPreviousMethodName()));
            if (printLog()) {
                log.debug("==>{}", String.format(format, "Parameters", Arrays.toString(args)));
            }
        } catch (Exception ignored) {
            // nothing to do here
        }

        T t = supplier.get();

        try {
            if (printLog()) {
                String object;
                if (t == null) {
                    object = null;
                } else if (t instanceof String) {
                    object = (String) t;
                } else {
                    object = JSON.toJSONString(t);
                }
                log.debug("<=={}", String.format(format, "Json", filterIgnoreFiled.handle(object)));
            }
            log.debug(String.format("<== ExecuteTime: %s ms", System.currentTimeMillis() - ctm));
        } catch (Exception ignored) {
            // nothing to do here
        }

        return t;
    }
}
